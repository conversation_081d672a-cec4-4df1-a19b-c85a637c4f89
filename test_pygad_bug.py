#!/usr/bin/env python3
"""Test script to reproduce the PyGAD optimizer bug with n_cores."""

import numpy as np
import sys
import os

# Add the optimagic source to the path
sys.path.insert(0, 'optimagic_/src')

# Simple test without full optimagic import to avoid dependency issues
import pygad
from optimagic.optimizers.pygad_new import Pygad


def square_function(params):
    """Simple square function to minimize: sum(x^2)"""
    return np.sum(params**2)


def test_pygad_direct():
    """Test PyGAD directly to isolate the batch function issue"""
    print("Testing PyGAD directly with batch fitness function...")

    def fitness_func_single(ga_instance, solution, solution_idx):
        """Single solution fitness function"""
        return -np.sum(solution**2)  # Negative because PyGAD maximizes

    def fitness_func_batch(ga_instance, batch_solutions, batch_indices):
        """Batch fitness function - this is where the bug occurs"""
        print(f"Batch function called with batch_solutions shape: {batch_solutions.shape}")
        print(f"Batch indices: {batch_indices}")

        # Calculate fitness for each solution in the batch
        fitness_values = []
        for i in range(batch_solutions.shape[0]):
            solution = batch_solutions[i]
            fitness = -np.sum(solution**2)  # Negative because PyGAD maximizes
            fitness_values.append(fitness)

        print(f"Returning fitness values: {fitness_values}")
        return fitness_values

    # Test single core first
    print("\n--- Testing single core (no batch) ---")
    try:
        ga_single = pygad.GA(
            num_generations=5,
            num_parents_mating=4,
            fitness_func=fitness_func_single,
            sol_per_pop=8,
            num_genes=3,
            gene_space=[{'low': -5, 'high': 5}] * 3,
            random_seed=42
        )
        ga_single.run()
        solution, fitness, _ = ga_single.best_solution()
        print(f"✓ Single core: Best fitness = {fitness:.6f}, solution = {solution}")
    except Exception as e:
        print(f"✗ Single core failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    # Test multi core with batch
    print("\n--- Testing multi core (with batch) ---")
    try:
        ga_batch = pygad.GA(
            num_generations=5,
            num_parents_mating=4,
            fitness_func=fitness_func_batch,
            fitness_batch_size=4,  # This triggers batch mode
            sol_per_pop=8,
            num_genes=3,
            gene_space=[{'low': -5, 'high': 5}] * 3,
            random_seed=42
        )
        ga_batch.run()
        solution, fitness, _ = ga_batch.best_solution()
        print(f"✓ Multi core: Best fitness = {fitness:.6f}, solution = {solution}")
        return True
    except Exception as e:
        print(f"✗ Multi core failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Testing PyGAD optimizer batch function bug...")
    print("Expected optimal solution: [0, 0, 0] with value 0")
    print("=" * 60)

    # Test PyGAD directly to isolate the issue
    success = test_pygad_direct()

    print("\n" + "=" * 60)
    print("SUMMARY:")
    if success:
        print("✅ PyGAD batch function works correctly")
    else:
        print("🐛 PyGAD batch function has issues")
