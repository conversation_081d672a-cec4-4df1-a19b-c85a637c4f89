#!/usr/bin/env python3
"""Test PyGAD integration directly without full optimagic import."""

import numpy as np
import sys
import os

# Add the optimagic source to the path
sys.path.insert(0, 'optimagic_/src')

# Import pygad directly
import pygad

# Mock the optimagic components we need
class MockDirection:
    MINIMIZE = "minimize"
    value = "minimize"

class MockProblem:
    def __init__(self):
        self.direction = MockDirection()
        self.bounds = MockBounds()
        
    def fun(self, x):
        """Single evaluation"""
        return np.sum(x**2)
    
    def batch_fun(self, x_list, n_cores, batch_size=None):
        """Batch evaluation"""
        print(f"batch_fun called with {len(x_list)} solutions, n_cores={n_cores}, batch_size={batch_size}")
        results = []
        for i, x in enumerate(x_list):
            result = np.sum(x**2)
            results.append(result)
            print(f"  solution[{i}]: {x} -> result: {result}")
        print(f"  returning: {results}")
        return results

class MockBounds:
    def __init__(self):
        self.lower = np.array([-5.0, -5.0, -5.0])
        self.upper = np.array([5.0, 5.0, 5.0])

class MockConverter:
    def params_to_internal(self, params):
        return np.array(params)
    
    def params_from_internal(self, internal):
        return internal


def test_batch_function_integration():
    """Test the batch function integration directly"""
    print("Testing batch function integration...")
    
    problem = MockProblem()
    problem.converter = MockConverter()
    
    # Test the batch fitness function from our implementation
    def _fitness_func_batch(
        _ga_instance,
        batch_solutions,
        _batch_indices,
    ):
        print(f"\n_fitness_func_batch called:")
        print(f"  batch_solutions shape: {batch_solutions.shape}")
        print(f"  batch_indices: {_batch_indices}")
        
        # Convert 2D array to list of 1D arrays for batch_fun
        solutions_list = [
            batch_solutions[i] for i in range(batch_solutions.shape[0])
        ]
        
        batch_results = problem.batch_fun(
            solutions_list,
            n_cores=2,
            batch_size=2,
        )

        # PyGAD maximizes fitness, so convert based on direction
        if problem.direction.value == "minimize":
            return [-float(result) for result in batch_results]  # Lower criterion = higher fitness
        else:  # maximize
            return [float(result) for result in batch_results]   # Higher criterion = higher fitness
    
    # Test with PyGAD
    print("\n" + "="*50)
    print("Testing with PyGAD batch mode")
    print("="*50)
    
    try:
        ga_instance = pygad.GA(
            num_generations=3,
            num_parents_mating=2,
            fitness_func=_fitness_func_batch,
            fitness_batch_size=2,  # This triggers batch mode
            sol_per_pop=4,
            num_genes=3,
            gene_space=[{'low': -2, 'high': 2}] * 3,
            random_seed=42
        )
        
        ga_instance.run()
        solution, fitness, _ = ga_instance.best_solution()
        print(f"\n✓ Batch integration test passed!")
        print(f"  Best fitness: {fitness:.6f}")
        print(f"  Best solution: {solution}")
        print(f"  Actual function value: {np.sum(solution**2):.6f}")
        return True
        
    except Exception as e:
        print(f"\n✗ Batch integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Testing PyGAD batch function integration...")
    print("This tests our fix for the n_cores issue")
    print("=" * 60)
    
    success = test_batch_function_integration()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    if success:
        print("✅ Batch function integration works correctly")
        print("🎉 The n_cores bug should be fixed!")
    else:
        print("🐛 Batch function integration has issues")
