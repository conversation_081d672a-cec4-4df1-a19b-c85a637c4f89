"""Examples demonstrating the new PyGAD mutation system in optimagic.

This file shows how to use the new mutation configuration system that eliminates
complex parameter interdependencies and provides type-safe mutation configuration.
"""

import numpy as np
import optimagic as om


def simple_quadratic(x):
    """Simple quadratic function for optimization."""
    return np.sum(x**2)


def main():
    """Demonstrate different mutation configurations."""
    
    # Define a simple optimization problem
    params = np.array([1.0, 2.0, 3.0])
    bounds = om.Bounds(lower=np.array([-5.0, -5.0, -5.0]), 
                       upper=np.array([5.0, 5.0, 5.0]))
    
    print("PyGAD Mutation System Examples")
    print("=" * 40)
    
    # Example 1: String-based mutation (backward compatible)
    print("\n1. String-based mutations (backward compatible):")

    algos_string = [
        om.algos.pygad(mutation="random", num_generations=5),
        om.algos.pygad(mutation="swap", num_generations=5),
        om.algos.pygad(mutation="inversion", num_generations=5),
        om.algos.pygad(mutation="scramble", num_generations=5),
        om.algos.pygad(mutation="adaptive", num_generations=5),
    ]

    for algo in algos_string:
        print(f"   - {algo.mutation} mutation: {type(algo.mutation).__name__}")

    # Example 2: Class-based mutations (default configurations)
    print("\n2. Class-based mutations (default configurations):")

    algos_class = [
        om.algos.pygad(mutation=om.pygad.RandomMutation, num_generations=5),
        om.algos.pygad(mutation=om.pygad.SwapMutation, num_generations=5),
        om.algos.pygad(mutation=om.pygad.InversionMutation, num_generations=5),
        om.algos.pygad(mutation=om.pygad.ScrambleMutation, num_generations=5),
        om.algos.pygad(mutation=om.pygad.AdaptiveMutation, num_generations=5),  # Now works with defaults!
    ]

    for algo in algos_class:
        print(f"   - {algo.mutation.__name__} class: default configuration")

    # Example 3: Configured mutations with specific parameters
    print("\n3. Instance-based mutations (custom configurations):")
    
    # Random mutation with custom parameters
    random_mutation = om.pygad.RandomMutation(
        probability=0.1,           # 10% chance per gene
        by_replacement=True        # Replace values instead of adding
        # Note: min_val/max_val not needed - optimagic uses gene_space from bounds
    )
    algo_random = om.algos.pygad(mutation=random_mutation, num_generations=5)
    print(f"   - Custom random mutation: probability={random_mutation.probability}, by_replacement={random_mutation.by_replacement}")
    
    # Swap mutation (no configurable parameters - hardcoded in PyGAD)
    swap_mutation = om.pygad.SwapMutation()
    algo_swap = om.algos.pygad(mutation=swap_mutation, num_generations=5)
    print(f"   - Swap mutation: hardcoded behavior (no parameters)")

    # Inversion mutation (no configurable parameters - hardcoded in PyGAD)
    inversion_mutation = om.pygad.InversionMutation()
    algo_inversion = om.algos.pygad(mutation=inversion_mutation, num_generations=5)
    print(f"   - Inversion mutation: hardcoded behavior (no parameters)")

    # Scramble mutation (no configurable parameters - hardcoded in PyGAD)
    scramble_mutation = om.pygad.ScrambleMutation()
    algo_scramble = om.algos.pygad(mutation=scramble_mutation, num_generations=5)
    print(f"   - Scramble mutation: hardcoded behavior (no parameters)")
    
    # Example 4: Adaptive mutations (the main improvement)
    print("\n4. Adaptive mutations (eliminates parameter interdependencies):")

    # Default adaptive mutation
    adaptive_default = om.pygad.AdaptiveMutation()  # Uses default values
    algo_adaptive_default = om.algos.pygad(mutation=adaptive_default, num_generations=5)
    print(f"   - Adaptive (default): bad={adaptive_default.probability_bad}, "
          f"good={adaptive_default.probability_good}")

    # Custom adaptive mutation with probability-based control
    adaptive_prob = om.pygad.AdaptiveMutation(
        probability_bad=0.3,     # High mutation for bad solutions
        probability_good=0.1,    # Low mutation for good solutions
        by_replacement=True
    )
    algo_adaptive_prob = om.algos.pygad(mutation=adaptive_prob, num_generations=5)
    print(f"   - Adaptive (custom probability): bad={adaptive_prob.probability_bad}, "
          f"good={adaptive_prob.probability_good}")

    # Adaptive mutation with gene count control
    adaptive_genes = om.pygad.AdaptiveMutation(
        probability_bad=None,    # Disable probability to use num_genes
        probability_good=None,
        num_genes_bad=5,         # Mutate 5 genes for bad solutions
        num_genes_good=1         # Mutate 1 gene for good solutions
    )
    algo_adaptive_genes = om.algos.pygad(mutation=adaptive_genes, num_generations=5)
    print(f"   - Adaptive (gene count): bad={adaptive_genes.num_genes_bad}, "
          f"good={adaptive_genes.num_genes_good}")

    # Adaptive mutation with percentage control
    adaptive_percent = om.pygad.AdaptiveMutation(
        probability_bad=None,    # Disable probability to use percent_genes
        probability_good=None,
        percent_genes_bad=25.0,  # Mutate 25% of genes for bad solutions
        percent_genes_good=5.0   # Mutate 5% of genes for good solutions
    )
    algo_adaptive_percent = om.algos.pygad(mutation=adaptive_percent, num_generations=5)
    print(f"   - Adaptive (percentage): bad={adaptive_percent.percent_genes_bad}%, "
          f"good={adaptive_percent.percent_genes_good}%")
    
    # Example 5: Comparison with old system (commented out as it would cause errors)
    print("\n5. Benefits over the old system:")
    print("   OLD (complex interdependencies):")
    print("   ❌ mutation_type='adaptive' + mutation_probability=[0.3, 0.1]")
    print("   ❌ Confusing parameter relationships")
    print("   ❌ Type errors when mixing adaptive/non-adaptive")
    print()
    print("   NEW (clear and type-safe):")
    print("   ✅ AdaptiveMutation(probability_poor=0.3, probability_good=0.1)")
    print("   ✅ Clear parameter names")
    print("   ✅ Type safety and validation")
    print("   ✅ No parameter interdependencies")
    
    # Example 6: Demonstrate that the algorithms can be created successfully
    print("\n6. Algorithm creation verification:")
    
    all_algorithms = [
        ("String random", om.algos.pygad(mutation="random", num_generations=2)),
        ("Class random", om.algos.pygad(mutation=om.pygad.RandomMutation, num_generations=2)),
        ("Instance random", om.algos.pygad(mutation=random_mutation, num_generations=2)),
        ("Adaptive probability", om.algos.pygad(mutation=adaptive_prob, num_generations=2)),
        ("Adaptive genes", om.algos.pygad(mutation=adaptive_genes, num_generations=2)),
    ]
    
    for name, algo in all_algorithms:
        try:
            # Test parameter conversion
            if hasattr(algo, '_convert_mutation_to_pygad_params'):
                params = algo._convert_mutation_to_pygad_params()
                print(f"   ✅ {name}: mutation_type='{params['mutation_type']}'")
            else:
                print(f"   ✅ {name}: algorithm created successfully")
        except Exception as e:
            print(f"   ❌ {name}: {e}")
    
    print("\n" + "=" * 40)
    print("All examples completed successfully!")
    print("\nKey improvements:")
    print("- No more complex parameter interdependencies")
    print("- Clear, descriptive parameter names (poor/good vs low/high fitness)")
    print("- Type-safe configuration")
    print("- Multiple usage patterns: strings, classes, instances, custom functions")
    print("- Backward compatibility with string mutations")
    print("- Works for both minimize and maximize problems")
    print("- Clean separation: dataclasses for config, optimizer handles conversion")


if __name__ == "__main__":
    main()
