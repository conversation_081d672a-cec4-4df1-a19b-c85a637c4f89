#!/usr/bin/env python3
"""Examples of using all PyGAD mutation types through optimagic interface.

This file demonstrates all supported mutation patterns:
1. String mutations (backward compatible)
2. Class mutations (default configuration)
3. Instance mutations (custom configuration)
4. Custom function mutations
5. None mutations (disabled)

Each example shows how to configure the mutation and what PyGAD parameters
are generated internally.
"""

import numpy as np
import optimagic as om


def simple_objective(x):
    """Simple quadratic objective function for testing."""
    return np.sum(x**2)


def custom_mutation_function(offspring, ga_instance):
    """Custom mutation function example.
    
    Args:
        offspring: 2D array of offspring solutions to be mutated.
        ga_instance: The PyGAD GA instance.
        
    Returns:
        2D array of mutated offspring solutions.
    """
    # Simple random mutation with 10% probability
    for i in range(len(offspring)):
        for j in range(len(offspring[i])):
            if np.random.random() < 0.1:
                offspring[i][j] = np.random.uniform(-1, 1)
    return offspring


def demonstrate_string_mutations():
    """Demonstrate string-based mutations (backward compatible)."""
    print("\n" + "="*60)
    print("📝 STRING-BASED MUTATIONS (Backward Compatible)")
    print("="*60)
    
    # Define problem
    bounds = om.Bounds(lower=np.array([-5, -5, -5]), upper=np.array([5, 5, 5]))
    
    string_mutations = ["random", "swap", "inversion", "scramble", "adaptive"]
    
    for mutation_str in string_mutations:
        print(f"\n🔧 Using mutation='{mutation_str}':")
        
        # Create algorithm with string mutation
        algo = om.algos.pygad(
            mutation=mutation_str,
            num_generations=2,  # Small for demo
            sol_per_pop=10
        )
        
        print(f"   ✅ Algorithm configured with mutation='{mutation_str}'")
        print(f"   📋 This will be converted to PyGAD parameters internally")
        
        # You can run the optimization like this:
        # result = om.minimize(simple_objective, x0=np.array([1, 2, 3]), bounds=bounds, algorithm=algo)


def demonstrate_class_mutations():
    """Demonstrate class-based mutations (default configuration)."""
    print("\n" + "="*60)
    print("🏗️  CLASS-BASED MUTATIONS (Default Configuration)")
    print("="*60)
    
    bounds = om.Bounds(lower=np.array([-5, -5, -5]), upper=np.array([5, 5, 5]))
    
    mutation_classes = [
        (om.pygad.RandomMutation, "Default random mutation"),
        (om.pygad.SwapMutation, "Default swap mutation"),
        (om.pygad.InversionMutation, "Default inversion mutation"),
        (om.pygad.ScrambleMutation, "Default scramble mutation"),
        (om.pygad.AdaptiveMutation, "Default adaptive mutation (0.2/0.05 probabilities)"),
    ]
    
    for mutation_class, description in mutation_classes:
        print(f"\n🔧 Using mutation={mutation_class.__name__}:")
        print(f"   📝 {description}")
        
        # Create algorithm with class mutation
        algo = om.algos.pygad(
            mutation=mutation_class,  # Pass the class, not an instance
            num_generations=2,
            sol_per_pop=10
        )
        
        print(f"   ✅ Algorithm configured with mutation={mutation_class.__name__}")
        print(f"   📋 Default instance will be created automatically")


def demonstrate_instance_mutations():
    """Demonstrate instance-based mutations (custom configuration)."""
    print("\n" + "="*60)
    print("🔧 INSTANCE-BASED MUTATIONS (Custom Configuration)")
    print("="*60)
    
    bounds = om.Bounds(lower=np.array([-5, -5, -5]), upper=np.array([5, 5, 5]))
    
    # RandomMutation with custom parameters
    print(f"\n🎯 Custom RandomMutation:")
    random_mutation = om.pygad.RandomMutation(
        probability=0.15,           # 15% mutation probability per gene
        by_replacement=True,        # Replace gene values instead of adding
        num_genes=2                 # Mutate exactly 2 genes (overrides probability)
    )
    
    algo = om.algos.pygad(
        mutation=random_mutation,
        num_generations=2,
        sol_per_pop=10
    )
    print(f"   ✅ Custom RandomMutation: probability=0.15, by_replacement=True, num_genes=2")
    
    # AdaptiveMutation with probability-based control
    print(f"\n🎯 Custom AdaptiveMutation (probability-based):")
    adaptive_prob = om.pygad.AdaptiveMutation(
        probability_bad=0.3,       # 30% mutation for bad solutions
        probability_good=0.1,      # 10% mutation for good solutions
        by_replacement=True
    )
    
    algo = om.algos.pygad(
        mutation=adaptive_prob,
        num_generations=2,
        sol_per_pop=10
    )
    print(f"   ✅ Adaptive (probability): poor=30%, good=10%, by_replacement=True")
    
    # AdaptiveMutation with gene count control
    print(f"\n🎯 Custom AdaptiveMutation (gene count-based):")
    adaptive_genes = om.pygad.AdaptiveMutation(
        probability_poor=None,      # Disable probability to use num_genes
        probability_good=None,
        num_genes_poor=5,           # Mutate 5 genes for poor solutions
        num_genes_good=1            # Mutate 1 gene for good solutions
    )
    
    algo = om.algos.pygad(
        mutation=adaptive_genes,
        num_generations=2,
        sol_per_pop=10
    )
    print(f"   ✅ Adaptive (gene count): poor=5 genes, good=1 gene")
    
    # AdaptiveMutation with percentage control
    print(f"\n🎯 Custom AdaptiveMutation (percentage-based):")
    adaptive_percent = om.pygad.AdaptiveMutation(
        probability_poor=None,      # Disable probability to use percent_genes
        probability_good=None,
        percent_genes_poor=25.0,    # Mutate 25% of genes for poor solutions
        percent_genes_good=5.0      # Mutate 5% of genes for good solutions
    )
    
    algo = om.algos.pygad(
        mutation=adaptive_percent,
        num_generations=2,
        sol_per_pop=10
    )
    print(f"   ✅ Adaptive (percentage): poor=25%, good=5%")


def demonstrate_custom_function_mutations():
    """Demonstrate custom function mutations."""
    print("\n" + "="*60)
    print("⚙️  CUSTOM FUNCTION MUTATIONS")
    print("="*60)
    
    bounds = om.Bounds(lower=np.array([-5, -5, -5]), upper=np.array([5, 5, 5]))
    
    print(f"\n🎯 Custom mutation function:")
    print(f"   📝 User-defined function with signature (offspring, ga_instance) -> offspring")
    
    algo = om.algos.pygad(
        mutation=custom_mutation_function,
        num_generations=2,
        sol_per_pop=10
    )
    
    print(f"   ✅ Algorithm configured with custom mutation function")
    print(f"   📋 Function will be passed directly to PyGAD")


def demonstrate_disabled_mutations():
    """Demonstrate disabled mutations."""
    print("\n" + "="*60)
    print("🚫 DISABLED MUTATIONS")
    print("="*60)
    
    bounds = om.Bounds(lower=np.array([-5, -5, -5]), upper=np.array([5, 5, 5]))
    
    print(f"\n🎯 Disabled mutation (None):")
    print(f"   📝 No mutation will be applied - only crossover and selection")
    
    algo = om.algos.pygad(
        mutation=None,
        num_generations=2,
        sol_per_pop=10
    )
    
    print(f"   ✅ Algorithm configured with mutation=None")
    print(f"   📋 PyGAD will skip mutation step entirely")


def demonstrate_complete_example():
    """Demonstrate a complete optimization example."""
    print("\n" + "="*60)
    print("🚀 COMPLETE OPTIMIZATION EXAMPLE")
    print("="*60)
    
    # Define optimization problem
    def objective(x):
        """Minimize sum of squares with some noise."""
        return np.sum(x**2) + 0.1 * np.sum(np.sin(10 * x))
    
    x0 = np.array([2.0, -1.5, 3.0])
    bounds = om.Bounds(lower=np.array([-5, -5, -5]), upper=np.array([5, 5, 5]))
    
    # Use custom adaptive mutation
    adaptive_mutation = om.pygad.AdaptiveMutation(
        probability_bad=0.25,      # High mutation for bad solutions
        probability_good=0.05,     # Low mutation for good solutions
        by_replacement=True
    )
    
    # Create algorithm
    algo = om.algos.pygad(
        mutation=adaptive_mutation,
        num_generations=10,
        sol_per_pop=20,
        num_parents_mating=10,
        crossover_probability=0.8,
        keep_elitism=2
    )
    
    print(f"🎯 Running optimization with adaptive mutation...")
    print(f"   📝 Problem: minimize f(x) = sum(x²) + 0.1*sum(sin(10x))")
    print(f"   🔧 Mutation: AdaptiveMutation(probability_bad=0.25, probability_good=0.05)")
    
    # Run optimization
    result = om.minimize(
        fun=objective,
        x0=x0,
        bounds=bounds,
        algorithm=algo
    )
    
    print(f"   ✅ Optimization completed!")
    print(f"   📊 Best solution: {result.x}")
    print(f"   📊 Best fitness: {result.fun}")
    print(f"   📊 Converged: {result.success}")


if __name__ == "__main__":
    print("🚀 PyGAD Mutation System Usage Examples")
    print("="*60)
    print("This demonstrates all supported mutation patterns through optimagic interface")
    
    try:
        demonstrate_string_mutations()
        demonstrate_class_mutations()
        demonstrate_instance_mutations()
        demonstrate_custom_function_mutations()
        demonstrate_disabled_mutations()
        demonstrate_complete_example()
        
        print("\n" + "="*60)
        print("🎉 ALL EXAMPLES COMPLETED SUCCESSFULLY!")
        print("✅ String mutations: mutation='random', 'swap', etc.")
        print("✅ Class mutations: mutation=om.pygad.RandomMutation")
        print("✅ Instance mutations: mutation=om.pygad.RandomMutation(probability=0.1)")
        print("✅ Custom functions: mutation=my_custom_function")
        print("✅ Disabled mutations: mutation=None")
        print("✅ Complete optimization example with adaptive mutation")
        print("="*60)
        
    except Exception as e:
        print(f"\n❌ Example failed: {e}")
        import traceback
        traceback.print_exc()
