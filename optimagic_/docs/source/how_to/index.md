(how-to)=

# How-to Guides

How-to Guides show how to achieve specific tasks. In many cases they show you how to use
advanced options. For a more basic introduction, check out the [tutorials](tutorials).

```{toctree}
---
maxdepth: 1
---
how_to_criterion_function
how_to_start_parameters
how_to_derivatives
how_to_specify_algorithm_and_algo_options
how_to_algorithm_selection
how_to_bounds
how_to_constraints
how_to_globalization
how_to_multistart
how_to_visualize_histories
how_to_scaling
how_to_logging
how_to_errors_during_optimization
how_to_slice_plot
how_to_benchmarking
how_to_add_optimizers
how_to_document_optimizers
```
