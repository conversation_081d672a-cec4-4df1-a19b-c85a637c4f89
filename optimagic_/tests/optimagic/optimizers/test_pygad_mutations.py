"""Tests for PyGAD mutation configurations."""

import numpy as np
import pytest

from optimagic.config import IS_PYGAD_INSTALLED

if IS_PYGAD_INSTALLED:
    from optimagic.optimizers.pygad_mutations import (
        AdaptiveMutation,
        InversionMutation,
        RandomMutation,
        ScrambleMutation,
        SwapMutation,
    )
    from optimagic.optimizers.pygad_optimizer import Pygad


@pytest.mark.skipif(not IS_PYGAD_INSTALLED, reason="PyGAD not installed")
class TestPygadMutations:
    """Test PyGAD mutation configurations."""

    def test_random_mutation_creation(self):
        """Test creating RandomMutation instances."""
        # Default parameters
        mutation = RandomMutation()
        assert mutation.probability is None
        assert mutation.num_genes is None
        assert mutation.percent_genes == "default"
        assert mutation.by_replacement is False
        assert mutation.min_val == -1.0
        assert mutation.max_val == 1.0

        # Custom parameters
        mutation = RandomMutation(
            probability=0.1,
            by_replacement=True
        )
        assert mutation.probability == 0.1
        assert mutation.by_replacement is True

    def test_swap_mutation_creation(self):
        """Test creating SwapMutation instances."""
        # SwapMutation has no parameters (hardcoded in PyGAD)
        mutation = SwapMutation()
        # Should be an empty dataclass
        assert hasattr(mutation, '__dataclass_fields__')

    def test_inversion_mutation_creation(self):
        """Test creating InversionMutation instances."""
        # InversionMutation has no parameters (hardcoded in PyGAD)
        mutation = InversionMutation()
        # Should be an empty dataclass
        assert hasattr(mutation, '__dataclass_fields__')

    def test_scramble_mutation_creation(self):
        """Test creating ScrambleMutation instances."""
        # ScrambleMutation has no parameters (hardcoded in PyGAD)
        mutation = ScrambleMutation()
        # Should be an empty dataclass
        assert hasattr(mutation, '__dataclass_fields__')

    def test_adaptive_mutation_creation(self):
        """Test creating AdaptiveMutation instances."""
        # Default configuration
        mutation = AdaptiveMutation()
        assert mutation.probability_poor == 0.2
        assert mutation.probability_good == 0.05
        assert mutation.by_replacement is False

        # Custom configuration with probabilities
        mutation = AdaptiveMutation(
            probability_poor=0.3,
            probability_good=0.1,
            by_replacement=True
        )
        assert mutation.probability_poor == 0.3
        assert mutation.probability_good == 0.1
        assert mutation.by_replacement is True

        # Valid configuration with num_genes (disable probability)
        mutation = AdaptiveMutation(
            probability_poor=None,
            probability_good=None,
            num_genes_poor=5,
            num_genes_good=2
        )
        assert mutation.probability_poor is None
        assert mutation.probability_good is None
        assert mutation.num_genes_poor == 5
        assert mutation.num_genes_good == 2

        # Valid configuration with percent_genes (disable probability)
        mutation = AdaptiveMutation(
            probability_poor=None,
            probability_good=None,
            percent_genes_poor=20.0,
            percent_genes_good=5.0
        )
        assert mutation.probability_poor is None
        assert mutation.probability_good is None
        assert mutation.percent_genes_poor == 20.0
        assert mutation.percent_genes_good == 5.0

    def test_adaptive_mutation_validation(self):
        """Test AdaptiveMutation parameter validation."""
        # Should raise error if no poor parameters specified
        mutation = AdaptiveMutation(probability_good=0.1)
        with pytest.raises(ValueError, match="At least one parameter must be specified for poor fitness"):
            mutation.validate_parameters()

        # Should raise error if no good parameters specified
        mutation = AdaptiveMutation(probability_poor=0.3)
        with pytest.raises(ValueError, match="At least one parameter must be specified for good fitness"):
            mutation.validate_parameters()

    def test_pygad_optimizer_with_string_mutations(self):
        """Test Pygad optimizer with string mutation types."""
        # Test all string mutation types
        for mutation_type in ["random", "swap", "inversion", "scramble", "adaptive"]:
            optimizer = Pygad(mutation=mutation_type, num_generations=1)
            assert optimizer.mutation == mutation_type

    def test_pygad_optimizer_with_mutation_classes(self):
        """Test Pygad optimizer with mutation classes (not instances)."""
        # Random mutation class
        optimizer = Pygad(mutation=RandomMutation, num_generations=1)
        assert optimizer.mutation is RandomMutation

        # Swap mutation class
        optimizer = Pygad(mutation=SwapMutation, num_generations=1)
        assert optimizer.mutation is SwapMutation

        # AdaptiveMutation class (now works with defaults)
        optimizer = Pygad(mutation=AdaptiveMutation, num_generations=1)
        assert optimizer.mutation is AdaptiveMutation

    def test_pygad_optimizer_with_mutation_instances(self):
        """Test Pygad optimizer with mutation instances."""
        # Random mutation
        mutation = RandomMutation(probability=0.1, by_replacement=True)
        optimizer = Pygad(mutation=mutation, num_generations=1)
        assert optimizer.mutation is mutation

        # Swap mutation (no parameters)
        mutation = SwapMutation()
        optimizer = Pygad(mutation=mutation, num_generations=1)
        assert optimizer.mutation is mutation

        # Adaptive mutation
        mutation = AdaptiveMutation(probability_poor=0.3, probability_good=0.1)
        optimizer = Pygad(mutation=mutation, num_generations=1)
        assert optimizer.mutation is mutation

    def test_mutation_parameter_conversion(self):
        """Test conversion of mutation instances to PyGAD parameters."""
        optimizer = Pygad(num_generations=1)

        # Test string mutation conversion
        optimizer = Pygad(mutation="random", num_generations=1)
        params = optimizer._convert_mutation_to_pygad_params()
        assert params["mutation_type"] == "random"
        assert params["mutation_probability"] is None
        assert params["mutation_percent_genes"] == "default"

        # Test class mutation conversion
        optimizer = Pygad(mutation=RandomMutation, num_generations=1)
        params = optimizer._convert_mutation_to_pygad_params()
        assert params["mutation_type"] == "random"
        assert params["mutation_probability"] is None
        assert params["mutation_percent_genes"] == "default"
        assert params["mutation_by_replacement"] is False

        # Test AdaptiveMutation class conversion (with defaults)
        optimizer = Pygad(mutation=AdaptiveMutation, num_generations=1)
        params = optimizer._convert_mutation_to_pygad_params()
        assert params["mutation_type"] == "adaptive"
        assert params["mutation_probability"] == [0.2, 0.05]  # Default values
        assert params["mutation_by_replacement"] is False

        # Test RandomMutation conversion
        mutation = RandomMutation(probability=0.1, by_replacement=True)
        optimizer = Pygad(mutation=mutation, num_generations=1)
        params = optimizer._convert_mutation_to_pygad_params()
        assert params["mutation_type"] == "random"
        assert params["mutation_probability"] == 0.1
        assert params["mutation_by_replacement"] is True
        # min_val/max_val not included because optimagic always provides gene_space

        # Test SwapMutation conversion (no parameters)
        mutation = SwapMutation()
        optimizer = Pygad(mutation=mutation, num_generations=1)
        params = optimizer._convert_mutation_to_pygad_params()
        assert params["mutation_type"] == "swap"
        assert params["mutation_probability"] is None
        assert params["mutation_percent_genes"] == "default"
        assert params["mutation_by_replacement"] is False

        # Test AdaptiveMutation conversion
        mutation = AdaptiveMutation(
            probability_poor=0.3,
            probability_good=0.1,
            by_replacement=True
        )
        optimizer = Pygad(mutation=mutation, num_generations=1)
        params = optimizer._convert_mutation_to_pygad_params()
        assert params["mutation_type"] == "adaptive"
        assert params["mutation_probability"] == [0.3, 0.1]
        assert params["mutation_by_replacement"] is True

    def test_none_mutation(self):
        """Test disabling mutation with None."""
        optimizer = Pygad(mutation=None, num_generations=1)
        params = optimizer._convert_mutation_to_pygad_params()
        assert params["mutation_type"] is None
        assert params["mutation_probability"] is None


@pytest.mark.skipif(not IS_PYGAD_INSTALLED, reason="PyGAD not installed")
def test_optimagic_pygad_namespace():
    """Test that mutation classes are available in optimagic.pygad namespace."""
    import optimagic as om
    
    # Test that classes are accessible
    assert hasattr(om.pygad, "RandomMutation")
    assert hasattr(om.pygad, "SwapMutation")
    assert hasattr(om.pygad, "InversionMutation")
    assert hasattr(om.pygad, "ScrambleMutation")
    assert hasattr(om.pygad, "AdaptiveMutation")
    
    # Test that we can create instances
    mutation = om.pygad.RandomMutation(probability=0.1)
    assert mutation.probability == 0.1
    
    adaptive = om.pygad.AdaptiveMutation(probability_poor=0.3, probability_good=0.1)
    assert adaptive.probability_poor == 0.3
    assert adaptive.probability_good == 0.1


@pytest.mark.skipif(IS_PYGAD_INSTALLED, reason="PyGAD is installed")
def test_pygad_not_installed_error():
    """Test that helpful errors are raised when PyGAD is not installed."""
    import optimagic as om
    
    with pytest.raises(ImportError, match="PyGAD is not installed"):
        om.pygad.RandomMutation()
    
    with pytest.raises(ImportError, match="PyGAD is not installed"):
        om.pygad.AdaptiveMutation()


if __name__ == "__main__":
    pytest.main([__file__])
