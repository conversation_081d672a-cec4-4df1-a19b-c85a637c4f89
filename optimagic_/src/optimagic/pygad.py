"""PyGAD-specific configurations and utilities for optimagic.

This module provides PyGAD-specific mutation configurations that can be used
with the pygad optimizer in optimagic.

Example:
    import optimagic as om
    
    # Use default random mutation
    algo = om.algos.pygad(mutation="random")
    
    # Use configured random mutation
    algo = om.algos.pygad(
        mutation=om.pygad.RandomMutation(
            probability=0.1,
            by_replacement=True,
            min_val=-2.0,
            max_val=2.0
        )
    )
    
    # Use adaptive mutation
    algo = om.algos.pygad(
        mutation=om.pygad.AdaptiveMutation(
            probability_bad=0.3,    # high mutation for bad solutions
            probability_good=0.1,   # low mutation for good solutions
        )
    )
"""

from optimagic.config import IS_PYGAD_INSTALLED

if IS_PYGAD_INSTALLED:
    from optimagic.optimizers.pygad_mutations import (
        AdaptiveMutation,
        BaseMutation,
        InversionMutation,
        RandomMutation,
        ScrambleMutation,
        SwapMutation,
    )

    __all__ = [
        "AdaptiveMutation",
        "BaseMutation", 
        "InversionMutation",
        "RandomMutation",
        "ScrambleMutation",
        "SwapMutation",
    ]
else:
    # If PyGAD is not installed, provide dummy classes that raise helpful errors
    class _PyGADNotInstalledError:
        def __init__(self, *args, **kwargs):
            raise ImportError(
                "PyGAD is not installed. Please install it with 'pip install pygad' "
                "to use PyGAD mutation configurations."
            )
    
    AdaptiveMutation = _PyGADNotInstalledError
    BaseMutation = _PyGADNotInstalledError
    InversionMutation = _PyGADNotInstalledError
    RandomMutation = _PyGADNotInstalledError
    ScrambleMutation = _PyGADNotInstalledError
    SwapMutation = _PyGADNotInstalledError
    
    __all__ = [
        "AdaptiveMutation",
        "BaseMutation",
        "InversionMutation", 
        "RandomMutation",
        "ScrambleMutation",
        "SwapMutation",
    ]
