"""PyGAD mutation function configurations for optimagic.

This module provides dataclass-based configurations for PyGAD mutation operators,
allowing type-safe and clear configuration of mutation parameters without complex
parameter interdependencies.

Usage patterns:
1. String: mutation="random" (default configuration)
2. Class: mutation=RandomMutation (default configuration)
3. Instance: mutation=RandomMutation(probability=0.1) (custom configuration)
4. Custom function: mutation=my_custom_function (follows protocol)
5. Subclass: mutation=MyCustomMutation() (custom implementation)
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import Any, Literal, Protocol

import numpy as np
from numpy.typing import NDArray

from optimagic.typing import PositiveFloat, PositiveInt, ProbabilityFloat


class MutationFunction(Protocol):
    """Protocol for user-defined mutation functions.

    Args:
        offspring: 2D array of offspring solutions to be mutated.
        ga_instance: The PyGAD GA instance.

    Returns:
        2D array of mutated offspring solutions.
    """

    def __call__(
        self, offspring: NDArray[np.float64], ga_instance: Any
    ) -> NDArray[np.float64]: ...


@dataclass(frozen=True)
class BaseMutation(ABC):
    """Base class for PyGAD mutation configurations.

    Provides default parameter conversion for simple mutation types. Complex
    mutation configurations should override the to_pygad_params method.
    """

    def to_pygad_params(self) -> dict[str, Any]:
        """Convert this mutation configuration to PyGAD parameters.

        Default implementation that works for simple mutations.
        Complex mutations (RandomMutation, AdaptiveMutation) should override this.

        Returns:
            Dictionary of PyGAD mutation parameters.
        """
        # Get mutation_type from the subclass field if it exists, otherwise use "random"
        mutation_type = getattr(self, 'mutation_type', 'random')

        return {
            "mutation_type": mutation_type,
            "mutation_probability": None,
            "mutation_percent_genes": "default",
            "mutation_num_genes": None,
            "mutation_by_replacement": False,
        }


@dataclass(frozen=True)
class RandomMutation(BaseMutation):
    """Configuration for random mutation in PyGAD genetic algorithms.

    Random mutation changes gene values by either replacing them with random values
    or adding random values to existing genes. Gene selection is controlled through
    probability, exact count, or percentage parameters.

    Suitable for continuous optimization problems requiring exploration of the
    parameter space.
    """

    probability: ProbabilityFloat | None = None
    """Probability of mutating each gene. Takes precedence over other selection methods."""

    num_genes: PositiveInt | None = None
    """Exact number of genes to mutate per solution. Ignored if probability is specified."""

    percent_genes: PositiveFloat | str = "default"
    """Percentage of genes to mutate. Default is 10%. Ignored if probability or num_genes are specified."""

    by_replacement: bool = False
    """If True, replace gene values with random values. If False, add random values to existing genes."""

    def to_pygad_params(self) -> dict[str, Any]:
        """Convert RandomMutation configuration to PyGAD parameters."""
        return {
            "mutation_type": "random",
            "mutation_probability": self.probability,
            "mutation_percent_genes": self.percent_genes,
            "mutation_num_genes": self.num_genes,
            "mutation_by_replacement": self.by_replacement,
        }


@dataclass(frozen=True)
class SwapMutation(BaseMutation):
    """Swap mutation for PyGAD genetic algorithms.

    Swaps values between two randomly selected genes in each solution.
    Preserves all gene values while changing their positions.
    This mutation type does not require any additional parameters.
    """

    mutation_type: str = "swap"


@dataclass(frozen=True)
class InversionMutation(BaseMutation):
    """Inversion mutation for PyGAD genetic algorithms.

    Reverses the order of genes within a randomly selected contiguous segment.
    Preserves all gene values while changing their relative positions.
    This mutation type does not require any additional parameters.
    """

    mutation_type: str = "inversion"


@dataclass(frozen=True)
class ScrambleMutation(BaseMutation):
    """Scramble mutation for PyGAD genetic algorithms.

    Randomly shuffles genes within a randomly selected contiguous segment.
    Provides more disruption than inversion while preserving all gene values.
    This mutation type does not require any additional parameters.
    """

    mutation_type: str = "scramble"


@dataclass(frozen=True)
class AdaptiveMutation(BaseMutation):
    """Adaptive mutation for PyGAD genetic algorithms.

    Adjusts mutation intensity based on solution fitness relative to population average.
    Bad solutions (below average) get higher mutation rates, good solutions (above average) get lower rates.
    Control mutation using probability, exact gene count, or percentage parameters.
    """

    probability_bad: ProbabilityFloat | None = 0.2
    """Probability of mutating each gene for bad solutions."""

    probability_good: ProbabilityFloat | None = 0.05
    """Probability of mutating each gene for good solutions."""

    num_genes_bad: PositiveInt | None = None
    """Exact number of genes to mutate for bad solutions."""

    num_genes_good: PositiveInt | None = None
    """Exact number of genes to mutate for good solutions."""

    percent_genes_bad: PositiveFloat | None = None
    """Percentage of genes to mutate for bad solutions."""

    percent_genes_good: PositiveFloat | None = None
    """Percentage of genes to mutate for good solutions."""

    by_replacement: bool = False
    """If True, replace gene values with random values. If False, add random values to existing genes."""

    def to_pygad_params(self) -> dict[str, Any]:
        """Convert AdaptiveMutation configuration to PyGAD parameters."""

        # Convert adaptive mutation parameters to PyGAD format (length-2 arrays)
        # Use elif to ensure only one parameter type is used (priority: probability > num_genes > percent_genes)
        if (self.probability_bad is not None and
            self.probability_good is not None):
            mutation_probability = [self.probability_bad, self.probability_good]
            mutation_num_genes = None
            mutation_percent_genes = None  # Must be None when using probability
        elif (self.num_genes_bad is not None and
              self.num_genes_good is not None):
            mutation_probability = None
            mutation_num_genes = [self.num_genes_bad, self.num_genes_good]
            mutation_percent_genes = None  # Must be None when using num_genes
        elif (self.percent_genes_bad is not None and
              self.percent_genes_good is not None):
            mutation_probability = None
            mutation_num_genes = None
            mutation_percent_genes = [self.percent_genes_bad, self.percent_genes_good]
        else:
            # Use default values from the class defaults
            mutation_probability = [self.probability_bad, self.probability_good]
            mutation_num_genes = None
            mutation_percent_genes = None  # Must be None when using probability

        return {
            "mutation_type": "adaptive",
            "mutation_probability": mutation_probability,
            "mutation_percent_genes": mutation_percent_genes,
            "mutation_num_genes": mutation_num_genes,
            "mutation_by_replacement": self.by_replacement,
        }


def create_mutation_from_string(mutation_type: str) -> BaseMutation:
    """Create a mutation instance from a string type.

    Args:
        mutation_type: String mutation type (e.g., "random", "swap", etc.)

    Returns:
        Appropriate mutation instance.

    Raises:
        ValueError: If mutation_type is not supported.
    """
    mutation_map = {
        "random": RandomMutation,
        "swap": SwapMutation,
        "inversion": InversionMutation,
        "scramble": ScrambleMutation,
        "adaptive": AdaptiveMutation,
    }

    if mutation_type not in mutation_map:
        raise ValueError(f"Unsupported mutation type: {mutation_type}")

    return mutation_map[mutation_type]()
