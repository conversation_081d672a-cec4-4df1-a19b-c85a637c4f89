"""This code was auto-generated by a pre-commit hook and should not be changed.

If you manually change this code, all of your changes will be overwritten the next time
the pre-commit hook runs.

Detailed information on the purpose of the code can be found here:
https://optimagic.readthedocs.io/en/latest/development/ep-02-typing.html#algorithm-selection

"""

from dataclasses import dataclass
from typing import Type, cast

from optimagic.optimization.algorithm import Algorithm
from optimagic.optimizers.bayesian_optimizer import BayesOpt
from optimagic.optimizers.bhhh import BHHH
from optimagic.optimizers.fides import Fides
from optimagic.optimizers.iminuit_migrad import IminuitMigrad
from optimagic.optimizers.ipopt import Ipopt
from optimagic.optimizers.nag_optimizers import NagDFOLS, NagPyBOBYQA
from optimagic.optimizers.neldermead import NelderMeadParallel
from optimagic.optimizers.nevergrad_optimizers import (
    NevergradBayesOptim,
    NevergradCGA,
    NevergradCMAES,
    NevergradDifferentialEvolution,
    NevergradEDA,
    NevergradEMNA,
    NevergradMeta,
    NevergradNGOpt,
    NevergradOnePlusOne,
    NevergradPSO,
    NevergradRandomSearch,
    NevergradSamplingSearch,
    NevergradTBPSA,
)
from optimagic.optimizers.nlopt_optimizers import (
    NloptBOBYQA,
    NloptCCSAQ,
    NloptCOBYLA,
    NloptCRS2LM,
    NloptDirect,
    NloptESCH,
    NloptISRES,
    NloptLBFGSB,
    NloptMMA,
    NloptNelderMead,
    NloptNEWUOA,
    NloptPRAXIS,
    NloptSbplx,
    NloptSLSQP,
    NloptTNewton,
    NloptVAR,
)
from optimagic.optimizers.pounders import Pounders
from optimagic.optimizers.pygad_optimizer import Pygad
from optimagic.optimizers.pygmo_optimizers import (
    PygmoBeeColony,
    PygmoCmaes,
    PygmoCompassSearch,
    PygmoDe,
    PygmoDe1220,
    PygmoGaco,
    PygmoGwo,
    PygmoIhs,
    PygmoMbh,
    PygmoPso,
    PygmoPsoGen,
    PygmoSade,
    PygmoSea,
    PygmoSga,
    PygmoSimulatedAnnealing,
    PygmoXnes,
)
from optimagic.optimizers.scipy_optimizers import (
    ScipyBasinhopping,
    ScipyBFGS,
    ScipyBrute,
    ScipyCOBYLA,
    ScipyConjugateGradient,
    ScipyDifferentialEvolution,
    ScipyDirect,
    ScipyDualAnnealing,
    ScipyLBFGSB,
    ScipyLSDogbox,
    ScipyLSLM,
    ScipyLSTRF,
    ScipyNelderMead,
    ScipyNewtonCG,
    ScipyPowell,
    ScipySHGO,
    ScipySLSQP,
    ScipyTruncatedNewton,
    ScipyTrustConstr,
)
from optimagic.optimizers.tao_optimizers import TAOPounders
from optimagic.optimizers.tranquilo import Tranquilo, TranquiloLS


@dataclass(frozen=True)
class AlgoSelection:
    def _all(self) -> list[Type[Algorithm]]:
        raw = [field.default for field in self.__dataclass_fields__.values()]
        return cast(list[Type[Algorithm]], raw)

    def _available(self) -> list[Type[Algorithm]]:
        _all = self._all()
        return [
            a
            for a in _all
            if a.algo_info.is_available  # type: ignore
        ]

    @property
    def All(self) -> list[Type[Algorithm]]:
        return self._all()

    @property
    def Available(self) -> list[Type[Algorithm]]:
        return self._available()

    @property
    def AllNames(self) -> list[str]:
        return [str(a.name) for a in self._all()]

    @property
    def AvailableNames(self) -> list[str]:
        return [str(a.name) for a in self._available()]

    @property
    def _all_algorithms_dict(self) -> dict[str, Type[Algorithm]]:
        return {str(a.name): a for a in self._all()}

    @property
    def _available_algorithms_dict(self) -> dict[str, Type[Algorithm]]:
        return {str(a.name): a for a in self._available()}


@dataclass(frozen=True)
class BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms(
    AlgoSelection
):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )


@dataclass(frozen=True)
class BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    scipy_shgo: Type[ScipySHGO] = ScipySHGO


@dataclass(frozen=True)
class BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr


@dataclass(frozen=True)
class BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Parallel(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Scalar(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientFreeParallelScalarAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA


@dataclass(frozen=True)
class BoundedGradientFreeLocalParallelScalarAlgorithms(AlgoSelection):
    tranquilo: Type[Tranquilo] = Tranquilo


@dataclass(frozen=True)
class BoundedGradientFreeLeastSquaresLocalParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS


@dataclass(frozen=True)
class BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Global(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def GradientFree(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms(AlgoSelection):
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Scalar(self) -> BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientBasedScalarAlgorithms(AlgoSelection):
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientBasedNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Bounded(self) -> BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientBasedLocalNonlinearConstrainedAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Scalar(self) -> BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientBasedLocalScalarAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientBasedLeastSquaresLocalAlgorithms(AlgoSelection):
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF


@dataclass(frozen=True)
class GradientBasedLocalNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientBasedNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Global(self) -> BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms()

    @property
    def Local(self) -> BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms(AlgoSelection):
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Parallel(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientFreeScalarAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> BoundedGlobalGradientFreeParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientFreeParallelAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGlobalGradientFreeParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientFreeNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(
        self,
    ) -> GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientFreeNonlinearConstrainedParallelAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientFreeParallelScalarAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGlobalGradientFreeParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeParallelScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeLocalNonlinearConstrainedAlgorithms(AlgoSelection):
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA

    @property
    def Scalar(self) -> BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeLocalScalarAlgorithms(AlgoSelection):
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> BoundedGradientFreeLocalParallelScalarAlgorithms:
        return BoundedGradientFreeLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeLeastSquaresLocalAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    pounders: Type[Pounders] = Pounders
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Parallel(self) -> BoundedGradientFreeLeastSquaresLocalParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeLocalParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def LeastSquares(self) -> BoundedGradientFreeLeastSquaresLocalParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientFreeLocalParallelScalarAlgorithms:
        return BoundedGradientFreeLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLocalNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA

    @property
    def Bounded(self) -> BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLocalParallelScalarAlgorithms(AlgoSelection):
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Bounded(self) -> BoundedGradientFreeLocalParallelScalarAlgorithms:
        return BoundedGradientFreeLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLeastSquaresLocalParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedGradientFreeLeastSquaresLocalParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Global(self) -> BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Local(self) -> BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(
        self,
    ) -> BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeNonlinearConstrainedParallelAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Global(self) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeParallelScalarAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Global(self) -> BoundedGlobalGradientFreeParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeParallelScalarAlgorithms()

    @property
    def Local(self) -> BoundedGradientFreeLocalParallelScalarAlgorithms:
        return BoundedGradientFreeLocalParallelScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeLeastSquaresParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Local(self) -> BoundedGradientFreeLeastSquaresLocalParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class GradientFreeNonlinearConstrainedParallelScalarAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(
        self,
    ) -> BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms()

    @property
    def Global(self) -> GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def GradientBased(
        self,
    ) -> BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientFree(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalNonlinearConstrainedParallelAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def GradientFree(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalParallelScalarAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def GradientFree(self) -> BoundedGlobalGradientFreeParallelScalarAlgorithms:
        return BoundedGlobalGradientFreeParallelScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalNonlinearConstrainedParallelScalarAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms()

    @property
    def GradientFree(
        self,
    ) -> GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLocalNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def GradientBased(
        self,
    ) -> BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientFree(
        self,
    ) -> BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLocalParallelScalarAlgorithms(AlgoSelection):
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def GradientFree(self) -> BoundedGradientFreeLocalParallelScalarAlgorithms:
        return BoundedGradientFreeLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLeastSquaresLocalParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def GradientFree(self) -> BoundedGradientFreeLeastSquaresLocalParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class BoundedNonlinearConstrainedParallelScalarAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Global(self) -> BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelScalarAlgorithms()

    @property
    def GradientFree(
        self,
    ) -> BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientBasedAlgorithms(AlgoSelection):
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> BoundedGlobalGradientBasedScalarAlgorithms:
        return BoundedGlobalGradientBasedScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientBasedNonlinearConstrainedAlgorithms(AlgoSelection):
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Bounded(self) -> BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> GlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return GlobalGradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientBasedScalarAlgorithms(AlgoSelection):
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Bounded(self) -> BoundedGlobalGradientBasedScalarAlgorithms:
        return BoundedGlobalGradientBasedScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return GlobalGradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientBasedLocalAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def LeastSquares(self) -> BoundedGradientBasedLeastSquaresLocalAlgorithms:
        return BoundedGradientBasedLeastSquaresLocalAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientBasedLocalNonlinearConstrainedAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientBasedLocalScalarAlgorithms:
        return BoundedGradientBasedLocalScalarAlgorithms()


@dataclass(frozen=True)
class GradientBasedLocalNonlinearConstrainedAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedGradientBasedLocalNonlinearConstrainedAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> GradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return GradientBasedLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GradientBasedLocalScalarAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_bfgs: Type[ScipyBFGS] = ScipyBFGS
    scipy_conjugate_gradient: Type[ScipyConjugateGradient] = ScipyConjugateGradient
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_newton_cg: Type[ScipyNewtonCG] = ScipyNewtonCG
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedGradientBasedLocalScalarAlgorithms:
        return BoundedGradientBasedLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return GradientBasedLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GradientBasedLeastSquaresLocalAlgorithms(AlgoSelection):
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_lm: Type[ScipyLSLM] = ScipyLSLM
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF

    @property
    def Bounded(self) -> BoundedGradientBasedLeastSquaresLocalAlgorithms:
        return BoundedGradientBasedLeastSquaresLocalAlgorithms()


@dataclass(frozen=True)
class GradientBasedLikelihoodLocalAlgorithms(AlgoSelection):
    bhhh: Type[BHHH] = BHHH


@dataclass(frozen=True)
class BoundedGradientBasedNonlinearConstrainedAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Global(self) -> BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def Local(self) -> BoundedGradientBasedLocalNonlinearConstrainedAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientBasedScalarAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Global(self) -> BoundedGlobalGradientBasedScalarAlgorithms:
        return BoundedGlobalGradientBasedScalarAlgorithms()

    @property
    def Local(self) -> BoundedGradientBasedLocalScalarAlgorithms:
        return BoundedGradientBasedLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientBasedLeastSquaresAlgorithms(AlgoSelection):
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF

    @property
    def Local(self) -> BoundedGradientBasedLeastSquaresLocalAlgorithms:
        return BoundedGradientBasedLeastSquaresLocalAlgorithms()


@dataclass(frozen=True)
class GradientBasedNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedNonlinearConstrainedScalarAlgorithms()

    @property
    def Global(self) -> GlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return GlobalGradientBasedNonlinearConstrainedScalarAlgorithms()

    @property
    def Local(self) -> GradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return GradientBasedLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalGradientFreeAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedGlobalGradientFreeParallelAlgorithms:
        return BoundedGlobalGradientFreeParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGlobalGradientFreeScalarAlgorithms:
        return BoundedGlobalGradientFreeScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientFreeNonlinearConstrainedAlgorithms(AlgoSelection):
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> GlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientFreeScalarAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect

    @property
    def Bounded(self) -> BoundedGlobalGradientFreeScalarAlgorithms:
        return BoundedGlobalGradientFreeScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> GlobalGradientFreeParallelScalarAlgorithms:
        return GlobalGradientFreeParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientFreeParallelAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGlobalGradientFreeParallelAlgorithms:
        return BoundedGlobalGradientFreeParallelAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GlobalGradientFreeParallelScalarAlgorithms:
        return GlobalGradientFreeParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeLocalAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    pounders: Type[Pounders] = Pounders
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def LeastSquares(self) -> BoundedGradientFreeLeastSquaresLocalAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientFreeLocalNonlinearConstrainedAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedGradientFreeLocalParallelAlgorithms:
        return BoundedGradientFreeLocalParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientFreeLocalScalarAlgorithms:
        return BoundedGradientFreeLocalScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLocalNonlinearConstrainedAlgorithms(AlgoSelection):
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA

    @property
    def Bounded(self) -> BoundedGradientFreeLocalNonlinearConstrainedAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> GradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return GradientFreeLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLocalScalarAlgorithms(AlgoSelection):
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_praxis: Type[NloptPRAXIS] = NloptPRAXIS
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Bounded(self) -> BoundedGradientFreeLocalScalarAlgorithms:
        return BoundedGradientFreeLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return GradientFreeLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> GradientFreeLocalParallelScalarAlgorithms:
        return GradientFreeLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLeastSquaresLocalAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    pounders: Type[Pounders] = Pounders
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedGradientFreeLeastSquaresLocalAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalAlgorithms()

    @property
    def Parallel(self) -> GradientFreeLeastSquaresLocalParallelAlgorithms:
        return GradientFreeLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class GradientFreeLocalParallelAlgorithms(AlgoSelection):
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    pounders: Type[Pounders] = Pounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedGradientFreeLocalParallelAlgorithms:
        return BoundedGradientFreeLocalParallelAlgorithms()

    @property
    def LeastSquares(self) -> GradientFreeLeastSquaresLocalParallelAlgorithms:
        return GradientFreeLeastSquaresLocalParallelAlgorithms()

    @property
    def Scalar(self) -> GradientFreeLocalParallelScalarAlgorithms:
        return GradientFreeLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeNonlinearConstrainedAlgorithms(AlgoSelection):
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Global(self) -> BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Local(self) -> BoundedGradientFreeLocalNonlinearConstrainedAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeScalarAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Global(self) -> BoundedGlobalGradientFreeScalarAlgorithms:
        return BoundedGlobalGradientFreeScalarAlgorithms()

    @property
    def Local(self) -> BoundedGradientFreeLocalScalarAlgorithms:
        return BoundedGradientFreeLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> BoundedGradientFreeParallelScalarAlgorithms:
        return BoundedGradientFreeParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeLeastSquaresAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    pounders: Type[Pounders] = Pounders
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Local(self) -> BoundedGradientFreeLeastSquaresLocalAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalAlgorithms()

    @property
    def Parallel(self) -> BoundedGradientFreeLeastSquaresParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresParallelAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeParallelAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pounders: Type[Pounders] = Pounders
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Global(self) -> BoundedGlobalGradientFreeParallelAlgorithms:
        return BoundedGlobalGradientFreeParallelAlgorithms()

    @property
    def LeastSquares(self) -> BoundedGradientFreeLeastSquaresParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresParallelAlgorithms()

    @property
    def Local(self) -> BoundedGradientFreeLocalParallelAlgorithms:
        return BoundedGradientFreeLocalParallelAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientFreeParallelScalarAlgorithms:
        return BoundedGradientFreeParallelScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Global(self) -> GlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Local(self) -> GradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return GradientFreeLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> GradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeNonlinearConstrainedParallelAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Global(self) -> GlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeParallelScalarAlgorithms(AlgoSelection):
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Bounded(self) -> BoundedGradientFreeParallelScalarAlgorithms:
        return BoundedGradientFreeParallelScalarAlgorithms()

    @property
    def Global(self) -> GlobalGradientFreeParallelScalarAlgorithms:
        return GlobalGradientFreeParallelScalarAlgorithms()

    @property
    def Local(self) -> GradientFreeLocalParallelScalarAlgorithms:
        return GradientFreeLocalParallelScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLeastSquaresParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedGradientFreeLeastSquaresParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresParallelAlgorithms()

    @property
    def Local(self) -> GradientFreeLeastSquaresLocalParallelAlgorithms:
        return GradientFreeLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalNonlinearConstrainedAlgorithms(AlgoSelection):
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def GradientBased(self) -> BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms:
        return BoundedGlobalGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def GradientFree(self) -> BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms:
        return BoundedGlobalGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedGlobalNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGlobalNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalScalarAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def GradientBased(self) -> BoundedGlobalGradientBasedScalarAlgorithms:
        return BoundedGlobalGradientBasedScalarAlgorithms()

    @property
    def GradientFree(self) -> BoundedGlobalGradientFreeScalarAlgorithms:
        return BoundedGlobalGradientFreeScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> BoundedGlobalNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> BoundedGlobalParallelScalarAlgorithms:
        return BoundedGlobalParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalParallelAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def GradientFree(self) -> BoundedGlobalGradientFreeParallelAlgorithms:
        return BoundedGlobalGradientFreeParallelAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGlobalNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGlobalParallelScalarAlgorithms:
        return BoundedGlobalParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Bounded(self) -> BoundedGlobalNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientBased(self) -> GlobalGradientBasedNonlinearConstrainedScalarAlgorithms:
        return GlobalGradientBasedNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientFree(self) -> GlobalGradientFreeNonlinearConstrainedScalarAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> GlobalNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalNonlinearConstrainedParallelAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGlobalNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelAlgorithms()

    @property
    def GradientFree(self) -> GlobalGradientFreeNonlinearConstrainedParallelAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GlobalNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalParallelScalarAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGlobalParallelScalarAlgorithms:
        return BoundedGlobalParallelScalarAlgorithms()

    @property
    def GradientFree(self) -> GlobalGradientFreeParallelScalarAlgorithms:
        return GlobalGradientFreeParallelScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GlobalNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLocalNonlinearConstrainedAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def GradientBased(self) -> BoundedGradientBasedLocalNonlinearConstrainedAlgorithms:
        return BoundedGradientBasedLocalNonlinearConstrainedAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeLocalNonlinearConstrainedAlgorithms:
        return BoundedGradientFreeLocalNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> BoundedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLocalScalarAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def GradientBased(self) -> BoundedGradientBasedLocalScalarAlgorithms:
        return BoundedGradientBasedLocalScalarAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeLocalScalarAlgorithms:
        return BoundedGradientFreeLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> BoundedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> BoundedLocalParallelScalarAlgorithms:
        return BoundedLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLeastSquaresLocalAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    pounders: Type[Pounders] = Pounders
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def GradientBased(self) -> BoundedGradientBasedLeastSquaresLocalAlgorithms:
        return BoundedGradientBasedLeastSquaresLocalAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeLeastSquaresLocalAlgorithms:
        return BoundedGradientFreeLeastSquaresLocalAlgorithms()

    @property
    def Parallel(self) -> BoundedLeastSquaresLocalParallelAlgorithms:
        return BoundedLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class BoundedLocalParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def GradientFree(self) -> BoundedGradientFreeLocalParallelAlgorithms:
        return BoundedGradientFreeLocalParallelAlgorithms()

    @property
    def LeastSquares(self) -> BoundedLeastSquaresLocalParallelAlgorithms:
        return BoundedLeastSquaresLocalParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedLocalParallelScalarAlgorithms:
        return BoundedLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class LocalNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedLocalNonlinearConstrainedScalarAlgorithms:
        return GradientBasedLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLocalNonlinearConstrainedScalarAlgorithms:
        return GradientFreeLocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class LocalParallelScalarAlgorithms(AlgoSelection):
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Bounded(self) -> BoundedLocalParallelScalarAlgorithms:
        return BoundedLocalParallelScalarAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLocalParallelScalarAlgorithms:
        return GradientFreeLocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class LeastSquaresLocalParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedLeastSquaresLocalParallelAlgorithms:
        return BoundedLeastSquaresLocalParallelAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLeastSquaresLocalParallelAlgorithms:
        return GradientFreeLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class BoundedNonlinearConstrainedScalarAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Global(self) -> BoundedGlobalNonlinearConstrainedScalarAlgorithms:
        return BoundedGlobalNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientBased(self) -> BoundedGradientBasedNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientBasedNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeNonlinearConstrainedScalarAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Local(self) -> BoundedLocalNonlinearConstrainedScalarAlgorithms:
        return BoundedLocalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> BoundedNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedNonlinearConstrainedParallelAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Global(self) -> BoundedGlobalNonlinearConstrainedParallelAlgorithms:
        return BoundedGlobalNonlinearConstrainedParallelAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeNonlinearConstrainedParallelAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedParallelScalarAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Global(self) -> BoundedGlobalParallelScalarAlgorithms:
        return BoundedGlobalParallelScalarAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeParallelScalarAlgorithms:
        return BoundedGradientFreeParallelScalarAlgorithms()

    @property
    def Local(self) -> BoundedLocalParallelScalarAlgorithms:
        return BoundedLocalParallelScalarAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLeastSquaresParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def GradientFree(self) -> BoundedGradientFreeLeastSquaresParallelAlgorithms:
        return BoundedGradientFreeLeastSquaresParallelAlgorithms()

    @property
    def Local(self) -> BoundedLeastSquaresLocalParallelAlgorithms:
        return BoundedLeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class NonlinearConstrainedParallelScalarAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedNonlinearConstrainedParallelScalarAlgorithms:
        return BoundedNonlinearConstrainedParallelScalarAlgorithms()

    @property
    def Global(self) -> GlobalNonlinearConstrainedParallelScalarAlgorithms:
        return GlobalNonlinearConstrainedParallelScalarAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeNonlinearConstrainedParallelScalarAlgorithms:
        return GradientFreeNonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalGradientBasedAlgorithms(AlgoSelection):
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Bounded(self) -> BoundedGlobalGradientBasedAlgorithms:
        return BoundedGlobalGradientBasedAlgorithms()

    @property
    def NonlinearConstrained(self) -> GlobalGradientBasedNonlinearConstrainedAlgorithms:
        return GlobalGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> GlobalGradientBasedScalarAlgorithms:
        return GlobalGradientBasedScalarAlgorithms()


@dataclass(frozen=True)
class GradientBasedLocalAlgorithms(AlgoSelection):
    bhhh: Type[BHHH] = BHHH
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_bfgs: Type[ScipyBFGS] = ScipyBFGS
    scipy_conjugate_gradient: Type[ScipyConjugateGradient] = ScipyConjugateGradient
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_lm: Type[ScipyLSLM] = ScipyLSLM
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    scipy_newton_cg: Type[ScipyNewtonCG] = ScipyNewtonCG
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedGradientBasedLocalAlgorithms:
        return BoundedGradientBasedLocalAlgorithms()

    @property
    def LeastSquares(self) -> GradientBasedLeastSquaresLocalAlgorithms:
        return GradientBasedLeastSquaresLocalAlgorithms()

    @property
    def Likelihood(self) -> GradientBasedLikelihoodLocalAlgorithms:
        return GradientBasedLikelihoodLocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> GradientBasedLocalNonlinearConstrainedAlgorithms:
        return GradientBasedLocalNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> GradientBasedLocalScalarAlgorithms:
        return GradientBasedLocalScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientBasedAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Global(self) -> BoundedGlobalGradientBasedAlgorithms:
        return BoundedGlobalGradientBasedAlgorithms()

    @property
    def LeastSquares(self) -> BoundedGradientBasedLeastSquaresAlgorithms:
        return BoundedGradientBasedLeastSquaresAlgorithms()

    @property
    def Local(self) -> BoundedGradientBasedLocalAlgorithms:
        return BoundedGradientBasedLocalAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> BoundedGradientBasedNonlinearConstrainedAlgorithms:
        return BoundedGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientBasedScalarAlgorithms:
        return BoundedGradientBasedScalarAlgorithms()


@dataclass(frozen=True)
class GradientBasedNonlinearConstrainedAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedGradientBasedNonlinearConstrainedAlgorithms:
        return BoundedGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def Global(self) -> GlobalGradientBasedNonlinearConstrainedAlgorithms:
        return GlobalGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def Local(self) -> GradientBasedLocalNonlinearConstrainedAlgorithms:
        return GradientBasedLocalNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> GradientBasedNonlinearConstrainedScalarAlgorithms:
        return GradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GradientBasedScalarAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_bfgs: Type[ScipyBFGS] = ScipyBFGS
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_conjugate_gradient: Type[ScipyConjugateGradient] = ScipyConjugateGradient
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_newton_cg: Type[ScipyNewtonCG] = ScipyNewtonCG
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedGradientBasedScalarAlgorithms:
        return BoundedGradientBasedScalarAlgorithms()

    @property
    def Global(self) -> GlobalGradientBasedScalarAlgorithms:
        return GlobalGradientBasedScalarAlgorithms()

    @property
    def Local(self) -> GradientBasedLocalScalarAlgorithms:
        return GradientBasedLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> GradientBasedNonlinearConstrainedScalarAlgorithms:
        return GradientBasedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GradientBasedLeastSquaresAlgorithms(AlgoSelection):
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_lm: Type[ScipyLSLM] = ScipyLSLM
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF

    @property
    def Bounded(self) -> BoundedGradientBasedLeastSquaresAlgorithms:
        return BoundedGradientBasedLeastSquaresAlgorithms()

    @property
    def Local(self) -> GradientBasedLeastSquaresLocalAlgorithms:
        return GradientBasedLeastSquaresLocalAlgorithms()


@dataclass(frozen=True)
class GradientBasedLikelihoodAlgorithms(AlgoSelection):
    bhhh: Type[BHHH] = BHHH

    @property
    def Local(self) -> GradientBasedLikelihoodLocalAlgorithms:
        return GradientBasedLikelihoodLocalAlgorithms()


@dataclass(frozen=True)
class GlobalGradientFreeAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect

    @property
    def Bounded(self) -> BoundedGlobalGradientFreeAlgorithms:
        return BoundedGlobalGradientFreeAlgorithms()

    @property
    def NonlinearConstrained(self) -> GlobalGradientFreeNonlinearConstrainedAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> GlobalGradientFreeParallelAlgorithms:
        return GlobalGradientFreeParallelAlgorithms()

    @property
    def Scalar(self) -> GlobalGradientFreeScalarAlgorithms:
        return GlobalGradientFreeScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLocalAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_praxis: Type[NloptPRAXIS] = NloptPRAXIS
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    pounders: Type[Pounders] = Pounders
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedGradientFreeLocalAlgorithms:
        return BoundedGradientFreeLocalAlgorithms()

    @property
    def LeastSquares(self) -> GradientFreeLeastSquaresLocalAlgorithms:
        return GradientFreeLeastSquaresLocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> GradientFreeLocalNonlinearConstrainedAlgorithms:
        return GradientFreeLocalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> GradientFreeLocalParallelAlgorithms:
        return GradientFreeLocalParallelAlgorithms()

    @property
    def Scalar(self) -> GradientFreeLocalScalarAlgorithms:
        return GradientFreeLocalScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGradientFreeAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    pounders: Type[Pounders] = Pounders
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Global(self) -> BoundedGlobalGradientFreeAlgorithms:
        return BoundedGlobalGradientFreeAlgorithms()

    @property
    def LeastSquares(self) -> BoundedGradientFreeLeastSquaresAlgorithms:
        return BoundedGradientFreeLeastSquaresAlgorithms()

    @property
    def Local(self) -> BoundedGradientFreeLocalAlgorithms:
        return BoundedGradientFreeLocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> BoundedGradientFreeNonlinearConstrainedAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedGradientFreeParallelAlgorithms:
        return BoundedGradientFreeParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGradientFreeScalarAlgorithms:
        return BoundedGradientFreeScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeNonlinearConstrainedAlgorithms(AlgoSelection):
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGradientFreeNonlinearConstrainedAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Global(self) -> GlobalGradientFreeNonlinearConstrainedAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Local(self) -> GradientFreeLocalNonlinearConstrainedAlgorithms:
        return GradientFreeLocalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> GradientFreeNonlinearConstrainedParallelAlgorithms:
        return GradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GradientFreeNonlinearConstrainedScalarAlgorithms:
        return GradientFreeNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeScalarAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_praxis: Type[NloptPRAXIS] = NloptPRAXIS
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Bounded(self) -> BoundedGradientFreeScalarAlgorithms:
        return BoundedGradientFreeScalarAlgorithms()

    @property
    def Global(self) -> GlobalGradientFreeScalarAlgorithms:
        return GlobalGradientFreeScalarAlgorithms()

    @property
    def Local(self) -> GradientFreeLocalScalarAlgorithms:
        return GradientFreeLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> GradientFreeNonlinearConstrainedScalarAlgorithms:
        return GradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> GradientFreeParallelScalarAlgorithms:
        return GradientFreeParallelScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeLeastSquaresAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    pounders: Type[Pounders] = Pounders
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedGradientFreeLeastSquaresAlgorithms:
        return BoundedGradientFreeLeastSquaresAlgorithms()

    @property
    def Local(self) -> GradientFreeLeastSquaresLocalAlgorithms:
        return GradientFreeLeastSquaresLocalAlgorithms()

    @property
    def Parallel(self) -> GradientFreeLeastSquaresParallelAlgorithms:
        return GradientFreeLeastSquaresParallelAlgorithms()


@dataclass(frozen=True)
class GradientFreeParallelAlgorithms(AlgoSelection):
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pounders: Type[Pounders] = Pounders
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedGradientFreeParallelAlgorithms:
        return BoundedGradientFreeParallelAlgorithms()

    @property
    def Global(self) -> GlobalGradientFreeParallelAlgorithms:
        return GlobalGradientFreeParallelAlgorithms()

    @property
    def LeastSquares(self) -> GradientFreeLeastSquaresParallelAlgorithms:
        return GradientFreeLeastSquaresParallelAlgorithms()

    @property
    def Local(self) -> GradientFreeLocalParallelAlgorithms:
        return GradientFreeLocalParallelAlgorithms()

    @property
    def NonlinearConstrained(
        self,
    ) -> GradientFreeNonlinearConstrainedParallelAlgorithms:
        return GradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GradientFreeParallelScalarAlgorithms:
        return GradientFreeParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedGlobalAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def GradientBased(self) -> BoundedGlobalGradientBasedAlgorithms:
        return BoundedGlobalGradientBasedAlgorithms()

    @property
    def GradientFree(self) -> BoundedGlobalGradientFreeAlgorithms:
        return BoundedGlobalGradientFreeAlgorithms()

    @property
    def NonlinearConstrained(self) -> BoundedGlobalNonlinearConstrainedAlgorithms:
        return BoundedGlobalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedGlobalParallelAlgorithms:
        return BoundedGlobalParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedGlobalScalarAlgorithms:
        return BoundedGlobalScalarAlgorithms()


@dataclass(frozen=True)
class GlobalNonlinearConstrainedAlgorithms(AlgoSelection):
    nlopt_isres: Type[NloptISRES] = NloptISRES
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Bounded(self) -> BoundedGlobalNonlinearConstrainedAlgorithms:
        return BoundedGlobalNonlinearConstrainedAlgorithms()

    @property
    def GradientBased(self) -> GlobalGradientBasedNonlinearConstrainedAlgorithms:
        return GlobalGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def GradientFree(self) -> GlobalGradientFreeNonlinearConstrainedAlgorithms:
        return GlobalGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> GlobalNonlinearConstrainedParallelAlgorithms:
        return GlobalNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GlobalNonlinearConstrainedScalarAlgorithms:
        return GlobalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class GlobalScalarAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Bounded(self) -> BoundedGlobalScalarAlgorithms:
        return BoundedGlobalScalarAlgorithms()

    @property
    def GradientBased(self) -> GlobalGradientBasedScalarAlgorithms:
        return GlobalGradientBasedScalarAlgorithms()

    @property
    def GradientFree(self) -> GlobalGradientFreeScalarAlgorithms:
        return GlobalGradientFreeScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> GlobalNonlinearConstrainedScalarAlgorithms:
        return GlobalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> GlobalParallelScalarAlgorithms:
        return GlobalParallelScalarAlgorithms()


@dataclass(frozen=True)
class GlobalParallelAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedGlobalParallelAlgorithms:
        return BoundedGlobalParallelAlgorithms()

    @property
    def GradientFree(self) -> GlobalGradientFreeParallelAlgorithms:
        return GlobalGradientFreeParallelAlgorithms()

    @property
    def NonlinearConstrained(self) -> GlobalNonlinearConstrainedParallelAlgorithms:
        return GlobalNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> GlobalParallelScalarAlgorithms:
        return GlobalParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLocalAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    pounders: Type[Pounders] = Pounders
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def GradientBased(self) -> BoundedGradientBasedLocalAlgorithms:
        return BoundedGradientBasedLocalAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeLocalAlgorithms:
        return BoundedGradientFreeLocalAlgorithms()

    @property
    def LeastSquares(self) -> BoundedLeastSquaresLocalAlgorithms:
        return BoundedLeastSquaresLocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> BoundedLocalNonlinearConstrainedAlgorithms:
        return BoundedLocalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedLocalParallelAlgorithms:
        return BoundedLocalParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedLocalScalarAlgorithms:
        return BoundedLocalScalarAlgorithms()


@dataclass(frozen=True)
class LocalNonlinearConstrainedAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedLocalNonlinearConstrainedAlgorithms:
        return BoundedLocalNonlinearConstrainedAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedLocalNonlinearConstrainedAlgorithms:
        return GradientBasedLocalNonlinearConstrainedAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLocalNonlinearConstrainedAlgorithms:
        return GradientFreeLocalNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> LocalNonlinearConstrainedScalarAlgorithms:
        return LocalNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class LocalScalarAlgorithms(AlgoSelection):
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_praxis: Type[NloptPRAXIS] = NloptPRAXIS
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_bfgs: Type[ScipyBFGS] = ScipyBFGS
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_conjugate_gradient: Type[ScipyConjugateGradient] = ScipyConjugateGradient
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_newton_cg: Type[ScipyNewtonCG] = ScipyNewtonCG
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Bounded(self) -> BoundedLocalScalarAlgorithms:
        return BoundedLocalScalarAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedLocalScalarAlgorithms:
        return GradientBasedLocalScalarAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLocalScalarAlgorithms:
        return GradientFreeLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> LocalNonlinearConstrainedScalarAlgorithms:
        return LocalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> LocalParallelScalarAlgorithms:
        return LocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class LeastSquaresLocalAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    pounders: Type[Pounders] = Pounders
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_lm: Type[ScipyLSLM] = ScipyLSLM
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedLeastSquaresLocalAlgorithms:
        return BoundedLeastSquaresLocalAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedLeastSquaresLocalAlgorithms:
        return GradientBasedLeastSquaresLocalAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLeastSquaresLocalAlgorithms:
        return GradientFreeLeastSquaresLocalAlgorithms()

    @property
    def Parallel(self) -> LeastSquaresLocalParallelAlgorithms:
        return LeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class LikelihoodLocalAlgorithms(AlgoSelection):
    bhhh: Type[BHHH] = BHHH

    @property
    def GradientBased(self) -> GradientBasedLikelihoodLocalAlgorithms:
        return GradientBasedLikelihoodLocalAlgorithms()


@dataclass(frozen=True)
class LocalParallelAlgorithms(AlgoSelection):
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    pounders: Type[Pounders] = Pounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedLocalParallelAlgorithms:
        return BoundedLocalParallelAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLocalParallelAlgorithms:
        return GradientFreeLocalParallelAlgorithms()

    @property
    def LeastSquares(self) -> LeastSquaresLocalParallelAlgorithms:
        return LeastSquaresLocalParallelAlgorithms()

    @property
    def Scalar(self) -> LocalParallelScalarAlgorithms:
        return LocalParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedNonlinearConstrainedAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Global(self) -> BoundedGlobalNonlinearConstrainedAlgorithms:
        return BoundedGlobalNonlinearConstrainedAlgorithms()

    @property
    def GradientBased(self) -> BoundedGradientBasedNonlinearConstrainedAlgorithms:
        return BoundedGradientBasedNonlinearConstrainedAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeNonlinearConstrainedAlgorithms:
        return BoundedGradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Local(self) -> BoundedLocalNonlinearConstrainedAlgorithms:
        return BoundedLocalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedNonlinearConstrainedParallelAlgorithms:
        return BoundedNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedNonlinearConstrainedScalarAlgorithms:
        return BoundedNonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class BoundedScalarAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Global(self) -> BoundedGlobalScalarAlgorithms:
        return BoundedGlobalScalarAlgorithms()

    @property
    def GradientBased(self) -> BoundedGradientBasedScalarAlgorithms:
        return BoundedGradientBasedScalarAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeScalarAlgorithms:
        return BoundedGradientFreeScalarAlgorithms()

    @property
    def Local(self) -> BoundedLocalScalarAlgorithms:
        return BoundedLocalScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> BoundedNonlinearConstrainedScalarAlgorithms:
        return BoundedNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> BoundedParallelScalarAlgorithms:
        return BoundedParallelScalarAlgorithms()


@dataclass(frozen=True)
class BoundedLeastSquaresAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    pounders: Type[Pounders] = Pounders
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def GradientBased(self) -> BoundedGradientBasedLeastSquaresAlgorithms:
        return BoundedGradientBasedLeastSquaresAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeLeastSquaresAlgorithms:
        return BoundedGradientFreeLeastSquaresAlgorithms()

    @property
    def Local(self) -> BoundedLeastSquaresLocalAlgorithms:
        return BoundedLeastSquaresLocalAlgorithms()

    @property
    def Parallel(self) -> BoundedLeastSquaresParallelAlgorithms:
        return BoundedLeastSquaresParallelAlgorithms()


@dataclass(frozen=True)
class BoundedParallelAlgorithms(AlgoSelection):
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pounders: Type[Pounders] = Pounders
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Global(self) -> BoundedGlobalParallelAlgorithms:
        return BoundedGlobalParallelAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeParallelAlgorithms:
        return BoundedGradientFreeParallelAlgorithms()

    @property
    def LeastSquares(self) -> BoundedLeastSquaresParallelAlgorithms:
        return BoundedLeastSquaresParallelAlgorithms()

    @property
    def Local(self) -> BoundedLocalParallelAlgorithms:
        return BoundedLocalParallelAlgorithms()

    @property
    def NonlinearConstrained(self) -> BoundedNonlinearConstrainedParallelAlgorithms:
        return BoundedNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedParallelScalarAlgorithms:
        return BoundedParallelScalarAlgorithms()


@dataclass(frozen=True)
class NonlinearConstrainedScalarAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedNonlinearConstrainedScalarAlgorithms:
        return BoundedNonlinearConstrainedScalarAlgorithms()

    @property
    def Global(self) -> GlobalNonlinearConstrainedScalarAlgorithms:
        return GlobalNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedNonlinearConstrainedScalarAlgorithms:
        return GradientBasedNonlinearConstrainedScalarAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeNonlinearConstrainedScalarAlgorithms:
        return GradientFreeNonlinearConstrainedScalarAlgorithms()

    @property
    def Local(self) -> LocalNonlinearConstrainedScalarAlgorithms:
        return LocalNonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> NonlinearConstrainedParallelScalarAlgorithms:
        return NonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class NonlinearConstrainedParallelAlgorithms(AlgoSelection):
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )

    @property
    def Bounded(self) -> BoundedNonlinearConstrainedParallelAlgorithms:
        return BoundedNonlinearConstrainedParallelAlgorithms()

    @property
    def Global(self) -> GlobalNonlinearConstrainedParallelAlgorithms:
        return GlobalNonlinearConstrainedParallelAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeNonlinearConstrainedParallelAlgorithms:
        return GradientFreeNonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> NonlinearConstrainedParallelScalarAlgorithms:
        return NonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class ParallelScalarAlgorithms(AlgoSelection):
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Bounded(self) -> BoundedParallelScalarAlgorithms:
        return BoundedParallelScalarAlgorithms()

    @property
    def Global(self) -> GlobalParallelScalarAlgorithms:
        return GlobalParallelScalarAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeParallelScalarAlgorithms:
        return GradientFreeParallelScalarAlgorithms()

    @property
    def Local(self) -> LocalParallelScalarAlgorithms:
        return LocalParallelScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> NonlinearConstrainedParallelScalarAlgorithms:
        return NonlinearConstrainedParallelScalarAlgorithms()


@dataclass(frozen=True)
class LeastSquaresParallelAlgorithms(AlgoSelection):
    pounders: Type[Pounders] = Pounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedLeastSquaresParallelAlgorithms:
        return BoundedLeastSquaresParallelAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLeastSquaresParallelAlgorithms:
        return GradientFreeLeastSquaresParallelAlgorithms()

    @property
    def Local(self) -> LeastSquaresLocalParallelAlgorithms:
        return LeastSquaresLocalParallelAlgorithms()


@dataclass(frozen=True)
class GradientBasedAlgorithms(AlgoSelection):
    bhhh: Type[BHHH] = BHHH
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    scipy_bfgs: Type[ScipyBFGS] = ScipyBFGS
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_conjugate_gradient: Type[ScipyConjugateGradient] = ScipyConjugateGradient
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_lm: Type[ScipyLSLM] = ScipyLSLM
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    scipy_newton_cg: Type[ScipyNewtonCG] = ScipyNewtonCG
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedGradientBasedAlgorithms:
        return BoundedGradientBasedAlgorithms()

    @property
    def Global(self) -> GlobalGradientBasedAlgorithms:
        return GlobalGradientBasedAlgorithms()

    @property
    def LeastSquares(self) -> GradientBasedLeastSquaresAlgorithms:
        return GradientBasedLeastSquaresAlgorithms()

    @property
    def Likelihood(self) -> GradientBasedLikelihoodAlgorithms:
        return GradientBasedLikelihoodAlgorithms()

    @property
    def Local(self) -> GradientBasedLocalAlgorithms:
        return GradientBasedLocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> GradientBasedNonlinearConstrainedAlgorithms:
        return GradientBasedNonlinearConstrainedAlgorithms()

    @property
    def Scalar(self) -> GradientBasedScalarAlgorithms:
        return GradientBasedScalarAlgorithms()


@dataclass(frozen=True)
class GradientFreeAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_praxis: Type[NloptPRAXIS] = NloptPRAXIS
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    pounders: Type[Pounders] = Pounders
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedGradientFreeAlgorithms:
        return BoundedGradientFreeAlgorithms()

    @property
    def Global(self) -> GlobalGradientFreeAlgorithms:
        return GlobalGradientFreeAlgorithms()

    @property
    def LeastSquares(self) -> GradientFreeLeastSquaresAlgorithms:
        return GradientFreeLeastSquaresAlgorithms()

    @property
    def Local(self) -> GradientFreeLocalAlgorithms:
        return GradientFreeLocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> GradientFreeNonlinearConstrainedAlgorithms:
        return GradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> GradientFreeParallelAlgorithms:
        return GradientFreeParallelAlgorithms()

    @property
    def Scalar(self) -> GradientFreeScalarAlgorithms:
        return GradientFreeScalarAlgorithms()


@dataclass(frozen=True)
class GlobalAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_shgo: Type[ScipySHGO] = ScipySHGO

    @property
    def Bounded(self) -> BoundedGlobalAlgorithms:
        return BoundedGlobalAlgorithms()

    @property
    def GradientBased(self) -> GlobalGradientBasedAlgorithms:
        return GlobalGradientBasedAlgorithms()

    @property
    def GradientFree(self) -> GlobalGradientFreeAlgorithms:
        return GlobalGradientFreeAlgorithms()

    @property
    def NonlinearConstrained(self) -> GlobalNonlinearConstrainedAlgorithms:
        return GlobalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> GlobalParallelAlgorithms:
        return GlobalParallelAlgorithms()

    @property
    def Scalar(self) -> GlobalScalarAlgorithms:
        return GlobalScalarAlgorithms()


@dataclass(frozen=True)
class LocalAlgorithms(AlgoSelection):
    bhhh: Type[BHHH] = BHHH
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_praxis: Type[NloptPRAXIS] = NloptPRAXIS
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    pounders: Type[Pounders] = Pounders
    scipy_bfgs: Type[ScipyBFGS] = ScipyBFGS
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_conjugate_gradient: Type[ScipyConjugateGradient] = ScipyConjugateGradient
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_lm: Type[ScipyLSLM] = ScipyLSLM
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_newton_cg: Type[ScipyNewtonCG] = ScipyNewtonCG
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedLocalAlgorithms:
        return BoundedLocalAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedLocalAlgorithms:
        return GradientBasedLocalAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLocalAlgorithms:
        return GradientFreeLocalAlgorithms()

    @property
    def LeastSquares(self) -> LeastSquaresLocalAlgorithms:
        return LeastSquaresLocalAlgorithms()

    @property
    def Likelihood(self) -> LikelihoodLocalAlgorithms:
        return LikelihoodLocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> LocalNonlinearConstrainedAlgorithms:
        return LocalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> LocalParallelAlgorithms:
        return LocalParallelAlgorithms()

    @property
    def Scalar(self) -> LocalScalarAlgorithms:
        return LocalScalarAlgorithms()


@dataclass(frozen=True)
class BoundedAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    pounders: Type[Pounders] = Pounders
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Global(self) -> BoundedGlobalAlgorithms:
        return BoundedGlobalAlgorithms()

    @property
    def GradientBased(self) -> BoundedGradientBasedAlgorithms:
        return BoundedGradientBasedAlgorithms()

    @property
    def GradientFree(self) -> BoundedGradientFreeAlgorithms:
        return BoundedGradientFreeAlgorithms()

    @property
    def LeastSquares(self) -> BoundedLeastSquaresAlgorithms:
        return BoundedLeastSquaresAlgorithms()

    @property
    def Local(self) -> BoundedLocalAlgorithms:
        return BoundedLocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> BoundedNonlinearConstrainedAlgorithms:
        return BoundedNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> BoundedParallelAlgorithms:
        return BoundedParallelAlgorithms()

    @property
    def Scalar(self) -> BoundedScalarAlgorithms:
        return BoundedScalarAlgorithms()


@dataclass(frozen=True)
class NonlinearConstrainedAlgorithms(AlgoSelection):
    ipopt: Type[Ipopt] = Ipopt
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr

    @property
    def Bounded(self) -> BoundedNonlinearConstrainedAlgorithms:
        return BoundedNonlinearConstrainedAlgorithms()

    @property
    def Global(self) -> GlobalNonlinearConstrainedAlgorithms:
        return GlobalNonlinearConstrainedAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedNonlinearConstrainedAlgorithms:
        return GradientBasedNonlinearConstrainedAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeNonlinearConstrainedAlgorithms:
        return GradientFreeNonlinearConstrainedAlgorithms()

    @property
    def Local(self) -> LocalNonlinearConstrainedAlgorithms:
        return LocalNonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> NonlinearConstrainedParallelAlgorithms:
        return NonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> NonlinearConstrainedScalarAlgorithms:
        return NonlinearConstrainedScalarAlgorithms()


@dataclass(frozen=True)
class ScalarAlgorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_praxis: Type[NloptPRAXIS] = NloptPRAXIS
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_bfgs: Type[ScipyBFGS] = ScipyBFGS
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_conjugate_gradient: Type[ScipyConjugateGradient] = ScipyConjugateGradient
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_newton_cg: Type[ScipyNewtonCG] = ScipyNewtonCG
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr
    tranquilo: Type[Tranquilo] = Tranquilo

    @property
    def Bounded(self) -> BoundedScalarAlgorithms:
        return BoundedScalarAlgorithms()

    @property
    def Global(self) -> GlobalScalarAlgorithms:
        return GlobalScalarAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedScalarAlgorithms:
        return GradientBasedScalarAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeScalarAlgorithms:
        return GradientFreeScalarAlgorithms()

    @property
    def Local(self) -> LocalScalarAlgorithms:
        return LocalScalarAlgorithms()

    @property
    def NonlinearConstrained(self) -> NonlinearConstrainedScalarAlgorithms:
        return NonlinearConstrainedScalarAlgorithms()

    @property
    def Parallel(self) -> ParallelScalarAlgorithms:
        return ParallelScalarAlgorithms()


@dataclass(frozen=True)
class LeastSquaresAlgorithms(AlgoSelection):
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    pounders: Type[Pounders] = Pounders
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_lm: Type[ScipyLSLM] = ScipyLSLM
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedLeastSquaresAlgorithms:
        return BoundedLeastSquaresAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedLeastSquaresAlgorithms:
        return GradientBasedLeastSquaresAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeLeastSquaresAlgorithms:
        return GradientFreeLeastSquaresAlgorithms()

    @property
    def Local(self) -> LeastSquaresLocalAlgorithms:
        return LeastSquaresLocalAlgorithms()

    @property
    def Parallel(self) -> LeastSquaresParallelAlgorithms:
        return LeastSquaresParallelAlgorithms()


@dataclass(frozen=True)
class LikelihoodAlgorithms(AlgoSelection):
    bhhh: Type[BHHH] = BHHH

    @property
    def GradientBased(self) -> GradientBasedLikelihoodAlgorithms:
        return GradientBasedLikelihoodAlgorithms()

    @property
    def Local(self) -> LikelihoodLocalAlgorithms:
        return LikelihoodLocalAlgorithms()


@dataclass(frozen=True)
class ParallelAlgorithms(AlgoSelection):
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    pounders: Type[Pounders] = Pounders
    pygad: Type[Pygad] = Pygad
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedParallelAlgorithms:
        return BoundedParallelAlgorithms()

    @property
    def Global(self) -> GlobalParallelAlgorithms:
        return GlobalParallelAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeParallelAlgorithms:
        return GradientFreeParallelAlgorithms()

    @property
    def LeastSquares(self) -> LeastSquaresParallelAlgorithms:
        return LeastSquaresParallelAlgorithms()

    @property
    def Local(self) -> LocalParallelAlgorithms:
        return LocalParallelAlgorithms()

    @property
    def NonlinearConstrained(self) -> NonlinearConstrainedParallelAlgorithms:
        return NonlinearConstrainedParallelAlgorithms()

    @property
    def Scalar(self) -> ParallelScalarAlgorithms:
        return ParallelScalarAlgorithms()


@dataclass(frozen=True)
class Algorithms(AlgoSelection):
    bayes_opt: Type[BayesOpt] = BayesOpt
    bhhh: Type[BHHH] = BHHH
    fides: Type[Fides] = Fides
    iminuit_migrad: Type[IminuitMigrad] = IminuitMigrad
    ipopt: Type[Ipopt] = Ipopt
    nag_dfols: Type[NagDFOLS] = NagDFOLS
    nag_pybobyqa: Type[NagPyBOBYQA] = NagPyBOBYQA
    neldermead_parallel: Type[NelderMeadParallel] = NelderMeadParallel
    nevergrad_bo: Type[NevergradBayesOptim] = NevergradBayesOptim
    nevergrad_cga: Type[NevergradCGA] = NevergradCGA
    nevergrad_cmaes: Type[NevergradCMAES] = NevergradCMAES
    nevergrad_de: Type[NevergradDifferentialEvolution] = NevergradDifferentialEvolution
    nevergrad_eda: Type[NevergradEDA] = NevergradEDA
    nevergrad_emna: Type[NevergradEMNA] = NevergradEMNA
    nevergrad_meta: Type[NevergradMeta] = NevergradMeta
    nevergrad_NGOpt: Type[NevergradNGOpt] = NevergradNGOpt
    nevergrad_oneplusone: Type[NevergradOnePlusOne] = NevergradOnePlusOne
    nevergrad_pso: Type[NevergradPSO] = NevergradPSO
    nevergrad_randomsearch: Type[NevergradRandomSearch] = NevergradRandomSearch
    nevergrad_samplingsearch: Type[NevergradSamplingSearch] = NevergradSamplingSearch
    nevergrad_tbpsa: Type[NevergradTBPSA] = NevergradTBPSA
    nlopt_bobyqa: Type[NloptBOBYQA] = NloptBOBYQA
    nlopt_ccsaq: Type[NloptCCSAQ] = NloptCCSAQ
    nlopt_cobyla: Type[NloptCOBYLA] = NloptCOBYLA
    nlopt_crs2_lm: Type[NloptCRS2LM] = NloptCRS2LM
    nlopt_direct: Type[NloptDirect] = NloptDirect
    nlopt_esch: Type[NloptESCH] = NloptESCH
    nlopt_isres: Type[NloptISRES] = NloptISRES
    nlopt_lbfgsb: Type[NloptLBFGSB] = NloptLBFGSB
    nlopt_mma: Type[NloptMMA] = NloptMMA
    nlopt_newuoa: Type[NloptNEWUOA] = NloptNEWUOA
    nlopt_neldermead: Type[NloptNelderMead] = NloptNelderMead
    nlopt_praxis: Type[NloptPRAXIS] = NloptPRAXIS
    nlopt_slsqp: Type[NloptSLSQP] = NloptSLSQP
    nlopt_sbplx: Type[NloptSbplx] = NloptSbplx
    nlopt_tnewton: Type[NloptTNewton] = NloptTNewton
    nlopt_var: Type[NloptVAR] = NloptVAR
    pounders: Type[Pounders] = Pounders
    pygad: Type[Pygad] = Pygad
    pygmo_bee_colony: Type[PygmoBeeColony] = PygmoBeeColony
    pygmo_cmaes: Type[PygmoCmaes] = PygmoCmaes
    pygmo_compass_search: Type[PygmoCompassSearch] = PygmoCompassSearch
    pygmo_de: Type[PygmoDe] = PygmoDe
    pygmo_de1220: Type[PygmoDe1220] = PygmoDe1220
    pygmo_gaco: Type[PygmoGaco] = PygmoGaco
    pygmo_gwo: Type[PygmoGwo] = PygmoGwo
    pygmo_ihs: Type[PygmoIhs] = PygmoIhs
    pygmo_mbh: Type[PygmoMbh] = PygmoMbh
    pygmo_pso: Type[PygmoPso] = PygmoPso
    pygmo_pso_gen: Type[PygmoPsoGen] = PygmoPsoGen
    pygmo_sade: Type[PygmoSade] = PygmoSade
    pygmo_sea: Type[PygmoSea] = PygmoSea
    pygmo_sga: Type[PygmoSga] = PygmoSga
    pygmo_simulated_annealing: Type[PygmoSimulatedAnnealing] = PygmoSimulatedAnnealing
    pygmo_xnes: Type[PygmoXnes] = PygmoXnes
    scipy_bfgs: Type[ScipyBFGS] = ScipyBFGS
    scipy_basinhopping: Type[ScipyBasinhopping] = ScipyBasinhopping
    scipy_brute: Type[ScipyBrute] = ScipyBrute
    scipy_cobyla: Type[ScipyCOBYLA] = ScipyCOBYLA
    scipy_conjugate_gradient: Type[ScipyConjugateGradient] = ScipyConjugateGradient
    scipy_differential_evolution: Type[ScipyDifferentialEvolution] = (
        ScipyDifferentialEvolution
    )
    scipy_direct: Type[ScipyDirect] = ScipyDirect
    scipy_dual_annealing: Type[ScipyDualAnnealing] = ScipyDualAnnealing
    scipy_lbfgsb: Type[ScipyLBFGSB] = ScipyLBFGSB
    scipy_ls_dogbox: Type[ScipyLSDogbox] = ScipyLSDogbox
    scipy_ls_lm: Type[ScipyLSLM] = ScipyLSLM
    scipy_ls_trf: Type[ScipyLSTRF] = ScipyLSTRF
    scipy_neldermead: Type[ScipyNelderMead] = ScipyNelderMead
    scipy_newton_cg: Type[ScipyNewtonCG] = ScipyNewtonCG
    scipy_powell: Type[ScipyPowell] = ScipyPowell
    scipy_shgo: Type[ScipySHGO] = ScipySHGO
    scipy_slsqp: Type[ScipySLSQP] = ScipySLSQP
    scipy_truncated_newton: Type[ScipyTruncatedNewton] = ScipyTruncatedNewton
    scipy_trust_constr: Type[ScipyTrustConstr] = ScipyTrustConstr
    tao_pounders: Type[TAOPounders] = TAOPounders
    tranquilo: Type[Tranquilo] = Tranquilo
    tranquilo_ls: Type[TranquiloLS] = TranquiloLS

    @property
    def Bounded(self) -> BoundedAlgorithms:
        return BoundedAlgorithms()

    @property
    def Global(self) -> GlobalAlgorithms:
        return GlobalAlgorithms()

    @property
    def GradientBased(self) -> GradientBasedAlgorithms:
        return GradientBasedAlgorithms()

    @property
    def GradientFree(self) -> GradientFreeAlgorithms:
        return GradientFreeAlgorithms()

    @property
    def LeastSquares(self) -> LeastSquaresAlgorithms:
        return LeastSquaresAlgorithms()

    @property
    def Likelihood(self) -> LikelihoodAlgorithms:
        return LikelihoodAlgorithms()

    @property
    def Local(self) -> LocalAlgorithms:
        return LocalAlgorithms()

    @property
    def NonlinearConstrained(self) -> NonlinearConstrainedAlgorithms:
        return NonlinearConstrainedAlgorithms()

    @property
    def Parallel(self) -> ParallelAlgorithms:
        return ParallelAlgorithms()

    @property
    def Scalar(self) -> ScalarAlgorithms:
        return ScalarAlgorithms()


algos = Algorithms()
global_algos = GlobalAlgorithms()

ALL_ALGORITHMS = algos._all_algorithms_dict
AVAILABLE_ALGORITHMS = algos._available_algorithms_dict
GLOBAL_ALGORITHMS = global_algos._available_algorithms_dict
