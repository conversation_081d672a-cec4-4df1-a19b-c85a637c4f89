# This workflow will install Python dependencies and run tests in multiple versions of Python
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-python-with-github-actions

name: tests

on:
  push:
    branches: [ "master" ]
  pull_request:

permissions:
  contents: read

jobs:
  build:
    name: Python ${{ matrix.python-version }} - numpy ${{ matrix.numpy-version }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12", "3.13"]
        numpy-version: [">=1.25,<2", ">=2"]
        exclude:
          - python-version: "3.13"
            numpy-version: ">=1.25,<2" # numpy<2 is not supported on Python 3.13

    steps:
    - uses: actions/checkout@v3
    - name: Install uv and set the python version
      uses: astral-sh/setup-uv@v6
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install test dependencies
      run: |
        uv sync --extra dev
        uv pip install "numpy${{ matrix.numpy-version}}"
    - name: Run pytest
      run: uv run --no-sync pytest --cov-report xml --cov=bayes_opt/
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
          token: ${{ secrets.CODECOV_TOKEN }}
