.ipynb_checkpoints
*.pyc
*.egg-info/
build/
dist/
scratch/
.idea/
.DS_Store
bo_eg*.png
gif/

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*,cover
.hypothesis/

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
*temp*

docs/*
docsrc/.ipynb_checkpoints/*
docsrc/*.ipynb
docsrc/static/*
docsrc/README.md

poetry.lock
uv.lock

# Add log files and optimizer state files to gitignore
examples/logs.log
examples/optimizer_state.json
