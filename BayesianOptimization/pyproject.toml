[project]
name = "bayesian-optimization"
version = "3.1.0"
description = "Bayesian Optimization package"
authors = [{ name = "<PERSON>", email = "fmf<PERSON><PERSON><PERSON>@gmail.com" }]
license = { file = "LICENSE" }
readme = "README.md"
requires-python = ">=3.9"
classifiers = [
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
]
dependencies = [
    "colorama>=0.4.6",
    "numpy>=1.25; python_version<'3.13'",
    "numpy>=2.1.3; python_version>='3.13'",
    "scikit-learn>=1.0.0",
    "scipy>=1.0.0; python_version<'3.13'",
    "scipy>=1.14.1; python_version>='3.13'",
]

[project.optional-dependencies]
dev = [
    "coverage>=7.4.1",
    "jupyter>=1.0.0",
    "matplotlib>=3.0",
    "nbconvert>=7.14.2",
    "nbformat>=5.9.2",
    "nbsphinx>=0.9.4",
    "pre-commit>=3.7.1",
    "pytest>=8.0.0",
    "pytest-cov>=4.1.0",
    "ruff>=0.12.3",
    "sphinx-immaterial>=0.12.0",
    "sphinx>=7.0.0; python_version<'3.10'",
    "sphinx>=8.0.0; python_version>='3.10'",
    "sphinx-autodoc-typehints>=2.3.0; python_version<'3.10'",
    "sphinx-autodoc-typehints>=2.4.0; python_version>='3.10'",
]

[build-system]
requires = ["uv_build>=0.7.21,<0.8.0"]
build-backend = "uv_build"

[tool.uv.build-backend]
module-name = "bayes_opt"
module-root = ""

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "if TYPE_CHECKING:",
]
