{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Optimizing over non-float Parameters\n", "\n", "Sometimes, you need to optimize a target that is not just a function of floating-point values, but relies on integer or categorical parameters. This notebook shows how such problems are handled by following an approach from [\"Dealing with categorical and integer-valued variables in Bayesian Optimization with Gaussian processes\" by <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON>](https://arxiv.org/abs/1805.03463). One simple way of handling an integer-valued parameter is to run the optimization as normal, but then round to the nearest integer after a point has been suggested. This method is similar, except that the rounding is performed in the _kernel_. Why does this matter? It means that the kernel is aware that two parameters, that map the to same point but are potentially distinct before this transformation are the same."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import warnings\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from bayes_opt import BayesianOptimization\n", "from bayes_opt import acquisition\n", "\n", "from sklearn.gaussian_process.kernels import Matern\n", "\n", "# suppress warnings about this being an experimental feature\n", "warnings.filterwarnings(action=\"ignore\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Simple integer-valued function\n", "Let's look at a simple, one-dimensional, integer-valued target function and compare a typed optimizer and a continuous optimizer."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1000x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def target_function_1d(x):\n", "    return np.sin(np.round(x)) - np.abs(np.round(x) / 5)\n", "\n", "c_pbounds = {'x': (-10, 10)}\n", "bo_cont = BayesianOptimization(target_function_1d, c_pbounds, verbose=0, random_state=1)\n", "\n", "# one way of constructing an integer-valued parameter is to add a third element to the tuple\n", "d_pbounds = {'x': (-10, 10, int)}\n", "bo_disc = BayesianOptimization(target_function_1d, d_pbounds, verbose=0, random_state=1)\n", "\n", "fig, axs = plt.subplots(2, 1, figsize=(10, 6), sharex=True, sharey=True)\n", "\n", "bo_cont.maximize(init_points=2, n_iter=10)\n", "bo_cont.acquisition_function._fit_gp(bo_cont._gp, bo_cont.space)\n", "\n", "y_mean, y_std = bo_cont._gp.predict(np.linspace(-10, 10, 1000).reshape(-1, 1), return_std=True)\n", "axs[0].set_title('Continuous')\n", "axs[0].plot(np.linspace(-10, 10, 1000), target_function_1d(np.linspace(-10, 10, 1000)), 'k--', label='True function')\n", "axs[0].plot(np.linspace(-10, 10, 1000), y_mean, label='Predicted mean')\n", "axs[0].fill_between(np.linspace(-10, 10, 1000), y_mean - y_std, y_mean + y_std, alpha=0.3, label='Predicted std')\n", "axs[0].plot(bo_cont.space.params, bo_cont.space.target, 'ro')\n", "\n", "bo_disc.maximize(init_points=2, n_iter=10)\n", "bo_disc.acquisition_function._fit_gp(bo_disc._gp, bo_disc.space)\n", "\n", "y_mean, y_std = bo_disc._gp.predict(np.linspace(-10, 10, 1000).reshape(-1, 1), return_std=True)\n", "axs[1].set_title('Discrete')\n", "axs[1].plot(np.linspace(-10, 10, 1000), target_function_1d(np.linspace(-10, 10, 1000)), 'k--', label='True function')\n", "axs[1].plot(np.linspace(-10, 10, 1000), y_mean, label='Predicted mean')\n", "axs[1].fill_between(np.linspace(-10, 10, 1000), y_mean - y_std, y_mean + y_std, alpha=0.3, label='Predicted std')\n", "axs[1].plot(bo_disc.space.params, bo_disc.space.target, 'ro')\n", "\n", "for ax in axs:\n", "    ax.grid(True)\n", "fig.tight_layout()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see, that the discrete optimizer is aware that the function is discrete and does not try to predict values between the integers. The continuous optimizer tries to predict values between the integers, despite the fact that these are known.\n", "We can also see that the discrete optimizer predicts blocky mean and standard deviations, which is a result of the discrete nature of the function."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Mixed-parameter optimization"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def discretized_function(x, y):\n", "    y = np.round(y)\n", "    return (-1*np.cos(x)**np.abs(y) + -1*np.cos(y)) + 0.1 * (x + y) - 0.01 * (x**2 + y**2)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Bounded region of parameter space\n", "c_pbounds = {'x': (-5, 5), 'y': (-5, 5)}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["labels = [\"All-float Optimizer\", \"Typed Optimizer\"]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["continuous_optimizer = BayesianOptimization(\n", "    f=discretized_function,\n", "    acquisition_function=acquisition.ExpectedImprovement(xi=0.01, random_state=1),\n", "    pbounds=c_pbounds,\n", "    verbose=2,\n", "    random_state=1,\n", ")\n", "\n", "continuous_optimizer.set_gp_params(kernel=Matern(nu=2.5, length_scale=np.ones(2)))\n", "\n", "d_pbounds = {'x': (-5, 5), 'y': (-5, 5, int)}\n", "discrete_optimizer = BayesianOptimization(\n", "    f=discretized_function,\n", "    acquisition_function=acquisition.ExpectedImprovement(xi=0.01, random_state=1),\n", "    pbounds=d_pbounds,\n", "    verbose=2,\n", "    random_state=1,\n", ")\n", "\n", "discrete_optimizer.set_gp_params(kernel=Matern(nu=2.5, length_scale=np.ones(2)));"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["====================  All-float Optimizer  ====================\n", "\n", "|   iter    |  target   |     x     |     y     |\n", "-------------------------------------------------\n", "| \u001b[39m2        \u001b[39m | \u001b[39m0.03061  \u001b[39m | \u001b[39m-0.829779\u001b[39m | \u001b[39m2.2032449\u001b[39m |\n", "| \u001b[39m3        \u001b[39m | \u001b[39m-0.6535  \u001b[39m | \u001b[39m-4.998856\u001b[39m | \u001b[39m-1.976674\u001b[39m |\n", "| \u001b[35m4        \u001b[39m | \u001b[35m0.8025   \u001b[39m | \u001b[35m-0.829779\u001b[39m | \u001b[35m2.6549696\u001b[39m |\n", "| \u001b[35m5        \u001b[39m | \u001b[35m0.9203   \u001b[39m | \u001b[35m-0.981065\u001b[39m | \u001b[35m2.6644394\u001b[39m |\n", "| \u001b[35m6        \u001b[39m | \u001b[35m1.008    \u001b[39m | \u001b[35m-1.652553\u001b[39m | \u001b[35m2.7133425\u001b[39m |\n", "| \u001b[39m7        \u001b[39m | \u001b[39m0.9926   \u001b[39m | \u001b[39m-1.119714\u001b[39m | \u001b[39m2.8358733\u001b[39m |\n", "| \u001b[35m8        \u001b[39m | \u001b[35m1.322    \u001b[39m | \u001b[35m-2.418942\u001b[39m | \u001b[35m3.4600371\u001b[39m |\n", "| \u001b[39m9        \u001b[39m | \u001b[39m-0.5063  \u001b[39m | \u001b[39m-3.092074\u001b[39m | \u001b[39m3.7368226\u001b[39m |\n", "| \u001b[39m10       \u001b[39m | \u001b[39m-0.6432  \u001b[39m | \u001b[39m-4.089558\u001b[39m | \u001b[39m-0.560384\u001b[39m |\n", "| \u001b[39m11       \u001b[39m | \u001b[39m1.267    \u001b[39m | \u001b[39m-2.360726\u001b[39m | \u001b[39m3.3725022\u001b[39m |\n", "| \u001b[39m12       \u001b[39m | \u001b[39m0.4649   \u001b[39m | \u001b[39m-2.247113\u001b[39m | \u001b[39m3.7419056\u001b[39m |\n", "| \u001b[39m13       \u001b[39m | \u001b[39m1.0      \u001b[39m | \u001b[39m-1.740988\u001b[39m | \u001b[39m3.4854116\u001b[39m |\n", "| \u001b[39m14       \u001b[39m | \u001b[39m0.986    \u001b[39m | \u001b[39m1.2164322\u001b[39m | \u001b[39m4.4938459\u001b[39m |\n", "| \u001b[39m15       \u001b[39m | \u001b[39m-2.27    \u001b[39m | \u001b[39m-2.213867\u001b[39m | \u001b[39m0.3585570\u001b[39m |\n", "| \u001b[39m16       \u001b[39m | \u001b[39m-1.853   \u001b[39m | \u001b[39m1.7935035\u001b[39m | \u001b[39m-0.377351\u001b[39m |\n", "=================================================\n", "Max: 1.321554535694256\n", "\n", "\n", "====================  Typed Optimizer  ====================\n", "\n", "|   iter    |  target   |     x     |     y     |\n", "-------------------------------------------------\n", "| \u001b[39m2        \u001b[39m | \u001b[39m0.8025   \u001b[39m | \u001b[39m-0.829779\u001b[39m | \u001b[39m3        \u001b[39m |\n", "| \u001b[39m3        \u001b[39m | \u001b[39m-2.75    \u001b[39m | \u001b[39m-4.998856\u001b[39m | \u001b[39m0        \u001b[39m |\n", "| \u001b[39m4        \u001b[39m | \u001b[39m0.8007   \u001b[39m | \u001b[39m-0.827713\u001b[39m | \u001b[39m3        \u001b[39m |\n", "| \u001b[39m5        \u001b[39m | \u001b[39m-0.749   \u001b[39m | \u001b[39m2.2682240\u001b[39m | \u001b[39m-5       \u001b[39m |\n", "| \u001b[39m6        \u001b[39m | \u001b[39m0.2379   \u001b[39m | \u001b[39m-2.449054\u001b[39m | \u001b[39m4        \u001b[39m |\n", "| \u001b[39m7        \u001b[39m | \u001b[39m0.7479   \u001b[39m | \u001b[39m4.995975 \u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[35m8        \u001b[39m | \u001b[35m0.8296   \u001b[39m | \u001b[35m4.9889151\u001b[39m | \u001b[35m-3       \u001b[39m |\n", "| \u001b[39m9        \u001b[39m | \u001b[39m-1.199   \u001b[39m | \u001b[39m-1.446743\u001b[39m | \u001b[39m-5       \u001b[39m |\n", "| \u001b[39m10       \u001b[39m | \u001b[39m-0.2539  \u001b[39m | \u001b[39m-4.999373\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[35m11       \u001b[39m | \u001b[35m1.429    \u001b[39m | \u001b[35m4.9898854\u001b[39m | \u001b[35m3        \u001b[39m |\n", "| \u001b[39m12       \u001b[39m | \u001b[39m0.2149   \u001b[39m | \u001b[39m4.9849287\u001b[39m | \u001b[39m5        \u001b[39m |\n", "| \u001b[39m13       \u001b[39m | \u001b[39m-0.1697  \u001b[39m | \u001b[39m-4.992664\u001b[39m | \u001b[39m-3       \u001b[39m |\n", "| \u001b[39m14       \u001b[39m | \u001b[39m1.137    \u001b[39m | \u001b[39m4.9998822\u001b[39m | \u001b[39m4        \u001b[39m |\n", "| \u001b[39m15       \u001b[39m | \u001b[39m0.3508   \u001b[39m | \u001b[39m4.9904899\u001b[39m | \u001b[39m-2       \u001b[39m |\n", "| \u001b[35m16       \u001b[39m | \u001b[35m1.741    \u001b[39m | \u001b[35m3.9775298\u001b[39m | \u001b[35m3        \u001b[39m |\n", "=================================================\n", "Max: 1.7409514743244399\n", "\n", "\n"]}], "source": ["for lbl, optimizer in zip(labels, [continuous_optimizer, discrete_optimizer]):\n", "    print(f\"====================  {lbl}  ====================\\n\")\n", "    optimizer.maximize(\n", "        init_points=2,\n", "        n_iter=13\n", "    )\n", "    print(f\"Max: {optimizer.max['target']}\\n\\n\")"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x = np.linspace(c_pbounds['x'][0], c_pbounds['x'][1], 1000)\n", "y = np.linspace(c_pbounds['y'][0], c_pbounds['y'][1], 1000)\n", "\n", "X, Y = np.meshgrid(x, y)\n", "\n", "Z = discretized_function(X, Y)\n", "\n", "params = [{'x': x_i, 'y': y_j} for y_j in y for x_i in x]\n", "array_params = [continuous_optimizer._space.params_to_array(p) for p in params]\n", "c_pred = continuous_optimizer._gp.predict(array_params).reshape(X.shape)\n", "d_pred = discrete_optimizer._gp.predict(array_params).reshape(X.shape)\n", "\n", "vmin = np.min([np.min(Z), np.min(c_pred), np.min(d_pred)])\n", "vmax = np.max([np.max(Z), np.max(c_pred), np.max(d_pred)])\n", "\n", "fig, axs = plt.subplots(1, 3)\n", "\n", "axs[0].set_title('Actual function')\n", "axs[0].contourf(X, Y, Z, cmap=plt.cm.coolwarm, vmin=vmin, vmax=vmax)\n", "\n", "\n", "axs[1].set_title(labels[0])\n", "axs[1].contourf(X, Y, c_pred, cmap=plt.cm.coolwarm, vmin=vmin, vmax=vmax)\n", "axs[1].scatter(continuous_optimizer._space.params[:,0], continuous_optimizer._space.params[:,1], c='k')\n", "\n", "axs[2].set_title(labels[1])\n", "axs[2].contourf(X, Y, d_pred, cmap=plt.cm.coolwarm, vmin=vmin, vmax=vmax)\n", "axs[2].scatter(discrete_optimizer._space.params[:,0], discrete_optimizer._space.params[:,1], c='k')\n", "\n", "def make_plot_fancy(ax: plt.Axes):\n", "    ax.set_aspect(\"equal\")\n", "    ax.set_xlabel('x (float)')\n", "    ax.set_xticks([-5.0, -2.5, 0., 2.5, 5.0])\n", "    ax.set_ylabel('y (int)')\n", "    ax.set_yticks([-4, -2, 0, 2, 4])\n", "\n", "for ax in axs:\n", "    make_plot_fancy(ax)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Categorical variables\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also handle categorical variables! This is done under-the-hood by constructing parameters in a one-hot-encoding representation, with a transformation in the kernel rounding to the nearest one-hot representation. If you want to use this, you can specify a collection of strings as options.\n", "\n", "NB: As internally, the categorical variables are within a range of `[0, 1]` and the GP used for BO is by default isotropic, you might want to ensure your other features are similarly scaled to a range of `[0, 1]` or use an anisotropic GP."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def f1(x1, x2):\n", "    return -1*(x1 - np.sqrt(x1**2 + x2**2) * np.cos(np.sqrt(x1**2 + x2**2))**2 + 0.5 * np.sqrt(x1**2 + x2**2))\n", "\n", "def f2(x1, x2):\n", "    return -1*(x2 - np.sqrt(x1**2 + x2**2) * np.sin(np.sqrt(x1**2 + x2**2))**2 + 0.5 * np.sqrt(x1**2 + x2**2))\n", "\n", "def SPIRAL(x1, x2, k):\n", "    \"\"\"cf <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\n", "    \"\"\"\n", "    if k=='1':\n", "        return f1(10 * x1, 10 * x2)\n", "    elif k=='2':\n", "        return f2(10 * x1, 10 * x2)\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|   iter    |  target   |    x1     |    x2     |     k     |\n", "-------------------------------------------------------------\n", "| \u001b[39m2        \u001b[39m | \u001b[39m-2.052   \u001b[39m | \u001b[39m-0.165955\u001b[39m | \u001b[39m0.4406489\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[35m3        \u001b[39m | \u001b[35m13.49    \u001b[39m | \u001b[35m-0.743751\u001b[39m | \u001b[35m0.9980810\u001b[39m | \u001b[35m1        \u001b[39m |\n", "| \u001b[39m4        \u001b[39m | \u001b[39m-15.7    \u001b[39m | \u001b[39m-0.763608\u001b[39m | \u001b[39m0.9785936\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[39m5        \u001b[39m | \u001b[39m-0.3783  \u001b[39m | \u001b[39m-0.093284\u001b[39m | \u001b[39m0.1023150\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[39m6        \u001b[39m | \u001b[39m3.247    \u001b[39m | \u001b[39m-0.806061\u001b[39m | \u001b[39m-0.713343\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m7        \u001b[39m | \u001b[39m3.551    \u001b[39m | \u001b[39m-0.196292\u001b[39m | \u001b[39m0.2582785\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m8        \u001b[39m | \u001b[39m6.097    \u001b[39m | \u001b[39m-0.611686\u001b[39m | \u001b[39m-0.354793\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m9        \u001b[39m | \u001b[39m0.878    \u001b[39m | \u001b[39m-0.109889\u001b[39m | \u001b[39m-0.701415\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m10       \u001b[39m | \u001b[39m-13.98   \u001b[39m | \u001b[39m0.9422961\u001b[39m | \u001b[39m-0.625876\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m11       \u001b[39m | \u001b[39m-5.565   \u001b[39m | \u001b[39m0.8618152\u001b[39m | \u001b[39m0.5454722\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[39m12       \u001b[39m | \u001b[39m-5.01    \u001b[39m | \u001b[39m0.2751833\u001b[39m | \u001b[39m-0.369982\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m13       \u001b[39m | \u001b[39m-8.922   \u001b[39m | \u001b[39m-0.635194\u001b[39m | \u001b[39m0.6307052\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[39m14       \u001b[39m | \u001b[39m1.136    \u001b[39m | \u001b[39m-0.182129\u001b[39m | \u001b[39m-0.271731\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[39m15       \u001b[39m | \u001b[39m-10.96   \u001b[39m | \u001b[39m-0.639808\u001b[39m | \u001b[39m0.6662940\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[39m16       \u001b[39m | \u001b[39m4.995    \u001b[39m | \u001b[39m-0.214441\u001b[39m | \u001b[39m0.5711843\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m17       \u001b[39m | \u001b[39m-6.05    \u001b[39m | \u001b[39m-0.202833\u001b[39m | \u001b[39m0.5261994\u001b[39m | \u001b[39m2        \u001b[39m |\n", "| \u001b[39m18       \u001b[39m | \u001b[39m1.284    \u001b[39m | \u001b[39m0.0906321\u001b[39m | \u001b[39m-0.584503\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m19       \u001b[39m | \u001b[39m-2.206   \u001b[39m | \u001b[39m0.1402396\u001b[39m | \u001b[39m0.1141631\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m20       \u001b[39m | \u001b[39m0.5288   \u001b[39m | \u001b[39m-0.134786\u001b[39m | \u001b[39m0.0990788\u001b[39m | \u001b[39m1        \u001b[39m |\n", "| \u001b[39m21       \u001b[39m | \u001b[39m-9.041   \u001b[39m | \u001b[39m0.8403564\u001b[39m | \u001b[39m0.4792001\u001b[39m | \u001b[39m2        \u001b[39m |\n", "=============================================================\n"]}], "source": ["pbounds = {'x1': (-1, 1), 'x2': (-1, 1), 'k': ('1', '2')}\n", "\n", "categorical_optimizer = BayesianOptimization(\n", "    f=SPIRAL,\n", "    acquisition_function=acquisition.ExpectedImprovement(1e-2),\n", "    pbounds=pbounds,\n", "    verbose=2,\n", "    random_state=1,\n", ")\n", "discrete_optimizer.set_gp_params(alpha=1e-3)\n", "\n", "categorical_optimizer.maximize(\n", "        init_points=2,\n", "        n_iter=18,\n", "    )"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["res = categorical_optimizer._space.res()\n", "k1 = np.array([[p['params']['x1'], p['params']['x2']] for p in res if p['params']['k']=='1'])\n", "k2 = np.array([[p['params']['x1'], p['params']['x2']] for p in res if p['params']['k']=='2'])"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAnUAAAEsCAYAAAClnkX2AAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjkuNCwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8ekN5oAAAACXBIWXMAAA9hAAAPYQGoP6dpAABk6ElEQVR4nO29e3xU1bn//5mZJJOEMDNEciEaDBEbguEiieTEY2t7zMsEsRWPrWChXI7CUYstxaNCfwhVtCj165dKOaVVEax4QP2KtWijNIq2yuESRAhGyv2ecAnJ5EICyezfHzjDTDKXfVlr77X3PO/Xa78ge9be+1l7r/2sz37WzSZJkgSCIAiCIAjC1NiNNoAgCIIgCILQDok6giAIgiAIC0CijiAIgiAIwgKQqCMIgiAIgrAAJOoIgiAIgiAsAIk6giAIgiAIC0CijiAIgiAIwgKQqCMIgiAIgrAAJOoIgiAIgiAsAIk6Qih+9atfwWaz4cyZM0abQhAEwRTybwRvSNQRccOWLVvw4IMPori4GImJibDZbEabRBAEoRmfz4eVK1fiBz/4AXJzc9GnTx8UFRXhqaeeQkdHh9HmETpCoo6IG95//3289NJLsNlsyM/PN9ocgiAIJrS3t2PatGk4ffo07r//fixZsgSjR4/GggULMGbMGNAS7/FDgtEGEIRePPDAA3jssceQkpKCmTNn4p///KfRJhEEQWgmKSkJn332GW688cbAvunTpyMvLw8LFixAdXU1ysvLDbSQ0AuK1BHCc/jwYQwePBhFRUVoaGhQfZ6srCykpKQwtIwgCEIbLPxbUlJSiKDzc+eddwIA6urqNNlImAeK1BFCs3//fvzbv/0b0tPTsWHDBvTv3x/t7e1ob2+PeazD4UC/fv10sJIgCEI5vP1bfX09AKB///5M7CXEh0QdISxff/01brnlFlx55ZX44IMPAg5s8eLFeOKJJ2Ief/XVV+PQoUOcrSQIglCOHv5t8eLFcLlcGDNmDAuTCRNAoo4QktraWowfPx6DBw/GX//6V7hcrsBvkydPxk033RTzHNTUShCEiOjh337961/jb3/7G/77v/8bHo9Hq8mESSBRRwjJ97//fWRlZeGDDz5AWlpayG/5+fk0epUgCNPC27+tXbsW8+bNw7333osHHnhA07kIc0GijhCSu+66C6tWrcLq1avxn//5nyG/tba2orW1NeY5HA4HMjIyeJlIEAShCp7+bcOGDZg8eTLGjh2L5cuXM7OZMAck6ggh+c1vfoOEhAQ8+OCD6Nu3L3784x8HfnvuueeoTx1BEKaFl3/bvHkz7rzzTpSUlOCNN95AQgJV8fEGPXFCSGw2G/74xz+ipaUFU6ZMQVpaGn7wgx8AoD51BEGYGx7+ra6uDmPHjkVeXh7Wr19P/i9OIVFHCIvdbsdrr72GcePG4e6778b777+Pf/u3f1Pd5+Tw4cP405/+BADYtm0bAOCpp54CcOmr9yc/+Qk74wmCIKLA0r+1tLSgoqIC586dwyOPPIL33nsv5PdrrrkGZWVlLM0nBIVEHSE0iYmJeOuttzBmzBjccccd+Nvf/obS0lJV5zp48CAef/zxkH3+v2+++WYSdQRB6Aor/3b27FkcPXoUADBnzpxev0+ZMoVEXZxgk2hROIIgCIIgCNNDy4QRBEEQBEFYABJ1BEEQBEEQFoBEHUEQBEEQhAXgKuo+/fRTfP/730dOTg5sNhveeeedmMds3LgRo0aNgtPpxODBg7Fy5cpeaZYtW4a8vDwkJyejtLQUW7ZsYW88QRBEDMjHEQQhElxFXVtbG0aMGIFly5bJSn/w4EGMHTsW3/ve97Bjxw7MmjUL9913Hz744INAmrVr12L27NlYsGABtm/fjhEjRqCiogKnTp3ilQ2CIIiwkI8jCEIkdBv9arPZsG7dOowbNy5imsceewzvvfceamtrA/smTJiApqYmVFVVAQBKS0txww034He/+x0AwOfzITc3Fw899FDYodwEQRB6QD6OIAijEWqeuk2bNqG8vDxkX0VFBWbNmgUAuHDhAmpqajB37tzA73a7HeXl5di0aVPE83Z2dqKzszPwt8/nQ2NjI6644grYbDa2mSAIggmSJKGlpQU5OTmw263R/Zd8HEEQfnj4OKFEXX19PbKyskL2ZWVlwev14vz58zh37hy6u7vDpvn6668jnnfRokWy1tIjCEI8jh49iquuuspoM5hAPo4giJ6w9HFCiTpezJ07F7Nnzw783dzcjIEDB+LK/zsH9hRnYH9R3omI5/j3zO2Kr7tnmxfPzdgbM91//fFaFJS48PapUVHT1R7KUXR95yFn7ETf0JnXGTtRHKPkXkbCvd+n6XhPnTfq702FLlXnbb6GbxQsUr4j5ae7dg8AoAsX8Q+8j759+3KzzSpE8nH5v5gPuzM5sD9aGYxVvvzP5RxO40t8HtOmUYPGo1+fqyP+rrS8aimnWt+9cMS6X1ZArU+JhlZ/o/RZyn1O/vIdDUdRQdTfo92vcPn2dXbgwP99kqmPE0rUZWdno6GhIWRfQ0MDXC4XUlJS4HA44HA4wqbJzs6OeF6n0wmns3elbE9xwp6SjBH5x77ZkxT2+PHZWwE4FOUFAEZ8x4P07CQ0NlwAwvVctAHp2UkY8R0P3jw9Gol9Ip/rywNXwa5gfebkA04ghg7pyL8s5OxIjpKSuFgY+nfyAeUir3XopX89e9VVMK3Dk+HZ3Rzx9/7/vPw8m65zyz7vFUeApmv5CbvWoeHzHCk/CSOGo3tnXeCdsVLzoe4+zpkMR5CoS0iMILB3NwOOyGW6e2cdEmyJl/4vdUVMF0xX9wUkhDmnv2zKrXz8ZVO5Bw4qd4kqDg53vuDyGuV+WYVgnwIo8yuRuOLIN+dS6XOU+tFYftNPwO9EY/cBOIYXRvy5/z87I96jaH6WpY8TStSVlZXh/fffD9m3YcOGwJp1SUlJKC4uRnV1daAzss/nQ3V1NWbOnKnqmpcFXXguCTp12B02TJ43EEse2gfYECrsvnmGk/+/gXjz9OiI5/jygPKQbCTBESzi9CLW/VWCmnvBC/+9VCPu/C+2GnHXdJ1bloPyp5HrhP228BJ3Tdfaw+Y3Un4cwwvR9eVOLrYYiRE+zk+k8harPPWs6JJkfgA6E9N67VMiCrSWRbUfT73OI+N9ixeC74VWgafV5yjxo35bYz1Lv2CLJu66d9ZFFXae3c0R741nr4/rBzTAWdS1trZi3759gb8PHjyIHTt2ID09HQMHDsTcuXNx/PhxvPrqqwCA+++/H7/73e/w6KOP4j/+4z/w0Ucf4Y033sB7770XOMfs2bMxZcoUlJSUYPTo0ViyZAna2towbdo0xfZdam4NH50DtAk6PzdUpGPW0sF49akjaKy/ENifnp2Eyf/fQBwYURHxWBaCTg8hx1K4KbmOCCIv+P4qFXhqxZ1cYQeIJe4UC7uiAqC2126hEN3H+VEr6MLRDxlwIgWdOB8xTXKiC/36DAz8bTYxR0IuNkp9S8TzCCruzCrsuIq6bdu24Xvf+17gb3+fjylTpmDlypU4efIkjhw5Evh90KBBeO+99/CLX/wCv/3tb3HVVVfhpZdeQkXFZeEzfvx4nD59GvPnz0d9fT1GjhyJqqqqXh2LtcJC0Pm5oSIdxeX98PW2FjSdughPZiJ2XvU9HHBEfrBqBQtvEaeXgJPDiPxjQgg7P2qjd2rEnVzn5EeNuOP9RRmMEqEqEmbwcVpETriKzWazoUAaiZ2IPBp3SM6tsNkulR+5ZY7EnDlhFb3T6nOUijutUTtRhZ1u89SJhNfrhdvtxpiq6UjsExqpYynmIrG2/oaov4skVEQSceEQ6V71RE3TLKC8clJTEekZOelJtPwF56WruxPVtYvR3NwMl4t9h20r4/dxxT96CgmJ4ZtLlTa7+vFXZA1Ndag78QE6L7YEfktOdGFIzq3I8hTqJuYAbYKOhBx7tEbv9CwTcp5/tKhdNGEHRL4XTdfa0d3ZgX3P/JKpjxOqT53RkKC7hOhCziyobZpVGrlTE+VSErlj/VUZqRmW0A+tgg4AsjyFyHQX4FzbEXRebIUzMQ39+gyEzWaXVa5IzFkXrdE7Fj5Hrp/RGrVTG7Hz7PXh7MAwB2iERN03GC3ojBZzZhNyRt8vpahpmlUifpQ2x/qRK+5Y97VT2r+OYAcLQefHZrMjPS0v8LcZonNUvvRFbd87Fj5H7geyXL8Tq69dJCIJOx5T7VhjmnaNxKugG5F/LLCZhS8PXGU6QReM0j6PTdfaFTk1tc0esvvmMYywRcoXi2kTiPCoEXSO4YUxm5gAec9NaXkOh2evT1U59OxuJkFnIGrvPwufI6fcNV3nllWGw70LcoSeXmUv7iN1cgXdhLRzIX+vae0n+xoiCTozCTg/ZhZx4VAbtQPYdQIOhxFRu2gRu7SdtIA9S9SUCTliDohdZlhFeNWKOUIc1ETuWPkcOa0faptjYzXD6kVcR+rkrhLRU9ApQRRBZ9aInNUEXTAd+Z2qIney0mkZhaZz1C5ixI7DbPbxitLO4Eqic6IKOorMiY3oUTs59HxHYkXs9CiPcR+pi0YkMSc3SieCoDOLkLOyeItFR34nl6id2n52wcfIidrxjNgR2lEj6OQgspgjzIHaqJ0ePoflhMXBRJvqhAVxHamLhpboHGC8oDNDZC4eonFyMXPUjsSYNZAbnQNI0BFsURpVVduvsic8onZG96+jSF0Yogk6OVE6IwWd6EIOiO+oXCx4Ru3UOhI5X9OsRqqRQGSLp84ra41SJX2BRBR0ZhNzeg8GMsv9URrF0jNqJ3d0rFw8u5tx5lvs1w8mURdErOicyIJOdDFHQk4+vKY/0TpdiByHy2JWeBJ2YiKimAPEFSwijeKO+kEm2P0zavUbVsLOaEjUfYPW5lbAGEFHYu4SaldvUIMe6+n6r0PCjhABEQWdSBWsSAJOKT1tF+W+KonasRR2/vOF/Z2xsPPUeZmdyw+JOsgTdLGidHoLOpHFHE8hp6d4U2oDD7HHozmWhbDznydiGp3XjiX4QYKuN2YWcbEIu/qBQfdb6co3AN+ploJtMboMRiLuRZ3ZBF08iTkRBJwSwtnLQugpFXYAu5Fd0Yj1Ja1F2DVda0faV2otI1ghmqAzqiK1soiTQ3D+jXgGRkXtzNgcG9ei7odp5wA4NJ2DBB3bfJpNyMWiZ37Uijwewg7g3xyr5eu5+RqK9BlJtOfKMgorqqCLdyEXCaMEnhHrVZtR2MW1qJODkpUjgmEpdKwu5qwm5KLBSuTJRQRhB1BzrNmIV0FHQk4ZRgg8uVG7eBV2JOqioLbZ1eqCjkX+4knIRUMPkaeXsPOfJ2IaEnamIB4FnZ5izqh3gPfgIz0FnhHCzn++sL8LJOxI1EXAaEFnRTFHQi42vO6RHgMoAL797Aj+iCToeFeSPIWciGU8kk08xJ4egwn0FnZA7AEUIgg7EnUqiDdBR2LOOujRlEDCzpzEi6DjIebMXJ7D2c5sXWfO0TsSdr0hUReGaFG6aAMjtKJFzI3P3hryNys7tQg6M4k5tU7MjM6chB3Rk3gQdCzFnNXLbs/8MVmSi1P0Tu91qgGxhZ0uJXPZsmXIy8tDcnIySktLsWXLlohpv/vd78Jms/Xaxo4dG0gzderUXr9XVlYysVWtoNMazVIj6MZnbw1srNGyJmvyAadpBJ3WNQT9xwdvZoDVeofRiOXYzHKvYmEm/xYOqwu6puvcmsuzf41QOWuFWhGWeWfxPMKh9zrV0e6FkQNuuEfq1q5di9mzZ2P58uUoLS3FkiVLUFFRgT179iAzM7NX+rfffhsXLlwI/H327FmMGDECP/rRj0LSVVZW4pVXXgn87XQaJyL0FnQ8RFwwWsSc2eCxekHP84laCVDETjtm929WFnQshBzRm+D7osV38ojc6T0SX8SIHXdR9/zzz2P69OmYNm0aAGD58uV47733sGLFCsyZM6dX+vT09JC/16xZg9TU1F5Oz+l0Ijs7m6mtaqJ0WgQdLzGntuk1nsRcMHIGEWhBZJGnx5JcVhZ2ZvJvPYn0TFg/C70FnRYxZ9ZyaBQsBB5rcRfvwo5rCb5w4QJqampQXl5++YJ2O8rLy7Fp0yZZ53j55ZcxYcIE9OnTJ2T/xo0bkZmZiYKCAjzwwAM4e/ZsxHN0dnbC6/WGbD0hQac8L2ZqZpWDXk0rojXT6tGMYMWmWFH8GyDPxwVjRUGnpVkvXptVWaK1iZZls2w8N8VyLcVnzpxBd3c3srKyQvZnZWWhvr4+5vFbtmxBbW0t7rvvvpD9lZWVePXVV1FdXY1nn30Wn3zyCcaMGYPu7u6w51m0aBHcbndgy83NlZ0HHgMjlAg6JX3m1Niqtu+clcRcT/QWdyIIGhJ2yhHFvwHKfJxelYxegk6tGIjnPnK8EUHcxauwE7o0v/zyyxg2bBhGjx4dsn/ChAn4wQ9+gGHDhmHcuHFYv349tm7dio0bN4Y9z9y5c9Hc3BzYjh49GvK7mlUj1EbplAo6nlB0Ljp6OnwRxB0JO31h5d+A2D7Oj5X60GkVcwR/tAhnEnbq4Nqnrn///nA4HGhoaAjZ39DQELO/SFtbG9asWYMnn3wy5nXy8/PRv39/7Nu3D7fcckuv351OZ8SOxno2u8oVdGrEnJIoXbz2nVML7z53wQRfw4iKR4/+IUoW5xYZUfwbEN3H+bGaoFN8jIBCjqdPES2/avwoi/52evsbPfopR4PrU09KSkJxcTGqq6sD+3w+H6qrq1FWVhb12DfffBOdnZ2YNGlSzOscO3YMZ8+exYABAzTb7IeloBuRf4wEnQXQ+wtfhOhdT/SI2ImW50iYyb9ZRdCpic6JEpnTe/ojUadbUvM8tPodvVsIIuVPD3HJffTr7NmzMWXKFJSUlGD06NFYsmQJ2traAqPFJk+ejCuvvBKLFi0KOe7ll1/GuHHjcMUVV4Tsb21txRNPPIG77roL2dnZ2L9/Px599FEMHjwYFRUVimx7q7UfUvv23s+yHx1PMQeYU9DxWN9UT9GpZ+Qu+Dp6VUwiLGDt2evD2YFcL8EEkf2bH70EnRy0CjpF6QURciIh0kh8pX6U99JjrEfhR/KjvP0nd1E3fvx4nD59GvPnz0d9fT1GjhyJqqqqQOfiI0eOwG4PvZF79uzBP/7xD3z44Ye9zudwOLBz506sWrUKTU1NyMnJwa233oqFCxdyn6tOqSiKZ0HHQ7ipuR5PsWdlcSfCVCfu/WJViOEws3/Te6Rrz4pMknw413YEnRdb4UxMQ78+A2Gz9bbJTGJONBEXC6O7e/ivq0eTrN5TnQDGCDubJEkSlzMLjNfrhdvtxovbi5Ha1xHyG6tm13gRdHqLNxbwEnp6OnQ9B3BE/I3hVBTh6LrYgZo356G5uRkul4vJteIFv48r/tFTSEhM7vW70YKuoakOdSc+QOfFlsA+Z2JfFOZUIMtTeNlOkwg6s4m5WJjlPqrxQXLKlF7vR9rOU6iuXczUx9Har0GwanYVRdDxWLfVjCKuJ8F5YCnwjBhQwdv50sAJ62HEXHTBNDTVYcfht3rt77zYgh2H38JI/BDOf/0XRec0QoRYTcgFo3eXDz9qmmR5ROz0oqnQBdSyPafxnQ5MgBJxZAVBB1wSPuE2q8Ejb1YbTCHCVCcEG4yeXFiSfKg78UHU9F+d/hskn8xK3YBBECINOuCNUYMslDxTNYNnRBk4wQMSdd/AotnVKoIuXmEp8IwYKWt2SNhZj57P9FIfupYIqS9xob0JLacPxDw3iTl90Tv/Sn0o6+ibWYUdiTpG8BZ0ciFBxwaW4k4veDpdESbVJLRhdD86AOi82Crr3BfORxZ+NLWQsRgh7mSnVeCL9J6YGNCnPiBRB+1ROjmCTslyX+GQE6UjQcceFuLOKlE7aoY1L0b3o/PjTEyTlS4pJcxcU7DOR5IVEDVqp6Q51gh/w7sMx72o0zo4Qq6g0wIJOuNhJe70goQd4ceQQQQRnqEn9SoAtugH22xIuyIvZJdVPoyshhWidlZbujDuRV0kWIkkEnTWQqu4i7f1ZNVCwo4NzdewL2tqml39NLUfAxBjFi1JQuvZQ5ePoeic8Oh534zoZ2emZti4FnVvnxoVdj/LZlctkKATFxbiTi/0dEjUv87aaC1LSvvU0QeQudBT2Clpjo2FlZphaZ46lejR7BoLUQVdrHsjqt1q6MjvVD3XnZ4LP+s1UzrAcP66Oq/mcxD6Euu5X7w2CzgS+zxJKX2FmGCbUI6IK9/I8Umx5q9j7UMBPpH0uI7UhUOO4BChH51IwmhE/rGQLRip24czXxzHsb/9E2e+OA6p28c9wqk3ZmqOZYkIa2sS+qGl2RW4VLH2zchHUmr0yEmCy4PuGwcrtk8NJOj4IVpzrJr57HpihvJCkTqFiCDojEauKDvxyX7U/vbv6DjdFtiXnNEHRT//NpArjihlgV/YiR61020lCs6LVhP6orVs+itTm92Oq4vHYe/fV0VMm1k5DjY73/JphsrZCvCIboVDif+M5ptEWm1CLfSpHUSs6Jcogs6IKF2kSFwkTnyyH9vmVYUIOgDoON2GbfOq0L6N8dooghBv/eyofx0BxBgc0aMcpOcOw7XfntIrYpfg8iDn7qnoWzici41+SNDpi179FVn3s4uE6OWHInUM4d2HDtBf0KlpKpW6faj97d+jpmlcvR4po4Zy/yI3gnjtZ0dYEy3lMVLlmZ47DP2uvA4tpw/gwvkWXLjWjZSB+RShszCiRe0iRezkRusknw/njxxAV4sXCX1dupRfOZCo+watUToWgk6kfnRa+r2d3XmyV4SuJ92NzejccwjJhfmqryMy8STs9Bg0QYhLuOcrp1K02e1wZQ1G07V2XSoiEnTGYxZhFwvfR1/i4JfvoMt7+dgElxuZlXdyjzTHwnhZaQL06NgviqBT0sQaiY6z0QWdn+4ma49ujKemWIr4WRM1ZUNJ05ZZBwn1RPL54G3YhzOHvoC3YR8kHwnISIg4gKIn0YRe49Fd2Pv3VSGCDgC6vM048cZKtNTtVGYoYyhSh+iCSYR+dHoIOpbCNfmKPrLSOTwuZte0ImaL2EWConXxQzwKusaju3C45h1caL9cxpNS3bi6eBzSc4dxvbZZMWvETvL5cLjmnahpTlW9g7SCIsOaYknUaUSPfnQ84RGFvGL4ACRn9InaBOtId8NZkMf82j1R2wQKaIu0BZ9Diw1mEnZabe3eWQfH8ELVxxPRUTpCW86zDK4IRRN0erw3/qhNTy60N2Pv31fh2m9PIWEXARGFHRA9Stdy+kCIeA9Hl7cJ548cQGpe6LQ8WmdIkEvcizotUTqz96Pj1axsc9hR9PNvY9u8qohp0ifezvRLhseLEuucLESfHMwk7CIh50vYMbyQhB0H9Cin8Sjo5ERtDtf8Gf2uvE6Wr2NxX8zWb1A0YQdc9lXhBkz4VzqJRVdL5K5FWj/0Y6FLfHDZsmXIy8tDcnIySktLsWXLlohpV65cCZvNFrIlJyeHpJEkCfPnz8eAAQOQkpKC8vJy7N27l6nNIkyQy0vQseg3F40vD1yF07k3I+OhiXD0C30pHOluZDw0EaklRZqukXzAGbIZQU8bIm0sMEsfOxZTnHTvrFN9fSMQ2b915qkTdEoqQNEEnV7IidpcaG9Cy+kDIfv8U2/03FgQ6dwi33fR+tgBkX1VUkpfWccn9I3etYjnhxb3SN3atWsxe/ZsLF++HKWlpViyZAkqKiqwZ88eZGZmhj3G5XJhz549gb9tNlvI74sXL8YLL7yAVatWYdCgQXj88cdRUVGBr776qpeDjEbtoRzYU9TlS48oHQ94i9VgIZpaUoSUUUPRuecQupu8cHhccBbkqY7QGSXeRMEsETsWdnbvrAOuE39ktMj+TTSs0ofOj9yoTZO7FT4BRFXP+y9SVE/UiF1P/CuiRBPzCS4PUgbG9l0d+Z1I5PD9yv0uPv/885g+fTqmTZuGoUOHYvny5UhNTcWKFSsiHmOz2ZCdnR3YsrKyAr9JkoQlS5Zg3rx5uOOOOzB8+HC8+uqrOHHiBN555x0mNlux2ZV3dA4Ib7PNbkdyYT76lI1EcqHyeXyMjsaJhshf3HKIFdUxW9OrGf1bLHhU9lYTdAC7qI1RiBbJEzFiF46Ma/4l6u9KVkRRG0mPBtcneeHCBdTU1KC8vPzyBe12lJeXY9OmTRGPa21txdVXX43c3Fzccccd2L17d+C3gwcPor6+PuScbrcbpaWlEc/Z2dkJr9cbskXCis2ueuSJtc0k5CJjhgqShY3dtXtiJzIQUfwbIN/HGfFOmaG8qkHuOrZyojYiIILAEyl62JPGo7uw492ncXzXB2F/12tFlFhwfXpnzpxBd3d3yJcoAGRlZaG+vj7sMQUFBVixYgX+/Oc/47XXXoPP58ONN96IY8cuCRP/cUrOuWjRIrjd7sCWm5urOk9ma3bVIzrHUtCRmJOHGserZloRLlEbiywfJop/A9j5ONbP26qCDgCaCxLQ//Y7o6bRYx1bHhgp7vRaUkwJ/lHOkZpdrxx2K/J/Ps9wQQcIOPlwWVkZJk+ejJEjR+Lmm2/G22+/jYyMDPzhD39Qfc65c+eiubk5sB09ejRsOis1uxrV3KoWEnPKUSvslIo7tU5WbaVgtiZYJfDwb4B8H2dF9BZ0wYKnb+Fw5Nw9FQkuY9ax5Y0I0TteyM2TnFHOp/dviVkO9arfuA6U6N+/PxwOBxoaGkL2NzQ0IDs7W9Y5EhMTcf3112Pfvn0AEDiuoaEBAwYMCDnnyJEjw57D6XTC6Yx+Q63U7Gqm5lYScvoRPK2I3PUN/bDuyGyFCYlF8W+APB8XCzNG6fQUdJHy07dwONIKikLWAU2+Kg8dxw7Bu2u7UOuCasGffz3uuUgDJxSNci74FkvzVMH1riUlJaG4uBjV1dWBfT6fD9XV1SgrK5N1ju7ubuzatSvg4AYNGoTs7OyQc3q9XmzevFn2OdVglmZXEnTxhRbHZ3ZRZTRm8296vm9WiuzIiVTZ7Hak5g2Ga9godJ9vx8GlT+Poqv/Gybdfw9FV/40Dv11o+PJRrNArcifKwAm5o5wvnG8Rok8g9ycze/ZsvPjii1i1ahXq6urwwAMPoK2tDdOmTQMATJ48GXPnzg2kf/LJJ/Hhhx/iwIED2L59OyZNmoTDhw/jvvvuA3Bp5NisWbPw1FNP4d1338WuXbswefJk5OTkYNy4caps1KPZNRYshJJZBB01tbJFy/xLSoSdqnVAVc5bZ5YmWDP4NzmwrIys0o9OjXhpqduJE2+sFHZdUJboIe5EEElyRznLSadHvcd9nrrx48fj9OnTmD9/Purr6zFy5EhUVVUFOgIfOXIE9qCw9Llz5zB9+nTU19ejX79+KC4uxueff46hQ4cG0jz66KNoa2vDjBkz0NTUhJtuuglVVVVCz+HEO0pnJkFHsEfL3HBKmmL1ahYxC+TfQrGCoFObB8nnw6mqdVHTGL0uKA/0nD+TF9HyIGduuqRUD/pmXBrlbLSPtEmSJBl2dYPwer2XRogtX4DrrzsTNS2rKF00UadVLJGgIwBlFV24CB3PlQEi2RYtUtjV3Ynq2sVobm6GyyXmXF+iEuzj7CnJMd89VpWy2UWdFvvbD+3D0VX/HTNd7pQHe60LahVEfC5KiGR/pDV+/fRc4zeavcGrSfjOd+Do/U8w9XHW+VxQQVHeCV2uwzNKR4KO8KPV8RnRx84q05uYGRJ0bJoSo633qSadGeHVJGt0/7r03GG49ttTes1LmJTq6SXojIZ786uZEb0vHQk6oidym0IijT6V2xSrtInBCk00RGTMKuhY2i135QhRV5hgiRXf9/TcYeh35XWXRsOeb0FSSl/0zQg/qjmaf0w+4OS69mtcR+r0gFezq1kEHWFdmEV4KFpnGFareJXAWoimDMzvNV9dT8y0woRWWEftjI7WAZdGObuyBqN/3vVwZQ0Wsm+keBYJgh5ROpGhaUvMi1xHGklM0VQn1oL3O2jGKB0Pm212OzIrrbnChBbMOLDKjDb7oeZXjpg5SseKjvzOXpUKi9AziUW+8GiGtWKTTLxjtsqPt73+FSZOVa0LmdYkweVBZuU4068woRZW777RI0uVYFQTLIm6MIgcpTNjsyuPwhvunEYLvZ5Oy0jnw8KJKl11QgtWWGHCbOgtsMN94MmFha16vY/hVpiwwooSWjH6o05p+TPaXrXEdynjCI8Rr2YUdHrSkd8ZshmNZ68vsBmBnEqMhWhTkj+zfGUTsZH7LIPfR6PeS73LXfAKE6l5Yva9MgIW/ey0+FOldYMZ/ZX5LOaMqCNeSdApR0SBZzbkRs+YRFJowARznIf4RK+VCDqtaC1bZqyYrY7Rz0SvOsEIn0+lnQBgPUHXEz3EnRxHpfdLziJaR82ihBpE+JgyWjwQkdG0bjUDPyq3fPIqQ7y6C1GJD0LU1SPMNDBCdHiLO7nCzoxRu1jIzRNVtMajpfzJeX6s3jHedhLGYvQzEuHDgzVU6gWHml35IMLLLMK8S4E0gkTrqAmWYIHRYoGQj9pnxcp/yqkLjI4qKoFKPmN4rvHKAxFt0gteUTslDsBqETur5YcIxQxROhJ05sPoZ8Zb2OmJOazUARGnMaFmV30QQdjxFkMsHBLLaJ1ZHKQV4VnWYr1LVpkImWCPmmfHsiyL0HrDAnoDBIWaXfXFaGEHGB/lYtX0qXm0IjXBCkms8mx0pUiCzvyI/gxZ28djdLrYd1AnRB0gQeiL0ZUSwFfYiRatI8yD3pWt0vdAdDFAyMfI0aa86gA9P9jpTRAQitIZB+uX2ugmBaWIEq0jzIWRH0Qk6KyHkf2SY5Vl0cub2NYRXCBBFx2rR+y0wipaJ7pzJC4hcrMrlSGCCCXu3wjRml6tOjhC8vnQUXcAbZt2oKPuACSfuKKFNUYP2Q9Gz0pQ0xxj1K+OC0Z+LMgdJEHzHRKAsc/XzNG6BKMNIPTFiChd+7ZaNL62Ht3nLkd4HP3cSJ90O1JLinS3xwjMtDh003XumNE4z+5mEl5xgKhROpErVYIdcv2mZ69PVplIPuAUoiWGJ7q8GcuWLUNeXh6Sk5NRWlqKLVu2REz74osv4tvf/jb69euHfv36oby8vFf6qVOnwmazhWyVlZW8s8EdK0bp2rfV4vTS1SGCDgC6zzXj9NLVaN9Wa5Bl5sDs0bp4gPwbQfDDKH/FOlqn10c997u1du1azJ49GwsWLMD27dsxYsQIVFRU4NSpU2HTb9y4Effccw8+/vhjbNq0Cbm5ubj11ltx/PjxkHSVlZU4efJkYPuf//kfxbb9e+Z2VXnqiVlGvepti+TzofG19VHTNK5eHzdNsSI1w7JATt+6WLabXWCK7N94Q1E6Qi/omcuH+516/vnnMX36dEybNg1Dhw7F8uXLkZqaihUrVoRNv3r1ajz44IMYOXIkhgwZgpdeegk+nw/V1dUh6ZxOJ7KzswNbv379eGeFK1aM0nXuOdQrQteT7sZmdO45pI9BMuE9Qaoa9BZ2IjStNhW6jDYhJlb2bywrUlb96ahyJ/TEjE21XN+QCxcuoKamBuXl5ZcvaLejvLwcmzZtknWO9vZ2XLx4Eenp6SH7N27ciMzMTBQUFOCBBx7A2bNnI56js7MTXq83ZIs3jIgYdjfJu89y07Ek+YAz4sYTUSolUewwM6L4N0B/H2dEZUdlNr4R8fmLaBNXi86cOYPu7m5kZWWF7M/KykJ9fb2sczz22GPIyckJcZyVlZV49dVXUV1djWeffRaffPIJxowZg+7u7rDnWLRoEdxud2DLzc1VnykFyBVSVozSAYDDIy/SIjedGowQbrwQsRmWRROsWRHFvwHyfJyS5yBiZUUQRpRLs0XrhB79+swzz2DNmjXYuHEjkpOTA/snTJgQ+P+wYcMwfPhwXHPNNdi4cSNuueWWXueZO3cuZs+eHfjb6/UyE3bR+tPFO86CPDj6uaM2wTrS3XAW5DG9ruiiTctIWLmjvMyEmUYGs4SVfwP4+riemC1K19Ne0f2DVQl+DlqeQSR/YdQIWNH8F9faoX///nA4HGhoaAjZ39DQgOzs7KjHPvfcc3jmmWfw4YcfYvjw4VHT5ufno3///ti3b1/Y351OJ1wuV8gWTxg1WMNmtyN90u1R06RPvB02u/ZiaPYonBHEcoBy+9XF69Jhovg3gK2PE+2jgaWgI4yh53PoyO801bMxk61c396kpCQUFxeHdAL2dwouKyuLeNzixYuxcOFCVFVVoaSkJOZ1jh07hrNnz2LAgAFM7NYTqza9+kktKULGQxPh6BcqEBzpbmQ8NJHJPHVmFHJaKiqRvgrlYkabY0H+jT8k6KyN2mdk1g8PPfwg9+bX2bNnY8qUKSgpKcHo0aOxZMkStLW1Ydq0aQCAyZMn48orr8SiRYsAAM8++yzmz5+P119/HXl5eYG+KWlpaUhLS0NrayueeOIJ3HXXXcjOzsb+/fvx6KOPYvDgwaioqOCdHUIFqSVFSBk19NJo2CYvHB4XnAV5miN0ZhRzhLWIN/9mFqFkFjuJS89KjS8XrdlTFLiLuvHjx+P06dOYP38+6uvrMXLkSFRVVQU6Fx85cgT2oMr997//PS5cuIAf/vCHIedZsGABfvWrX8HhcGDnzp1YtWoVmpqakJOTg1tvvRULFy6E06lvJW+G+elEscNmtyO5MJ/Z+awg6Khvnfmxmn/jUabUvqtqbSFBZz5YCDve/lCtjXpjkyRJMtoIvfF6vXC73XhxezFS+zpUn0erqNOj6VUUUccSM7xYctG0PiojJxbLBrl95uT0wYtkczgbui52oObNeWhubo67frBa8fu4wXN+DYfz0iAMOWUtWplSK5aUvq9aPljk2Ggl/2EW5JYdtc9GaZnhVZaVvmPdnR3Y98wvmfo4+tQnTIXVHHK89a2LBEUdCT8UoSOUopf/MEMZI09qYawWpbOaoCMIkSBhTRiNXqLJynUJvcUGYfVRr4Q+iBSti9epTeIB0SMUotsX7ygRUWZ+liJ8GBlvgUkxwyAJK2HlLyujHYGe1xdJhBLWwMwigDAfopc3EnUWhYSlMiSfD+2H9sG7azvaD+2D5CPx4UfuJMSEeTH6w4IgghFdOImM0MuEEfGD5PMxn8dOLi11O3Gqah26vJebDxNcbmRW3om+hdFn+xcBmt6E4InIFazItsVC8vlw/sgBdLV4kdDXhZSB+br5PIIfRs+fR6LOAKg/XSjt22rR+Nr6kDViHf3cSJ90e2DFCV5zBLXU7cSJN1b22t/lbcaJN1Yi5+6pphB2BEGYB7N/SOqByPPCiWwbfRYQhtK+rRanl64OEXQA0H2uGaeXrkb7ttrAPtbrBUo+H05VrYua5lTVO7o0xVol0kaDJcyHWcueWaN0/g/JYEEHXP6QbKnbaZBlhBUw59tMWALJ50Pja+ujpmlcvb6XqGLlzM8fOdDLsfaky9uE80cOMLkeT8w0AMFMthIES0T6kDQDZhXuRn4okahjjAgDFESwQQ6dew71itD1pLuxGZ17DvXazyJq19XiZZrOzIgQrRHBBiIUUStVUe2KhZU+JAkxIS9KGEZ3kzyxFC2dFnGX0Ffesixy0xGE2SAhrS/0IakcUQW8qHbRG62CaHPUEfJxeOSJJTnp1LxgKQPzkeCKPl1HgsuDlIH5is9tNWhaE4LQDn1Ixg9GfTCRqNMZGvl6GWdBHhz9oosFR7obzoI8WedTKuxsdjsyK++Mmiazcpxu0wxodQLUV42IB0SNkITD35Lg34z8kOxpi5nuo1XQQ+iRqCMMw2a3I33S7VHTpE+8nauo6ls4HDl3T+3laBNcHprOhLA0sSoYqvTVE0k0dQ6+aMiHZKRnaRZxZwYbRYHmqSMMJbWkCBkPTew9T126G+kTL89TJxc18wf1LRyOtIIimgiUEZ7dzdRcKyBGT4oaL8QSIIljC5CDqWHmqfMgs3Ic8w9JOYJI5HnXzIwR7xyJOsJwUkuKkDJqqGErSgCXooapeYN1u168Q6tgEGoQPWIj177EsQXIL3hcqA9JnsJO9OemFhHFMIk6i2GW6Ux6YrPbkVxIAxIIQg9IULNHTZ9e3h+SSm0SUaT4Edk2Oej1ztGbTRAEESeQmOODmkiUqNErUe0yK3q/c7pcbdmyZcjLy0NycjJKS0uxZcuWqOnffPNNDBkyBMnJyRg2bBjef//9kN8lScL8+fMxYMAApKSkoLy8HHv37uWZBYIgiLCYzb/JqWSsXLGzzpuo90pUu+IRPYUd9yutXbsWs2fPxoIFC7B9+3aMGDECFRUVOHXqVNj0n3/+Oe655x7ce++9+OKLLzBu3DiMGzcOtbWX1wBdvHgxXnjhBSxfvhybN29Gnz59UFFRgY6ODt7ZIQiCCED+TT+sKFJEzZOodrHCyvnjLuqef/55TJ8+HdOmTcPQoUOxfPlypKamYsWKFWHT//a3v0VlZSUeeeQRFBYWYuHChRg1ahR+97vfAbj0FbtkyRLMmzcPd9xxB4YPH45XX30VJ06cwDvvvMM7OwRBEAHIv8U3LMQBD4FhZdFCRIerqLtw4QJqampQXl5++YJ2O8rLy7Fp06awx2zatCkkPQBUVFQE0h88eBD19fUhadxuN0pLSyOes7OzE16vN2QjrImZO9KygKas0A9R/BtAPs7ssBRhrM4l2nlYn4sVotnEVdSdOXMG3d3dyMrKCtmflZWF+vr6sMfU19dHTe//V8k5Fy1aBLfbHdhyc3NV5YcgCMKPKP4NENvHiVbpsULPvnlyVoIwy0TCBF/iYijU3Llz0dzcHNiOHj1qtEkEJ+LdqdHoxviEfJwyRPUT4ZbyCmerXkt+iXqfiMhwrQH69+8Ph8OBhoaGkP0NDQ3Izs4Oe0x2dnbU9P5/lZzT6XTC5XKFbARBEFoQxb8B1vdxJC4IQh5cRV1SUhKKi4tRXV0d2Ofz+VBdXY2ysrKwx5SVlYWkB4ANGzYE0g8aNAjZ2dkhabxeLzZv3hzxnARBEKwh/xa/kMiUB90n/eG+osTs2bMxZcoUlJSUYPTo0ViyZAna2towbdo0AMDkyZNx5ZVXYtGiRQCAn//857j55pvxf/7P/8HYsWOxZs0abNu2DX/84x8BADabDbNmzcJTTz2Fa6+9FoMGDcLjjz+OnJwcjBs3jnd2CIIgApB/My9mX6GAUIfVhSZ3UTd+/HicPn0a8+fPR319PUaOHImqqqpAR+AjR47AHrTe3Y033ojXX38d8+bNwy9/+Utce+21eOedd1BUdHlh90cffRRtbW2YMWMGmpqacNNNN6GqqgrJycm8s0MQBBGA/BthddSKX579/EiMR8YmSZJktBF64/V64Xa78eL2YqT2dSg+fm39DRF/i7X26oj8Y4qvpxSzrv/KCjO/8FqnJNEyUCLWtT27m+XbcZ07dpowtgbb0HWxAzVvzkNzc7Pl+ojxxu/jBs/5NRxO5WKQR4Ws5b3kGV0RSbCIimj3SLSypNae7s4O7Hvml0x9HA2VIwhCd2iUrrjEm2Ah2ENlyDjIsxIEwRQ5UTqCEAWlAoQEi3mJh2dHoo6wFGZueiUIgjA78SCcRIZEHWEZzC7ojOxPRxC8EbmyF9k2glAC1QI6E++DGHhhdkEnOkoGSRAEEZ+QODYeEnUqGJ+91WgTiCBI0BGE/ozIPxbYrAAJksjIuTei3z8l9pm5bJOoY4wIhUAEG/Qg+YCTBB0jtDb9WsUGgi9WqvgJa2L2+pNEHWE6SMz1hvrTEXrSs+Ize0UYDAk75dA9EweqCQjTYGUxZ5UoldpJhwnzQ8IuPjHDvZJrY7gyzLNcd+axv3fkXQ2ABksQBBGvmEEEAOHtNIvtekH3QzxI1BGmwKoROoIQDS3vmpWidQCJlmCC70VHfqdp7o2WKJ0ZIVFnUaxSQInY8G7OpOlMCNaYRRAAlwWMmWzmBd0H8SFRpxKa1kRfrOxIjO5Pp+f1qT8dQRBa4NFqY0QQhFedRh6WA3IKCPWrI1ggkkiiNV/Fg0dH7FhQKwEhClYOBkRCnBqBIGIQjy8oQVgVep8JEbDaRwiJOgtjtcIKWK8i0NL0KVKUTitGN0ETbLGi7yHMhch1BU/brFMrmBBqglWHyC+r2YglplgOkrCSCDUTIr8vcm2j0e8ED6z48UFeVgM0WMI4RK6o5GKlKB31pyN6YsUKkzAHVqgf1MK1ZmhsbMTEiRPhcrng8Xhw7733orW1NWr6hx56CAUFBUhJScHAgQPxs5/9DM3NodECm83Wa1uzZg3PrChGFIcmih08iOcXlzCeePZvLKH3mDACVnWj0igy7/KewPPkEydOxMmTJ7FhwwZcvHgR06ZNw4wZM/D666+HTX/ixAmcOHECzz33HIYOHYrDhw/j/vvvx4kTJ/DWW2+FpH3llVdQWVkZ+Nvj8fDMCje+PHCVpYUXb/wvSDw1z4gWpYtXzOTfOvI7DXlHRuQfk9XNxCj7COsR7x8J3ERdXV0dqqqqsHXrVpSUlAAAli5dittuuw3PPfcccnJyeh1TVFSE//f//l/g72uuuQZPP/00Jk2ahK6uLiQkXDbX4/EgOzubl/mWQq5jNTNmE3ciDAxg1Z8uHtd7Jf9GhEPy+dC55xC6m7xweFxwFuTBZrdW2TeSWP493laPCAe30rZp0yZ4PJ6AwwOA8vJy2O12bN68WfZ5mpub4XK5QhweAPz0pz9F//79MXr0aKxYsQKSJEU8R2dnJ7xeb8jGCupXJxZWn/3dauIIEEPgKkUk/waw93EsP47kVqBmf2fbt9Xi+OzFaHjmRZxZvhYNz7yI47MXo31brdGmERpgGRDRo4xzqyHq6+uRmZkZsi8hIQHp6emor6+XdY4zZ85g4cKFmDFjRsj+J598Em+88QY2bNiAu+66Cw8++CCWLl0a8TyLFi2C2+0ObLm5ucozpAK5zszqUTSjCBZ4Iok9M4oYIhSR/Bsgz8eJUPZjYQYbw9G+rRanl65G97nQ6Hb3uWacXrqahJ0OUJTuEopF3Zw5c8J25A3evv76a82Geb1ejB07FkOHDsWvfvWrkN8ef/xx/Ou//iuuv/56PPbYY3j00Ufxm9/8JuK55s6di+bm5sB29OhRzfaZDasXZCVEEnuiiL5IsIzSsRKWVmt6NaN/A8T3cSz8D4vo4Yj8Y8x9oeTzofG19VHTNK5eD8kXnx9zPO55T0T223qjuE/dww8/jKlTp0ZNk5+fj+zsbJw6dSpkf1dXFxobG2P2FWlpaUFlZSX69u2LdevWITExMWr60tJSLFy4EJ2dnXA6e7/4Tqcz7H6RoAET4sCz07ZaMaW3MGI5P51qG+rYdZOQixn9G6C/j1Pjr0QaNOG3nUUrSeeeQ70idD3pbmxG555DSC7M13w9s9CzfIjQt9vIOlYv4alY1GVkZCAjIyNmurKyMjQ1NaGmpgbFxcUAgI8++gg+nw+lpaURj/N6vaioqIDT6cS7776L5OTkmNfasWMH+vXrp9ipvX1qFCb1/VLRMeEYn70Va+tvCPubCAXZj0i2xCNmEXRyYRGlE60p2kr+LRxmGWXKw85wFToLcdfdJO/jQ246K6CneDKy2VXEd4lbbVFYWIjKykpMnz4dW7ZswWeffYaZM2diwoQJgZFhx48fx5AhQ7BlyxYAlxzerbfeira2Nrz88svwer2or69HfX09uru7AQB/+ctf8NJLL6G2thb79u3D73//e/z617/GQw89xCsrukGCi9AD0YSUGSH/pg4lFaueTWpaKnyHx8U0nZnRo6k1GLM0u+ppJ9d56lavXo2ZM2filltugd1ux1133YUXXngh8PvFixexZ88etLe3AwC2b98eGDk2ePDgkHMdPHgQeXl5SExMxLJly/CLX/wCkiRh8ODBeP755zF9+nSeWbEMFK0zBjNF6eQ0vdIKEub1b3KjYMkHnIZXmiwjdrGajNVG7ZwFeXD0c0dtgnWku+EsyFN0XrPBW8z1LAdKyqZa23jXlUV5J8C696tNijVW3oJ4vV643W6MqZqOxD5JzKYlidQECygrHLxfDhJ1sWEdVlcj6ngJOhbz07EaIBHJFs/uZnR1d6K6dnFg2g9CPn4fl7t8AewpvZt45ZbvaBWnFj+l1Af57dUqMnnNSOAf/RqJjIcmIrWkSNE5zYKScqCl7gkus0rLAS9RJ+c9imbr0KwD+Gvli0x9nJiddQiu0ICM6MSzoNMTkWyJN1hE4LRU0Ep9kN4j05U2I6aWFCHjoYlw9Av92HGku0nQfYPWYILaGQpEre942cW1+ZVQR7hmAqnbh7M7T6LjbBuSr+iDK4YPgM1Bmpw1VhZ0ctAzSkfwpyjvBL5qiJ8Rl6xR0l0ltaQIKaOGxsWKEqIKpZ7wtFNrlI4XJOpwqdmURRMsr1GwJz7Zj9rf/h0dp9sC+5Iz+qDo599Gzs3XqDon9a3rjRZBJ/l8OH/kALpavEjo60LKwHxVzpynGKLIGBGM0SNhjfBBaqdi8R8bC5vdbvlpS8wi6LRi1vqRRJ2g+J3PiU/2Y9u8ql6/d5xuw7Z5VSh5qpKEHQO0VG4tdTtxqmoduryXo1wJLjcGjRgH5A6TfR6jo1uiROlEmCOPuATvwRJm8kEs57YzI3r2oWSB0eKTV3/UWFgvLqySaIMcjGLHvhzU/vbvUdPUvvAPSN0UgdGCVkF34o2VIYIOALq8zdj791VoPLpL1nl4CzoWUTqWI14paqgf0SoQrYLNjAJHq81GiwW90XuaEhaYzV6WkKhjTLRmXKUFrXPPoZAm13B0nGrF2Z0nFZ1Xi01WIvmAU3OT66mqdVHTHK75c9TlgZqutRseoWOJlfJC6IMZfZAZhY5SWOXRjMI/FrHqDaOidACJOqGROwN5x9nowi8WVndO4WDRl+j8kQO9InQ9udDehJbTB8L+ppcAkhMVi9Xkqde8dNT0ygee0ToW6O2DWAkNv/Cxkg81e37iXYiSqAuCVRMsq2id3BnIk6/oI/uc8Y7W6FwwXS3yRPeF8y299lkxoiU3T9T0ai5ivS8sBZKesK64rSCGWNuvtzgS4f4bGaUDaKCE0MiZqTw5Mw1XDB+g+Vpm6rCsBh6j/BL6yhPdSSl9A//XW8yZKUpH8CXaO270SFgrYaYBFTxFRjwKOhGwXrhAIyINmLDZ7UifdHvUNEU/u4nZfHVWeyn8UTlelVXKwHwkuKILnqRUD/pm5Ju275xcQccib9T0aizUDMsWUZtmRbVLCyzzomUVCaOjdACJOm6waoKNNFN5cmaapulMWNgmIryFXDA2ux2ZlXdGTdP/9nFoLjAmIC5iM6eINsUTvN5vluLISsLOT7CQ0jt/RlzbDFFK1ojwUQRQ82tYWE1GzIqeM5V/a2gnrSgBPk2qSulbOBw5d08NM0+dB5mV49C3cLiB1sUmWnRMzygdIQaRmmF5z1fXE727g6iZlFgL4a7FIr8ifJSbudmVl+16PhcSdRxhucJE8Ezl/TkXEJYOVQThxZu+hcORVlDEZEUJVmiNiOkt6KjpVT9ivd9q+9exFkZW7+fbExEEmVbMLOi0IEqUDqDm14iI1LeuJ3o1FxDysdntSM0bDNewUUjNG2wKQae3kKKmV3Ewy/utp53xJCB5EA+CLtzHTixBp7edJOo4w3Iy4mDMIuxE+oIh5EFROkKk95aEnfhYQdCpyYNogg4gURcVkaN1AAk7ojdao3S8BB1F6cQj1rvd872V0yRrBVFkhTzoiRUEnZWIa1FXeyhHl+vwitYBJOyIy+jVj44lFKUTG1HeWyuOiLUCVhF0VonSAXEu6uSgR7SOhB2hJ+GElBJBp1eUrrt2j6rjCPnIea+VvrdWGEEIXMoHibvIWEXQqYFFXTY+eyv+PXM7A2tCiXtRp1fB5D1FCgm7+EZLlI6noCOMRU6lwUPY8cKIip2EXW+sJOiU5KUjv1PWu2CkAOXqoRsbGzFx4kS4XC54PB7ce++9aG1tjXrMd7/7XdhstpDt/vvvD0lz5MgRjB07FqmpqcjMzMQjjzyCrq4u1XbGeqhmiNYBJOziFSWCrmeUzsglwKI1vXbvrNPREnWYxb+x+qBU8t7qsVKDnpCwu4yVBB0P5NjLM8jDVdRNnDgRu3fvxoYNG7B+/Xp8+umnmDFjRszjpk+fjpMnTwa2xYsXB37r7u7G2LFjceHCBXz++edYtWoVVq5cifnz5/PMChNhp8eExmYSdiTutKNXhA5QF6Wz8gAJM/m3WL7HbBUnQP3s9MaI5mjez9iIZ8pbB3ATdXV1daiqqsJLL72E0tJS3HTTTVi6dCnWrFmDEydORD02NTUV2dnZgc3lurxw+ocffoivvvoKr732GkaOHIkxY8Zg4cKFWLZsGS5cuKDaXhFeWFYF2CzCDiBxpyfBkTE9BJ1cW8yI2fybEZjJD8klXvvZGZFnK35o6BHY4SbqNm3aBI/Hg5KSksC+8vJy2O12bN68Oeqxq1evRv/+/VFUVIS5c+eivb095LzDhg1DVlZWYF9FRQW8Xi92794d9nydnZ3wer0hWzj0aIbV64tZD+fD8qUjYacctVEwvQSd6gESJmh6Fcm/AfJ8nBWjdQD1s+OJUSJWj2fKOl8iCDqAo6irr69HZmZmyL6EhASkp6ejvr4+4nE//vGP8dprr+Hjjz/G3Llz8ac//QmTJk0KOW+wwwMQ+DvSeRctWgS32x3YcnNz1WaLCXquK2s2YUfiTh5qBFPTdW7DI3SA+aN0gFj+DZDv4/QWdnoJAqOEnVXFnZFizqwfF6Kg2GPPmTOnV0ffntvXX3+t2qAZM2agoqICw4YNw8SJE/Hqq69i3bp12L9/v+pzzp07F83NzYHt6NGjEdNaZdBEMHoIOx7ijgReeMzQT42FjY6iAgaWKMOM/g1Q5uP0/KgE9BV2JO60Y1RezLxyiChROgBIUHrAww8/jKlTp0ZNk5+fj+zsbJw6dSpkf1dXFxobG5GdnS37eqWlpQCAffv24ZprrkF2dja2bNkSkqahoQEAIp7X6XTC6WS3sPza+hs0P6Tx2Vt1XbGC9WLb4eCxALdf2KlZYNyK6CnojIjSGd30akb/Bij3cdH8D4/3WE+Mst9/TTNGmox+3iTo2KFY1GVkZCAjIyNmurKyMjQ1NaGmpgbFxcUAgI8++gg+ny/gyOSwY8cOAMCAAQMC53366adx6tSpQPPHhg0b4HK5MHToUEV5Kco7ga8a8nvt10MAxYKHY9LD6fByqMFRu3gVeGYRdEyidMML0dWtf6TWSv5NC6zfY719qpHCNPi6RtcjsYgnMWcEegs6gGOfusLCQlRWVmL69OnYsmULPvvsM8ycORMTJkxATs6l5bmOHz+OIUOGBL5M9+/fj4ULF6KmpgaHDh3Cu+++i8mTJ+M73/kOhg8fDgC49dZbMXToUPzkJz/Bl19+iQ8++ADz5s3DT3/6U6bROCsNmuiJ2ZpjexLcPGt0E21PW8JtLDCLoIuG3L50juGFXK7PErP7N8C6Ayf8iNA/S8SmWb9NRttlhQEu0fJghKADOM9Tt3r1agwZMgS33HILbrvtNtx000344x//GPj94sWL2LNnT2D0V1JSEv72t7/h1ltvxZAhQ/Dwww/jrrvuwl/+8pfAMQ6HA+vXr4fD4UBZWRkmTZqEyZMn48knn1RlY7SHYnVhZ6ZBFNFQIqYknw/th/bBu2s72g/tg+SLLZZ4C7ZYmEnQabG1e2edKQSdHzP4t1hYadBWJIwWdkCokDKqaVgEIQeIIbZZwCIPb58axcCSUGySJEnMzyo4Xq8XbrcbY6qmI7FPUtSCrsfs0LHEodnnexLBkQBA+7ZaNL62Ht3nLkeMHP3cSJ90O1JLirhdV0tzsVUEnZoRr13dnaiuXYzm5uaQudyI2Ph93Ivbi5Ha1xEzfTQfpHcfJJ6I4ot6wvKeiJpHwDrPnlU/utf2j8BfK19k6uMU96mzItH6X+jRFyTWoAk9+ofwzKf/vEY6m/ZttTi9dHWv/d3nmnF66WpkPDSRi7CLF0EXDStMYRLPmH3gRDCi5kVEm1hidGROz/trVLOrH1qdWwZWboYNhnd43qgXW/L50Pja+qhpGlevl9UUq4R4EnRmmGaFiIye/sdoAWOV5j8zIMK9FrUfHa/ZL0jUfUOsgmfF+esiwbP/hREveOeeQyFNruHobmxG555DzK4ZT4IuGrGidJLkQ2PrIZw8V4vG1kOQJBKHLFHSZyeehB0ghuCwMla8tyIOjOgJiTqGaBV2cgqFFdY61DsP3U3hl4VTmy4W8Sbo1Nrc0FSHT+pewNb9f8LOI+uwdf+f8EndC2hoEn+ZMKsSDwMnekLiji0i3U89+9EpgWcQiERdEFqjdYA+ws4IRBo9pRSHR14HVLnpokGCLui3KFG6hqY67Dj8FjovtoTs77zYgh2H3yJhxxClPimaDxKlsuaBSGLEjIh2/0QcGKEHJOoUIoKwM/rFMZvAcxbkwdEv+pqnjnQ3nAV5mq4Tb4IuGtEEnST5UHfig6jHf33iQ2qKNRC9hJ1WHzI+e2vIxgLRxInI+O8Vq/vF6nmKLOh4d9UiUdcDOYVTBDEjitNRM/+S3vfPZrcjfdLtUdOkT7wdNru61yH5gNMUgq7pWjtTQafW7nNtR3pF6HrScdGLpvbI65cSylBTkZhF2AXDQ9yJ4mtFgpeQEw0zRej80JQmKok1BYjW9WHlrA0r4vB80ezxk1pShIyHJvaepy7djfSJ6uepM4OYA9hH57TMSdd5sVXWNc5lSMBBRWYROsLS/7CeUsnve1lFRUSYlsloWItbXoJIr2ekxn49BlTGtaj798zt+Evbv/Taz8pZxauwE5XUkiKkjBp6aTRskxcOjwvOgjxVETqt68+aWdBFQ86cdM7ENFnnSkyRl46Qhxp/pOccmmqEXaw88RJ3QPwIPKPEnJpnplezq4gROj/U/BoBVs2wVhwRa2ZsdjuSC/PRp2wkkgvzFQs6rU2tgPkFnVb7+/UZCGdi36hpklI96Ns/T9N1CDaI3sdXDjya96zaPBucL9bR03gWdHpE6QASdZpfdBJ28QErMWfW/nN+WCwFZrPZUZhTETXN1cV3ACr7OBKRUeuL9BJ2aipmJXni1X+LlxDSC562K73fVhR0ekJeMwpyCzgJO3ZIPh866g6gbdMOdNQdYL7Kg1JYiDnA/NE5gO3arlmeQoy8+oe9InZJqR5c++0pSM8dpspGgh9WEXZ+eHbO7ynyRPLNetmm1+AHMwg6vaJ0QJz3qfMjp+9aLOT0B9Hax04OZu5j176ttvdAhn5upE9SP5BBLSyEHGANMceLLE8hMt0F34yGbYUzMQ22G4apHoVMyEOLH9Krjx2P/nWRYN3vLhKR8sPLXxshJLXWb0qfgRkEXTRqD+UwP6dNkiSJ+VkFx+v1wu1248XtxUjt6wjsj1Sg5BYcyefDVS1b0XG2DclX9MEVwwfA5ghfQfFulzebsGvfVovTS1dH/D3joYnchR0rIefHSoKOZZQuEk3Xhc4l2HWxAzVvzkNzczNcLu0TQ8cTfh83pmo6Evsk9fqdd+XLyv+oESYsKmA9IytWwIh7bhZBFy1fX+zuj6P3P8HUx1GkTgZyvj79UaYjQVGm5Iw+KPr5t5Fz8zW90mv5qpRT+LV+MYcTOB35narPFw3J50Pja+ujpmlcvR4po4Yyj+KwFnKAtcQcYIygI9hQeygH1193ptd+3iPzzRixC0av6J2ZYRW9MrIPnZHz0F3KQwfz81I7RxBqH6A/ytRz0fiO023YNq8KJz7ZH/Y4Xp2W/WgJv4cTcP7+ZcEbCzr3HOp173rS3diMzj2HmFyPtf1+9BwIAZCgI7RhlpVv9OpjFw6RJ8Y1Atb3w+qCTmvrnxpI1PUg0oOMVADkRJlqX/gHpO7wlaMewo5n3woWQq+7ycs0XU94CNFgjBBzRgs6VpCg4w/PCkR0Yccy0havAo9XvuNV0PGGRJ0CwhUEOVGmjlOtOLvzZMTfeQs7QJ1zVdvcGk7oRRNTDo+8vgSx0im9rlasKuaA2IKOVZQuGs3XkHviDYuKR46wYyHu1FboPCpXHmvOigTvvFld0EWDd393rl6zsbEREydOhMvlgsfjwb333ovW1shLBB06dAg2my3s9uabbwbShft9zZo1iu37Ydq5sPuVPFS50aOOs21Rf1f7VamHsGPVly6S6HInFiDBFT1ik+DywJ1YEPEcPMVbT6ws5uRAza6XEN2/BROtItFD2AFsonZK1pgOhnfUxMwir6ftPO1XW8+ZTdAZ2ReTa00xceJE7N69Gxs2bMD69evx6aefYsaMGRHT5+bm4uTJkyHbE088gbS0NIwZMyYk7SuvvBKSbty4cUxtl9sMKzfKlHxFH1npRBR2AL9BEsClVR4yK++MmiazcpzhU13Ei5gToR+dSCI2Embzb7wjBHrOpSlCc2w09BRKSjDSLrX33kqCTo9ZKbiNfq2rq0NVVRW2bt2KkpISAMDSpUtx22234bnnnkNOTu/5WRwOB7Kzs0P2rVu3DnfffTfS0kLXgfR4PL3SqmFC2jmsae2n6JjgkV3Ogjw4+rmjNsE60t041vcGHD9gl+XUeM9np3Zx6o78Tm4Rsb6Fw5Fz91ScqlqHLu/le5ng8iCzchz6Fg7ncl056CnkAGMFDQk6eZjFv8mFlc+RM2rUyJGxgD7zhYZDzjX1mKTeCIwUdHLKiNkmSY4Gt3nqVqxYgYcffhjnzl1u4uzq6kJycjLefPNN3Hln9MgMANTU1KCkpASfffYZbrzxxstG22zIyclBZ2cn8vPzcf/992PatGmw2Wxhz9PZ2YnOzsuRJq/Xi9zcXJz7Zz5c38xTF0nYyVHdSudYU+KIRF1ehZe4k3w+nD9yAF0tXiT0dSFloPL1WVlhtZGssRBN0HV3dmDfM78Ucp46kfwbENnH5S5fAHtKckhavSZZleOP9Kq0IyGqCLIKokfnAP3KfLg8+c53MJ+njltNUl9fj8zMzJB9CQkJSE9PR319vaxzvPzyyygsLAxxeADw5JNP4o033sCGDRtw11134cEHH8TSpUsjnmfRokVwu92BLTc3V3Y+5Dzw1JIiZDw0EY5+oRWWI90ddtJcJQVW6bqGShGtOdZmtyM1bzBcw0YhNW+wIYLOCmu0KkU0QSc6Ivk3QJmP0ytioGc/O7XQPHR80NLUHS+CjheKm1/nzJmDZ599Nmqauro61Qb5OX/+PF5//XU8/vjjvX4L3nf99dejra0Nv/nNb/Czn/0s7Lnmzp2L2bNnB/72f8UGE60ZNtJEm8HNCKklRUgZNfTSaNgmLxweF5wFeRFFiZKmA/+15RRANUueaWmOBfhF7fRGTyEnEiJMXSLKPTGjfwPk+Tg5sG6alOOPWDTH+o9X2xwLUNSOBSyW29SKKM2tfvRe3UmxqHv44YcxderUqGny8/ORnZ2NU6dOhezv6upCY2OjrL4ib731Ftrb2zF58uSYaUtLS7Fw4UJ0dnbC6ewtMJxOZ9j9PdHav85mtyO5MF/2sUr7hMh1PmrXslXrXHn2tdML3sJGFNHSEz2mLjGLoAPM6d8A+T7OTzTfY5Sw89ulBbX97AASd1rRIujMGp3zI1LEV7Goy8jIQEZGRsx0ZWVlaGpqQk1NDYqLiwEAH330EXw+H0pLS2Me//LLL+MHP/iBrGvt2LED/fr1U+TUlBLNMWn50lTzhcnT+cRb1I6XmBNJqESCBF1v4sm/6S3s/OeNhtFRO4DEnVLMEp0D9Bd0RqzBzs2jFhYWorKyEtOnT8eWLVvw2WefYebMmZgwYUJgZNjx48cxZMgQbNmyJeTYffv24dNPP8V9993X67x/+ctf8NJLL6G2thb79u3D73//e/z617/GQw89xMTuCRHmrgP0WAdOGTyH6Gvpa8dz+hPWsOjT5j9H8CY6JOi0YVb/1hO9Kx25/eyM7msH6DsFitnw3xut0TkSdOzhNqUJAKxevRozZ87ELbfcArvdjrvuugsvvPBC4PeLFy9iz549aG9vDzluxYoVuOqqq3Drrbf2OmdiYiKWLVuGX/ziF5AkCYMHD8bzzz+P6dOnM7Nba/86tWgZos8DLU0iZovcmVlgKIUEHRvM6t/kwmvqD7ndQ0SI2gGh/jXeo3es6hqzizlATEEHcJzSRGS8Xi/cbnfIlCY9idW3jvdCvTzXa1WL1ryZReBZGTMJOpGnNBEdv48LN6VJOPSa5qQnckWC0VOf9CSexB3LoIHe9aNRZVduPnlMaUKiLoKoA0jYRYLEHV8iNV9rvW9mEnQAiTotKBV1gPjCDhBP3AHWFHg8Wn+sEJ3zwypKx0PUcW1+NTtqRsMCbGdM959PJLSOVAsWLSTw+C7BBsgbDCKaoCP0R8+BE8HIHUQBsBkly9qvWqV5llc3Hr2FuFkEHS9I1GmA14jYnmgZps8TFg42HgWengNJ9BB0scQcQILOCvBeXkvJVEyi9LfrSU/7RRZ5vAeBWE3MAeILOiDORd1brf3wH329UdPEitbpKez85xQNVnNM9RQ7VhF5LEScmnthdkHn3q/vmruE8R+Qekft/MfzynOkfOgt9vQcxWtEE7lZBV1R3gkcVWtQBOK6T92L24uR2tcRdRoTP2r71wF8FLyI4s4Pry8WM4g8HlE4swo6rdG5tK/aUfPmPOpTpwI1feqCMap/XTBKhYjV+jLLvc+iTLtiVTEHxL7HavI+Iv8YLrZdwF8rX6SBElrpKeqA6PPT+RFN2AHiOKBw6BWONkLs6dWEqjRvVuk/59nrQ9fFDhJ1KvH7uDFV0/FVg/xVboIRQdgBJO5Ex6jBK2YXdAC4iLq4bn4NZk1rP1nCLhp6NcUGY3RzSTSC7eIp8NQIrGCxJOpkySIKOj36z+m1Bm+8oNb3GDVwoidKmmQBdn5H5C4vImB1MQfwFXS8oN7LQcSKxMkRfdEKHK+HyWpmbp6wmiWeFf6VL0QUdMkHnCToCKaoffei+RW9m/3UVOYs/I7fv4ruY/WA1b1Q81zGZ28lQScDitT1wC/stETtjIjYAeb4stQremdWeIg5gAQdYf6IHaA8aueHonfqMbo524gRxGYVdACJuohEao6VO3edUcIOMI/jIYEXioiCjqYrsRY8fI/ewg5QL+4AtnPdBZ/PShgt5ADjpoMxs6ADaKBEYKBEJCJF7OROSizKvDZmcjzxJvB4iTnAPIIuXJ5ooIR6ggdKJPZJ6vU7j4rJyDnZtDYFs/Q5ZvK1wYgg5ACxyxHr9+b7ff4X00fV0OhXrQQ7vEnXfCnrmJ7iTslKE6IIO8B8DsfKAo/XVCUAfzEH8BV0AJC28xSqaxeTqFNB8IfrX9r+JWI6pe+XyMIOYNPPj7XPEdXniiZkRS87rAXd+OytaG/pJlHHgp5fsWaduFArojqbSFhJ4FF07hKR8uXZ3Yyu7k4SdSrp2RrB0v+ILuwAMRehD0Zv3ytyHsxQXngIOgAk6lgRrmkiXoUdYD5xB5hX4PGMzgHqBZ3e0TkguqADQKJOA+G6mOgp7AAxKmuA/ShdkbrN6GULjzrCLOWDl6ADSNQxI1J/k3gWdn7MJvBEuGdy4C3mAHWCTq6YA/QRdACJOhZE6jccr8IO4DsFi1n8kFx41QMilQfAWEEH8BF1NPo1CD1GccUaFQsY6yB6XttsIk80SMwpg8VqF0RkWI7KlzPxuREjYyMRbAdrgRfpPogs9vT07aKUgWBYCzpRVmChSF2YkWFGR+wAcZ2BaCJP1Pukh5gDlIsgJWIO4CPoYjW7+qFInXpijfA3U8ROyyC1WFhp3VQ/ovholvUo6zIgiqC7TTqDft86QM2vWokl6gDzCju9l78y0oGIJujUrkGrdtJdJYJOBDEHyBd0AIk6LciZtoml/+Ep7KJNBG9FgWdmeAq5YNQ+dznPWC9BNyHtHLwt3cxFHbd2laeffho33ngjUlNT4fF4ZB0jSRLmz5+PAQMGICUlBeXl5di7d29ImsbGRkycOBEulwsejwf33nsvWltbVdlYeygn4m9r62/g/pLHWvZEq2DyLzelZtkpuQQvG8N7OR09rqEGtffXs9enOjonV9A1XedW3NQqgqADgO7aPVzsYIUZfNwP085FrBxZ+h8576NanxqtAp/wTf60rtsNXL4fIjYVigzr+8bqefZENEHHC26RugULFsDj8eDYsWN4+eWX0dTUFPOYZ599FosWLcKqVaswaNAgPP7449i1axe++uorJCcnAwDGjBmDkydP4g9/+AMuXryIadOm4YYbbsDrr78u2zb/V2zu8gWwpyQLMUSf5VezHIEh4pqnZkKLSNayHJYSMacUnn3nFAu6nXXoki5iI/4sbKTODD7u3D/z4fomUhdNHJmhOVZJRcgyggdQFK8nrOtEuc9WlAidFs0QnFcekTruza8rV67ErFmzYjo8SZKQk5ODhx9+GP/1X/8FAGhubkZWVhZWrlyJCRMmoK6uDkOHDsXWrVtRUlICAKiqqsJtt92GY8eOIScncuQtmJ6iDhBj7iXWzbFKhAeJvNhojXZqXdtUjqATTcwB6gQdAOFFnR+RfVywqAPUCztAjOZYNREO1gIPiD+Rx6P+U/os1TxHuc/JCEEH8BF1wox+PXjwIOrr61FeXh7Y53a7UVpaik2bNmHChAnYtGkTPB5PwNkBQHl5Oex2OzZv3ow777wz7Lk7OzvR2XlZtDQ3X6pMfOcv7/tid38AQFHeibDneG3/CPx75nb1GZTB9/v8L94+NSri70OzDkRtMu5J+4AOOA/JEyKJdaF/d+aRyAMQcv+60aHqHO79l0RNlwY7PHXeiMc3FQY5g4vybWy+5hsxx/FRu/f7wtodKT/BTa5duAjgkhiyAkb4OG9rqKC+DWfwVoTKkaX/+WJ3/4i+1M9r+0cAgCK/uqLlUln/oQJBcBvOAEDEfKvh+33+t9e+aPfOTIR7Hu0tbM79w5AolfzjLj27bkXXuvQ8LsRMd6lMx/ab/vJ8sS387/77Fu5e+fPdM8/+95OljxNG1NXX1wMAsrKyQvZnZWUFfquvr0dmZmbI7wkJCUhPTw+kCceiRYvwxBNP9Np//BfP9Np3NIqNf43yGztqdLkKYSFqjTaAL2fPnoXbrTwCKRpG+LirRx1SaCU7/xPNlwajxq9OV3EMf6zhu3nWc/o+N7bPI1Z5jnbfYuWbpY9TJOrmzJmDZ599Nmqauro6DBkyRJNRrJk7dy5mz54d+LupqQlXX301jhw5YonKIhperxe5ubk4evSo0E1YWomXfALxk9fm5mYMHDgQ6enpul2TfJy5iJd3AYifvMZLPgE+Pk6RqHv44YcxderUqGny8/NVGZKdnQ0AaGhowIABAwL7GxoaMHLkyECaU6dOhRzX1dWFxsbGwPHhcDqdcDp7N0O63W7LFxo/LpcrLvIaL/kE4ievdrt+kx+TjzMn8fIuAPGT13jJJ8DWxykSdRkZGcjIyGB28WAGDRqE7OxsVFdXBxyc1+vF5s2b8cADDwAAysrK0NTUhJqaGhQXFwMAPvroI/h8PpSWlnKxiyCI+IF8HEEQZobbJ/CRI0ewY8cOHDlyBN3d3dixYwd27NgRMt/SkCFDsG7dOgCAzWbDrFmz8NRTT+Hdd9/Frl27MHnyZOTk5GDcuHEAgMLCQlRWVmL69OnYsmULPvvsM8ycORMTJkyQPSqMIAiCBeTjCIIQDokTU6ZMkQD02j7++ONAGgDSK6+8Evjb5/NJjz/+uJSVlSU5nU7plltukfbs2RNy3rNnz0r33HOPlJaWJrlcLmnatGlSS0uLIts6OjqkBQsWSB0dHVqyaAriJa/xkk9Jip+8ip5P8nHGEy/5lKT4yWu85FOS+OQ1LpcJIwiCIAiCsBr69UAmCIIgCIIguEGijiAIgiAIwgKQqCMIgiAIgrAAJOoIgiAIgiAsQNyIuqeffho33ngjUlNT4fF4ZB0jSRLmz5+PAQMGICUlBeXl5di7dy9fQzXS2NiIiRMnwuVywePx4N577w2ZYiEc3/3ud2Gz2UK2+++/XyeL5bNs2TLk5eUhOTkZpaWl2LJlS9T0b775JoYMGYLk5GQMGzYM77//vk6WakdJXleuXNnr+SUnJ+torTo+/fRTfP/730dOTg5sNhveeeedmMds3LgRo0aNgtPpxODBg7Fy5UrudpqBePFvAPm4YMzq4+LBvwHG+Li4EXUXLlzAj370o8Akn3JYvHgxXnjhBSxfvhybN29Gnz59UFFRgY4OdQu768HEiROxe/dubNiwAevXr8enn36KGTNmxDxu+vTpOHnyZGBbvHixDtbKZ+3atZg9ezYWLFiA7du3Y8SIEaioqOg1+76fzz//HPfccw/uvfdefPHFFxg3bhzGjRuH2lrxF0pVmlfg0uzrwc/v8OHDOlqsjra2NowYMQLLli2Tlf7gwYMYO3Ysvve972HHjh2YNWsW7rvvPnzwwQecLRWfePFvAPk4P2b1cfHi3wCDfByzyVFMwiuvvCK53e6Y6Xw+n5SdnS395je/CexramqSnE6n9D//8z8cLVTPV199JQGQtm7dGtj317/+VbLZbNLx48cjHnfzzTdLP//5z3WwUD2jR4+WfvrTnwb+7u7ulnJycqRFixaFTX/33XdLY8eODdlXWloq/ed//idXO1mgNK9yy7TIAJDWrVsXNc2jjz4qXXfddSH7xo8fL1VUVHC0zFxY2b9JEvm4YMzq4+LRv0mSfj4ubiJ1Sjl48CDq6+tRXl4e2Od2u1FaWopNmzYZaFlkNm3aBI/Hg5KSksC+8vJy2O12bN68Oeqxq1evRv/+/VFUVIS5c+eivb2dt7myuXDhAmpqakKehd1uR3l5ecRnsWnTppD0AFBRUSHss/OjJq8A0Nraiquvvhq5ubm44447sHv3bj3M1RWzPlMRMaN/A8jHBWPG94H8W3RYPFNFa7/GE/X19QCArKyskP1ZWVmB30Sjvr4emZmZIfsSEhKQnp4e1eYf//jHuPrqq5GTk4OdO3fisccew549e/D222/zNlkWZ86cQXd3d9hn8fXXX4c9pr6+3lTPzo+avBYUFGDFihUYPnw4mpub8dxzz+HGG2/E7t27cdVVV+lhti5EeqZerxfnz59HSkqKQZaZDzP6N4B8XDBm9HHk36LDwseZOlI3Z86cXh0oe26RCoqZ4J3PGTNmoKKiAsOGDcPEiRPx6quvYt26ddi/fz/DXBC8KCsrw+TJkzFy5EjcfPPNePvtt5GRkYE//OEPRptGaCBe/BtAPo6IDPk3ZZg6Uvfwww9j6tSpUdPk5+erOnd2djYAoKGhAQMGDAjsb2howMiRI1WdUy1y85mdnd2rs2lXVxcaGxsD+ZFDaWkpAGDfvn245pprFNvLmv79+8PhcKChoSFkf0NDQ8R8ZWdnK0ovCmry2pPExERcf/312LdvHw8TDSPSM3W5XJaM0sWLfwPIx8WLjyP/Fh0WPs7Uoi4jIwMZGRlczj1o0CBkZ2ejuro64OS8Xi82b96saIQZC+Tms6ysDE1NTaipqUFxcTEA4KOPPoLP5ws4MTns2LEDAEKcvZEkJSWhuLgY1dXVGDduHADA5/OhuroaM2fODHtMWVkZqqurMWvWrMC+DRs2oKysTAeL1aMmrz3p7u7Grl27cNttt3G0VH/Kysp6TdlghmeqlnjxbwD5uHjxceTfosPEx6kZxWFGDh8+LH3xxRfSE088IaWlpUlffPGF9MUXX0gtLS2BNAUFBdLbb78d+PuZZ56RPB6P9Oc//1nauXOndMcdd0iDBg2Szp8/b0QWZFFZWSldf/310ubNm6V//OMf0rXXXivdc889gd+PHTsmFRQUSJs3b5YkSZL27dsnPfnkk9K2bdukgwcPSn/+85+l/Px86Tvf+Y5RWQjLmjVrJKfTKa1cuVL66quvpBkzZkgej0eqr6+XJEmSfvKTn0hz5swJpP/ss8+khIQE6bnnnpPq6uqkBQsWSImJidKuXbuMyoJslOb1iSeekD744ANp//79Uk1NjTRhwgQpOTlZ2r17t1FZkEVLS0vgPQQgPf/889IXX3whHT58WJIkSZozZ470k5/8JJD+wIEDUmpqqvTII49IdXV10rJlyySHwyFVVVUZlQVhiBf/Jknk4/yY1cfFi3+TJGN8XNyIuilTpkgAem0ff/xxIA0A6ZVXXgn87fP5pMcff1zKysqSnE6ndMstt0h79uzR33gFnD17VrrnnnuktLQ0yeVySdOmTQtx7AcPHgzJ95EjR6TvfOc7Unp6uuR0OqXBgwdLjzzyiNTc3GxQDiKzdOlSaeDAgVJSUpI0evRo6X//938Dv918883SlClTQtK/8cYb0re+9S0pKSlJuu6666T33ntPZ4vVoySvs2bNCqTNysqSbrvtNmn79u0GWK2Mjz/+OOw76c/blClTpJtvvrnXMSNHjpSSkpKk/Pz8kPc1nokX/yZJ5OOCMauPiwf/JknG+DibJEmSqjghQRAEQRAEIQymHv1KEARBEARBXIJEHUEQBEEQhAUgUUcQBEEQBGEBSNQRBEEQBEFYABJ1BEEQBEEQFoBEHUEQBEEQhAUgUUcQBEEQBGEBSNQRBEEQBEFYABJ1BEEQBEEQFoBEHUEQBEEQhAUgUUcQBEEQBGEBSNQRBEEQBEFYgP8f1TlVSVw/JEgAAAAASUVORK5CYII=", "text/plain": ["<Figure size 640x480 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["x1 = np.linspace(pbounds['x1'][0], pbounds['x1'][1], 1000)\n", "x2 = np.linspace(pbounds['x2'][0], pbounds['x2'][1], 1000)\n", "\n", "X1, X2 = np.meshgrid(x1, x2)\n", "Z1 = SPIRAL(X1, X2, '1')\n", "Z2 = SPIRAL(X1, X2, '2')\n", "\n", "fig, axs = plt.subplots(1, 2)\n", "\n", "vmin = np.min([np.min(Z1), np.min(Z2)])\n", "vmax = np.max([np.max(Z1), np.max(Z2)])\n", "\n", "axs[0].contourf(X1, X2, Z1, vmin=vmin, vmax=vmax)\n", "axs[0].set_aspect(\"equal\")\n", "axs[0].scatter(k1[:,0], k1[:,1], c='k')\n", "axs[1].contourf(X1, X2, Z2, vmin=vmin, vmax=vmax)\n", "axs[1].scatter(k2[:,0], k2[:,1], c='k')\n", "axs[1].set_aspect(\"equal\")\n", "axs[0].set_title('k=1')\n", "axs[1].set_title('k=2')\n", "fig.tight_layout()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Use in ML"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A typical usecase for integer and categorical parameters is optimizing the hyperparameters of a machine learning model. Below you can find an example where the hyperparameters of an SVM are optimized."]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|   iter    |  target   |  kernel   |  log10_C  |\n", "-------------------------------------------------\n", "| \u001b[39m2        \u001b[39m | \u001b[39m-0.2361  \u001b[39m | \u001b[39mpoly2    \u001b[39m | \u001b[39m0.9943696\u001b[39m |\n", "| \u001b[39m3        \u001b[39m | \u001b[39m-0.2864  \u001b[39m | \u001b[39mrbf      \u001b[39m | \u001b[39m-0.999771\u001b[39m |\n", "| \u001b[39m4        \u001b[39m | \u001b[39m-0.2625  \u001b[39m | \u001b[39mpoly3    \u001b[39m | \u001b[39m0.7449728\u001b[39m |\n", "| \u001b[35m5        \u001b[39m | \u001b[35m-0.2361  \u001b[39m | \u001b[35mpoly2    \u001b[39m | \u001b[35m0.9944598\u001b[39m |\n", "| \u001b[39m6        \u001b[39m | \u001b[39m-0.298   \u001b[39m | \u001b[39mpoly3    \u001b[39m | \u001b[39m-0.999625\u001b[39m |\n", "| \u001b[35m7        \u001b[39m | \u001b[35m-0.2361  \u001b[39m | \u001b[35mpoly2    \u001b[39m | \u001b[35m0.9945010\u001b[39m |\n", "| \u001b[35m8        \u001b[39m | \u001b[35m-0.2152  \u001b[39m | \u001b[35mrbf      \u001b[39m | \u001b[35m0.9928960\u001b[39m |\n", "| \u001b[39m9        \u001b[39m | \u001b[39m-0.2153  \u001b[39m | \u001b[39mrbf      \u001b[39m | \u001b[39m0.9917667\u001b[39m |\n", "| \u001b[39m10       \u001b[39m | \u001b[39m-0.2362  \u001b[39m | \u001b[39mpoly2    \u001b[39m | \u001b[39m0.9897298\u001b[39m |\n", "| \u001b[39m11       \u001b[39m | \u001b[39m-0.2362  \u001b[39m | \u001b[39mpoly2    \u001b[39m | \u001b[39m0.9874217\u001b[39m |\n", "=================================================\n"]}], "source": ["from sklearn.datasets import load_breast_cancer\n", "from sklearn.svm import SVC\n", "from sklearn.metrics import log_loss\n", "from sklearn.model_selection import train_test_split\n", "from bayes_opt import BayesianOptimization\n", "\n", "data = load_breast_cancer()\n", "X_train, y_train = data['data'], data['target']\n", "X_train, X_val, y_train, y_val = train_test_split(X_train, y_train, test_size=0.2, random_state=1)\n", "kernels = ['rbf', 'poly']\n", "\n", "def f_target(kernel, log10_C):\n", "    if kernel == 'poly2':\n", "        kernel = 'poly'\n", "        degree = 2\n", "    elif kernel == 'poly3':\n", "        kernel = 'poly'\n", "        degree = 3\n", "    elif kernel == 'rbf':\n", "        degree = 3 # not used, equal to default\n", "\n", "    C = 10**log10_C\n", "\n", "    model = SVC(C=C, kernel=kernel, degree=degree, probability=True, random_state=1)\n", "    model.fit(X_train, y_train)\n", "\n", "    # Package looks for maximum, so we return -1 * log_loss\n", "    loss = -1 * log_loss(y_val, model.predict_proba(X_val))\n", "    return loss\n", "\n", "\n", "params_svm ={\n", "    'kernel': ['rbf', 'poly2', 'poly3'],\n", "    'log10_C':(-1, +1),\n", "}\n", "\n", "optimizer = BayesianOptimization(\n", "    f_target,\n", "    params_svm,\n", "    random_state=1,\n", "    verbose=2\n", ")\n", "\n", "kernel = Matern(nu=2.5, length_scale=np.ones(optimizer.space.dim))\n", "discrete_optimizer.set_gp_params(kernel=kernel)\n", "optimizer.maximize(init_points=2, n_iter=8)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Defining your own Parameter\n", "\n", "Maybe you want to optimize over another form of parameters, which does not align with `float`, `int` or categorical. For this purpose, you can create your own, custom parameter. A simple example is a parameter that is discrete, but still admits a distance representation (like an integer) while not being uniformly spaced.\n", "\n", "However, you can go further even and encode constraints and even symmetries in your parameter. Let's consider the problem of finding a triangle which maximizes an area given its sides $a, b, c$ with a constraint that the perimeter is fixed, i.e. $a + b + c=s$.\n", "\n", "We will create a parameter that encodes such a triangle, and via it's kernel transform ensures that the sides sum to the required length $s$. As you might expect, the solution to this problem is an equilateral triangle, i.e. $a=b=c=s/3$.\n", "\n", "To define the parameter, we need to subclass `BayesParameter` and define a few important functions/properties.\n", "\n", "- `is_continuous` is a property which denotes whether a parameter is continuous. When optimizing the acquisition function, non-continuous parameters will not be optimized using gradient-based methods, but only via random sampling.\n", "- `random_sample` is a function that samples randomly from the space of the parameter.\n", "- `to_float` transforms the canonical representation of a parameter into float values for the target space to store. There is a one-to-one correspondence between valid float representations produced by this function and canonical representations of the parameter. This function is most important when working with parameters that use a non-numeric canonical representation, such as categorical parameters.\n", "- `to_param` performs the inverse of `to_float`: Given a float-based representation, it creates a canonical representation. This function should perform binning whenever appropriate, e.g. in the case of the `IntParameter`, this function would round any float values supplied to it.\n", "- `kernel_transform` is the most important function of the Parameter and defines how to represent a value in the kernel space. In contrast to `to_float`, this function expects both the input, as well as the output to be float-representations of the value.\n", "- `to_string` produces a stringified version of the parameter, which allows users to define custom pretty-print rules for ththe ScreenLogger use.\n", "- `dim` is a property which defines the dimensionality of the parameter. In most cases, this will be 1, but e.g. for categorical parameters it is equivalent to the cardinality of the category space.  "]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["from bayes_opt.logger import ScreenLogger\n", "from bayes_opt.parameter import BayesParameter\n", "from bayes_opt.util import ensure_rng\n", "\n", "\n", "class FixedPerimeterTriangleParameter(BayesParameter):\n", "    def __init__(self, name: str, bounds, perimeter) -> None:\n", "        super().__init__(name, bounds)\n", "        self.perimeter = perimeter\n", "\n", "    @property\n", "    def is_continuous(self):\n", "        return True\n", "\n", "    def random_sample(self, n_samples: int, random_state):\n", "        random_state = ensure_rng(random_state)\n", "        samples = []\n", "        while len(samples) < n_samples:\n", "            samples_ = random_state.dirichlet(np.ones(3), n_samples)\n", "            samples_ = samples_ * self.perimeter # scale samples by perimeter\n", "\n", "            samples_ = samples_[np.all((self.bounds[:, 0] <= samples_) & (samples_ <= self.bounds[:, 1]), axis=-1)]\n", "            samples.extend(np.atleast_2d(samples_))\n", "        samples = np.array(samples[:n_samples])\n", "        return samples\n", "\n", "    def to_float(self, value):\n", "        return value\n", "\n", "    def to_param(self, value):\n", "        return value * self.perimeter / sum(value)\n", "\n", "    def kernel_transform(self, value):\n", "        return value * self.perimeter / np.sum(value, axis=-1, keepdims=True)\n", "\n", "    def to_string(self, value, str_len: int) -> str:\n", "        len_each = (str_len - 2) // 3\n", "        str_ = '|'.join([f\"{float(np.round(value[i], 4))}\"[:len_each] for i in range(3)])\n", "        return str_.ljust(str_len)\n", "\n", "    @property\n", "    def dim(self):\n", "        return 3 # as we have three float values, each representing the length of one side.\n", "\n", "def area_of_triangle(sides):\n", "    a, b, c = sides\n", "    s = np.sum(sides, axis=-1) # perimeter\n", "    A = np.sqrt(s * (s-a) * (s-b) * (s-c))\n", "    return A\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|      iter       |     target      |      sides      |\n", "-------------------------------------------------------\n", "| \u001b[39m2              \u001b[39m | \u001b[39m0.4572         \u001b[39m | \u001b[39m0.29|0.70|0.00 \u001b[39m |\n", "| \u001b[35m3              \u001b[39m | \u001b[35m0.5096         \u001b[39m | \u001b[35m0.58|0.25|0.15 \u001b[39m |\n", "| \u001b[39m4              \u001b[39m | \u001b[39m0.5081         \u001b[39m | \u001b[39m0.58|0.25|0.15 \u001b[39m |\n", "| \u001b[35m5              \u001b[39m | \u001b[35m0.5386         \u001b[39m | \u001b[35m0.44|0.28|0.26 \u001b[39m |\n", "| \u001b[39m6              \u001b[39m | \u001b[39m0.5279         \u001b[39m | \u001b[39m0.38|0.14|0.47 \u001b[39m |\n", "| \u001b[39m7              \u001b[39m | \u001b[39m0.5328         \u001b[39m | \u001b[39m0.18|0.36|0.45 \u001b[39m |\n", "| \u001b[39m8              \u001b[39m | \u001b[39m0.4366         \u001b[39m | \u001b[39m0.02|0.22|0.74 \u001b[39m |\n", "| \u001b[39m9              \u001b[39m | \u001b[39m0.4868         \u001b[39m | \u001b[39m0.00|0.61|0.37 \u001b[39m |\n", "| \u001b[39m10             \u001b[39m | \u001b[39m0.4977         \u001b[39m | \u001b[39m0.56|0.01|0.42 \u001b[39m |\n", "| \u001b[35m11             \u001b[39m | \u001b[35m0.5418         \u001b[39m | \u001b[35m0.29|0.40|0.30 \u001b[39m |\n", "| \u001b[39m12             \u001b[39m | \u001b[39m0.3361         \u001b[39m | \u001b[39m0.06|0.87|0.06 \u001b[39m |\n", "| \u001b[39m13             \u001b[39m | \u001b[39m0.06468        \u001b[39m | \u001b[39m0.99|0.00|0.00 \u001b[39m |\n", "| \u001b[39m14             \u001b[39m | \u001b[39m0.01589        \u001b[39m | \u001b[39m0.0|0.00|0.99  \u001b[39m |\n", "| \u001b[39m15             \u001b[39m | \u001b[39m0.4999         \u001b[39m | \u001b[39m0.21|0.16|0.61 \u001b[39m |\n", "| \u001b[39m16             \u001b[39m | \u001b[39m0.499          \u001b[39m | \u001b[39m0.53|0.46|0.00 \u001b[39m |\n", "| \u001b[39m17             \u001b[39m | \u001b[39m0.4937         \u001b[39m | \u001b[39m0.00|0.41|0.58 \u001b[39m |\n", "| \u001b[39m18             \u001b[39m | \u001b[39m0.5233         \u001b[39m | \u001b[39m0.33|0.51|0.14 \u001b[39m |\n", "| \u001b[39m19             \u001b[39m | \u001b[39m0.5204         \u001b[39m | \u001b[39m0.17|0.54|0.28 \u001b[39m |\n", "| \u001b[39m20             \u001b[39m | \u001b[39m0.5235         \u001b[39m | \u001b[39m0.51|0.15|0.32 \u001b[39m |\n", "| \u001b[39m21             \u001b[39m | \u001b[39m0.5412         \u001b[39m | \u001b[39m0.31|0.27|0.41 \u001b[39m |\n", "| \u001b[39m22             \u001b[39m | \u001b[39m0.4946         \u001b[39m | \u001b[39m0.41|0.00|0.57 \u001b[39m |\n", "| \u001b[39m23             \u001b[39m | \u001b[39m0.5355         \u001b[39m | \u001b[39m0.41|0.39|0.19 \u001b[39m |\n", "| \u001b[35m24             \u001b[39m | \u001b[35m0.5442         \u001b[39m | \u001b[35m0.35|0.32|0.32 \u001b[39m |\n", "| \u001b[39m25             \u001b[39m | \u001b[39m0.5192         \u001b[39m | \u001b[39m0.16|0.28|0.54 \u001b[39m |\n", "| \u001b[39m26             \u001b[39m | \u001b[39m0.5401         \u001b[39m | \u001b[39m0.39|0.23|0.36 \u001b[39m |\n", "=======================================================\n"]}], "source": ["param = FixedPerimeterTriangleParameter(\n", "    name='sides',\n", "    bounds=np.array([[0., 1.], [0., 1.], [0., 1.]]),\n", "    perimeter=1.\n", ")\n", "\n", "pbounds = {'sides': param}\n", "optimizer = BayesianOptimization(\n", "    area_of_triangle,\n", "    pbounds,\n", "    random_state=1,\n", ")\n", "\n", "# Increase the cell size to accommodate the three float values\n", "optimizer.logger._verbose = 2\n", "optimizer.logger._default_cell_size = 15\n", "optimizer.logger._is_constrained = False\n", "\n", "optimizer.maximize(init_points=2, n_iter=23)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This seems to work decently well, but we can improve it significantly if we consider the symmetries inherent in the problem: This problem is permutation invariant, i.e. we do not care which side specifically is denoted as $a$, $b$ or $c$. Instead, we can, without loss of generality, decide that the shortest side will always be denoted as $a$, and the longest always as $c$. If we enhance our kernel transform with this symmetry, the performance improves significantly. This can be easily done by sub-classing the previously created triangle parameter."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["|      iter       |     target      |      sides      |\n", "-------------------------------------------------------\n", "| \u001b[39m2              \u001b[39m | \u001b[39m0.4572         \u001b[39m | \u001b[39m0.00|0.29|0.70 \u001b[39m |\n", "| \u001b[35m3              \u001b[39m | \u001b[35m0.5096         \u001b[39m | \u001b[35m0.15|0.25|0.58 \u001b[39m |\n", "| \u001b[39m4              \u001b[39m | \u001b[39m0.498          \u001b[39m | \u001b[39m0.06|0.33|0.60 \u001b[39m |\n", "| \u001b[35m5              \u001b[39m | \u001b[35m0.5097         \u001b[39m | \u001b[35m0.13|0.27|0.58 \u001b[39m |\n", "| \u001b[35m6              \u001b[39m | \u001b[35m0.5358         \u001b[39m | \u001b[35m0.19|0.36|0.43 \u001b[39m |\n", "| \u001b[35m7              \u001b[39m | \u001b[35m0.5443         \u001b[39m | \u001b[35m0.33|0.33|0.33 \u001b[39m |\n", "| \u001b[39m8              \u001b[39m | \u001b[39m0.5405         \u001b[39m | \u001b[39m0.28|0.28|0.42 \u001b[39m |\n", "| \u001b[39m9              \u001b[39m | \u001b[39m0.5034         \u001b[39m | \u001b[39m0.01|0.49|0.49 \u001b[39m |\n", "| \u001b[39m10             \u001b[39m | \u001b[39m0.4977         \u001b[39m | \u001b[39m0.01|0.42|0.56 \u001b[39m |\n", "| \u001b[39m11             \u001b[39m | \u001b[39m0.5427         \u001b[39m | \u001b[39m0.27|0.36|0.36 \u001b[39m |\n", "=======================================================\n"]}], "source": ["class SortingFixedPerimeterTriangleParameter(FixedPerimeterTriangleParameter):\n", "    def __init__(self, name: str, bounds, perimeter) -> None:\n", "        super().__init__(name, bounds, perimeter)\n", "\n", "    def to_param(self, value):\n", "        value = np.sort(value, axis=-1)\n", "        return super().to_param(value)\n", "\n", "    def kernel_transform(self, value):\n", "        value = np.sort(value, axis=-1)\n", "        return super().kernel_transform(value)\n", "\n", "param = SortingFixedPerimeterTriangleParameter(\n", "    name='sides',\n", "    bounds=np.array([[0., 1.], [0., 1.], [0., 1.]]),\n", "    perimeter=1.\n", ")\n", "\n", "pbounds = {'sides': param}\n", "optimizer = BayesianOptimization(\n", "    area_of_triangle,\n", "    pbounds,\n", "    random_state=1,\n", ")\n", "\n", "optimizer.logger.verbose = 2\n", "optimizer.logger._default_cell_size = 15\n", "\n", "optimizer.maximize(init_points=2, n_iter=8)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "bayesian-optimization-tb9vsVm6-py3.9", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.1"}}, "nbformat": 4, "nbformat_minor": 2}