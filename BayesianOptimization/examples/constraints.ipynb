{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Constrained Optimization\n", "\n", "Constrained optimization refers to situations in which you must for instance maximize \"f\", a function of \"x\" and \"y\", but the solution must lie in a region where for instance \"x<y\".\n", "\n", "There are two distinct situations you may find yourself in:\n", "\n", "1. Simple, cheap constraints: in this case, you know whether or not a given solution violates your constraints **before** you even assess it. In this case, you can codify your constraints directly into the objective function and you should read **1** below.\n", "2. Expensive constraints - in other situations, you may not know whether or not a given solution violates your constraints until you have explicitly evaluate the objective function there - which is typically an expensive operation. In such situations, it is desirable to **learn** the constrained regions on the fly in order to avoid unnecessary expensive calls to the objective function. The way to handle these situations is described in **2. Advanced Constrained Optimization**\n", "\n", "\n", "## 1. Simple Constrained Optimization\n", "\n", "In situations where you know in advance whether or not a given point violates your constraints, you can normally simply code them directly into the objective function. To demonstrate this, let's start with a standard non-constrained optimization:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the best solution with no constraints is {'target': -3.000000000116973, 'params': {'x': 2.0, 'y': 0.9999891845990944}}\n"]}], "source": ["from bayes_opt import BayesianOptimization\n", "\n", "def black_box_function_no_constraints(x, y):\n", "    return -x ** 2 - (y - 1) ** 2 + 1\n", "\n", "# Bounded region of parameter space\n", "pbounds = {'x': (2, 4), 'y': (-3, 3)}\n", "\n", "optimizer = BayesianOptimization(\n", "    f=black_box_function_no_constraints,\n", "    pbounds=pbounds,\n", "    random_state=0,\n", "    verbose=0\n", ")\n", "\n", "optimizer.maximize(\n", "    init_points=2,\n", "    n_iter=100\n", ")\n", "\n", "print(f'the best solution with no constraints is {optimizer.max}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's rerun this example, except with the constraint that y>x. To do this, we are simply going to return a 'bad' value from the objective function whenever this constrain is violated. What constitutes a 'bad' value is objective function specific - in general, it's a good idea for the 'bad' value you use to be similar in magnitude to the worst value that the objective function naturally has."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the best solution with y>x is {'target': -4.021127762033562, 'params': {'x': 2.0008684793556073, 'y': 2.0087879313090253}}\n"]}], "source": ["def black_box_function_with_constraints(x, y):\n", "    if y <= x:\n", "        return -10\n", "    else:\n", "        return -x ** 2 - (y - 1) ** 2 + 1\n", "    \n", "optimizer = BayesianOptimization(\n", "    f=black_box_function_with_constraints,\n", "    pbounds=pbounds,\n", "    random_state=0,\n", "    verbose=0\n", ")\n", "\n", "optimizer.maximize(\n", "    init_points=2,\n", "    n_iter=100\n", ")\n", "\n", "print(f'the best solution with y>x is {optimizer.max}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Ok, this seems to have worked pretty well; our constraints have been respected and the target value isn't even **that** much worse!\n", "\n", "In certain other cases, you may be able to reformulate your objective function such that the constraint is explicitly embedded. For instance, consider the constraint `x+y=4`. Since this implies that `y=4-x`, we could simply reformulate the objective function explicitly:"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["the best solution with y=4-x is {'target': -4.0, 'params': {'x': 2.0}}\n"]}], "source": ["def surrogate_objective(x):\n", "    y=4-x\n", "    return black_box_function_no_constraints(x,y)\n", "\n", "pbounds = {'x': (2, 4)}\n", "# note that in general, we would have to update pbounds such that the values that x were allowed to take on\n", "# respected the bounds of y. In this case (4-4=0)<=y<=(4-2=2) already respect our original bounds -3<=y<=3\n", "\n", "optimizer = BayesianOptimization(\n", "    f=surrogate_objective,\n", "    pbounds=pbounds,\n", "    random_state=0,\n", "    verbose=0,\n", "    allow_duplicate_points=True\n", ")\n", "\n", "optimizer.maximize(\n", "    init_points=2,\n", "    n_iter=10\n", ")\n", "\n", "print(f'the best solution with y=4-x is {optimizer.max}')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["> Note: in this last example, we have set `allow_duplicate_points=True`. The reason we are getting some duplicate points in this example is probably because the space is now so constrained that the optimizer quickly hones in on only one 'interesting' point and repeatedly probes it. The default behavior in these cases is `allow_duplicate_points=False` which will raise an error when a duplicate is registered."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Advanced Constrained Optimization"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In some situations, certain regions of the solution domain may be infeasible or not allowed. In addition, you may not know whether specific parameter combinations fall into these regions until you have evaluated the constraint function at these points. In other words, checking for feasibility is as expensive, or close to as expensive as evaluating the objective function. This notebook demonstrates how you can handle these situations by modelling the constraints as a Gaussian process. This approach is based on a paper by [Gardner et. al., 2014](http://proceedings.mlr.press/v32/gardner14.pdf).\n", "\n", "Note that if the constrained regions are known/if the constraint function is cheap to evaluate, then other approaches are preferable due to the computational complexity of modelling using Gaussian processes.\n", "In this case, at the time of writing the best approach is to return a low number to the optimizer when it tries to evaluate these regions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.1 Simple, single constraint"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from bayes_opt import BayesianOptimization\n", "import matplotlib.pyplot as plt\n", "from scipy.optimize import NonlinearConstraint"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We illustrate the use of advanced constrained bayesian optimization on the examples <PERSON> et al. used in their paper.\n", "\n", "Define the target function ($f$ or `target_function`) we want to optimize along with a constraint function ($c$ or `constraint_function`) and constraint limit ($c^{lim}$ or `constraint_limit`). The mathematical problem we are trying to solve is\n", "$$\n", " \\max f(x, y)\n", "$$\n", "$$\n", " \\text{subj. to} \\: \\: c(x, y) \\leq c^{\\text{lim}}\n", "$$\n", "Note that the constraint function should have the same parameter names as the target function (i.e. in this case `x` and `y`)."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def target_function(x, y):\n", "    # Gardner is looking for the minimum, but this packages looks for maxima, thus the sign switch\n", "    return np.cos(2*x)*np.cos(y) + np.sin(x)\n", "\n", "def constraint_function(x, y):\n", "    return np.cos(x) * np.cos(y) - np.sin(x) * np.sin(y)\n", "\n", "constraint_limit = 0.5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["A `scipy.NonlinearConstraint` object stores the constraint configuration. Since we do not have a lower bound on our problem, provide `-np.inf` as `lb` argument."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["constraint = NonlinearConstraint(constraint_function, -np.inf, constraint_limit)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create a `BayesianOptimization` model as you would usually, providing the `ConstraintModel` instance as additional keyword argument.  "]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Bounded region of parameter space\n", "pbounds = {'x': (0, 6), 'y': (0, 6)}\n", "\n", "optimizer = BayesianOptimization(\n", "    f=target_function,\n", "    constraint=constraint,\n", "    pbounds=pbounds,\n", "    verbose=0, # verbose = 1 prints only when a maximum is observed, verbose = 0 is silent\n", "    random_state=1,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Run the optimization as usual -- the optimizer automatically estimates the probability for the constraint to be fulfilled and modifies the acquisition function accordingly. This means, that the optimizer avoids sampling points that are likely unfeasible."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["optimizer.maximize(\n", "    init_points=3,\n", "    n_iter=10,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The best combination of parameters, target value and constraint function value found by the optimizer can be accessed via the property `optimizer.max`."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'target': 1.9734131381980626, 'params': {'x': 1.64727683929071, 'y': 3.2975037081163068}, 'constraint': 0.230305457787476}\n"]}], "source": ["print(optimizer.max)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def plot_constrained_opt(pbounds, target_function, optimizer):\n", "    \"\"\"\n", "    Plots a number of interesting contours to visualize constrained 2-dimensional optimization.\n", "    \"\"\"\n", "\n", "    # Set a few parameters\n", "    n_constraints = optimizer.constraint.lb.size\n", "    n_plots_per_row = 2+n_constraints\n", "\n", "    # Construct the subplot titles\n", "    if n_constraints==1:\n", "        c_labels = [\"constraint\"]\n", "    else:\n", "        c_labels = [f\"constraint {i+1}\" for i in range(n_constraints)]\n", "    labels_top = [\"target\"] + c_labels + [\"masked target\"]\n", "    labels_bot = [\"target estimate\"] + [c + \" estimate\" for c in c_labels] + [\"acquisition function\"]\n", "    labels = [labels_top, labels_bot]\n", "\n", "    # Setup the grid to plot on\n", "    x = np.linspace(pbounds['x'][0], pbounds['x'][1], 1000)\n", "    y = np.linspace(pbounds['y'][0], pbounds['y'][1], 1000)\n", "    xy = np.array([[x_i, y_j] for y_j in y for x_i in x])\n", "    X, Y = np.meshgrid(x, y)\n", "\n", "    # Evaluate the actual functions on the grid\n", "    Z = target_function(X, Y)\n", "    # This reshaping is a bit painful admittedly, but it's a consequence of np.meshgrid\n", "    C = optimizer.constraint.fun(X, Y).reshape((n_constraints,) + Z.shape).swapaxes(0, -1)\n", "    \n", "    \n", "    fig, axs = plt.subplots(2, n_plots_per_row, constrained_layout=True, figsize=(12,8))\n", "\n", "    for i in range(2):\n", "        for j in range(n_plots_per_row):\n", "            axs[i, j].set_aspect(\"equal\")\n", "            axs[i, j].set_title(labels[i][j])\n", "    \n", "    \n", "    # Extract & unpack the optimization results\n", "    max_ = optimizer.max\n", "    res = optimizer.res\n", "    x_ = np.array([r[\"params\"]['x'] for r in res])\n", "    y_ = np.array([r[\"params\"]['y'] for r in res])\n", "    c_ = np.array([r[\"constraint\"] for r in res])\n", "    a_ = np.array([r[\"allowed\"] for r in res])\n", "\n", "\n", "    Z_est = optimizer._gp.predict(xy).reshape(Z.shape)\n", "    C_est = optimizer.constraint.approx(xy).reshape(Z.shape + (n_constraints,))\n", "    P_allowed = optimizer.constraint.predict(xy).reshape(Z.shape)\n", "\n", "    Acq = np.where(Z_est >0, Z_est * P_allowed, Z_est / (0.5 + P_allowed))\n", "    \n", "    \n", "    target_vbounds = np.min([Z, Z_est]), np.max([Z, Z_est])\n", "    constraint_vbounds = np.min([C, C_est]), np.max([C, C_est])\n", "\n", "\n", "    axs[0,0].contourf(X, Y, Z, cmap=plt.cm.coolwarm, vmin=target_vbounds[0], vmax=target_vbounds[1])\n", "    for i in range(n_constraints):\n", "        axs[0,1+i].contourf(X, Y, C[:,:,i], cmap=plt.cm.coolwarm, vmin=constraint_vbounds[0], vmax=constraint_vbounds[1])\n", "    Z_mask = Z\n", "\n", "    Z_mask[~np.squeeze(optimizer.constraint.allowed(C))] = np.nan\n", "    axs[0,n_plots_per_row-1].contourf(X, Y, Z_mask, cmap=plt.cm.coolwarm, vmin=target_vbounds[0], vmax=target_vbounds[1])\n", "\n", "    axs[1,0].contourf(X, Y, Z_est, cmap=plt.cm.coolwarm, vmin=target_vbounds[0], vmax=target_vbounds[1])\n", "    for i in range(n_constraints):\n", "        axs[1,1+i].contourf(X, Y, C_est[:, :, i], cmap=plt.cm.coolwarm, vmin=constraint_vbounds[0], vmax=constraint_vbounds[1])\n", "    axs[1,n_plots_per_row-1].contourf(X, Y, Acq, cmap=plt.cm.coolwarm, vmin=0, vmax=1)\n", "\n", "    for i in range(2):\n", "        for j in range(n_plots_per_row):\n", "            axs[i,j].scatter(x_[a_], y_[a_], c='white', s=80, edgecolors='black')\n", "            axs[i,j].scatter(x_[~a_], y_[~a_], c='red', s=80, edgecolors='black')\n", "            axs[i,j].scatter(max_[\"params\"]['x'], max_[\"params\"]['y'], s=80, c='green', edgecolors='black')\n", "\n", "    return fig, axs"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will now visualize the constrained optimization.\n", "\n", "In the following figure you will see two rows of plots with 3 quadratic plots each. The top row contains - in order -- contour visualizations of the target function, constraint function and the target function (masked such that only areas where the constraint is fulfilled are plotted). Additionally we have visualized the points sampled by the optimizer -- the optimal point is plotted in green, allowed (but non-optimal) points in white, and disallowed points in red. The bottom row shows -- in order -- the approximation of the target function using the gaussian regressors, the approximation of the constraint function and a visualization of the acquisition function, i.e. the function that guides the next point to sample."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"image/png": "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****************************************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********************************/WJuRAxtvWOm1EqmN/YOUwsCGSFyyJ6YGDBiA/Pz8+o+cnBw16jIlLp0jTzk7ddW7BCLFsX8oxyjTLVYLpBoy4ms3yveVr7icz3i06h+c1tGXmu+/1UIpkV8vf87IHbvsB9jt/C2FyZQey0NYjwS9yyAik2P/sBajhTJqcb0PoUMH61yJZ0KOZqO85xC9yyBqhP3DM9z43D2RQxoiqiN7Yurw4cOIj49H9+7dcffdd+PYsWOtHl9VVYWSkpJGH6KwJ3TRuwQiy+LPn/WYqX/oSfSpFiNOCmmB7wmR97TsH5zm0Ida77uVQykrv3YyHlnB1HXXXYc33ngDGzZswEsvvYTTp09jxIgRKCoqavExixYtQkRERP1HYmKiz0UbkejL+ESoz376J71LICKViNA/uHxHfQxfWmeU90f08FMJ/PfAOEToH0REpC5ZwdTEiRNx1113ITk5GWPHjsXHH38MAHj99ddbfMzChQtx4cKF+o/c3FzfKiahcQN0InKH/UMZIgcGRgld9Mb3iUgePfqHEaemuIyvOU4MicuIP2OkLtl7TDUUEhKC5ORkHD58uMVjAgMDERgY6MtpSCPca4qItML+YS4MW+Qxwr5TVthrqtulAzge0FfvMkgm9g/zUSOkYChV56X1Djw8qVrvMojaJHuPqYaqqqqwf/9+xMXFKVWPKYmwTI6ISCTsH/KJOi3FUMp7or93on7PkbVp1T840WFcDKWIjEdWMDV37lxs2bIFP/74I77++mtMnjwZJSUlmDZtmuwTO2O58bGIGKIRkRqU7B8kDtGDFSPge0jUOvYPc2MAqD4GdWQEsoKpvLw8/PKXv0SfPn1w5513IiAgADt27EDXrl29Ormzk3ePI7IC/nyQmSjdP6xGxMkVBirKEfm9FPF7j6xFz/5hlNCE+0tdwRDGOIzy80XakLXH1Nq1axUvwNmpK+/GJhhf95py5p2APYETcUR0hRr9g/QjcpBiVGU7vxd6zymz4j5T4mP/MC+lgwmGUi3jXlMkOp/2mFKKmSdDuDSOiIjMhKGUekR9bzk1RVbGqQ5jYChFZGxCBFMkHgZqRERiECkUEDU4MRO+x0QkB5fxMZQiMgNhgim9pqa45IyIiKhtDEysTaSAVGndLh3QuwQSHKemlMf3VHsiBnj8PiAXYYIpwNxL+oiIiIyKoZS2+H4TEXlGxLCFiOQTKpgyG6Mvh/OlfmfeCQUrISKyJjNPqVDrGE4RUVvUWsan9vJApaZkGErJx/eMRCVcMMWpKWvjHRqJiMTCgEQ/or33DErJyrjkiIhIPcIFUyQWo099ERG58Jbw8okWjBCphftMERkLJ3+IzEXIYIpTU0REZHWcTiFAvHCQ35dEYjDq3fiUmDxjKOUb0d4/TiMSIGgwBRg/nOKkERERGZlogYiV8WtBJAZeQOtPtFCFiJQhbDBF4vA2ZOMG6ERExsQghIhIXFaeliIicxI6mDL61BSRr/gzQEREgFhhoVmX83GfKSKxcVpKOXwvSTR2vQsgIiIicYgUgMjxY+Zej4/tnjpAxUrUU7bze4QOHax3GUSWdjh1FnpnPqt3GUREpiJ8MOXs1BX20z/pXYbllR7LQ1iPBL3LIJOxJ3Thkk8iN8w6kaIkOUFUW481alBFRNZj1WV8nPAhMjehl/JpxZ7QRe8SiIhIA8cD+updgtBEn5b6MXNv/YcRnlcNonyNGJ4SmZtIARhDKfPj/mNkiGDKaPvsmPWOfN68Lm+mYTghR0REDWkZGhkloCIiIvIFAz8SiSGCKSIiIlKXKJM4DekZEokcUIn4tTILboBOntBrukOkKSY5fHm/GJ4QWQODKSIiIhKOKKGQKHWIiMv5iIiISAmGCaaMtpzPrMy6TJGIrIP7TIlPtDBItHoATk0RkflxWorIOgwTTBEREZE6RAk5RF4+J3JtRGR+Rl3GR2Jj+EeiMFQwxakpY/JmA3QiIrIWo4Q+RqmTiNRlhbuIKRGGefs+MTCxHiv8TFHL7HoXYDZaLnUrPJjf6t9H94lT5bylx/IQ1iNBlecmIiLrMVrY82PmXnRPHaB3GSjb+T1Chw7WtYaQo9ko7zlE1xqIzI7TUkRkdoaamAI4NeXSVijlOsaT44iIrIb7TF2h9zI+o4VSLkatm4hIdJyWIrIewwVTVudN2GTEgMp++ie9SyAiIpUx3CERdbt0QO8SiIg0wyCQRMBgykB8DZeMFk4RERGJTIRgTe+JNyJSl5GX8XmzZxBDEiJrMmQwZcXlfEqFSko9j9y9tLgBOhGJhsv59CVCqKMEs7wOX4Qczda7BCJdWGGzZiMHY0RkHNz8/DJ7QhdhwxOlJ50KD+artjG62gqKirE1ew9KKyoRFhyEUUOS0DEqUu+yiIhIBj3CnKJL1ci+UILymhqE+PtjSEQ4ogL4m3kiIlGIOi1VUVqAk0e/RHVVGRyBoejc8wYEh3XUuywiU2EwZVFGC6f2HjmOZ17PwAeZ21BdU1v/eYe/H25PHYnfTUvDgF7d9CuQiAzpeEBfS+8nY4VlYEfKK/Ba3klkFZ5DdYPPOwCkRHfA/Qmd0Ssk2Kdz6H2XPhHuzkdEZDZF+XuRnbkcx3avR01tTf3n/f380WPgJAxJnYOoOP3v0EpkBoZcygdYZzmfyPtCyV3O561NO7KR+uvfITtzGxbX1KIAQA2AAgCLa2qRnbkNqb/+HTbt4FICIiLRaTkt9VXxeTz4wx4cKTyHxUDj/gHgSOE5PPjDHnxVfN7nc3FJHxEpjcvo9HPi4Od4f8UYOHevx9Lamkb9Y2ltDZy71+P9FWNw4uDnOldKZA6GDaasQO1QSuTQC6i7M9/eI8dxz5N/RUq1E7trajEbQAzqvnFjAMwGsLumFinVTtzz5F+x98hxPUsmIgPiXlPmdKS8Agv2H0KKJCEHcNs/cgCkSBIW7D+EI+UVOlZLRCQubwIyuftvibSMryh/Lz577R6MdV7Cntoat/1jT20Nxjov4bPX7kFRvvF/MSHS+0/WxGBKQVpNEClJy3DKmz28nnk9A3E1NXhXktDSQotgAO9KEuJqarD8df5miYhIVFpOFb2WdxJxkoQMoNX+kQEgTpLwet4pzWojIvOwwgboVpOduRyda53IQOvXHxmQ0LnWiezMv2tZnqnx58m6DB1MmXk5n+jTTFo4c74EH2Ruw/Sa2habgkswgMdqavF+5pc4e+68BtUREZGoii5VI6vwHGag5VDKJRjAdACZhUU4d6m6jaNbp+dyPivsF0ZkJVzGp4+K0gIc270ej9fWeNQ/ZtTW4NjuD1BRdlaL8ohMy9DBFCnDlxBMzSmxL/YcRnVNLaZ6ePxUANU1tdiavUe1mojInERbzlfec4jeJShOy9Am+0IJqgF5/QPAzpIS9YoyuZCj3OeRiLwj0jKyk0e/RE1tjaz+UVNbg1NHv1SzLCLTYzAlID2mpUSc0CqrrAIARHt4vOu4Eu4TQkRkaeU1dXdPkts/yp01rR7nCW6Cbg5WvlsnkTtWmeCqrioDIL9/XLpYqko9RFZh+GDKzMv5rC40KBAAUOjh8a7jwn287TcRWZNoU1PkvRB/fwDy+0eI3V+VeoiIyBgcgaEA5PePgHZhqtRDZBWGD6ZIOd5OTclZzidnA/Qbk3rD4eeHNR4evwaAw98Po4YkeXwOIqKGGE6Zw5CIcDgAef0DwNDwcPWKIiLykFWmkwCxlvEBQOeeN8Dfz19W//D380d8zxvULIvI9BhMCUbEJXV6iW0fjjtGXI2V/n5oa3FeBYBV/n64I/UGxHRor0F1RETkKa2Xt0UFOJAS3QHpgEf9YyWA1OgodAgQ6wKJiEgkVgjMgsM6osfASXjez9+j/pHu548eA29HcGiMFuURmZYpgiku51OOaMHYvMkTkO/vjyk2W4vNoQLAFJsN+f7+mDNtspblEZEJiTI1ZcYN0LV0f0Jn5NtsSEPL4VQFgDQA+TYbpiXEK3Zu7jNFZC28xb25DEmdg5N+dqSh9euPNNhw0s+OIamztSyPyJRMEUwpxZ7QRdfzixYKiSCpWzzeWvIHZDnsGOjvh+UACgDUXv7f5QAG+vshy2HHW0v+gAG9uulZLhERCaJXSDCe7ncVsmw2JANu+0cygCybDU/3uwq9uD8hEREBiIobgPH3v4VN9gAk+fm77R9Jfv7YZA/A+PvfQlTcAF3rJTIDBlPUjDcBmZx9puQae/0QZP7zGQxNvQEL/P0QC8AfQCyABf5+GJp6AzL/+QzGXs/pAiJShihTU+Sb4ZHt8cqgJPSOjsICoHH/ANA7OgqvDErC8Mj2epapmLKd3+tdAhH5yArL5VxE21+qoS59xuCOmZ/DPvB2POnn36h/POnnD/vA23HHzM/Rpc8YnSslMge73gUoxdmpK+ynf9K7DPKAM++E7Om0Ab264ZW/zMXTsx/C1uw9KCmvQHhIMEYNSeKeUkSkiuMBfXnLeBPoFRKMv/Tphdndu2JnSQnKnTUIsftjaHg495QiIpJp84x1GJ1uja0zouIGYNw9L6Ni0iKcOvolLl0sRUC7MMT3vIF7ShEpzDTBlNGJtoyv8GA+ovvE6V0GAMB++qf6fcRiOrTHnWN51wsisobynkMQcjRb7zJMoUOAA+Oio/Qug4iIDCY4NAa9Bt2hdxlEpsalfApRcykbERHpg0v6iIiIiIjUxWCKWiR3iovhHBGZEcMpIiKiK6y0DxYRacNUwZRruRcREZFZlPdU78YOoUMHq/bcRERWcjh1lt4lEBEZlqmCKaMSbX8pLTjzTuhdAhGRxzg1RUREauMkEhFZFYMpapUooZlV77ho1ddNJCKGU0RERHV8DdFeWs+7ohLRFQymSFHcZ4qIzEyvcErN5XxERERERHryKZhatGgRbDYbZs2apVA5vuM+U0RE4hOxfxARkfh86R+vf2xXviAL49JDIlKK18HUt99+i9WrV2PgwIFK1mM5oiyVa40RaiQi4zB6/zDb1BQ3QCcio1Cif3AJGRGReLwKpsrKynDPPffgpZdeQmRkpFcnLgvv7NXj1GZP6KJ3CZYhdwN07rdEZHxK9A8RcL8pEhFDRuXwZ1w8ZukfRETUnFfB1PTp03HLLbdg7NixbR5bVVWFkpKSRh9kPHKmprjPFBG1xEz9gxeuRETaUbJ/qDU1dTh1lirPK7KWlvP1znxW20KIyNBkB1Nr165FdnY2Fi1a5NHxixYtQkRERP1HYmJi/d+VRiS28kjvcZ8pIiLxKNk/RKF1OGXUTdC7pw7QuwQiMjAz9g8iIrpCVjCVm5uLJ554AmvWrEG7du08eszChQtx4cKF+o/c3NxGf69WOGUE3LuJiKxCjf5ByuESMGUxiCNSjlr9g3tNKYeboBORr2TdmmLnzp0oKCjA0KFD6z9XU1ODL774Aunp6aiqqoK/v3+jxwQGBiIwMFCZaomIyJDM3D+OB/RFt0sHNDtfec8hCDmardn5iIj0pGb/eGm9Aw9Pqla8ZiIikkfWxNSYMWOQk5ODXbt21X8MGzYM99xzD3bt2tWsKXjKylNTRiLChBc3QCcyJrX6hyi43xRZnVGXmZL4zN4/zMLd1BT3mTIOBrSkN1kTU2FhYUhKSmr0uZCQEERFRTX7vFylEYkIu6DcMg1np64MMXRUeiwPYT0S2jzOmXeCd0IksgA1+4cotJycUmNqKnToYJTt/F7R52yoe+oA/Ji5V7XnFwWX8REpS+3+wakpfTw8qZrLKakZhpnW5dVd+YiIiKg5LSenOKFCDXGfMCIxWPHOfEREvpI1MeXO5s2bFSijjtJTU1Z16quCRn+OH95RsecuPJiP6D5xij0fEVmXkv1DJFrvOaUkTk0RkREo3T84NaWMzTPWYXT6ZL3LICID4sSUTtTYr+nUVwXNQqnWPm9UVlmiaZXXSWRGWk1OcWpKLFzGR0TUGJdmEZEnhAumuBG6fJ4GT2YKp8gcnHkn9C6BSDVG3RBd7SVhDG9IdEb92SUSgbtN0ImI2iJcMKUkZ6euepcgHC3DqdJjeR4dx3CCiMxKiwtcTk2JwcqBG78HyQy4Ebd6ODUlNi5jJREIGUzpPTVlpLvEeRM0+RpOqbEMkYjIrBhONWflEEcN3PiciETi6dQUAxEichEymCLP+BIwGX1ZH/dfIiIjMdrSIAYd8jBoIzIHTk0ph0v6iEgOYYMpvaemRKdEsGT0cIqIyEjUDqeUnpriXlNERN45nDpL7xKEw+V8RNQaYYMpIiIiszHa5JTazBBO6f0aON1GpCxOTSnHk6kpLufTl0jvP8NLaxM6mFJiasqMG6ArOenk7XN5us8UN0AnImpMzXDKaFNTgP7Bji+MXLtSjLa/WVsYHhOph8EDEbVE6GDKrLh5uDLMvM+UmV8bERkrnCIiIvIW95oiIk8wmCLuNUVEpAOjhFOcmnJPhJq5jI9IHVzOp6yG4ZS7qSmRlpMRkT6ED6a4CXpjDJGIiMyDy4auECHo8ZSRaiUiInKHgSCJRPhgirThTeDFJYlERL5TK5wy2tQUYIzAR5QaRZiW4rJRopbxznyNcWqKiFpjiGDK16kps2yAbtRpKU83QPcG92IiIjNgOHWFKMGPOyLXRr7jBCM1xOV8ymsrnCLr4vcD2fUugDxTVHUJW6ouoEKqRbDNDwMdIYj0M9eXz5l3AvaELnqXQUSki+MBfdHt0gHFn/fHiG747uP3UFZZhdCgQNyY1Bux7cMVP4+SXAHQj5l7da7kCoZSRGQ1xZITOVIlKlGLIPgh2RaESJt61x8PT6pmIKgRTqiRaMyVbJjQoZIyvHIsF5tOn0V1TW395+1+frjBEYZfBEahu72dIuc69VUB4od3VOS5iIhIPiXDqX2HjuLZl9/ER5s2o7r6yn+AOux23DF8EObdNR5J3eJlPWfo0MEo2/m9IvV5onvqAN3DKREDKS7jIzKn0emThbiL3XGpChlSMbZJZXBKDa4/bH4YaQtFmi0S3WyBXj335hnrMDp9MoC6KRkueSQiwCBL+QDtN0EXYXJn29lzuHfHLhx0BGHx0mUoKChATU0NCgoKsGTZMpzoFInZ5Sfw3aUyvUslBXF5IpG1HQ/o6/OSosxtX+Pmqb/BDwePYvHixY36x+IlS7Dz5DnctGA5Nn6/X/Zzax2KdE8doFs4JGIoRUTaseL0zs7acsyVTiI3sQOWPNPk+uOZZchNjMRc6SR21pZ7fY7WwjdO8hBZk2GCKas5VFKGOd/vQ8q4ccjZuxezZ89GTEwM/Pz8EBMTg9mzZyNn316kjh+HpypP4UfnRV3qFGEDdAY5RGRG3oZT+w4dxf2z/4CUlBTszslx2z925+xBSuoY3L34Few5fkr2OfSY2NEyJNIzDGuLCNNSZsT9pYjqJqUW4QxSJ4xDzr6Wrj/2IXXCOCzCGRyXqrw+lyuc4kbo2uP7SyJiMCWoV47lIq5zAjIy1iE4ONjtMcHBwchYtw7xCQl4t6pIkfMadYN1IiIz8uZi+dmX30RcXBzezchotX+8m5GBuPh4LPv3Rl/L1IzagZHIgZRIuIyPyDNGW6aWIRUjPjEBGes8uP5I7IwMqViR83Lja2vj158AgwVTviznM9Kd+YqqLmHT6bOYMXNmi03BJTg4GNNnPo6t1aU4X+vUqEIiItKKnHCqoPAcPtq0GdNnzPCofzw2fQb+vX0XCs6Xyq5Lz8kdV4CkVIhklECK01JE2rPKcr5iyYltUhlmPOHp9cdMbJPKcF7y/vqjtbv0capHHXxfSVSGCqas4tui86iuqcXUqVM9On7q1Klw1tZid7X3a73VVnosz6PjnHknVK6EiMxo/5lIjz+MyNNwatt32aiurpbVP6qdTnyx57BXdYkQlDQMqTwJmOQeLwoR3muA01JEZpUjVcIpybz+kGqRI1X6dF7uN0VEAO/KJ6RyZ91vHqKjoz063nVceYO7ZvjCiHfns5/+yVBTcUTkvUMFkQgqD/f68a2FU/1ilVmWoAZP7thXVl4BQH7/KK30fp9Cre/U5wmjhE0kBu4vRQRUou46Qm7/qIDv1x+uO/W5u0vfw5OqLTO1RmRlhpuY0vrufHoIsdflhYWFhR4d7zouxGa4Lyc1YaWN3DkdRyISfbqqrQvo0JC65Rdy+0dYUDuf6hJlmses+P4S6csKwUjQ5ctCuf0jWKHLSW6Grj4R30fuL0UuTDI05sld7K6Jag+Hvx/WrFnj0XOuWbMGdj8/DHSE+FqeV0S4Mx8RkRpEDKpaC6dGDhsCh8Mhq3847HbcmNTb57oYnqhDpPeVy/iIzCvZFgS7Teb1h80PybYgxWthOEVkPQymBBQVGICxnWKQvmIFKioqWj22oqICK1c8j1GOMLT348pMIiI1iRJSHQ/o6zag6hjdAbeNHY2V6eke9Y9VK1fi5+NT0LF9mCJ1iRSiEHmCy/iI6kTa7BhpC0X6c55ef6zASFso2tuUu/5obTN0gOGUL/jekegsFUwZaQ+iB3skIv9kHtLSJrfYHCoqKpA2eTJO5eVhSmCUouc/9VWBos+nBSstgyMi/YkQUrm7qJ710L3Iz8/HlLS0VvvHlLQ05Ofn44kH71V0EobhlHJEei85LUVW5+1yvqZ7JokszRaJU7l5SJvswfVHbh7SbMr3P4ZTRNZkyGDKCvtMXRUeiuWD+yNr40Yk9x+A5cuXo6CgALW1tSgoKMDy5cuR3H8AMj/biP8Jikd3u2/7g4iEew8RkdHoGVA1Daf6X9UTr/39r8jKysLA5GS3/WNgcjKysrLw2t//iv5X9QSgbPAgUqBiVHwPiUhr3WyBWIhYZG5o7fqjPzI3fIaF6IRutkBV6vAknGJA5TlR3yvuL0UN2SRJkrQ8YUlJCSIiInDw260ICw31+nnCLuR69Ti5UzWehiSlx/I8Ok7ufkzbtp7Au1VF2FpdCmftlbte2P38MMoRhimBUR6FUj86L+LTqmKUSbUItfnh5sDINh8n58580X3iPDourEeCR8fZE7p4fO6GjDQV15TVJr4YQKqrpLIKneetwIULFxAe7v0d7ETi6h+r/3seQSHivyat7/DX9I59+w4dxXOvvIkPN2ahutpZ/3mHw46fj0vBEw/eWx9KNRRyNLvRn/f8dAqvbtyOC+WViAgJwgPjRiCpa7xHNYl2tz6jEC2UMuu0FJfxuVdWVoLRQ7ubsn889JefENDO+9fk7QW+3AvwhsGMHo5LVciQirFNKoOzwV2/7TY/jLSFIs0W6VEodbz2IjZIJShHDULgjwm2cHTz8/yX6aPTJ9f//5Ymz6ywMb2vGEyRVkoqqxA/+xmv+gc3JRJcd3s7zLd3xm9qndhdXY5yqRYhtrqNzj3ZU+rLqhKsrjyNglpno8+vrypGRz87HgnqhBsCzfEfHUBduGPkcIqIzMM1QaVVQHU8oG+jcKr/VT3x4uI/4aknn8C2b7NRWl6OsJAQjLxmCGKiWp7uKu85BCFHs/HB9l148tV/I/ds4/pX/WcLEmMiseSBO3H7iKtbrSl06GCGUzKJFkoRkfV0swVinq0THpacyJEqUYFaBKNuo3NP9pTaVluKl6WzOCs1vv74SDqPGMmOh2wxGOnX9t6Gm2esqw+nemc+6zaccoUuDKjcEzWUImrKsMFUaUSi11NTRtTez44bAyNkPeZfFWfxetVZIALAdQAGAggCUAlgN1DwtRNPXcjDtJoY/DI4RvmiiYhI04CqaTgFADFRkbj95jGynucvn+fgb+mv1PWPCWjWP3K/LsYvl76CP/3qVsxPm9DqczGcMjazTksReeOl9Q5LXei3t9kxyibv5hjv1BThTRS1eP1x9msnFl3Ix701l/AL/7b3yPUknALqAhiGU41Z6XuVjM+Qe0xR276sKqkLpXoBeAzAcAAhqPuKh1z+82MAegGvV53Fl1Ul+hVLRGQBWu1D5evypI82bsbf0ld71D/+9PZ/8MH2XW0+J6eAPCPa+2TmUIrL+IiUt622tC6U8qB/vIkibKst9eh529pzyoV7TxkHl/FRU5YLpqyyzGt15em631SkAQho4aCAy38fcfn4Jox4Zz6jstr+UkRWpkVAdTygr9cX3v9v6QpZ/WP+q+979LyihS6i4ftDZF5GujOfL16WzsrqHy9LZz1+7qbhFAOq1ln99ZPxWC6YsoIfnRfr9pS6Di03BZcAANcCBbVO/OS86PU55W7qriaGPERkBFoFVHLsO3QUeafPyOofJ86ew74TnvUAhi/uifi+cFqKiOQ4Xnuxbk8pGf3jrOTET7VVHp+j6abwbU3dWDWgsuJrJuMzdDBVGpGodwlC+rTq8j4mAz18wOXj/lul7R2kWsM7thGRVagdUMm5CF/z748ACbL7x6ufbff4HCKGMHri+0FkLNzHyL0N0uVtQWT2jw3SBVnnkRtOAdYNqETFZXzkjqGDKXKvzHVb12APH3D5uNIGt4MlIiJtqRlQeRpOXSi5vN+HzP5RXF4hq57QoYMtH8iI/B5wWoqI5CpHTd3/kdk/ylyPk8GbcAqwRkBl9tdH5sVgyoRCbZe/rJ5eJ1w+LszGbwc9cOkhETWkVkDlyQV5RPjluy/J7B+RIZ5eiTQmajCjNpFft5lDKSJSTwj86/6PzP4R6nqcTN6GU4B5AyozviayDt2SiHP+sXqd2vRuDrx8QbPbwwdcPm5ioPp3i9IKwx6xcakmUdvUCKjaCqem3nkbYIPs/vHA+BFe1yRySKMGq71ekXBainK+3KN3CaY1wRZe939k9o8Jtgivz+kunLJqQGWU18FlfNQSjsi0wZ7QxaPjwnokqFyJ57rb26Gjnx34GsClNg6+BOAboKOfHV3t7VSvrfRYnurnICIyEy3Dqf5X9URCp1hZ/aNLTAf07xLnU00iL2tTkuivkdNSRJ5Re5+p0emTVX1+NXTza4cYm7zrjxibHV39An06b9NwCpAffrgCKqOEO00ZtW6ihnQNporsnXx+Dm82QHd26urzeUX3SFAn4AKADLTcHC5d/vsLl48nIiIhKT091Vo49Zd5M2X1j8UP3KFYXaIHN94yQvDGUIqs4octP+hy3sOps3Q5r5YessXI6h8P2WIUOa8S4ZSL0QIqI9XKaSlqDSemTOqGwHBMC4wBjgBYBWA7gDIAtZf/d/vlzx8BpgXG4IbAcP2KVYkRlvMZoUYiEoeSAdXxgL5uA6rbxo3G72c84lH/+P2MR3D7iKsVqcfFCCGOp4zyWqwQSnEZH5H6RvqF4V5EedQ/7kUURvqFKXZuJcMpwBgBlej1Eclh17uAInsnRDlP612GKf0yOAaJ/oFYXXIaBZ85gc8a/31HPzseCe5kylCKiMjM9p+JRL/YYkWe63hAX3S7dKDR5+Y8Mg29u3fF/y57HrkbTzfuHzYgMa4T/vy/j+O2caNRDiDkaLYitTTkCnTKdn6v+HNrwQiBFGCNUIqoqR+2/IBBNw3SuwxT+oV/FBJqA/DyhbM46+b6I8Zmx0O2GEVDKZfNM9Y1WwbpCqe8nVhrGP6ovYRTDoZSZDa6B1NWE90nDoUH8zU73w2B4bghMBw/OS/iv1XFKJVqEWbzw8TASE32lPKFM++Ex3t8ERFZjWtySomAyl04ddu40bht3GgcOPIj3nzvQ5y/UIL2EeG4966fo2+v7o2OLe85RJVwCjBeQGWUQMpKOC1FpK2RfmEYiTD8VFuFDdIFlKEGofDHBFuEz3tKtcVdOAXUBVS+Lqd0hUF6B1RGDKW4jI/aIkQw5evUVGlEIsIu5CpYkfl0tbfDo3bfNqclIiLxKDU95S6cAoC+vbrjr/OfaPPxaoZTgPgBlREDKStMSzGUIrW8tN5hyIBAS139AvEIOmp+XjXDKUC/KSp+v5GZcY8pMjWR93ASuTYiMhal9p7y9SJei6BDpH2bXLWIUo8cVgiliFqj1ybopA13e04Byk/uaLEXlRH2u2oNp6XIEwymiIiITMIq4RSgXyhk5DDKxSqhFKeliKxNq3AKuBIeKRkgGT2QIpJDiKV8gPaboDs7deXEChERmY4SS/taWtbnKVfwoebSvoaahkRKLvczcgDljlVCKSJPaL0J+uHUWZwe0Vhry/oA7zdFb03TMEnOcj+zBVH8fidPCRNMEanFfvonODt11buMRhiKEpGalNgY3ddwClB/36mWmC1MUoqVQilOSxGRJ5Tad6o1ZgubiNRgmqV8pRGJqj037wxHpBxn3gm9SyBSzJ4DVW4/ROHr0j4lLu6tFIaIzEpfB4ZSpBU1N752N+VD3mlpSZ8Lp3qI9CcrmHrhhRcwcOBAhIeHIzw8HMOHD8d///tfxYopsndS7LmIiEgcavcPtckNoFo6Xo/QSolwyij7TpF7fP/JyNTuH9wE3RoYTmmP7ynJISuYSkhIwNNPP43vvvsO3333HVJTUzFp0iTs3btXrfqIFCHS0jmRaiHSihH7h1phkh5BlRJ37WM4ZUxWe985LWU+RuwfJCaGU0TikrXH1G233dboz3/961/xwgsvYMeOHRgwYIDbx1RVVaGq6sp/eJeUlHhRpjGE9UhA6bE8vcsgIhKOkfqH1lNNDc+X1DdQ1XP5ujG60TZFtzKrBVIAQymzMlL/IPG1tBm6ixZ7TlkBQz6Sy+s9pmpqarB27VqUl5dj+PDhLR63aNEiRERE1H8kJra+FxSX85GZcVqKSL3+4SsR9ofSYpJK78kpwJqhiZb4/pJZqdU/uJzPWjg5RSQe2cFUTk4OQkNDERgYiEcffRTvv/8++vfv3+LxCxcuxIULF+o/cnNzfSpYSaLdqY3UxVCISF+i9g8RAil31KyL4ZR5WfV95bSUuYnaP7zBaRxjYDjlPb531vXl3A+8fqzsYKpPnz7YtWsXduzYgd/+9reYNm0a9u3b1+LxgYGB9ZsVuj7Uouad+UgfvIMbkXmI2D9EDKSaUiug8nXfKYZTYinvOcSy7ydDKfMTsX+4qHlnPlJPW1NTAAMWb/A9I2/JDqYCAgLQq1cvDBs2DIsWLcKgQYPw3HPPqVEbkalwYousTqT+IeqUVGvUDKi8pVQ4ZdVARSl8/8jsROofZB4Mp4jE4fUeUy6SJDXaXFAJ3GfqivjhHfUuwVQYDhGJQ43+4QmjBVJNqRFQ6R1OAQxXvMFQj9NSVqVG/+A+U9QShlOe4ftkbZ4Eva2RdVe+3//+95g4cSISExNRWlqKtWvXYvPmzfj00099KsII7AldFFtWFt0nDoUH8xV5LgAornVid3U5KqRaBNv8MNARgkg/WV9aUhkDMbI6UfqH0UOphlyvxZc7+V04dwb7v9+Mi5Wl2BEUhp+PvxpR0fJ/IeLr3fpceNc+z1k9kAIYSlmFKP1DD6PTJ/t8saeWYsmJHKkSlahFEPyQbAtCpM141x9t3aXPhXfrI1KXrH89zpw5g3vvvRf5+fmIiIjAwIED8emnn2LcuHFq1Uet+NF5Ee9UFeHL6lI4a2vrP2/388MNjjD8IjAK3e3tdKyQqDHuGWZdIvQPM4VSDXkTUOUezcFHbz2Nbzevg9NZXf/5F//qwJibf44HHpmFXn1a3ljYHaXCKYABVWsYSNVhKGUdIvQPuuK4VIUMqRjbpDI4pQbXHzY/jLSFIs0WiW42739hIjKGUy3jtBT5StZSvldeeQXHjx9HVVUVCgoKsGnTJjYFnXx3qQyzy0/gRKdILFm2DAUFBaipqUFBQQGWLFuGE50iMbv8BL67VOb1Ocy6jJDTS0TaY/9Qn6fB2+5vNuD/fjscZ459gyVLFjfuH0sW4/C+XXjgFxPw1dZM2TUoHRYwhLmCy/auYChlLewf4thZW4650knkJnbAkmeaXH88swy5iZGYK53EztpyvUuVRc5UGgMYouaUmOz0eY8ptXCfqZb96LyIpypPIXX8OOTs24vZs2cjJiYGfn5+iImJwezZs5Gzby9Sx4/DU5Wn8KPzot4lWxqDMCL9mXVaqqm29p/KPZqDFf9zF8akpiAnZ7f7/pGzGykpKXjy8Wk4crDlu161RI1wysqBjNVff1MMpUhN3GeqZcelKizCGaROaO36Yx9SJ4zDIpzBccm8fZfhVGN8P0gJwgZT3iqNSJR1vLNTV0XPH9YjQdHnc+edqiLEJyQgY906BAcHuz0mODgYGevWIT4hAe9WFaleExGRqKwSSjXU0mv+6K2n0Tk+DhkZ77bePzLeRVxcHF5b7d1dr9QID6wU0Lheq1Ver6cYSpGoXlrvkHW8EZeDZUjFiE/08PojsTMypGKNK/SN3IkPhjF1+D6QUkwXTJlRwyV1xbVOfFldihlPzGyxKbgEBwdj+szHsbW6FOdrnWqXaShaTTFxWoqI9NJ0eurCuTP4dvM6zJgx3bP+Mf0xbPp0Pc4VnfXq/GqFCGYObcz6upTAUIpIP8WSE9ukMhnXHzOxTSrDeYnXH0TkGQZTBrO7uhzO2lpMnTrVo+OnTp0KZ20tdleru9Y7uk+cqs9PROQNK05LNeV6D/Z/vxlOZ7W8/uGsxoef7fL63GqHCWYIcswctCmFoRSRvnKkSjglmdcfUi1ypEqVK1MWp6bksfrrpzpK3TmUwZROvA1yKi7f/SI6Otqz81w+rrzBXTM8YdaNzxtSe5qJ01JE+mIodcWeA1W4WFkKQH7/qKwowf4zkV6f+3hAX80CKqOEO0arV08MpYj0Vwnvrj8qIO/6w4isGs5Y9XWTeoQOprgBenPBtrovWWFhoUfHu44LsQn9pSYiIpWdLW4HQH7/CAoOBwCfwilAu4ChYegjQvDTtB4RajIKhlKkB26A3lwQvLv+CBb7UlMxDGmIfGeNfy0UYk/ootu5XRNMAx0hsPv5Yc2aNR49bs2aNbD7+WGgI0TN8gxLrakmTks158w7oXcJRJbWpc+N8Lc75PUPuwN9B4+u/5xRwqmG3AVDaoRDWp3HKhhKEdUZnT5Z7xKQbAuC3Sbz+sPmh2RbkMqVKc/bZUlWCqes9FqpdUot4wMAu2LPRJqI9LPjBkcY0p9bgd/85jetbkBYUVGBlSuexyhHGNr78UutFYZSRPrjMr7mQsJj0WfIHXg+faVn/WPlKlw7Og0RkY2Xdu8/E4l+sd7fbel4QF90u3TA68crhaGRuBhKEYkl0mbHSFuojOuPFRhpC0V7G68/zIahFKmFE1MG9IvAKJzKy0Pa5MmoqKhwe0xFRQXSJk/Gqbw8TAmMkvX8DfeXKqq6hE9PFeC9E6fw6akCFFVd8ql2ETFIIiKruP7meTh5Mh9paVNa7x9pU3DyVD5uvWe+22M8mZwqKizAZ5+8jw8y3sRnn7yPosKC+r9j8EAt4fcGkZjSbJE4levh9UduHtJs3k/YFktOfFFbig21F/BFbSmKDXJ3P4Y2ZCVKTksBJp2YKo1IRNiFXN3OH9YjAaXH8lR7/u72dvifoHg89dlGJPcfgOkzH8fUqVMRHR2NwsJCrFmzBitXPI9TeXn4n6B4dLe3k32OQyVleOVYLjadPovqmisbFzr8/TC2Uwwe7JGIq8JDlXxZpsCQi4hasj+79X8f+g3pqnoNMQlJmPToWqz/x91ISh6IGdMfa94/Vq7CyVP5mPnUe0jsmdzic7U0OXXk4D68uvpZZG74ENXV1fWfdzgcSJ3wczzwyCz06tO/PoAQYXqKxMBQiozspfUOPDypuu0DDaqbLRALpVgs2tDa9ccKnMrNw0J0QjdboOxzHJeqkCEVY5tUBmeDGzfZbX4YaQtFmi3Sq+fVUu/MZ3E4dZbeZaiCwRupiRNTAJyd1L8YcEfunfkaTjINCwjF30O6oOvpYsyfNw+xsbHw9/dHbGws5s+bh66ni/H3kC4YFiAvPIof3hHbzp7DvTt24aAjCIuXLkNBQQFqampQUFCAxUuX4aAjCPfu2IVtZ8/Jfh1hPRJk1aMVJQIlhlJEYhBpGd/+7J/qP+Qc6+ljvNFjwDhMnb8FQdHD8OT8BY37x/wF6NTjOvzxha8w8NoJbdfcZHLqq62ZeOAXE3B4/y4sXry4cf9YvBiH9+/CA7+YgK+2ZtY/hmEEaXHnRiLRGDG8GOoXgmW2zkjMPYf5c5tcf8ydh8TcYiyzJWCon/y9bXfWlmOudBK5iR2w5JnG1x9LnlmG3MRIzJVOYmdtuQqvjNrCUIrUZpMkSdLyhCUlJYiIiMBX2YcQGhbW5vFRztNenUfuxJSnoYKnGzh7OjFVeDDfo+NcTn1V0Oxz52ud2F1djnKpFiG2uo3Ovd1TqmxAMO7dsQsp48YhI2Od2zXkdcs8JiNr40a8ef3VGHFNb4+f35tgSqtN530NKBlMtY6bn2urpLIKneetwIULFxAeHq53OYpw9Y/V/z2PoJCWX5PewZRagZIaE1XlJQU4cegLXKosRa+eHdB38Ohme0p5VFtsMY4c3IcHfjEBKSkpyMh4t5X+MQVZWVl49Z0N6NWnf6O/5/SU9TCQEk9ZWQlGD+1uyv5x3YSPYffghkCDbhrk1XnkTkx5erGv9JIZJZyXnMiRKlGBWgSjbqNzb/eUOi5VYa50EqkTxiFjXSvXH5MnI3PDRiyzdVZ1ckqJDeeNGDy2hKEUNdXSv0kVUg2m1Bz1qn8IPzFVZO+kdwlCaTg15dLez44bAyMwsV0kbgyM8Gmj81eO5SKuc0KLoRQABAcHIyNjHeI6J+Cfx/RbMqk0X4IlhlJEpOaUk1rPHxLeEf2GTcagUQ8gJH6SV6EUUDc59erqZxEXH9diKAW4+se7iIuPw2urn2v29wwprIVfbxLVD1t+0LsE4bW32THKLwwT/CIwyi/Mp43OM6RixCcmtBhKAZf7x7p1iE/sjAzJ+xtweEKJINAsYY5ZXgeJT/hgSjRaTe/oIXBIe2w6fRYzZs5s9W4bQF1zmP7449h4+iwKKy5qVKH6vAmYGEoRkZqBlLtzqXU+b6fNLpw7g88//RAzpk/3rH889hg+37Ae54rONvt7LusyP36NieRRYoJHVMWSE9ukMsx4wsPrj5kzsU0qw3mDbIhORJ5hMGVA7qamlPBt0XlU19Ri6tSpHh0/depUVNfUYntu8+WFRiYnaGIo5Rku4yOt6LGMT8tQqul51Ti3N+/h/u83w+msltc/qqux8+ttLR7D8MKc+DUlooZypEo4JXnXH06pFjlSpcqV+c7o00ZGr5/UodbSYgZTKvF0LyW5G6CrJX54R5Q7637zEB0d7dFjXMeVXjLfHUjaCpzsp39iKEVEuoVSatcgN5y6WFkKQH7/KC8vbfNYBlTmwK8jEblTibq778ntHxWobeNIMRg13DFq3WRcDKYMSsmpKddzhdjr1oYXFhZ69DjXcWEBDsVqEYm78ImBFBG5iBBKuagxPSUnnGoXVHczE7n9IySk7ZuguDDYMCZ+3YioNUGXL0fl9o9gA13GGi3kMVq9ZA7G+Ykm1V0T1R4Ofz+sWbPGo+PXrFkDh78/RiR6FpJ5c0c+EbjCKAZSROQiUijVkF519Rs8Gna7Q17/cDgw9LqRss/FoMM4+HUiorYk24Jgt8m7/rDb6u4CSMpjKEWtUfMOoQymDEyJqamGzxEVGICxnWKQvmIFKioqWn1cRUUFVj7/PG7tnYDo4HY+10FEZBSihlIuSk5PeTo1FdEhFteMnoz09JWe9Y9VqzBmwiR0iIrxujYGVOLi14aIPBVps2OkLRTpz3l4/bFiBUbaQn26C6AejBD4GKFGMi8GU5c5O3XV7dy+7DPlSzjl7rEP9khE/sk8pKVNbrE5VFRUIC1tMk6fOokZ1/T3+vxEREYjeijVkNbh1G33LMDJU/lIS5vSRv+YgvxT+bj/kScUqc8VgjAI0Re/DkTyHU6dpXcJQkizReJUbh7SJrdx/TF5ct1xtkiNK1SGyMGPyLWRGNSclgIYTJmCN+FUS4+5KjwUywf3R9bGjUgeMADLly9HQUEBamtrUVBQgOXLlyN5wABkbdyIl28diX4x7X2snoiI1KJlOJXYMxkzn3oPn2dmITl5oPv+MXAgsrKysOT519Grj/K/2GA4oj2+30Tkq262QCxELDI3bERy/xauP/r3R+aGz7AQndDNFqh3yabCUIpEwGDKC/aELh4dp+WeSnLCqbaOHRnTAW9efzX6VldiwZPzEBsbC39/f8TGxmLBk/PQt7oSb15/NUZ3U/+Ogp6+1yQuZ94JvUsgUoSRpqUa0jKcGnjtBPzxha/Qqcd1mD9/QaP+MX/+AnTqfh1efWcDho9KVaSm1jCkUhffWyJtjU6frHcJqhrqF4Jlts5IzD2H+XMbX3/MnzsPibnFWGZLwFC/ENVrUfO9Fi0EEq0esi5jLc6VoTQiEWEXcvUuQ1OuwOnUVwWt/r0nrgoPxdNX98O8qp74rug8ypxOhNrtGBbVHlGBAT4tPyQiIm3tz/4J/Yb4vmR9z4EqJPVt/TfViT2T8dv/XYNfPb4cB77fjMqKEgQFh6Pv4NGIiOyIagD7zwD9Yot9rsdTDQOUbpcOaHZes2EQRdS6l9Y78PCkar3LMKxutkDMs3XCw5ITOVIlKlCLYNRtdG60PaVa0zvzWd2XcTKQIjnUXsYHmDiYMproPnEoPJivyHMpsSm6S1RgACbE+/58Rr0jHxERYNxpqYa0DKcAICKyI65LndJyPWciNQ2nXJqGKwyqWscwioi01t5mxyhbmN5lqErPcIqhFImIS/mIiIgsQusN0duy/4z+G9g2XPLH5WnN3w8iIlKHHgERQymSS4tpKcAgE1NF9k6Icp7Wuwy6jMv4iIiMS+vJqTbruRxO6TE91RJ3gYxZJ6sYPhERWQNDKRKZIYIpIwvrkYDSY3keHavkcj4iIlKGGZbxNSVaOAWIGVA11FqAY5TQiiEUked+2PIDBt00SO8yyOS0WNLHQIq8pdW0FMBgimTyZlqK+0sREYlHxHAK0G/vKV94EvhoEV4xeCIiMh5XcKR0QMVAioyEwZSX7Ald4Mw7oXcZRELjzwiR2EQOpwBxp6e8wdCIiHw1On2yphMMpC0lp6cYSpHRcPPzBpydfP+Pc19x/yYiItKSaBuiN7T/TKQQG6QTEalFrzuzkZh8CZR6Zz5b/0HkK61DcE5MaUDOPlN6Kqq6hG+LzqPc6USI3Y5rotojKjCg/u+1Ds3sCV00PR8RkVX5OjlVXnIGJw5+gR+2liGgXSjG3zIOER1ilavPhBNUREQEFEtO5EiVqEQtguCHZFsQIm36XaKOTp+s27ld5C7tYxBFZsBgSkBab4J+qKQMrxzLxabTZ1FdU1v/eYe/H8Z2isGDPRJxVXioV8/N/aWISEtJfQNVmdyxAm/CqbN5e7Dj06U4mP0+apzV9Z//+FUHrh09GbfdswCJPZOVq5EBFRGRKRyXqpAhFWObVAandOX6w27zw0hbKNJskehmU26JuBExcCK96LFkmEv5LG7b2XO4d8cuHHQEYfHSZSgoKEBNTQ0KCgqweOkyHHQE4d4du7DH2n2BiCzKjHfka42c13ts70asWXwTKgt3YumSxY36x9Ili3H66Df4v98Ox+5vNihfJ5f4EREZ1s7acsyVTiI3sQOWPNP4+mPJM8uQmxiJudJJ7Kwt17tUItIIgylBabFs7lBJGeZ8vw8p48YhZ+9ezJ49GzExMfDz80NMTAxmz56NnL17kTpuPB76zzbsP3te9ZqIiEhfnoRTZ/P2YP0/7sbYMSnYk7Pbbf/Ys2c3xqSmYMX/3IXcoznq1MqAiohUMuimQXqXYErHpSoswhmkThiHnH0tXH/s24fUCeOwCGdwXOIUNJGW9LrBgiGCqSjnab1LcEvOHkgiLml75Vgu4jonICNjHYKDg90eExwcjHczMhDXuTPSv90n6/lFfM1ERNS2tsKpHZ8uRefOccjIeLfV/pGR8S7i4+Kw5oW/qVFmPQZURGQVIuyB5IsMqRjxiQnIWNf69UfGunWIT+yMDIlLt4mswBDBlFWpOTVVVHUJm06fxYyZM1tsCi7BwcF4bMbj+M/hPBRWXFStJjIXZ94JvUsgIhWUl5zBwez38fiM6R71jxkzHsPB7H/j629yVa+NARURkbiKJSe2SWWY8YRn1x/TZ87ENqkM5yWnRhUSWZte01IAgynL+rboPKprajF16lSPjp86dSqqa2qwPbdA5crq8I58ROStpL7cFE8JLU1NnTj4BWqc1bL6R42zGicOfaHZxvSugIohFRGROHKkSjgledcfTqkWOVKlypXVMfo0GpGRMZgSnFpTU+XOut88REdHe1bH5eNKL1W3cWQdLuMjIjI+d+HUpYtlAOT3j0uVpQCAPQeqNL1zIgMqIiIxVKLu7nty+0cFats4koh8pee0FMBgSlPehjVqhFMhdjsAoLCw0KPjXceFBTgUr4WIiMTVNJwKaBcKQH7/CAgKa/R5LcMpgAEVEZHegi5fesrtH8G8ZCUyPf6UG4TS4dQ1Ue3h8PfDmjVrPDp+zZo1cPj7Y0RiR0XrICISWb8hXfUuQQgNw6kufW6Ev90hq3/42x3octWNzf5O6+kpgMv8iIj0kmwLgt0m7/rDbvNDsi1I5cqIrE3vaSmAwZTPjLoXUlRgAMZ2ikH6ihWoqKho9diKigqsSn8et/ZOQHRwuzafm8v4iEhv3GdKea5wKiQ8Fn2G3IHn01d61D/S01ehz5A7ERLe8i82tA6nXBhQEZERGXUvpEibHSNtoUh/zrPrj5UrVmCkLRTtbXaNKiQivZg2mAq7oP7df7zhS2ij9NTUgz0SkX8yD2lpk1tsDhUVFZiSlob8kycx45r+ip6/JUYN++gK3pGPyJxc4dT1N8/DyZP5SEub0mr/SEubgpOn8nH9zXPbfG49pqdcGFARkRIenuTZXqxWlmaLxKncPKRNbv36I23y5LrjbNr822zUsI/ILEwbTJmVkuHUVeGhWD64P7I2bsTApAFYvnw5CgoKUFtbi4KCAixfvhwDkwYga9NGvHzrSPSLad/mc3JaiojI3PZn/4SYhCRMenQtNn2ehaTkgW77R1LSQGz6PAuTHl2LmIQkj59fr3AK4DI/IiK1dbMFYiFikblhI5L7u7/+SO7fH5kbPsNCdEI3GyegidQkwjI+gMFUM85O4u8nomQ4NTKmA/5z9zgMDJSw4MknERsbC39/f8TGxmLBk09iYKCEj34xFqO7qXN3QCIitXA5n3r2Z/+EHgPGYer8LQiKHoYn5y9o1D+enL8AQTHDMHX+FvQYME728+s5PeXCgIqItHI4dZbeJWhqqF8Iltk6IzH3HObPndeof8yfOw+JucVYZkvAUL8QvUslIo1wwa4OwnokoPRYnk/P4QqnCg/m+/w80QBW/mwE/q/iIrbnFqD0UjXCAhwYkdjRoz2lXJSYluIyPiIiY9if/RP6DUnCbQ+9htSSJThx6AtcqixFQFAYulx1Y6t7Snlqz4Eq3QNGVzjVL7ZY1zqIiMykmy0Q82yd8LDkRI5UiQrUIhh1G51rvacUl/GRVYkyLQUwmFKEPaGLbnvqeBtQuZu6ig5uh5/3YTBERNRQvyFdG92Vjq6oC6e6IiS8I/oNU+c/7F2TUwyoiIiuGJ0+WaiLSm+1t9kxyhamdxlEpDMu5dOJ0nsxRfeJa3OJn+sYpTdRB7i3FBGJSe8wwwr2Z/+kSXCn99I+Fy7xIyIiIqMTLdiWFUwtWrQI11xzDcLCwtCxY0fcfvvtOHjwoFq1AQCinKdVfX6zaRg+Nf0g0gLvyEfu6NE/zKYoT+x+qFU4JVpAxZCKSF169I9BNw1S9fmJiKgxWcHUli1bMH36dOzYsQMbN26E0+nE+PHjUV5erlZ9pmaWKSOlXgf3lyIyL/YPZTCcqiNKOOXCgIpIPewfpCbuL0VWJNq0FCBzj6lPP/200Z9fffVVdOzYETt37sSNN97o9jFVVVWoqrryH5AlJSVelElEREamZ/9I6hvoc5Ahwj5TUQmdUJR3GkV5pxGV0EnXWlrj2ndKbaLsPdUQ96EiUh6vP4iIlCNiKAX4uMfUhQsXAAAdOnRo8ZhFixYhIiKi/iMxMdGXUwrL22kfo09NGb1+ItIH+4d3ohI6CR1KuWgZ4ok2PQVwgopITewfV3Dah4jMwutgSpIkzJkzBzfccAOSkpJaPG7hwoW4cOFC/Udubq63pyTBKBlKcRkfkXWwf1iD1uEUAyoi82P/ICUx2COrEXVaCpC5lK+hGTNmYPfu3fjyyy9bPS4wMBCBgdqO2YddMFbzCeuRgNJjeXqXQeQzbnxOntCjf5hlOZ/RaLWsz2XPgSqhlva5cIkfkTJEvv54eFK1pucjIjITr4Kpxx9/HB9++CG++OILJCRwKZcVhfVIQEFJObYezkVp1SWEBQZgVO9EdAwPkf1cnJYisg72D+txF06Vl5zBiYNf4NLFMgS0C0WXPjciJDxWkfOJuPeUy/4zkQyniLzE/kEAUCw5kSNVohK1CIIfkm1BiLTJv6TltBRZjcjTUoDMYEqSJDz++ON4//33sXnzZnTv3t3rE2/67GPccNMYREfHtHhMlFPsuw8pyUhTUycCA/HMa//BB7sOodpZU/95h90ft199FX437joM6Nzy15WIrEfJ/vHtlvcw6PqfIaKDvCCDU1P6cYVTZ/P2YMenS3Ew+33UOK9MF/jbHegz5A5cf/M8xCS0vDxHDk5PEZmDkv2j8PQX6NDxOgQEtrw/1aCbBnn9/KSe41IVMqRibJPK4JRq6z9vt/lhpC0UabZIdLOJ928+EXlG1h5T06dPx5o1a/D2228jLCwMp0+fxunTp1FZWSn7xP9vwWyMv3Eonpz9GA4d3C/78SLydfLHCBuJf32xGqnL30Z20UUsXrIUBQUFqKmpQUFBARYvWYrsootIXf42Nu37Ue9SiUggSvaPl55+ELMmd8GqP9+D3KM5KlRLatiyfg3WLL4JlYU7sXTJ4kb9Y+mSxag8uxNrFt+EY3s3KnZOEfedcuH+U0SeUbJ/HP1hMXZmTsGh7/+C8pJjKlSrPStM/uysLcdc6SRyEztgyTPLGvWPJc8sQ25iJOZKJ7GztlzvUomEJPq0FADYJEmSPD7YZnP7+VdffRX333+/R89RUlKCiIgIHD16FOvXr0f6ypXIP5WPv698GSNHpTQ61tuJKbl7TBUUFWNr9h6UVlQiLDgIoxPbI7Z9uFfn9nWPHZGnpvafPY/b3v0cKWPG4t2MDAQHBzc7pqKiAlPS0pD1+SZkzvlVm5NTXMZnLtxjShwllVXoPG8FLly4gPBw7/49U5Iq/SN9JU6eysfMp97DwGsneFyLr2FFw4mpixWFKDr1DZzV5bA7QhAVfy3aBUf79PxmVFJ0ENvW341xY1OQ0Ur/SEubgk2fZ2Hq/C2KTU65iDg91RAnqEgUZWUlGD20u6n7x4oV6cg7eQpXDf4zIjte2+hYbyem5O4xVVRYgO++3o7y8jKEhIQirXoPYsNDvTq3ES46vXVcqsJc6SRSJ4xDxrp1LfePyZORuWEjltk6tzk5ZYUwj6ghrf6NqJBqMKXmqFf9Q1YwpQRXY3AVW1FRgbQpU5CVmYU1Gf/BVX361R+rdjC198hxPPPGOnyQtR3V1c76zzvsdtwxfBDm3TUeSd3iZZ1biQtzUcOpJ77Yheyii9i9Z4/bpuBSUVGBgUlJGBrVDq/cf2urz8lgylwYTIlDtGBKCW77R9oUfJ6ZhT++8BUSeyZ79DxKTNF8vfEzHPl+NfJ//Aw1NQ2WpPk7ENd9PHoNfgThUX18Po9ZZG/6Hew1B7B3z+42+0dS0kAExQzDbQ+9pngdDKeI2iZaMKUEd/1jcloaNm7MRNLwVQgJ71F/rNrB1OGD+/DPF1fg8w0fobr6ymMcdn/cPrgvfnfzcCR17ijr3GYOppbWnkZuYgfk7NvbZv9I7t8fibnFmOfXqdXnZDBFVqLlvw++BFOylvKpITg4GBnvvou4+Di88uLzKCw8i08/Xo/33n0L6z/ZgLOFRbKez9NQatOObKQ+/CSyj+Rh8eIlTZakLcHOk+dw04Ll2Pi9vGWGSgQtIi7pq4yOxAe7DmH644+32hSAuq/pYzNm4P1dh3C2tOWRWoZS5sJQirQWHByMjIx30Tk+Dv95azEunDuDHZ+/g83/eRk7Pn8HF86dcfs4X8OJY3s3Ytv6u2GvOYClS5ssSVu6GPaaA9i2/m4U5G716TxmcbGiEPk/a6vLEgAALtlJREFUfoaZj0/3qH/MmPEYDmb/G+UlBYrXIvLSPoDL+4i0EhwcjHUZGUjoHI+TR9/GpapzOHsqE6dP/AeHd/0bFaXy/v3xNJTavjUT06b8DIf2/4DFixc33xKjsAKpS97Apr3ylhmaNWgplpzYJpVhxhMzPeof02fOxDapDOclZ4vHmfW9IjI63SemXObNm4fly5fDz98fzoa/PXA4cMv4MXj8kV+j31W923x+T4KpvUeOI/XhJ5GSktr2krTMz7Hl6TmyJqeUukAXZXIqrEcC3tt5APe/+hEKCgoQE9P2xuYFBQWIjY3F67++DXcO6ev2GAZT5sJgSixWmJhyqe8ffv5wNthQ22534JrRk3HbPQuaTVN5G1CczduDNYtvwtgxKcjIeLeVJWlp2LgpCyMnrbX85NTJI59g56Y5svvHzx95E/2GqXMBIfrkFMDpKdKPFSamXObNm4dnnlkOm80PtbVXwgx/uwM9kn+OISmzERU3oM3n9ySYOnxwH6ZN+RlSUlrvH/VbYjx5n6zJKTNOTX1RW4oltfmy+8d8vziM8gtzewyDKbISrf9dMPTEFABs2LABq1atQmJiIpY0/e3B4sXIOXAIt909DZu/3K7I+Z55Yx3i4uJbDKWAutT93YwMxMXHY9m/lduIVQ4RJqdcNZRWXQIAREd7tneK67iSi5fc/j1DKSJSQqP+0WRD7SVLFuPMsW/wf78djt3fbGj0OG+DiR2fLkXnznEtXlQArkmuDHTuHI8ju17y6jxm4qyum5yV2z8uVZaqVpPok1MAp6eI1ObqH126JGLZsiXNbsjgPL8L768cjxMHP1fkfP98cQXi4tvuH3XXH52xfMNXipzXyCpRd/c9uf2jArVu/56hFJG4dA+mcnJycOeddyIlJQX79u3D7NmzERMTAz8/P8TExGD27NnYvTsHKSkpeGjmXOw/dNin8xUUFeODrO2YPmOGZ0vSps/Av7fvQsF59f4DuTV6hlMNzx0WGAAAKCws9OixruPC2wUoXxgRETzrHzk5uzEmNQUr/ucun+/gV15yBgez38fjMzxbkvb4jMeQf2wDqirlLUk3G7sjBID8/hEQ5P633UoxQjgFgOEUkQo86R979uzG2DEp+OzN+1CUv9en8xUVFuDzDR9hxnTP+sdjM2bg/ewDKCix9l3mgi5fqsrtH8H6X+IS6c5oU5S6/9QuWrQIcXFxePddT357EIf0l1716Xxbs/egutqJqVOnenT81KlTUe104os9ngdiSk8DaR1OhfVIaHbOUb0T4bD7Y82aNR49x5o1a+Cw+2NU78Rmf8dpKSJSgqf9o+E+VA3JnZo6cfAL1DirZfWPmppqFJ78RtZ5zCYq/lr4+ztk9Q9/uwNdrrpR5cqMFU4xoCJSjtz+kZ31rE/n++7r7aiultc/qp012Hrop7YPvsyM00DJtiDYbX6y+ofd5odkW1CzvzPj+0NkJroGU2fOnMG6desw3dPfHjw2Hf/ZsAmFRefcHuPJ/lKlFZUA5I+EllZe9Oh4tbgLi9Q6jzsdw0Nw+9VXYeXzz6OioqLV56ioqMCq9HTccfVViAkLUaNMEgz3lyKtye0f06c/hm82Z+BCsfcbal+6WAZAfv9wVpd5fU4zaBccjbju47Hi+ZUe9Y/09FXoM+ROhITLuyuVt4wSTgGcniJSgtz+MWPGYziWsx4VZWfdHuPJ/lLl5d71j9IWtsSwikibHSNtoUh/boVH/WPlihUYaQtFe5tdowqJxGS0aSlA52Bq8+bN8n97UF2N7d985/U5w4LrEnS5I6FhQe28PqeS1AqnPAm+fjfuOuSfOokpaWktNgfXpo35p05izrjrmv09p6WISAne9A+nsxoHvt/c6PNypqYC2oUCkN8/7I5Qj89hVr0GP4KTJ08hrY3+kZY2BSdP5eP6m+dqWp/RwikGVETe86Z/1Dircerol16fMyTEu/4Rxi0xkGaLxKncPKRNntx6/5g8ue44W/N/HzktRVZixFAK0DmYKi2t27dJ9m8Pyrz/7fOoIUlwOOwyl6TZcWNS23cEbEjNAEbJ6Sk5zzWgcwzeevDnyPp8EwYmJWH58uUoKChAbW0tCgoKsHz5cgxMSkLW55vw1oM/x4DOje+ewVCKiJTibf+orCjx+pxd+twIf7vMJWn+DkR3vtbrc5pFeFQfDB23Ahs3ZWFA0kC3/WNA0kBs3JSJSY+uRUxCkuY1GimcAjg9ReQtb/vHpYve7zc77LoRcDjk9Q+H3R+jruoq6zxmDGC62QKxELHI3LARyf0HuO0fyf37I3PDZ1iITuhma/wLJzO+J0RmpFswdfbsWWzZsgWAF789CPX+t88doyJxe8oIrExP92xJ2sp03DnianRsr+4mrN5whUpyQypvHwcAY/t3R+acX2FoVDssmP8kYmNj4e/vj9jYWCyY/ySGRrVD5pxfYWz/7rKfm4jIE770j6Dg5reu9XRqKiQ8Fn2G3IHn0z1bkvZ8+irE9ZiAwKAoj57f7DomjsLISWvhtPfDk08uaNQ/nnxyAZz2fhg5aS2qqq7SrUaGU0Tm5kv/CGjn/bVAVHRHjJlwG9JXetY/VqWn444hfdExnFtiAMBQvxAss3VGYu45zJ87r1H/mD93HhJzi7HMloChfny/yNqMOi0FALotwO3Vqxfsdjv8/es21J49e3abj1mzZg0cDgdGXDus2d95sr+Uy+/um4zUh5/ElLQ0vJuR4XZ9+ZUlaacwd+bdHj93Q/aELprtvaPlBukDOsfglftvxdOl5dh6OBclFy8hvF0ARvVObHFPKU5LEZFSvO0fdrsDfQePdvv3SX0DPQolrr95HtYsvglpaVNavOV33ZK0NJw8eQojJy1v8zmtJDyqD4aMWYaqEQtRePIbOKvLYHeEIrrztQzwvLT/TCT6xRbrXQaRIfTq1Qs2P3/4+fnJ6h/+dgfie97Q7O882V/K5de/mYlpU37WZv+o3xJj6n0eP3dDo9MnG/ritCXdbIGYZ+uEhyUncqRKVKAWwajb6LylPaU4LUVkHLpNTP1l8bPY9OX3GD/x557/9mDVStw6YSyiozr4dO4BvbrhrUULkJWViYHJye6XpCUnISvzc6yd/yCSusX7dD6zigkLwZ1D+uL+EQNx55C+DKUsihufk9b+uCgdn3yxB2Mn3u5x/1i5chWuHZ2GiEjfNtSOSUjCpEfXYtPnWUhKdr8kLSlpIDZuysLQcSsQHtXHp/OZVWBQFDr3moiu/dLQudfEZqHU/mzP70SlNKNNTQGcnCLyVK9BCzBszDpExaVgxQrPVk+kp69Cj+RJCA6NafXYtvTu0x/L0v+JrKwsJA903z9cW2K8/cidSOqszQ0gjKa9zY5RfmGY4BeBUX5hDKWILjN6IK1bMDV23M8QFRWNhx59HPmn8pE2ZYoHG2rnY8bDDyhz/uuHIPOlJRjaKxELFsxvsiRtPoYlRGHL03MwbnA/n87DUIaISFkp429Bh6gYPPDIrLr+kdZ6/3BtqH3rPfNbfV5Pl/T1GDAOU+dvQVD0MDw5v8mStPkLEBQzDPcu+AIdE0fJfm0kBqOGUwyoiFoX1WkUAgIj0bnnPcg7eQqTPbwhw5CUWYqcf8SoVLz+7ifo038QFixY0HxLjJhgZD55H8YO6OHTeRjKEJHR2CRJkrQ8YUlJCSIiIvBV9iGEhtWt1d62NQuzpz+EuPg4TH/sMUydOhXR0dEoLCzEmjVrsGrVSuSfysfLK5Zh9A0j3D6vnKV8TZ09dx5bs/egpLwC7avLcWNSb0X3lLLyRAmDOfOz8ve3yEoqq9B53gpcuHAB4eHN91UyIlf/2LzzR4SG1r2mr7Zm4snHp7XYP1auXIWTp/Ix86n3MPDaCR6dR04oUV5SgBOHvsClylIEBIWhy1U3IiT8ym+59Zz8Mbp+Q+Rt+qsGOXdtFAmX9pEvyspKMHpod1P2j+smfAy7o27Cv7jgGxz6/n+R0Dkejz8+vVn/SE+v6x/j730DXfqMcfu8cpbyNXWu6Cy++3o7yspK0ev4Fxh1VVdF95Qy+vSELxjMkdWI8vNeIdVgSs1Rr/qHEMEUABw6uB+vvPg8Nn76H1RXX/lH3uFw4NYJYzHj4QfQ7yr3d8bzJZRqyn5anYsIK168M5SyBit+bxuBVYIpADhycB9eW/0cPt+wvlH/sNsduHZ0Gm69Zz4SeybLOpfSEzMMqLyjdzhl1GAKYDhF3rNKMAUA5SXHcPLo2zh3ZgtqnFf6h7/dgR7JkzAkZRai4ga4fU5fQqmmemc+q9hzNSTKxaqWGEqRFYnys+5LMKXb5udNXdWnHxYvX4Wn58/A9m++Q2lZGcJCQzHi2mE+7ylF2mMoRURa6dWnP5565kXM+f1T+PCzXaisKEFQcDj6Dh7t855SSuk3pCvDKQPac6DKsOEUN0UnaltIeA9cNfh/0HvoKpw6+iUuXSxFQLswxPe8wec9pUh7DKXIikQJpXwlTDDlEh3VAT+fOF6Xc6s1LQVoe4c+vTGUsg6rfE+TMXSIisH9vxynyD47nt6lTw6GU6Q1hlNEngkOjUGvQXfocm61pqUA896hzx2GUmRFZvr51m3zc3einKdlP0bJZXxqY2BDRKQ+pS7E1ZiU6Tekq+7L00geI26E3hA3RCdq3aCbBsl+jJLL+NTGwIaIjECoYMoKzB5Omf31EZExiBxOAfrvnUTWwnCKyNrMHk6Z/fURuWOmaSmAwRQpiKEUEZmRmuEUAypjMPrUFBGRWTGUIjIHBlM6MGOAY8bXRETGpuTeOmpugM2AirTAqSmi5rxZxmdUZgxwzPiaiDxhtmkpQKBgSu/9pdTc+Nzt+UwU5JjptZDnuPE5GYFRwimAAZXozDA1xXCKyHdK7i+l5sbn7pgpyDHTayEiAe/KZyWuQMeoF/gMpIjICPrFFit2Qa7G3fqacoVTvIMfqYF36iOyNlegY9SJCwZSZHVG/dltizATU1ZmxIDHiDUTkXUZaXLKxeoTVFZ+7Wrj5BQRGTHgMWLNROQZIYIpvZfxicBIQY+RaiUicjFiOAVcCagY1OjPDMv5iKiON/tLKbmMTwRGCnqMVCuRWsw6LQVwKR8A7feXaonoS/sYSJGLqN+jRFpyhVNahhUNwykzL/VjCKc+Lukj0pfW+0u1RPSlfQykiKxBiIkpasye0EW4EEi0eoiIvKHGhbiW01MNmXWSymyvR2Rc0kdELqPTJwsXAolWD5GeRA2PlaL7xJQ3y/isQoQJKgZSRGQ2Sm6G7qLFpuitMcskFUMpItKCN8v4rEKECSoGUkSNmT2UAgQIpryh5P5Soizja03DcEirkIqBFBGZmVrhFKD/PkRGDakYSumDS/qIPKPk/lKiLONrTcNwSKuLYgZSRNZlyGDKytQMqRhGEZGVqBFOAfpPTzXUNOwRMahiIEVEJDY1QyqGUUSts8K0FKBzMMVlfL5pGiTJDaoYRJE3uPE5mYma4RSg//RUU+5CID3DKoZSYuDUFFkJl/H5pmmQJPeimUEUEbljuIkpqy3jk4NBExGRfK4LcisFVA1pHVYZPYzac6BKtw3viUgfVlvGJweDJiL1WGVaCjBgMEVERKQGtaanALGW93mitfBITmhl9BDKHbOGUpyaIiIiIr3oFkx1qDkDIFSv0xMRETWjdjgFiD095Qkzhk1EZA3JNyTpXQIRkUesNC0FAH56FyAHl/EREZHa1J4aSeobaNqpGzI2tUJZIiPjMj4iIvUZKpgiIn1x43Oyin6xxQyoiIiIiEhzVpuWAiwaTHFaioiIPKHFnjuugIohFYmAU1NE6uC0FBFRywwTTCm5jI+IiIyr66XD6HbpgGbn03JDaAZURETiUHIZHxGRJ6w4LQUYKJhSCqeliIjMQetwigEV8WtCRN7gtBQRUessF0wRkXe4vxSJSMtwCtB2egpgQEX64HI+IiIi7Vl1WgoA7HoXQERE5Itulw7geEBfzc7nCqe0vHhvGE7tOVCl2XmJiIiIiNRmiIkppfaX4jI+IiJz6nbpgC7TU1pPUAGcotIL33Mia1Fqfyku4yMiT1h5WgrgxBQREZmI1tNTgD4TVACnqIiIiIjIHAwxMaUETksREVmD1pNTLnpMT7lwikpdVnxvuc8Uke84LUVEnrD6tBRggIkppZbxEZH3uPE5GY0rnLLK9JQLp6iIiHyn1DI+IiLyjPDBlBI4LUVEZE16LO0D9A+oAIZUSrHitBQR+Y7TUkTkCU5L1bHMUj4iIrImPTZGd9Frg/SmuNTPO3zPiIiIiNQn9MQUl/EREZFS9JqeAsSYoAI4RUVE1BYu4yMi0p7siakvvvgCt912G+Lj42Gz2fDBBx+oUJZyuIyPyDfcX4qUIkL/0HN6ChBnggrgFFVb+N4QiUOE/iEHl/ERkSe4jO8K2cFUeXk5Bg0ahPT0dDXqISIikxKpf4gSUIkQUrkCKgYxV/C9IBKLSP2DiIiUJ3sp38SJEzFx4kSPj6+qqkJV1ZXlAiUlJR49TollfJyWIiISh1b9Qw697t7XkCjL/AAu9QMYShGJSKv+ocQyPk5LEZEnOC3VmOqbny9atAgRERH1H4mJiWqfkoiITEDL/qHn9JSLKBNULlabpLLSayUyO15/EBEZi+qbny9cuBBz5syp/3NJSUmbzcFqm547O3X16DhOgJHWuL8U6cmb/uELEaanALEmqFzMPknFQIrIXLTuH0Z0OHWWR8dxAoxIeZyWak71YCowMBCBgdr/B5/oIY6nYZQnjxH9tRIReUOv/sGAqnVmCqkYSBGZkzf9wwrL+DwNozx5jOivlYiMRfVgiprzJpSS83wMqoiIfMeAqm1GDqkYShGRlXgTSsl5PgZVROQL4YIpM296rnQgJec8or4nRESiY0DlmaZBj6hBFQMpIlKDqMGM0oGUnPOI+p4Q6YnL+NyTHUyVlZXhyJEj9X/+8ccfsWvXLnTo0AFdunRRtDgz0SqUknt+BlbUEu4vRUozev9gQCWPSNNUDKOIjE3t/qHEMj4RaRVKyT0/Aysiakp2MPXdd98hJSWl/s+ujQWnTZuG1157TbHCzETvUKo1cmtjkEVE3jJL/2h4Bz89QyqjBFSA+2BI7bCKYRSReZilf2hJ71CqNXJrY5BFZsFpqZbJDqZGjx4NSZLUqMWUy/hEDqW84evrEe3rQ0TaUbN/6EWEKSojBVQNtRYcyQmtGEARmZ/o/UO04ETkUMobvr4e0b4+RNSccHtMkblxo3YiMiMGVMpi2ERERErhRu1E4vPTuwAzM9u0lBqcnbryfSIi0+h26UCjpX566BdbXB9SERFR68y2v5TZpqXUcDh1Ft8n0hyX8bVOmGDKbMv4GLbIw4BKLNz4nMg3DKjI6Pi9Q+QZkaZvGLbIw4CKSBzCBFNEAAM9IjIXBlRERERiYzhFpD8hgilOS1FDfP+IyGwYUBERicdMy/gYrviG7x+picv42iZEMEXUFMMpIjIjBlREROYh0jI+8h3DKSL9MJgiYTGc0gf3lyJSnygBFZE7/N4gIqtiOEWkD92DKS7jIyIiq9I7oOL0FBGR8TFMURbfT1ISl/F5Rvdgiqg1DPqIyAoYUBERactM+0uR8hhOEWlL12BKiWkpkTBEUQffVyKyCgZUpDd+/YmMhQEKEZmB4SemRFrGR2R03F+KSAzcf4qISGzc+Nz8GPqRr7iMz3N2vU4cWnISCAnW6/Q+O3O+BF/sOYyyyiqEBgXixqTeiOqkd1Xm5ezUlSEkEZlCQeE5bPsuG2XlFQgNCcbIYUPQMbpDs+Nc4dTxgL5alwjgSji1/0ykLucnIqLGzpSUYeuhEyi7eAmh7QIw6qouepdERKQI3YIpo9pz/BSWvvcZ3v/qB1Q7nfWfdzjsuD1lBH5332QM6NVNvwKJiEhI+w4dxbMvv4mPNm1GdfWVvU0cDgduGzsasx66F/2v6tnscd0uHdAtnAIYUFkJJ+XIKoy2v9SekwV45tOv8MH3B1DtrKn/vMPhwJgv8vDr38xE7z79dazQvA6nzuJ0HJEGDL2UT+sJmo3f78dNC5Zj58lzWLxkCQoKClBTU4OCggIsXrwE2UfykPrwk9i0I1vTuoiISGyZ277GzVN/gx8OHsXixYub9I/F+OHgUdw89TfI3Pa128frvfcUwNCCiEgPm/YeQ+qSN5BdWIHFS5Y26x+H9v+AaVN+hu1bM/UulYjIa4YOprS05/gp3L34FaSkjsHunD2YPXs2YmJi4Ofnh5iYGMyePRu7c3KQkpKKexY+jb1HjutdsulwE3R1cX8pInXsO3QU98/+A1JSUrA7J6eV/pGC+2f/AfsOHW3xuUQIpxhQmRO/rkTi2XOyAL9a/W+kjBmL3XvcX3/k7N6NlJQUzJ3xaxw+uE/vkonoMu4vJQ+DKQ8tfe8zxMXH492MDAQHu98bKzg4GO9mZCAuLh7L33hP4wqJiEhEz778JuLi4jzsH3F47pU3W30+UaanGGQQEanrmU+/Qlx85zb7R0bGu4iLj8Orq1doXKE1cBN0IvUZNpjSchnfmfMleP+rHzB9xuMtNgWX4OBgPDZ9Ot7P2oaz585rUyAREQmpoPAcPtq0GdNnzPC4f3y4MQtni9oOffQOpwBO2ZgFv45kJUbZX+pMSRk++P4Apj/u2fXH9Mcew6ZPP8K5orMaVUhEpBxufu6BL/YcRrXTialTp7Z4zJkzZ7B582aUlpYiNDQU1dVObM3egzvH3qBhpUREJJJt32Wjurpadv/Y9m02br95TJvPr/fG6AA3RyciUsPWQydQ7ayR2T+q8d3X2zH+Z5M0rJSImuIyPvkYTHmgrLIKABAdHd3s73JycrBo0SKsW7eu0V2WbDYbVrz9Afp0S+Bd+hTk7NRV803vrYD7S5HRhBz1/iYT5T2HKFhJ68rKKwDI7x+r3liLq3p0c3uXvqZck1MMqMgbnJYiElPZxUsA5PePN//5Arr37M279CmMd+cjUpchl/JpHUyEBgUCAAoLCxt9fsOGDbj++uvxzTffNLvL0jPPPIPiymrepY+ISGHBP+7y6fEhR7PdfqghNKRu+YXc/nG+rLLVu/S5I8LSPoD7TxkJv05E4gptFwBAfv8oLy3mXfqIyHAMGUxp7cak3nDY7VizZk3953JycnDnnXfW3WVp9273d8nYs4d36SMiMgg1QqqRw4bA4XB42T/avktfU6KEUwBDDyISz7RbnIo9l9obYo+6qgscdn/Z/WPP5f7Bu/QRkZEwmPJAbPtw3DF8EFamP4+KirplGYsWLaq7y9K77/IufUREJqPUNFXH6A64bexorExP97J/tH2XvqZEC6cYUImJXxciscWGh+L2wX2x8nn51x+8S586eHc+IvUYLpjSa3+heXeNR/6pU5iSloYff/wR69atw/Tp03mXPjI87i9F1DZfAqpZD92L/Px8r/uHp3fpa0ikcApgQCUafi2IjOF3Nw9H/qmTXvUP3qWPSB/c+Nw7hgum9JLULR5r5z+IrMzPMfz669q8y1JDU6dOrb9LH/nO2amr3iUQkUV5M0XV/6qeeO3vf0VWVhaGX3+9V/1j27fyQ7Fulw4IGVCRvvg1IDKOpM4d8fYjdyLr800Yfp031x91d+kjIhIdgykZxg3uhy1Pz0FiRN1vKdzdJcMd13Ell+/ORERExicnoEodeR0+XfMiOsdGAZDfP0rLy70rEpyeoiv4vhMZz9gBPZD55H1IDHEAkN8/yspKVauNiEgpDKZkSuoWj5mTUgA0v0tGS1zHhYe0PnZLRETG42k41f+qnvjtfXcDkN8/wkJCvCvuMtHCKYABldb4XhMZV1Lnjpgx9loA8vtHaGiYanURESnFUMGUXvtLNeXuLn2tWbNmDRwOO0YNSfLqfKURic0+iJTA/aWIlOHp9JS7u/S1xtU/Rl4zxNcShQynAAZURGRcWm6G7e4ufa2p6x8ODLtuhFfne2m9o9kHcQN0ah33l/KeoYIpUVy5S9+Vuyy1pKKiAqtWrsQdKSMR06G9rPO0FkIxpCIiEk9bAZW7u/S1xNU/fj4uBTFRkYrUJ2o4BTCgUhPfVyLjc92lL33lSo/6x8pVqzD25tvQISpG1nlaC6EYUhGRWhhMeWnOb6YhP7/uLn0tNYeKigpMSUtDfv4pzLnvLo+fW27gxICKiEgsrYVTDe/S13b/yMcTD96raG0ih1MAAyol8b0kMpe0/12J/FP5SEub0mr/SEubgvxT+XjgkZkeP7fcwIkBFREpyTDBlCjL+FwG9OqGtxYtQFZWJgYmJ2P58uUoKChAbW0tCgoKsHz5cgxMTkZWVibeWrQAA3p18+h5fQmYGE4REYmjpXCq4V36Wu8fWXjt739F/6t6aly5GBiq+IbvHZE2tFza1btPfyxL/yeysrKQPHCg2/6RPHAgsrKysCz9n+jdp79Hz+tLwMRwioiUYJhgSkRjrx+CzJeWYGivRCxYMB+xsbHw9/dHbGwsFiyYj6G9EpH50hKMvd6zvUGUCJY4PUVEJI6WwinXXfqu7tvTbf+4um9PfLrmRaSOvE6VukSfmmqIAZV8fL+IzGvEqFS8/u4n6NN/EBYsWNCkfyxAn/6D8Pq7n2DEqFSPnk+JYInTU0TkK5skSZKWJywpKUFERAROfr5W1l3qRJuYcnbq2ujPZ8+dx9bsPSgpr0B4SDBGDUmStaeUGmFS2IVcxZ9TFKJ9PxgRNz43t5LKKnSetwIXLlxAeHi43uUowtU/zry9BOHBQXqXI0t5z5Z/QXG2qBjbvs1GaXk5wkJCMPKaIYrtKdWW4wF9NTmPkvaf0ea9MSqGUuSrsrISjB7a3ZT9Y2v2UVXuUtc781nFn9OdptNZ54rO4ruvt6OsrBShoWEYdt0IWXtKqREmPTypWvHnFJFWX3MyDm58DlRINZhSc9Sr/mFXqSZFGSGEiOnQHneOvUHvMhopjUg0dThFRGQUIUezWwynYqIicfvNYzSuqE63SwcMF065ghcGVI0xkCKyng5RMRj/s0l6l9HIS+sdlgmniEg5XMrnhabTUr5Sc+kdl/UREYmhtQ3R9WSkZX0NcYnfFXwfiPSlxT5TSp9DzaV3XNZHRHIxmLIAhlNERGJgOKU8KwdUVn7tRCQ2hlNEJIfwwZQRlvH5QqvQiOEUNcT9pYioKSOHU8CVkMYqQY1VXieRUWh5dz5faRUaMZwiIk8ZYo8pUgb3nCIyv4KScmw9nIuCsgq9S6EWtLbfFCnDzPtQMZAitRQVFmDnN9tQXFSodynUhJFCr6a45xSR+RVLTuRIlbggOb1+DqGDKU5LERF5Zu/Js3jmsx344PuDqK7V9Gar5AVRwykjbobemoYhjtFDKgZSpJYjB/fh1Rf/jsxP16O6pkbvckhlekwxMZwis7PqHfmOS1XIqC3CNqkM3kdSdYRfyicapTc+1xrDMCLz2bTvR6QufRPZ3x/E4loJBQB4CUveMvqSvpYYcamfEWsmY/lqayYemDwWhz9dj8U1NewfPlJjssnI01JEZF47a8sxt+YEcqUyLAF87h8MpnSiZ0BkhnDK6AGhnri/lLnsPXkW96x+Hyk1NdhdK2E2gBjwH3cjEHUjdMC84ZRLw8BHtNBH1LrIfI4c3IcnH5uKlOpLyKmpYf+wAD33fDLbflMMDMnKjktVWFR7CqmQkAMo0j+E7T1mX8ZHvuH3B1GdZz7bgbjaWrwrAcF6F0NkUHoHVQyjSA+vvvh3xNU4kSFJ7B8KUjKwMFv4YbZwisiqMmqLEA8JGVDu+kPoPaZIPdwIncj4CkrK8cHl5Xu8qCClmW2/KTlaCoh83aeKwROJoqiwAJmXl++xf4jJbKEUEZlDseTEtsvL95TsHwymiIgMauvhXFTXSpiqdyHkNVE3QXexcjjlDoMlMoud32xDdU0N+4dKDqfOQu/MZ316vFlxI3QiY8uRKuEEFO8fQi7lM/syLVH2eBKlDtIO95cyl9KqSwCAaJ3rICIiY6koLwPA/qEmb8MltUIpkZbRiVQLEclTiVoAyvcPIYMpUXHDbSISSVhgAACgUOc6yNzMvhE6kRUFh4QCYP9Qm5yQ6XDqLFNPShGROQRdjpCU7h8MpiyOU1NExjWqdyIcfjas0bsQIiIylKHXjoTD35/9QwNthU1WDaQ4NUVmsXnGOr1L0FSyLQh2QPH+4VUwtWrVKnTv3h3t2rXD0KFDsXXrVsUKMvsyPiIipXQMD8Htg/tgpZ8NFXoX4yE1+weph1NTROYSFd0RqTdPQrq/P/uHBlzhk7sPIiIjibTZMdIWinRA0f4hO5h65513MGvWLPzhD3/A999/j1GjRmHixIk4cYJ713hCxAklEWsi5XF/KXP63fjrke/nhyk2ZZuDGtg/jI3hFJG5PPCb2cj3tyPNJv4vN9g/fMPpJCJSUppfFE7BhjQod/0hO5havnw5HnzwQTz00EPo168fnn32WSQmJuKFF17wuRhOSxERyTOgcwzeeuQOZPn7Y6CfDcsBFACXtyUUi5r9g4iI5OnVpz+WrFqDLEcAkv392T9IcwzMiIypmy0QC/3ikQkbkgFF+oddzsGXLl3Czp07sWDBgkafHz9+PLZv3+72MVVVVaiqqqr/84ULFwAApeXNszV7RaWccjTndFOzXGX+ZQpUojxJgdemJdG/V0TkrKxq+yAypGu7x+Ojmb9Aeua3mP/DYfxOuvJ3kiS1/EANKdo/Ki6qV6gOKsrK9S7BY1HYiZ8CeutdBhEpJHnwMKx8Yz3e/udKzN/0MX5Xe+Wywoz9o7ysVL1CBXbpoqxLPk2VlTn1LsFrJfxvawJQIdXoXYIu+tna4S9+CfigthhPogy/a/B33vQPWf9KFRYWoqamBrGxsY0+Hxsbi9OnT7t9zKJFi/B///d/zT7f9+e/lnNqIiLyQlFRESIiIvQuQ9H+0euh/1WlRiIiusKM/ePmG69Wo0Tywcv/T+8KiEhp3vQPr+Jzm83W6M+SJDX7nMvChQsxZ86c+j+fP38eXbt2xYkTJ4RodmopKSlBYmIicnNzER4ernc5qrDCawT4Os3GKq/zwoUL6NKlCzp06KB3KY2wf7TNCt+jVniNAF+n2VjldbJ/GJcVvket8BoBvk6zscrr9KV/yAqmoqOj4e/v3+y3EwUFBc1+i+ESGBiIwMDAZp+PiIgw9RfFJTw83PSv0wqvEeDrNBurvE4/P69uvqo49g/5rPA9aoXXCPB1mo1VXif7h3FZ4XvUCq8R4Os0G6u8Tm/6h6xHBAQEYOjQodi4cWOjz2/cuBEjRoyQfXIiIrIG9g8iIvIG+wcRkfnJXso3Z84c3HvvvRg2bBiGDx+O1atX48SJE3j00UfVqI+IiEyC/YOIiLzB/kFEZG6yg6lf/OIXKCoqwp///Gfk5+cjKSkJn3zyCbp27erR4wMDA/HHP/7R7XitmVjhdVrhNQJ8nWbD16kf9g/PWOF1WuE1AnydZsPXqR/2D89Y4XVa4TUCfJ1mw9fZNpskyr1giYiIiIiIiIjIUsTY1ZCIiIiIiIiIiCyHwRQREREREREREemCwRQREREREREREemCwRQREREREREREemCwRQREREREREREelC02Bq1apV6N69O9q1a4ehQ4di69atWp5eE1988QVuu+02xMfHw2az4YMPPtC7JMUtWrQI11xzDcLCwtCxY0fcfvvtOHjwoN5lKe6FF17AwIEDER4ejvDwcAwfPhz//e9/9S5LdYsWLYLNZsOsWbP0LkVRf/rTn2Cz2Rp9dOrUSe+yVHHy5ElMnToVUVFRCA4OxtVXX42dO3fqXZZP2D/Mgf3D3Ng/jI/9w5jYP8yD/WOW3qUoiv3D8/6hWTD1zjvvYNasWfjDH/6A77//HqNGjcLEiRNx4sQJrUrQRHl5OQYNGoT09HS9S1HNli1bMH36dOzYsQMbN26E0+nE+PHjUV5erndpikpISMDTTz+N7777Dt999x1SU1MxadIk7N27V+/SVPPtt99i9erVGDhwoN6lqGLAgAHIz8+v/8jJydG7JMUVFxdj5MiRcDgc+O9//4t9+/bhmWeeQfv27fUuzWvsH+bB/sH+YVTsH8bE/mEe7B/sH0bF/uEhSSPXXnut9Oijjzb6XN++faUFCxZoVYLmAEjvv/++3mWorqCgQAIgbdmyRe9SVBcZGSm9/PLLepehitLSUql3797Sxo0bpZtuukl64okn9C5JUX/84x+lQYMG6V2G6ubPny/dcMMNepehKPYP82L/MAf2D3Ng/zAH9g/zYf8wLvYPz2kyMXXp0iXs3LkT48ePb/T58ePHY/v27VqUQCq6cOECAKBDhw46V6KempoarF27FuXl5Rg+fLje5ahi+vTpuOWWWzB27Fi9S1HN4cOHER8fj+7du+Puu+/GsWPH9C5JcR9++CGGDRuGtLQ0dOzYEYMHD8ZLL72kd1leY/8wN/YPc2D/MAf2DzIS9g9zYP8wByX6hybBVGFhIWpqahAbG9vo87GxsTh9+rQWJZBKJEnCnDlzcMMNNyApKUnvchSXk5OD0NBQBAYG4tFHH8X777+P/v37612W4tauXYvs7GwsWrRI71JUc9111+GNN97Ahg0b8NJLL+H06dMYMWIEioqK9C5NUceOHcMLL7yA3r17Y8OGDXj00Ucxc+ZMvPHGG3qX5hX2D/Ni/zAH9g/zYP8go2D/MAf2D/NQon/YVayvGZvN1ujPkiQ1+xwZy4wZM7B79258+eWXepeiij59+mDXrl04f/483nvvPUybNg1btmwxVXPIzc3FE088gc8++wzt2rXTuxzVTJw4sf7/JycnY/jw4ejZsydef/11zJkzR8fKlFVbW4thw4bhb3/7GwBg8ODB2Lt3L1544QXcd999OlfnPfYP82H/MD72D/YPI2D/MB/2D+Nj/2D/aEqTiano6Gj4+/s3++1EQUFBs99ikHE8/vjj+PDDD5GVlYWEhAS9y1FFQEAAevXqhWHDhmHRokUYNGgQnnvuOb3LUtTOnTtRUFCAoUOHwm63w263Y8uWLVixYgXsdjtqamr0LlEVISEhSE5OxuHDh/UuRVFxcXHN/sOlX79+ht3olf3DnNg/zIH9g/1DZOwf5sT+YQ7sH+wfTWkSTAUEBGDo0KHYuHFjo89v3LgRI0aM0KIEUpAkSZgxYwb+/e9/IzMzE927d9e7JM1IkoSqqiq9y1DUmDFjkJOTg127dtV/DBs2DPfccw927doFf39/vUtURVVVFfbv34+4uDi9S1HUyJEjm90++dChQ+jatatOFfmG/cNc2D/YP8yA/cMY2D/Mhf2D/cMM2D9aptlSvjlz5uDee+/FsGHDMHz4cKxevRonTpzAo48+qlUJmigrK8ORI0fq//zjjz9i165d6NChA7p06aJjZcqZPn063n77baxfvx5hYWH1v4mKiIhAUFCQztUp5/e//z0mTpyIxMRElJaWYu3atdi8eTM+/fRTvUtTVFhYWLP1+SEhIYiKijLVuv25c+fitttuQ5cuXVBQUICnnnoKJSUlmDZtmt6lKWr27NkYMWIE/va3v2HKlCn45ptvsHr1aqxevVrv0rzG/sH+YTTsH+wfRsT+YVzsH+wfRsP+wf7RjE/39JNp5cqVUteuXaWAgABpyJAhpry9Z1ZWlgSg2ce0adP0Lk0x7l4fAOnVV1/VuzRF/frXv67/fo2JiZHGjBkjffbZZ3qXpQkz3q71F7/4hRQXFyc5HA4pPj5euvPOO6W9e/fqXZYqPvroIykpKUkKDAyU+vbtK61evVrvknzG/mEO7B/mx/5hbOwfxsT+YR7sH0/oXYai2D88Z5MkSfI6GiMiIiIiIiIiIvKSJntMERERERERERERNcVgioiIiIiIiIiIdMFgioiIiIiIiIiIdMFgioiIiIiIiIiIdMFgioiIiIiIiIiIdMFgioiIiIiIiIiIdMFgioiIiIiIiIiIdMFgioiIiIiIiIiIdMFgioiIiIiIiIiIdMFgioiIiIiIiIiIdMFgioiIiIiIiIiIdPH/AW8W+9dWD5QVAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1200x800 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_constrained_opt(pbounds, target_function, optimizer);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Simulation 2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We proceed with another slightly different problem of the same form. Here, we place a constraint from above and below on a function value.\n", "$$\n", " \\max f(x, y)\n", "$$\n", "$$\n", " \\text{subj. to} \\: \\: c^{\\text{low}} \\leq c(x, y) \\leq c^{\\text{up}}\n", "$$"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def target_function(x, y):\n", "    # Gardner is looking for the minimum, but this packages looks for maxima, thus the sign switch\n", "    return np.sin(x) + y\n", "\n", "def constraint_function(x, y):\n", "    return np.sin(x) * np.sin(y)\n", "\n", "# Note that the constraint limit in case of one-dimensional constraints can be both an array of shape (1,) or a number.\n", "constraint_lower = np.array([-0.9])\n", "constraint_upper = np.array([-0.3])"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["constraint = NonlinearConstraint(constraint_function, constraint_lower, constraint_upper)\n", "\n", "optimizer = BayesianOptimization(\n", "    f=target_function,\n", "    constraint=constraint,\n", "    pbounds=pbounds,\n", "    verbose=0, # verbose = 1 prints only when a maximum is observed, verbose = 0 is silent\n", "    random_state=1,\n", ")\n", "\n", "optimizer.maximize(\n", "    init_points=3,\n", "    n_iter=10,\n", ")"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_constrained_opt(pbounds, target_function, optimizer);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.2 Multiple Constraints"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Occasionally, one might need to fulfill multiple constraints. In this case, simply employ a multi-dimensional surrogate constraint function, i.e., in case of `n` constraints, a function returning an array of shape `(n,)`. Similarly, the `constraint_limit` should be an array of shape `(n,)`. The problem we are solving is\n", "$$\n", " \\max f(x, y)\n", "$$\n", "$$\n", " \\text{subj. to} \\: \\: c_1^{\\text{low}} \\leq c_1(x, y) < c_1^{\\text{up}}\n", "$$\n", "$$\n", " \\text{subj. to} \\: \\: c_2^{\\text{low}} \\leq c_2(x, y) < c_2^{\\text{up}}\n", "$$\n", "$$\n", " \\dots\n", "$$\n", "$$\n", " \\text{subj. to} \\: \\: c_n^{\\text{low}} \\leq c_n(x, y) < c_n^{\\text{up}}\n", "$$ "]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["def target_function(x, y):\n", "    # Gardner is looking for the minimum, but this packages looks for maxima, thus the sign switch\n", "    return np.cos(2*x)*np.cos(y) + np.sin(x)\n", "\n", "def constraint_function_2_dim(x, y):\n", "    return np.array([\n", "        - np.cos(x) * np.cos(y) + np.sin(x) * np.sin(y),\n", "        - np.cos(x) * np.cos(-y) + np.sin(x) * np.sin(-y)])\n", "\n", "constraint_lower = np.array([-np.inf, -np.inf])\n", "constraint_upper = np.array([0.6, 0.6])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Construct the problem is you would in the single-constraint case and run the optimization. Note that internally the optimizer assumes conditional independence between multiple constraints."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["constraint = NonlinearConstraint(constraint_function_2_dim, constraint_lower, constraint_upper)\n", "optimizer = BayesianOptimization(\n", "    f=target_function,\n", "    constraint=constraint,\n", "    pbounds=pbounds,\n", "    verbose=0, # verbose = 1 prints only when a maximum is observed, verbose = 0 is silent\n", "    random_state=1,\n", ")\n", "\n", "optimizer.maximize(\n", "    init_points=3,\n", "    n_iter=10,\n", ")"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABLsAAALcCAYAAAAPJ7IjAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdeVyVZf7/8TfLAQUBFREBTc0NFVDTzF1xHdusRhxrbLS9CVusKbXmVzbfKVMbmxadGZ0pMysL07apUUw0S80NE9w1LRcUcQEFZb1/f5wOiBzgvu79vs/7+XjwmAnPfc6lRz+e6+V97uMnSZIEIiIiIiIiIiIiB/A3ewFERERERERERERaYewiIiIiIiIiIiLHYOwiIiIiIiIiIiLHYOwiIiIiIiIiIiLHYOwiIiIiIiIiIiLHYOwiIiIiIiIiIiLHYOwiIiIiIiIiIiLHYOwiIiIiIiIiIiLHYOwiIiIiIiIiIiLHYOwi2TZs2IAZM2bg/PnzZi+lXidOnMCMGTOwY8cOs5dCRDby8ssv49NPP9Xlvo8cOQI/Pz8sWrRI0fGia1u8eDHGjx+PTp06wd/fH23atFH0uERkTU6ZVzk5Ofjzn/+Mvn37olmzZggPD0fPnj2xYMEClJeXK3p8ItLe2rVr4efnh2XLlun6OG3atMGkSZPqvE1RURFmzJiBtWvX6roWLc2fP1/xTCVlGLtItg0bNuDFF1+0Tex68cUXGbuISIiem8eYmBhs3LgRN910k6LjRdf23nvvYdeuXejduzfatWun6DGJyLqcMq+2bduGxYsXY9iwYVi8eDE++eQTDB48GH/84x/xwAMPKHp8InK2oqIivPjii4xdVKdAsxdAvq2oqAghISFmL4OISNilS5fQoEED+Pn5ybp9cHAw+vTpo/OqqqxcuRL+/u5/07r55puRnZ1t2GMTkbVYeV71798fhw4dgsvlqvzeiBEjUFJSgnnz5uHFF19Eq1atDFkLEfm20tJS+Pn5ITCQmcQJeGYXyTJjxgw8/fTTAIC2bdvCz88Pfn5+WLt2LT766COMHDkSMTExaNiwITp37oxp06ahsLCw2n1MmjQJjRo1QlZWFkaOHImwsDAMGzYMAHD+/Hncd999aNq0KRo1aoSbbroJP/30E/z8/DBjxoxq93PgwAHcddddaN68OYKDg9G5c2fMmzev8sfXrl2L66+/HgBwzz33VK716vshIvPs3bsXd955J6KjoxEcHIxrrrkGf/jDH1BcXFx5m+zsbIwZMwZNmjRBgwYN0L17d7z77rvV7sdzSv2HH36I5557DrGxsQgPD8fw4cOxb9++arfNzMzEzTffXDk7YmNjcdNNN+HYsWMAAD8/PxQWFuLdd9+tnBtDhgwBACxatAh+fn5YtWoV7r33XkRFRSEkJATFxcU4ePAg7rnnHnTo0AEhISGIi4vDLbfcgqysrGqP7+1tQTNmzICfnx927dqFO++8ExEREYiOjsa9996L/Pz8ytvVtbbaeEIXEanDeeWm17xq0qRJtdDl0bt3bwCo/DkT+SLPn7udO3ciJSUFERERaNq0KZ588kmUlZVh3759+M1vfoOwsDC0adMGs2fPrnb85cuX8dRTT6F79+6Vx/bt2xefffZZjcdKS0vDDTfcgIiICISEhODaa6/FvffeW+f6CgoKMGrUKERHR2Pz5s0AgJKSEvz1r39FfHw8goODERUVhXvuuQenT5+udmxpaSmeeeYZtGjRAiEhIRgwYEDlfdTlyJEjiIqKAgC8+OKLlXPG89ZHuXPOM5Pfe+89PPXUU4iLi0NwcDAOHjwIAFi4cCE6duyI4OBgdOnSBR988AEmTZpU47IQcn6+bdq0wa5du7Bu3brK9fLyEvpjsiRZ7r//fpw9exZvvvkmli9fjpiYGABAly5d8MYbb+DGG2/EE088gdDQUOzduxezZs3C5s2bsWbNmmr3U1JSgltvvRUPPfQQpk2bhrKyMlRUVOCWW27B1q1bMWPGDFx33XXYuHEjfvOb39RYx+7du9GvXz9cc801+Nvf/oYWLVpg5cqVeOyxx5CXl4cXXngB1113Hd555x3cc889+POf/1x5Cn7Lli31/4Uionr9+OOPGDBgAJo1a4a//OUv6NChA3JycvD555+jpKQEwcHB2LdvH/r164fmzZvjjTfeQGRkJJYsWYJJkybh1KlTeOaZZ6rd57PPPov+/fvj3//+NwoKCjB16lTccsst2LNnDwICAlBYWIgRI0agbdu2mDdvHqKjo3Hy5ElkZGTgwoULAICNGzdi6NChSE5Oxv/7f/8PABAeHl7tce69917cdNNNeO+991BYWAiXy4UTJ04gMjISr7zyCqKionD27Fm8++67uOGGG5CZmYlOnTrV+2vy29/+Fr/73e9w3333ISsrC9OnTwcAvP3227LXRkTa47yqyah5tWbNGgQGBqJjx47CxxI5zbhx4zBhwgQ89NBDSE9Px+zZs1FaWorVq1fjkUcewZ/+9Cd88MEHmDp1Ktq3b4877rgDAFBcXIyzZ8/iT3/6E+Li4lBSUoLVq1fjjjvuwDvvvIM//OEPANx/bn/3u9/hd7/7HWbMmIEGDRrg559/rrGXu9KxY8dw4403oqSkBBs3bsS1116LiooKjBkzBuvXr8czzzyDfv364eeff8YLL7yAIUOGYOvWrWjYsCEA4IEHHsDixYvxpz/9CSNGjEB2djbuuOOOyjlXm5iYGPzvf//Db37zG9x33324//77AaAygInOuenTp6Nv37745z//CX9/fzRv3hwLFizAQw89hN/+9rd47bXXkJ+fjxdffLHaP3IAkP3zXbFiBcaOHYuIiAjMnz8fgPsMWtKZRCTTnDlzJADS4cOHa71NRUWFVFpaKq1bt04CIP3444+VPzZx4kQJgPT2229XO+a///2vBED6xz/+Ue37M2fOlABIL7zwQuX3Ro0aJbVs2VLKz8+vdtvJkydLDRo0kM6ePStJkiRt2bJFAiC98847yn6yRKSboUOHSo0bN5Zyc3Nrvc348eOl4OBg6Zdffqn2/dGjR0shISHS+fPnJUmSpIyMDAmAdOONN1a73ccffywBkDZu3ChJkiRt3bpVAiB9+umnda4tNDRUmjhxYo3vv/POOxIA6Q9/+EO9P7+ysjKppKRE6tChgzRlypTK7x8+fLjGXHrhhRckANLs2bOr3ccjjzwiNWjQQKqoqKh3bXLcdNNNUuvWrRUdS+TLOK/eqfyeUfNKkiRp5cqVkr+/f7U1Efkiz5+7v/3tb9W+3717dwmAtHz58srvlZaWSlFRUdIdd9xR6/2VlZVJpaWl0n333Sf16NGj8vuvvvqqBKByXnnjmWFpaWlSZmamFBsbKw0cOFA6c+ZM5W0+/PBDCYD0ySefVDvWszebP3++JEmStGfPHglAjT/j77//vgSg3vlx+vTpGvvEun7O3uac5+czaNCgarcvLy+XWrRoId1www3Vvv/zzz9LLper2uspuT9fSZKkrl27SoMHD653vaQdvseBVPvpp59w1113oUWLFggICIDL5cLgwYMBAHv27Klx+9/+9rfV/nvdunUA3P9icaU777yz2n9fvnwZ33zzDW6//XaEhISgrKys8uvGG2/E5cuXsWnTJi1/akSksaKiIqxbtw7jxo2r/Bc4b9asWYNhw4bVuE7LpEmTUFRUhI0bN1b7/q233lrtv5OSkgAAP//8MwCgffv2aNKkCaZOnYp//vOf2L17t6L1Xz2/AKCsrAwvv/wyunTpgqCgIAQGBiIoKAgHDhzwOgO98bb+y5cvIzc3V9E6iUg9zivv9J5X27dvx7hx49CnTx/MnDlTk/sksrubb7652n937twZfn5+GD16dOX3AgMD0b59+8pZ4pGWlob+/fujUaNGCAwMhMvlwn/+859qf+Y9l4AZN24cPv74Yxw/frzWtaxcuRIDBw7EoEGDkJ6ejqZNm1b+2JdffonGjRvjlltuqbZX6969O1q0aFF5QfmMjAwAwO9///tq9z1u3DjV18sSnXNXz8p9+/bh5MmTNfam11xzDfr371/te3J/vmQOxi5S5eLFixg4cCB++OEH/PWvf8XatWuxZcsWLF++HID7gqhXCgkJqXEq+5kzZxAYGFhtUAJAdHR0jduVlZXhzTffhMvlqvZ14403AgDy8vK0/ikSkYbOnTuH8vLyet9WfObMmcq3S18pNja28sevFBkZWe2/PaeGe2ZQREQE1q1bh+7du+PZZ59F165dERsbixdeeAGlpaWy1+9tTU8++ST+3//7f7jtttvwxRdf4IcffsCWLVvQrVu3GjOwNvWtn4iMx3nlnZ7zKjMzEyNGjECHDh3w1Vdf8W0+RL+6ep8UFBSEkJAQNGjQoMb3L1++XPnfy5cvx7hx4xAXF4clS5Zg48aN2LJlC+69995qtxs0aBA+/fRTlJWV4Q9/+ANatmyJhIQEfPjhhzXW8umnn+LSpUv44x//WOPP6KlTp3D+/HkEBQXV2K+dPHmycq/mmYstWrSodnxgYGCNGSNKdM5dPSs9a7t6L+rte3J/vmQOXrOLVFmzZg1OnDiBtWvXVp7NBbgvOO+Nt08BioyMRFlZGc6ePVttkJ88ebLa7Zo0aYKAgADcfffdSE1N9Xr/bdu2VfCzICKjNG3aFAEBAfVecDgyMhI5OTk1vn/ixAkAQLNmzYQfOzExEUuXLoUkSdi5cycWLVqEv/zlL2jYsCGmTZsm6z68zbAlS5bgD3/4A15++eVq38/Ly0Pjxo2F10lE1sB5ZazMzEwMHz4crVu3xqpVqxAREWHqeoicYMmSJWjbti0++uijajPh6mtPAcCYMWMwZswYFBcXY9OmTZg5cybuuusutGnTBn379q283WuvvYaPPvoIo0ePxooVKzBy5MjKH2vWrBkiIyPxv//9z+t6wsLCAFRF85MnTyIuLq7yx8vKymr8A4GSn7PInLt6VnrWdurUqRq3vXp/KvfnS+bgmV0km7d/ufMMh6ur/r/+9S/Z9+uJZB999FG17y9durTaf4eEhCA5ORmZmZlISkpCr169anx5hhPPiiCypoYNG2Lw4MFIS0ur81+7hg0bVhnTr7R48WKEhISgT58+itfg5+eHbt264bXXXkPjxo2xffv2yh8LDg4Wnht+fn41ZuB///vfOt8CoISStRGRcpxXyomubceOHRg+fDhatmyJ9PR0NGnSRNP1EPkqPz8/BAUFVQs6J0+e9PppjB7BwcEYPHgwZs2aBcAdoq/UoEEDLF++HDfffDNuvfXWavd1880348yZMygvL/e6V/NcHN7zCa3vv/9+tfv++OOPUVZWVu/Pq669nto516lTJ7Ro0QIff/xxte//8ssv2LBhQ7Xvyf35etbM13HG4pldJFtiYiIA4PXXX8fEiRPhcrmQlJSEJk2a4OGHH8YLL7wAl8uF999/Hz/++KPs+/3Nb36D/v3746mnnkJBQQF69uyJjRs3YvHixQAAf/+qJvv6669jwIABGDhwIP74xz+iTZs2uHDhAg4ePIgvvvii8hND2rVrh4YNG+L9999H586d0ahRI8TGxla+pYCIzDN37lwMGDAAN9xwA6ZNm4b27dvj1KlT+Pzzz/Gvf/0LYWFheOGFF/Dll18iOTkZzz//PJo2bYr3338f//3vfzF79mzhf/H/8ssvMX/+fNx222249tprIUkSli9fjvPnz2PEiBGVt0tMTMTatWvxxRdfICYmBmFhYfV+OtnNN9+MRYsWIT4+HklJSdi2bRvmzJmj+SfAiq5t9+7dldf6OXnyJIqKirBs2TIA7k/S7dKli6brI3IizitlRNa2b98+DB8+HADw0ksv4cCBAzhw4EDlj7dr167Oa6YRUe1uvvlmLF++HI888gjGjh2Lo0eP4v/+7/8QExNT7c/Z888/j2PHjmHYsGFo2bIlzp8/j9dff73atZiv5HK58OGHH+L+++/H2LFjsXjxYtx5550YP3483n//fdx44414/PHH0bt3b7hcLhw7dgwZGRkYM2YMbr/9dnTu3BkTJkzA3//+d7hcLgwfPhzZ2dl49dVXZX16a1hYGFq3bo3PPvsMw4YNQ9OmTdGsWTO0adNG9Zzz9/fHiy++iIceeghjx47Fvffei/Pnz+PFF19ETExMtb2p3J8vUHXG7kcffYRrr70WDRo0qNxfk07MvT4+2c306dOl2NhYyd/fXwIgZWRkSBs2bJD69u0rhYSESFFRUdL9998vbd++vcan+EycOFEKDQ31er9nz56V7rnnHqlx48ZSSEiINGLECGnTpk0SAOn111+vdtvDhw9L9957rxQXFye5XC4pKipK6tevn/TXv/612u0+/PBDKT4+XnK5XLI/rYOIjLF7924pJSVFioyMlIKCgqRrrrlGmjRpknT58uXK22RlZUm33HKLFBERIQUFBUndunWr8QmrV34y0JWu/iSxvXv3SnfeeafUrl07qWHDhlJERITUu3dvadGiRdWO27Fjh9S/f38pJCREAlD5qTmeTzfbsmVLjZ/LuXPnpPvuu09q3ry5FBISIg0YMEBav369NHjw4GqfulPXp5udPn262n16Hu/KT7+tbW218dy3ty/OQyL5OK/c9JpXnuNr++Ina5Mvq+3PXW37qsGDB0tdu3at9r1XXnlFatOmjRQcHCx17txZWrhwYeX9enz55ZfS6NGjpbi4OCkoKEhq3ry5dOONN0rr16+vvI23GVZRUSE99thjkr+/v7Rw4UJJktyfCvnqq69K3bp1kxo0aCA1atRIio+Plx566CHpwIEDlccWFxdLTz31lNS8eXOpQYMGUp8+faSNGzdKrVu3lvVprqtXr5Z69OghBQcHV/sER7lzrraZ7LFgwQKpffv2UlBQkNSxY0fp7bfflsaMGVPtUyxFfr5HjhyRRo4cKYWFhUkA+CnZBvCTJEnSsaURKfbBBx/g97//Pb7//nv069fP7OUQERERERGRDzp//jw6duyI2267DQsWLDB7OSQD38ZIlvDhhx/i+PHjSExMhL+/PzZt2oQ5c+Zg0KBBDF1ERERERERkiJMnT+Kll15CcnIyIiMj8fPPP+O1117DhQsX8Pjjj5u9PJKJsYssISwsDEuXLsVf//pXFBYWIiYmBpMmTcJf//pXs5dGREREREREPiI4OBhHjhzBI488grNnz1Z+2Mg///lPdO3a1ezlkUx8GyMRERERERERETmGf/03qe748eOYMGECIiMjERISgu7du2Pbtm16rI2ISBXOKyKyC84rIrILzisisgOhtzGeO3cO/fv3R3JyMr7++ms0b94chw4dQuPGjXVaHhGRMpxXRGQXnFdEZBecV0RkF0JvY5w2bRq+//57rF+/Xs81ERGpxnlFRHbBeUVEdsF5RUR2IRS7unTpglGjRuHYsWNYt24d4uLi8Mgjj+CBBx6o9Zji4mIUFxdX/ndFRQXOnj2LyMhI+Pn5qVs9ETmaJEm4cOECYmNj4e8v9q5rzisiMhLnFRHZBecVEdmFmnkFSUBwcLAUHBwsTZ8+Xdq+fbv0z3/+U2rQoIH07rvv1nrMCy+8IAHgF7/4xS/FX0ePHhUZVZxX/OIXv0z74rziF7/4ZZcvzit+8YtfdvlSMq+EzuwKCgpCr169sGHDhsrvPfbYY9iyZQs2btzo9ZirS35+fj6uueYaHPz3X+C/f7/ch5YltHuSpvcHAEVtu2t+n1YQcniH5vdZuGOnpven9vmU+9z9HNShzh/fd6ppvfexe/8lWY+1d8cvsm5nhvju18i6XZeODeu9Tafos3X+eOuSA7Ieq3zXJrS//3mcP38eERERso7x0HJeDbtrDQKDGgk9vtk6Jcl7Pmsc16H+5/dKHaMLhG7f2u+w7NtGHMsWuu+S3cpnUM6mvYqPdZKYPvGKjw3qIjaz81smyL7tz1Jb2bfdfypcaB37Dsib316P3Vk108tKLuKbD4bafl5ZcXaIzA3AuNnBuVGFs0PMlbPDDFaZV9wPaqtplxvMXgIpMO7xfWYvoZLT9oNCF6iPiYlBly5dqn2vc+fO+OSTT2o9Jjg4GMHBwTW+HxbSAP7BQSIPX69GIWIvtOQIaBSq+X1aQagOv1ZWez7lPneNgup+cdPwYv0vfoIbumQ9lsvCwSS4obwXeQ1D639eGjUqq/PHw0rkPTflIQ0AQNEp7lrOq8CgRpZ+7rz5ae9ZdO7RRvi4I8eAzp3k/9k7ejEc8S3kb1rz0A1t/Q7Jum1FfB80Pvqj7PvG9TegJGuH/NtfIXxINxz/fpeiY53kwraDiOvfVdnBP+1DUGJ32TcPP3cI51t1k3XbBJzGYamdrNv2aATsPSl/09q9ezj27FO2aU26IQF7Mo9U+56d55WSmQGIzQwAv84Mec+Re17I//k0PvojIOPvKY+SrB1o0KDmr2N9jn+/C41cQi+jHUvxzACEZgYAnG/VDWEyb3tYage5r+L3ngxHQ4GX/Hv2XZL92q/GsZlHLPOawux5xf2gdiIT+pm9BFLof+9cj1se2mP2MgA4bz8o9KbH/v37Y9++6uVx//79aN26tfADa61Rzx5mL4E0xOfT2Y4EyfsXYDX/kmbleWWUqzfhso8T3PiLhAUAsqMFANkxxEN043QlNRs2J1ET/URjo0jMlBtJAQgFWEA81lQ7VmEgupIV5pWxoUsekeccEPv9BIj/fvVgGK9idOiSS+TvGdG/w5TGcUD538tWYoV5VRtf3T8wdNnfF//qbPYSDGfEflAodk2ZMgWbNm3Cyy+/jIMHD+KDDz7AggULkJqaqujBfXUgOZWVns/CdteZvQSvuvRsY/YSfIbW88quGLzEMHi5MXiJURu8zJ5XDF3yMXRVYegS44TQBXA/aDUMXc7hi8FLb0Kx6/rrr8eKFSvw4YcfIiEhAf/3f/+Hv//97/j973+veAEccHQ1I39PyC3KTufECKfHvLIrBi8xDF5uDF5ilF7rCjB3XjF0ycfQVYWhS4xTQhfA/aCVMHQ5j5nBy4n7QcHPbgRuvvlmZGVl4fLly9izZ0+dHzNrFA5IMaGHtut233wuyEqsOK/MwuAlhsHLjcHLOGbMK4Yu+Ri6qjB0iXFS6PKw4usrX9uDMHQ5F8/w0o5w7NKDlYeTnmGIarLy7wUiu2PwEsPg5cbg5UwMXfIxdFVh6BLjxNClFyvvAay2H2Tocj4GL21YInYBygeclQejr+JzSWRdDF5iGLzcGLychaFLPoauKgxdYhi6xHEPUT+GLt/B4KWeZWIXID6ofGmwOR2fSyLjMHiJYfByY/ByBoYu+Ri6qjB0iWHoUo77wdoxdPkeBi91LBW7APkDy5cGmx016tnDtOfSqp/E6OHEi/+R/TB4iWHwcmPwsjeGLvkYuqowdIlh6FKP+8GaGLp8F4OXcpaLXUD9ocSXBpvd1fc8OuW5TIi372aI8c13MXiJYfByY/CyJ4Yu+Ri6qjB0iWHo0g73g1UYukjv4OXU/WCg2Quoi1WGWOih7ZY/W0guMy6waJXnkYhq2pN5RNEmeM++S0Kb4L0nw4U2wYeldrI3wudbdRPaBAcldle8CY7r35UbYbhjgNJNcEnWDqFNcOOjP8reBLf1OyR7ExzfokBoE9y5U0NVm2AzMXTJxz/fVRi6xDB06cMq+wiz9oMMXeTxxb8645aH9pi9DFux5JldZJ5T5wuQ9t02vJO+AWnfbcOp82L/+k0k15GgeLOXQL+y6xlep/PO4LOvVuL9tBV4N+skTp3Ll33fPMNLPZ7hZQ8MXfLpGbryLhfj62MnsezwcXx97CTyLhfr9lhaYOgSw9BFejArdJ06dQofffQR/v3vf+Ojjz7CqVOnTFkH1eS0tzTqvR+09JldZJzsIycwZ9lKrNiwA6UVFZXfd/n74/Z+3fH02FFIaBNr4grlEfkXFzNjS5eebbB72xHTHl8pO79dk+pmpzO89uw/gDf/9Ta+XLkapeXlld93BQTg9oG98PSdNyGhbat675tneKnHM7ysjaFLPr3+PO/Pv4iF+w5j1fFclEpS5fddfn4YGdccD3Rqi44RjXR5bKUYusQwdJEezAhdWVlZmPnyy1i2bBlKy8oqv+8KDMTYsWMx/dlnkZiYaPi6qDqzz/Cy036QZ3YR0jP3YPDTc7Btww7MqqhALoByALkAZlVUYNuGHRj89BykZ/K0Sadx6vuzSRk7nOGV8d0G3JxyN3auXI1Z5eXV51V5Obat34rBk/8P6VuzZN03z/BSj2d4WRNDl3x6ha7vTp3BnRmbsff4KcySpOrzSpKw93gu7szYjO9OndHl8ZVg6BLD0EV6MCN0rVy5En1698bmZcswq6ys+rwqK8PmZcvQp3dvrFy50vC1UU1anuHl5P0gY5dMZlzrSmvefg7ZR05g/MsLkFxWjp0VFZgCIAru3xhRAKYA2FlRgeSycox/eQGyj5wwdtFEZCgrB689+w/g/tQnkVxaip3l5d7nVXk5ksvKMP6FN5F9+Kis+2bwUo/By1oYuuTT84yuxzf+iOSKCuyU4H1eSRKSKyrw+MYfsT//oi7rEMHQJYahy/cYsR8064yuO267DcnFxdhZVuZ9XpWVIbm4GHfcdhuysuT9gyLpy2lvadQDY5ePm7NsJWLKK/CxJCGkltuEAPhYkhBTXoFXP1ll5PKEOOVDBIjMZtXgNfOfHyOmvFzevKqowKsf/lf2fTN4qcfgZQ2dkq5RdBxDl7YW7juMWKkCaUCd8yoNQKwkYeG+w7qtRQ6GLjEMXaQHs67RNfPllxFTVibv9VVZGV6ZOdPI5VEdGLzqxtjlw06dL8CKDTuQWlFR62DzCAHwSEUFln+fidzzF4xYnuOZfcqo2Y9P1ma14HU2LxdrV32K1PJyefOqvBzL129F7jn5G3IGL/UYvOyJoUtbeZeLsep4LlKl2kOXRwiARyQJq47n4szlEt3WVBeGLjEMXaQHMy9Gv2zZMqSWlcmbV2VlSEtLQ25urhHLIxnUBC+n7wcZuwTY+a2M3tb+bfYBlFZUYILM+5gAoLSiAt9mH9B0beS7+ImM1mal4LV9y3qUlpeLzavycvzvuNhaGLzUY/CyF4Yu7W3JO4dSSRKbV5KEzXnn9FyWVwxdYhi6SI/9oFmhCwDWrl2L0rIysXlVVoa1a9fquCoSZeczvPTcDzJ2+bCLl9wfe91M5u09t7tw6bIu61FD9C2MVoksTq/pTqH0bUFOYJXgVVTovp6N8Ly6WCi0wQIYvLTgi8GrUwf7BS+GLn0Ulro/JVZ0XhWWltV5O60xdIlh6CI9mBm6AODCBfc7dkTnVUGB2N+RpD87By+9MHYJsuPZXbWtuVHDYABAnsz78dwurGED9YsiU+kd2XafbKrr/ZtB6QWfncAKwSsktBEABfOqUSgAsY0WwOClBV8MXnbC0KWfUFcAAPF5FeoK1GU93jB0iWHooitptR80O3QBQFhYGADxeRUeLvbnj4whErx8YT/I2OXDBiV0gMvfH0tk3n4JAJe/PwYldNBzWcJ4YXoyCoOXguM0Cl7XXT8QroAAsXkVEIB+vXtVfo/By3gMXtbE0KWv65s1gcvPT2xe+fmhd7Mmei6rEkOXGIYu0oMVQhcADBkyBK7AQLF5FRiIIUOG6LgqUoNneFVh7FLATmd31bXW6MbhuL1fd8zz90dRPfdTBGC+vz/u6N8DzRuHabpGX2f0Wxn51kl1GLwUHKdB8GrarDmGjLwNbwUEyJtXAQG4+TfD0Syy+r8qMXgZj8HLWhi69NesQTBGxjXHPD/Im1d+fhgZ1xyRDYJ0XxtDlxiGLqqNmv2gVUIXAERHR2Ps2LGYFxgob14FBiIlJQXNmzc3YnmkUH3By1f2g4xdDiZnCD89dhRyAvwxzs+v1gFXBGCcnx9yAvzxp9+O1HSNaul9VpfI6ZcJ8cqv1+IrA8cbq1w/TQSDl4LjNAhef3joKeQEBCJF1rwKwKMP3uv1NgxexmPwsgaGLuM80KktTvj5IwW1B68iACkATvj54YFObXVfE0OXGIYu0oOVQpfH9GefRU5goLz9YGAgpk2fbuTySCE7neGl136QsUshO53dVZeENrFY+uyDyAgMQJK/P+YCyAVQ8ev/zgWQ5O+PjMAALH32QSS0iTV1vVqwY1zRipqopibmORGDl4LjVAavdh274qW3PkCGKwiJAQHe51VAADJcLvx73lx07lj7W64ZvIzH4GUuhi5jdYxohNf7dkOGvz+S/OB9Xvn5IcPfH6/37YaOEY10XQ9DlxiGLpJDdD9oxdAFAImJiVj+6afICA5GUmCg93kVGIiM4GAs//RTJCYmmrpeks9b8PKl/SBjlwpWDl4iaxvRozPWzXkavfr3wDR/f0QDCAAQDWCavz969e+BdXOexoge1qrDTrtWl95nd/ny2WN6YfBScJzK4NVnwHAs+DgD7UfdjmkBAdXnVUAA2o26HV+mvYfkAfW/oGTwMh6DlzkYuswxIDoSHyb3RnxcNKb5+VWfV35+iI9rjg+Te2NAdKSu62DoEsPQRSLk7rmsGro8Ro0ahU2bN+OGlBRMCwysPq8CA3FDSgo2bd6MUaNGmbxSEnVl8PK1/aCfJEmSkQ9YUFCAiIgInPpgNsJD7FUGa2O16KImwuWev4Bvsw/gwqXLCGvYAIMSOlj2Gl1Kf91FzuwS/RSJ7L3KX1xVPua2I6rvwxu1w0205HdpcVb2bduU7PX6/QsXC9G270jk5+eb8qkvnnn12NyTCG5Y++P78gtjpcFPi8332TOnkbl5PQoLLyA0NAw9eg9E08goAGIbcKM234A1NuBW4MTN96XCAqTeEmG5ecXQZQ1nLpdgc945FJaWIdQViN7NmvAaXTIxdGmvtOQiVi7qbfq88pX9oNVD19Vyc3Oxdu1aFBQUIDw8HEOGDOE1uhzglof2+Nx+kGd2acAqZ3iFHtquei3NG4dh7IDrcM+Ifhg74DqfDl1m0aO4W73i2+F5qQvP8FJwnBYXrY+MwrDRd+DWsRMxbPQdlaELENtI8Qwv4/EML2MwdFlHZIMgjG4ZjbFt4zC6ZTRDl0wMXWQXte3B7Ba6AKB58+YYN24c7r//fowbN84SoevyZ2+ZvQTbs/o1vPTYDzJ2aUSL0KT28X2F1c6k04OWccrqocspGLwUHKdB8KoLg5e1MXjpi6HLtzF0iWHoIi1cvR+0Y+iyIk/oYvBSb9aDzjiTUi7GLo0ZHb3MjmykH7WRqkvPNgxdBmPwUnAcg5dPY/DSB0OXb2PoEsPQRVoLPbSdoUsjVwcuBi/1fCl4MXbpRO8I5auRy+pndWn9CRVKY5UdI5fd38roweCl4DgGL5/G4KWtTh0YunwZQ5cYhi7SQ4Mxk81egiPUFrbOZG8weCXOY9XgpfV+kLFLZ54opUWY0vK+7Eht6LJrTBE5Q0uvs7ns9jGzZmPwUnAcg5dPc0Lw6hhtneAlF0OXszB0ifHl0NUp6Rqzl+BYDF3aqC10efaDDF7qiQYvO+4HA81egC/x1UilBauf0WUEu52ttftkU6FP4HCazj3a+OwL6T2ZRxQFvz37Lgm9/WrvyXChzfphqZ3sDfv5Vt2ENutBid0Vb9bj+nflhh3uaKF0s16StUNos9746I+yN+tt/Q4JbdbtgqHLWRi6xPjq38+A+/VJ8SX7xXk7YOjSRn2hy+NM9ga+XVSlWQ82xNQFymdpfczeD/LMLvIJdj2ry9c47XniGV4KjuMZXj7NCWd42QFDl7MwdInx9dBF+mDo0oboNbl4hpd6VntLo5b7QcYusjyzzurafbKpKY9L1iN6DZwr+fILSwYvMQxebgxe+mLochaGLjEMXaQHhi5t1BW66toPMnipZ7XgpRXGLrI0O7590Y7vZ66L0T8fq57dJfrpZtWO9eEXmAxeYhi83Bi89MHQ5SwMXWIYukgPDF3aUBq6PBi81KsreNl1P2ha7Cpq292shyab0Cp0WTWekP0weCnD4CWGwcuNwUtbDF3OwtAlhqHLmuy+H2To0oba0OXB4KWe087wMvXMLjuetUPG4O8N32blQMngpQyDlxgGLzcGL20wdDkLQ5cYhi5rs+trfoYubWgVujwYvNSzSvDSYj9o+tsY7TrgSD9a/p4wK5o47a2MVB2DlzIMXmIYvNwYvNRh6HIWhi4xDF32YLf9IEOXNrQOXR4MXupZJXipZXrsAuw34Egfhe2u4+8FizEz2ln57C6AwUspBi8xDF5uDF7KMHQ5C0OXGIYue7HLHoChSxu1hS6t9oMMXup5gped94NCsWvGjBnw8/Or9tWiRQtVC/Bg6PBtejz3Vo8lvsDMT7TUc155MHgpw+AlhsHLzcnBS495xdDlLAxdYhi69OPL+0GGLm3UFbq0xOClnhZneJm5HxQ+s6tr167Iycmp/MrKytJ0QVYecKQPK4YuLf5Q8q2M6v0c1EHV8XrPK4DBSykGLzEMXm5ODl5aziuGLmdh6BLD0KU/X9wPMnRpw6jQBbj3g9v2n9X8fn3N3YPMfXw1+0Hh2BUYGIgWLVpUfkVFRdV5++LiYhQUFFT7qo8VBxzpg8+1dTkh1hkxrwAGL6UYvMQweLlZNXi19jssuJrqtJpXHaMZupyEoUsMQ5cxfG0/yNClDSND15UYvNQzO3gpJRy7Dhw4gNjYWLRt2xbjx4/HTz/9VOftZ86ciYiIiMqvVq1ayXocKw040odez7GV3r7ohGBkZ0bNK4DBSykGLzEMXm5WDV5qGDmvAIYuO2DoEsPQZRxf2g8ydGnD6NB19X6QwUs9OwYvodh1ww03YPHixVi5ciUWLlyIkydPol+/fjhz5kytx0yfPh35+fmVX0ePHpX9eFYYcKQPPrfW5oRIZ/S8Ahi8lGLwEsPg5eak4GX0vGLosj6GLjEMXcbxpf0gQ5c2zDqj62oMXurZLXj5SZIkKT24sLAQ7dq1wzPPPIMnn3xS1jEFBQWIiIjA4Y2rENYoVPZjhR7arnSZZCF6DzUtzurS4yJ62XuVv4Azg9axq0sLZX+5XLxYgCE92yI/Px/h4WIvmq+mZl7N+yIfDUPlPz5fsCuj9AW7aGQUuZ4RIBYGjIoCAMOAh1WiwIWLF9Hx+kGmz6v0zUcR2qj2x2fosj6r/J6+GkOX9Sj9e7NNy1Kk3hJh+ryy8n6QoUsb3kKX2fvBnh3Nu2C6U7z3rfgxZuwHhd/GeKXQ0FAkJibiwIEDau5GFp4JZH9mDzaSxwlndXmjZl6JXAMH4BleSvEMLzE8w8vNSWd4eej1+oqhy/oYusQwdCk4TsVrFG+cuB9k6NKGFUMXwDO8tGCXM7xUxa7i4mLs2bMHMTExWq2nTgxe9mWFwWYmuwQku6xTCbXzSvRsIAYvZRi8xDB4uTkteOnx+oqhy/oYusQwdCk4TuPQBThvP8jQpQ2rhi4PBi/17BC8hGLXn/70J6xbtw6HDx/GDz/8gLFjx6KgoAATJ07Ua301MHjZj5UGm5msHpL0XJ8ebw2tjx7zisHLGAxeYhi83OwcvPR+fcXQZX0MXWIYuhQcp1HocvJ+kKFLG1YPXR4MXuqJBC8z9oNCsevYsWO488470alTJ9xxxx0ICgrCpk2b0Lp1a73W5xWDl33Y7bnS+w+hVYOXVdelhl7zisHLGAxeYhi83OwavPR8fcXQZX0MXWIYuhQcp+EZXU7dDzJ0acOM0KUGg5d6Vj7DS9UF6pVQekFCb3jRemszYrBpfVaXUcXZShesNyp0KbkooZYXqFeirgs+84W9MXjRejEMCG5mxAMtL1CvhLd5xdBlfQxdYvj3oYLjvPx9eKmwQLML1Cthtf0gQ5c2zApdWuwHedF69eRctN7o/aCqa3aZzcqV2NfZZbCZxSpnUhm5DjNOXdUTz/AyBs/wEsMzvNzseoaXlhi6rI+hSwxDl4LjdLhGl9Wo3XMwdGnDzqEL4BleWpBzhpfR+0Fbxy6AwcuK7DTYrmT0H76E+IamRS8zH9tJGLyMweAlhsHLzZeDF0OX9TF0iWHoUnCcD4QuD6V7D4Yubdg9dHkweKlntbc02j52kbXYcbCZzcjwxMilPQYvYzB4iWHwcjMyeEUcy1b8WFpq7XdY6PYMXcZj6BLD0KXgOB8KXUoxdGnDKaHLQ85b8ahuVgpejohdPLvLd+g12KzwFjtPiNI6Rul1v0pY4ddZDwxexmDwEsPg5WZk8LIbhi7jMXSJYehScJyPhi6R/SBDlza8hS4j6L0fZPBSr67gZeR+0BGxC2DwsgIrfqSsXV0ZqERCldLjSD0GL2MweIlh8HJj8KqJoct4DF1iGLoUHOejoctDzl6EoUsbtYUup+wHGbzUs8IZXoFmL8CXXNyWqer4Rj17aLQS7dl5sNnlbCOnhKvdJ5sq+iQOO4hvUSC0EejcqaHijUDnHm18diOwJ/OIoo3Ann2XhDYCe0+GC0XMw1I72ddKOt+qm1BsCErsrjg2xPXvyuAAd3RRGhtKsnaoio5Ww9BlPIYuMb769xvA0KUnhi5tODF0edsPvvetNYKNnd09yHs4NGo/6JgzuwDrnd11cVtmtS+r3Z9d+NIZXWRNItfA4RlexuAZXmJ4hpcbz/Bi6DIDQ5cYhi4Fxwm+lugYLfZaxU5q2w8ydGnDaW9drA/P8FLPzGDoqNgFmB+8jIxRVoleev6a6z3Y7HJWl9PY8ddd5NPNGLyMweAlhsHLzZeDF0OX8Ri6xDB0KThO8DWE6GsUO7p6b8LQpY26QpeT94MMXup5C15G7AdNi10/B3Uw66F1Y1Z4skr00prZg430ZcdffwYv62HwEsPg5eaLwYuhy3gMXWIYuhQcZ/PQZcR+kKFLG049o0vufoTBSz0zgpepZ3Y55e1pVolNZqxDr4rvlN8bVDcGr+oYvJRh8BLD4OXmS8GLoct4DF1iGLoUHGfz0OWh12v+wnbXMXRppL7Q5Sv7QQYv9YwOXqa/jVGP38RGvpXRCpHralZckwgjBpsdIwtZB4OX9TB4iWHwcvOF4MXQZTyGLjEMXQqOc0jo8tDjtX9kQj/N79MXOfWMLkDZfpDBSz0jr+FleuwCrFdt5bJyVLLy2upi1cFG+rHr88HgZT0MXmIYvNycHLwYuozH0CWGoUvBcQ4LXR5a7gEYurTB0OUdg5d6VwcvvfaDlohdgPa/qfU+u8sOMUnvNWr9a2z1wUb6sevzwuBlPQxeYhi83JwYvBi6jMfQJYahS8FxDg1dHlrsBRi6tCE3dPnqfpDBSz0jgpdlYped2CF0edhlrXYZbKQfuz4/DF7Ww+AlhsHLzUnBi6HLeAxdYhi6FBzn8NClBYYubfCMLnkYvNTTO3hZKnbZ4e2MdolHVJ1dQ4qvsevzxOBlPQxeYhi83FQFr907NVyJchHHsoVuz9ClHkOXGIYuBcf5UOhSuh9k6NKGWaHLCHrsMxi81NMzeFkqdgH2CF52o0eg0/KUVat8pCxZg12fLwYv62HwEsPg5eZLEYehSz2GLjEMXQqO86HQ5SG6N2Do0oZo6OJ+0I3BSz29gpflYheg3W92rd9DbOezurRY+6nzBUj7bhveSd+AFf9bjdy8s6rv086DjfRj1+eNwcs6iovycOLQ11j54avYu3UZCgtOCR3P4OXbfCHmMHSpp1XoOnUuH2lrf8A7X69D2tofcOpcfo3bM3TZG0OXOLl7BIYubcgNXdwPesfgpZ4ewStQ9T3o5EhQPNqU7DV7GQQg+8gJzPlkFVZs/BGlZWWV33e5XLhl+BA8cf/d6NJR/gsrDz0Hm11jCVXZfbIpLhVadkTVqq3fIdkbjfgWBUIbjc6dGireaHTu0cYnNhoFZ/fj0I6FOHl4FcrLSwEAO78FAgJd6Njjdtww6k+IikuQdV979l0S2mjsPRkutNE4LLWTHUjPt+omdB2moMTuimNGXP+uDBpwRx2nxj+GLvW0CF3Zh49iztKvsGL91uqvrwIDcfvAXnh6/I1IaNuKocvmGLqUq28/yNClDTmhi/vB+r33bc1gQ2LuHlQ9HKrdD1ryzC4rsvNZXR5Kfg7pmXsweNpcbDt+FrNmz0Zubi7Ky8uRm5uLWbNm4cd9h/CbCQ9hzfc/6LBiZRi6SGui18DhGV7myD36HTZ+fidc5XsxZ86savNqzuxZuJy3DR/MGYLDu9Nl3yfP8PJtTow7DF3qaRG60rdmYfDjL2Hbz7k1X1/Nno1tP+di8OMvYcXPhbLvm6HLeqwaulr7HRa6vRUxdGlDTujiflA+nuGlnpbB0NKxi9fvMlf2kRMYP+s/SB46DDuzsjFlyhRERUXB398fUVFRmDJlCnZmZSE5ORmTpjyH3fvlb/D1em4Zukgvop9uxuBlrIKz+5H5zeMYMTwZu7J3ep1X2dk7MXxYMj5fcCdOH5cfMBm8fJuTIg9Dl3pandE1/i/zfn19lVX766uhw3D/Y09jz/4D9d43Q5f1WDV0ibw+sQJvewaGLm3IPaOL+0ExDF7qaRW8LB27APV/CLS+bpcvmfPJKsTExuLjtDSEhIR4vU1ISAg+TktDTEwMXv/Pe7Lu16mDjZyPwcu6Du1YiLi4WKTVM6/S0j5GXGwMflj5N6H7Z/DybU6IPQxd6ml1ja45S79CTGycvNdXsTF4c+GiOu+boct6GLq0deXegaFLG3Kv0cX9oDIMXuppEbwsH7uswAlvYfSQ+3M5db4AKzb+iNTJj9Y62DxCQkLwSGoqPk/PwOkz57RYpjCrDDZyPgYv6ykuysPJw6vw2KOpsubV5MmPYH/mchQW5Ao9DoOXb7Nz9GHoUk/Li9GvWL8VqZMny3t99Ugqvly5GnlnvF8EmqHLehi69HEkKJ6hSyN5z98n63bcD6rD4KWe2uBli9jFtzMa79vsAygtK8OECRNk3X7ChAkoLS3D91u213k7PZ5Lqw02cj4GL2s5k7MF5eWlQvOqvKwURw+sF34sBi/fZsf4w9ClnlahCwC+/XGvgtdXpdiweWuNH2Posh6GLv307MjX+1qQG7oA7ge1wOCl3vj+yo+1Rewi4128VAwAaNasmazbe253oVD+hVTV2n2yqWUHGzkfg5d1lJW6547ovPp5v7IL9DJ4+TY7RSCGLvW0DF0AcPHSZQAKXl9drP76iqHLehi69MPQpQ2R0AVwP6gVBi/z2CZ2mXV2l5Pewujx07oNSPtuG95Jd//vqfM1/9Js1DAYAJCXlyfrPj23CwsNrfU2Wj6HVh9q5BsYvPRXXJSHE4e+xi97l+HEoa9RXFRzJgW63HNHdF4FukIVb8wYvHybHWIQQ5d6or/fcy8U4pPM/Xh3UzY+O1OMU+fya9ymUcMGABS8vmpU9fqKoct6GLr0w9CljatDF/eDxmLwMkeg2Qsg4+w+dRavb9iJL/b8jNLy8srvuwIDcXvfbnj6tyOR0CYWADAooQMCAwLw1FNPYdCgQQgLC0PXrl2xa9cuXLhwAWFhYRgyZAiio6MBAEuWLIHLFYj+1+v/gQB2GmzkfI2P/igUG9r6HZK9UYlvUSC0UencqaHijUrnHm0stVEpOLsfh3YsxMnDq1BeXlr5/YAAF1q0HYl23R9AeNOOAIDImOvh7x8oNK8CAlyIjO0NwL1BU7JR2bPvktBGZe/JcKGNymGpneyNyvlW3YTia1Bid8UxJK5/VwYRuKOQVeMfQ5d6Is/trpw8zP1mKz7LOojSMs/rq2/cr68G9sLT429EQttWAIBB3eIVvL5yoV/vXgAYuqyIoUs/DF3auDJ0cT9onve+1e5TBkkeP0mSJCMfsKCgABEREVi77TAaNRL7SxgA2pTsVfS4oYfqfu9wbZxyZteaQ8dwz7IMxMS1ROqjj2LChAmIjIzEmTNnsGTJEsx7603knDiBpVPvQ0yTCMz5ZBU++T4T5RUVlffh5+eHK3+7uFwujB07Fk888QTuuvNOdI9vh3/NmuH18bWq+HYcbKTcpcICPHRjBPLz8xEeLj4v1PLMq1Mr5iM8tO4XpaJn13DDUrvco98h85vHERcXi8ceTa0xr954cx6OHz+BHsNeR4PQ5ji0YyFyfvofKiqqXrTVNa9+N/4ulAbEo8fQOdUe1wkbFtGzDZVGEYBhxMMTRQouF6PVc/80fV4dfelhhDcIFj6ez2cVkdC1eu/PuHux+9MVvb++egs5J45j6fOpiIlsjDlLv8In6zbLf311151I6twJ8+e8xL83LMiuf29cuHgRHa8fZPq8qms/yNCljStDF/eD1sDgJcYzL5TMK8auejghdu0+dRajF/0XycNH1PqxsUVFRRiXkoJvVq8GICGuZUukTvYyBOfNQ05ODt5++22cOHEC8+bNwy+//AJ/Pz+kf/hvdOno/YWYFsPN7oONxNkpdgEMXlooOLsfGz+/EyOGJyOtjnmVkpKCVenfABLQslVLr1GstnlVUeGH/rd/XHlm2JXsunG5EoOX8eL6d7V17OLzWEX0jK7hb6Yhedjw+l9ffbMakCTEtWyF1MmThV5ffZ32Hhp0+I3sdfnK3xdms/PfF1aPXQxd2rj6jC7uB62DwUs+n4pdgLLgpSR2GRG6Dq/x/gKz7VDt3hrx0Iq1+LFQws7s7Do/NraoqAhdunRBWWkp9h84UPsQHDcOGRkZ2LRpE9q1a4exY8ciY80arPpgodfhxsGmjS4tvH/s+JWc9utkt9gFMHiplbnmabjK92JX9k5Z86q0tBQHBOdV+uo16DfmI6+xC7D3BsaDwct4YT3b2zJ28fmrIvq21PuW/A+Z50t0f3218KMMtOsob22+8PeEFdj97wkrxy6GLm1cfY0u7geth8FLHjWxyzYXqHeSw2t2VX6puY0cuRcv4Ys9PyP10UfrHGwAEBISgsceewy5p0/j4sWLtd7m448/RkxMDF555RWEhIRg2bJliIuLw+v/eU/VWmvjtMEmR5cWZ2t86XkcaYcXrVeuuCgPJw+vwmOPpsqeV6cVzKuWLVvi0I6Ftd43L1ovxqrXrTJaziZlZ56biaGripKL0X+WdVD311exLVvivYWvyVoTQ5cx7B66ACDiWLbQfRuFoUsbV4cu7getiRet1x9jl4GUxis10ev7n3NQWl6OCRMmyLr9hAkTUFpairVr19Z6m5CQEDzyyCNIS0tDbm6u+79TU/F5egZOnzlX7bZmfYqmHekVqBi+1CvZvVPo9gxeypzJ2YLy8lLd59Wjkx/BycOrUHzpTK3HMXiJYfCyH4auKkp+/64/eBylZfq/vkp95BFkrPoUZ8+crvP+GbqM4YTQJfoaxSgMXdq4OnQB3A9aGYOXvmwZu+z4B0btGVpK7+NiiftTzJo1aybr9p7bFRTU/Zfs1UPQ/d9l+H6Lsmuj1caJFf9qRoYoRi/lRN/mxeAlrqy0EIAx86q8vBRnTmyu8zgGLzEMXvbB0FVF6e/bi8UlAIx6fVWKzM3raz2GocsYDF36YejShrfQBXA/aHUMXvoxLXbtO+Xs37RX0iJ0ybmv3IuXsGLXT3gvcx9W7PoJuRcvoVGQCwCQl5cn6/49t6vv/bBXD0HPf18oLJT1OHI4fbCZGZ4YvZRh8NJOcVEeThz6Gr/sXYYTh75GcVEeAl2hAIybV564VhcGLzEMXtbH0FVF7u/X3AuF+CRzP97dlI1PMvcj90IhGnfoAMC4eVVYeMHr7Rm6jMHQpY99p5oydGnEE7q4H7QXz55s237uy/SgKnbNnDkTfn5+eOKJJzRajnVodXF6LUNXbfe5+9RZPLRiLbq/8TEeXL4WT375PR5c7v7vT3cdRqC/P5YsWSLrvpcsWQKXy4UhQ4bUeburh6Dnv8NCQytvo+YMPF8YbFZglXUYQat5xeClTsHZ/chc8zTWfDgM2795Cju/fR7bv3kKaz4chhOHvoa/f6Bh88oT1+rD4CWGwUs9vV5fMXRVkfP7dFdOHu5b8j90+es7uHfJ13gs7Rvcu+RrdPnrIixbtxmBAQGGzavQ0LAat2XoMgZDV93UzKvx/bVfjy/Ke/4+7gdt6Op9GIOX9hTHri1btmDBggVISkpS/OBqfgPb4a2MeoSuq+97zaFjGL3ov/ixUMKsOXOQm5uL8vJy5ObmYtacOdhX4g8/fz/MmTULRUVFdd5nUVER3nrrLaSkpKB58+Z13vbqIej+70D0v/46TX5+Tma1wOQLZ3lpMa+uxOClTO7R77Dx8zvhKt+LOXNmVZtXc+bMQgO/w/Dz88OsWXN0n1cBAS5ExvaWvXYGLzEMXsppPa88GLqqyPn9uXrvzxj+Zhoyz5dg1uyrXl/Nno1dOefh5+eHObNnG/D6yoUevQdWux1DlzEYuuqm17wi+fKev4/7QRuqbe/F4KUtRbHr4sWL+P3vf4+FCxeiSZMmqhZgVLEtbGfsHzo9Q5dH+hdbcM+yDCQPH4Gd2dmYMmUKoqKi4O/vj6ioKEyZMgU7s7MxYuQo5J4+jd+MGlXrgCsqKkLK2LH45Zdf8Pjjj9f5uEVFRZg/f37lECwqKsL8efNw64hkREWq+/0AOLviWzkqWXltamg5r67E4CWm4Ox+ZH7zOEYMT8au7J1e59Wu7J0YOWoETp/Oxah65tXYsSmK59Wbb81Hi7YjEdwwUujnwOAlhsFLnF7ziqGritwzuu5e/BWShw2v/fVVVhZGjBwp7/VVivJ5NW/+fCSPvA1NI6Mqb8fQZQyGrrrpNa9Ivi0D+nA/aEP17bkYvLSjKHalpqbipptuwvDhw+u9bXFxMQoKCqp9OZ0RoQsAFh0/gZi4lvg4La3Wj5ENCQlBWloaWl9zDTZu2ICkhATMnTsXubm5qKioQG5uLubOnYukxARkrPkGgf7++MuLL9Y5BMeNG4ecnBxMmzbN/d8pKcjJycHj992t+ufky4ON9KHnvGLwku/QjoWIi4tFWj3zallaGq655hps2LARXROSvM6rrglJSF+9BvALwIuC8yolJQXHj51Au+4PCP8cAAYvUQxeYvSYVwxdVeT+fpz7zVbExMbJf321cSOSEhO9v75KSsSaNWsQGBAg/PoqJWUcck7k4O4HplTejqHLGAxd9eN+0FxbBvQBwP2g3cjdDzJ4aUM4di1duhTbt2/HzJkzZd1+5syZiIiIqPxq1apVjdso/Q1th7cy6uVMSSkyzpxD6qOP1jrYPEJCQpD66KOAnx/igysw7ZlnEB0djYCAAERHR2Pa1Kno1TIS3856CmnPPoCMNd8gKbGWIZiUhIyMDLz99ttIT09HUmIiMjIysOi1l9ClY9Vmy5efG2/sErrssk659JhXV2Pwql9xUR5OHl6Fxx5NlTWvHn10MuDnh8tSWzzzzLRq8+qZZ6ahNCAe/cYsRc8RbyF9dUatUezqedU1IQnp6RnoMfx1hDftqPjnzuAlhsFLHj3mFUNXFZGL0X+WdVD+66vJk+HnByTENMG0qVOrv76aNg1JnTvhq4/exTvz5iIjIwNJSbVFserzKvHX/37p9ffQrqN77QxdxmDoqp8Rr6+odp7Qxf2gvYjusxi81POTJEmSe+OjR4+iV69eWLVqFbp1c79QHjJkCLp3746///3vXo8pLi5GcXFx5X8XFBSgVatW+NdX+WgYWvWXttJNdpuSvbJvG3pI/segKr1AvVFndaWfPoPn9h9Ebm4uoqKi6r19bm4uoqOjsfCOIejfJgbfH8nBhZJSRMV3xKCEDmjeuOrCp9lHTuDV5elYvmEHSsvKKr/v5+eHK3+7uFyBuHVEMh6/7+5qgw1QNtycWvHtGJCs8lxcKizAQzdGID8/v95PhLmalvPq6EsPI7xBcJ2PJxoDRGODSMiw2oboxKGvsf2bp4Tn1XXD/obI2N44c2IzykoLEegKRWRs72pvPyw4ux+HdizEycOrUF5eWvn9q+dVQIALLdqORLvuD6gKXVfyxQ2RaNy9ktPDy8XSMvT+Yq1l5pXTf71FiATXTzL3494lXwvPq/ee+yMGJcXjf8cv4cLFQoQ1CkW/3r3QLLLq79M9+w/gzYWL8OXK1SgtrX1euVwuJI+8DXc/MIWhy2C+MtcLCi8h+vZHTJ9XSh7f13lCF8D9oJ2o2Q/6+ieWFhQUICJC2X5QKHZ9+umnuP322xEQEFD5vfLycvj5+cHf3x/FxcXVfqyuxV4duwBlvwl8NXZ9ejIXLx86jIqKCvj5+dV7+4qKCgQEBGDuzf1xd49Old9v1LNHrcfknr+Ab7MP4MKlywhr2ABt+w/D3oM/4UJhIcJCQ9H/+utqfU82h1sVxi7l1MQuLeeVnNgFMHjV5pe9y7Dz2+eF51XSoL/gmvixstZQfOlMtSgW1qQDLpw7UGsk04qvbIyuxODlnZrYpfW8urDtoJKfgiOJnln47qZsPJb2jfC8mj9lEm5/8DFZj5F35iw+/eEoCgsvIDQ0DG07dMHhA7sr/7tH74G8RpcJfGmeq4ldWs4rxi4xV4YugPtBO1G7H/Tl4KVmXgSK3HjYsGHIysqq9r177rkH8fHxmDp1ar2Dzen0CF1nSkqxPb8AheXlCA0IwHUR4YgMciH011/rvLw8WSW/8qNgg1yyH7t54zCMHVB1Yf/Cdm0R376t4M9AHg42a+nS4qztnxMz5lVJ1g6h4NX46I9Cwaut3yHZwSu+RYHQBqlzp4aKN0ide7TBnswjKC7Kw5mcLVWBKeZ6BIc0Q6DL/RHUovPKc5wcwQ0jEdtudLXvhTVtL/CzUGZP5hFFG6Q9+y4JbZD2ngwX2iAdltrJ3iCdb9VNKHgFJXZXHLzi+nd1dPBSSst5lbNpLxq5hF7eOVZdoSv3QiHWHzyOi8UlaBQchIHt49A8LBSNgoMAiM+rgFbyN3UXml6PYaOvr/a9a9t7P56hyxi+FLrU4n7QeKdOncJ/Bg3gftCmtNgPbtt/1qeDl1JCr4bCwsKQkJBQ7XuhoaGIjIys8X0ldp9sKvyb4UhQvNDZXXZxsLAIi46fQMaZcyitqKj8vsvfH8mRTXBb8+Zw+ftjyZIlmDJlSh335LZkyRK4AgLQv02MnssGwPdnkzXoPa9q44vB6/TxbBzKfBX7t6+o9lZCz1sHW3Uai4AAl9C8CghwITK2t/BazMDgJYbBqyaz5pWT1Ra6duXkYe43W/FZ1kGUlpVXft8VGIAxie0xqU9XuAIDxV5fuVzo17uXrHXZ9QxdJ/PF0FWye6fQ7a/EeWWcrKwszJw5E8s++oj7QWLwUkDRpzE6nZK3MGp5VtfGc+dxX/YeHAwNw6xXX0Vubi7Ky8uRm5uLWa++ioOhYXhq3wEkhoXirTfeqPWTMjyKioow/623cGvnNogKVX5Bar2w4luT3ddvJl+6aP3h3en4YM4QXM7bhjlzZlWbV3PmzIKrfC+2pT+Cxs174I0358maV2++NR8t2o7U5W2HeuFF68XwovWkp9p+f63e+zOGv5mGzPMlmDV7TvXXV7PnIPN8Cca981/07dIe8956S97rq/nzcPOo4dWuzVUbhi7r8cnQpeKt6GSclStXok+fPvhh40buB21M6/0UL1ovRnXsWrt2ba0XI1TCqb/R5TpYWIRp+w8hecQIZO3ahSlTpiAqKgr+/v6IiorClClTkLVrF5JHjMDuwks4fvQoUsaOrfujYVNSkHP8GB7rl2jwz4bIWrSeV3XxheB1+ng2Pl9wJ4YPS0Z29k6v82pX9k6MGJ6M/LydOHb0GFJSUuqcVykpKTh+7ATadX9AaM1WwOAlhsGrbkbOKyep64yuuxd/heRhw7EzO9vrvNqZnY3kocOwZd9POH7sKMbVM6/GpaQg50QOHn1gUr3rYuiyHoYu7XBeaSsrKwt33HEHhgwezP0g1cDgJR/P7LKYRcdPIKZlS6QtW1brR8iGhIQgbdkyxLZsiYRGochIT0di167ePxo2IQEZq9PxzthkdImuGRKVXoiffAPP7nLL2aTsrdJOD14/rHwVcbExSEv7uO55lZaGli3jENG8G9JXZ6BrQpLXedU1IQnp6RnoMfx1zT4x0WgMXmIYvEhLdf1+mvvNVsTExuHjtLQ659XHaWmIjWuJGzq3Q8aab5CUmOj99VVSIjIyMvDvN+agc8cOda6Loct6GLrIymbOnIkWzZtzP0i1YvCSx5Kxy25nd2n1FsYzJaXIOHMOkx97rNbB5hESEoLURx/FzgsXMbdTB3QovIhpTz+N6OhoBAQEIDo6GtOeeQbdQ/3x9aSbMLRdS03WqDW7PddyMRI5j9JrDDk1eBUWnML+zBV49NFUWfPq0cmP4PypTPQc+Q+UBsTjmWemVZtXzzwzDaUB8eg75kM0bzVAaJ1Ww+AlhsGLtFDfxeg/yzqI1EcflTWvHklNxYZdB7Hi/x5Hr9bNMW3q1Oqvr6ZNQ1LnTvhy6SIkD+hX5/0xdFkPQxdZ2alTp7Dso4+4H3QAvfeDDF7148f1WMj2/AKUVlRgwoQJsm4/YcIEPPXUUzhbVor/69gOU0quwbaCAjTsEI2wIBf6t4kx/D3ZvBghOdnx73cp2pg78aL1R/evR3lZqfC8Krl0Bj2GzkHxpWk4c2Jz1Sc3xva21TW66sOL1ovhRetJjfrm8vqDx1FaVi48r3LPX8Ci6Q9h9sN34tude3Gh6BICWsWjX+9evEaXTTF0kdX9Z9AA7gdJNl60vm6WPLNLlFP+QBWWuz8VqFmzZrJu77ld4a+fJtQ0yIURzSJxd49OuK3rtZa8+CCR3fEML7eSyxcAiM+rstJCAEBww0jEthuNa+LHIrbdaEeFLg+e4SWGZ3iREnJ+31wsLgEgPq8uFLn/LDZvEo6xg3vj9gcfw62jRzJ02RRDF1ndlgF9uB8kYTzDq3aWjV1OPZ2xLqEBAQCAvLw8Wbf33C40MEC3NXkUtrtO8/t06nPstLcwOu3nowUGLyCoQRgA8XkV6AoVely7Y/ASw+BFIuT+fmkUHARAfF6FhVTNPZE/Bwxd1sPQRVa3ZUAfANwPOoXR+ycGL+8sG7vsQqvrdQHAdRHhcPn7Y8mSJbJuv2TJErj8/dEzXOyFEhGp5+vBq1XHgQgIdAnNq4AAFyJjews9phMweIlh8CI5RH6fDGwfB1dggNjrq8BADEpyv3OAocveGLrI6jyhC+B+kJRj8KqJsctgBwuL8LefjmDG/kP4209HcLCw6iNiI4NcSI5sgrfeeKPWj471KCoqwrw338TQyKZoGuSq9mNaBjgiqp3Tg9exn7LxwVuP498zJ+KDtx7HsZ+yK1/8h4ZHo2OP2/Hmm/Nkzau33pqPjtfd4ci3K8rB4CWGwYvq4u33x+6c05i6Yh0e+mAlpq5Yh905pyt/rHlYKMYktse8N9+UNa/mz5uHOwb2QvMm4QxdNsfQRVaUnZ2Nxx9/HBMnTsT42BbcD5JmGLyqY+wyyJq8s7h1aybu2pGFj3JO4avTefgo5xTu2pGFW7dmYk2e+zfmpLhY5Bw7hpSxY2sdcEVFRUgZOxY5x45hYlyMkT8NzTj1lFXyPU4MXtu+/QR/urM1nr8/EauXv4EN6YuxevkbeP7+RPzpztYoOvUVAOCGUX/C8RM5SEkZV/e8ShmH4ydycMOopxRvPJyAwUsMgxd5c/Xvi893HkDCX99G31c/wD+/24Gl2/bin9/tQN9XP0DCX9/G5zsPAACeHNYLOTknMC4lpc55NS4lBTknjuNP429k6LI5hi6ymk8++QSt27RGYlIi3njjDSxevJj7QdIcg1cVS8curf8A6PE+YznePnoc0/YfwMmGJcAoAE8DeP7X/x0FnGxYgmn7D+Dto8fRPjQEr3Rsh4z0dCR27Yq5c+ciNzcXFRUVyM3Nxdy5c5HYtSsy0tPxSsd2aB9a90fS+pLsvZe8fpF6Rr/v3PPc7d5vj+fPScHryyUvYd6LY3G2+Bev8+ps8S+Y9+JYHNoyF1FxCbj1wQ+x+psMJCQkeZ1XCQlJWP1NBm598ENExSUAUL4BcQIGLzEMXnSlq38/zEnfjLsXf4Wj0gWv8+qodAF3L/4Kc9I3o8fI4Vj6fCoy1nyDpMREr/MqKTERGWu+wdLnU9Fy0M2y18XQZT0MXWQ1L730EsamjMUv+b8AI8H9oE64H3Rj8HLzkyRJMvIBCwoKEBERgX99lY+GofX/hS93k92mZK+s24Ue2l7vbS5uy5R1X0D9p4iuyTuLafsPAO0BpAAI8nKjEgBpAA4Cr3TsgKHNmuJgYRHePZ6DNWfOorSiovKmLn9/DI1siolxMXUOtrZD5W8QGvXsUe9t5IRCkU/F1DpkyhliCfH6fxqJky/mbtS/vlz5XBZfKsDrT7RAfn4+wk24FoFnXm2+ZQgauQLrvb3SjbloDBCNDXI3YmtXfY5np9wte16lvrAMIdE34vTxbPyw8m/Yn7kc5WWllTcNCHShY487cMOopypD15W4EVNwnI9uxJQGZSNdLC1D7y/W2mZe2Y23M7ruXvyV7Hn14f9LxW0DeiH78FG8uvQrLF+/FaVlZZU3dQUG4o6BvfCn8TcydNkc52v9Ci4Xo9Vz/zR9Xpn1+Eb75JNPMDZlLPeDV+F+UH89O9r/7Dk184Kxywu5sUvOe6Fv3ZrpPqPrEXgfbB4lAOYDLS4F4fNeVcPmbEkpthUUoLCsHKGBAegZHl7jPdne+NJwE6n1eg84Kw03rRkRu65+Lu0WuwB7B6/bhyfgVNFR2fMqMrg15nx4pHJDVliQi6MH1qPkUgGCGoajVYeBCA1vXudjckOm4Dgf2pBdyerBi7FLP97masJf33af0SVzXl0THIl9771a+e3ccwX4dudeXCi6hLCQhhiUFM9rdDkA56o8jF3Gat2mtfuMLu4Hq+F+0Bh2D15q5oWl38YoQuQPllEOFhbhZHEJcAPqHmz49cd7AyeLS/DTFRcpbBrkwohmkbitRXOMaBYpa7ABvnNRQl88LdWpnPJc2vUtjYf278apnKNC8+rMqZ9x/MiuKy5a3xzxPX+LpAH3IL7nb+sNXQDf0qjoOL6lkXxIbRejP3rugtC8+uXUGew+crzy282bhGPs4N64Z/RgjB3cm6HLARi6yIqys7Pxyy+/cD+oI6fsIfTiy29pdEzssqLPTuW6/0+SzAN+vd2nnuMcSquKr2SwcRhak9OeFzsGr8+XvQtIEJ5X6/77bwDim4UrMXgpOI7Bi3xAbc/3u5t2u/+P4Lx65+t1td6EocveGLrE5GyS944YUm/hwoWKXl9xPygP94Py+GrwsnzssvOnNFwoK3f/H7nXDPz1dgWe46hWvjikyF7sFrwuXjjv/j+C86qooOovTwYvZRi8xDB4+Ya6nufzly67/4/gvDp3odD7/TF02RpDlxirvyXcac6fP+/+P9wPao77QTG+GLwsH7uMJnJx+vqEBQa4/4/3T4yt6dfbhXuOI11wMFqLk58POwWvRmGN3f9HcF6FhFf/BwkGL2UYvMQweDlbfc9v44YN3P9HcF41CQut8UMMXfbG0CWGoct4xekr3f+H+0FLcfL+oy6+FrwYu3Q0JvrX69XslHnAr7e7Lbr+69z4Ml8dTmRPdglet46dCPhBeF4Nvun+Gj/E4KUMg5cYBi9nkvO8TuzTxf1/BOfVPaMHV/s2Q5e9MXSJYegy3pYBfbgf1An3g8r5UvBi7NJR+9AQtAgOAn6A+9M16lICYDPQIjgI19bxEbJEZrHaJ4vYiR2CV7uOXRAd00poXkVGt0ZcG+8bUwYvZRi8xDB4OYvc57NLTBRaNQkTmlfXREeiS5u4ym8zdNkbQ5cYhi7jbRnQBwD3g2RNvhK8GLsUkvvpFk+0aQ3kA0hD7QOu5Ncfz//19lQrrSq+Hv8awBhEdbFD8Hr8mZeF5tX4P/6tzvtm8FKGwUsMg5cziD6PL986UGhezXpofOW3GbrsjaFLDEOX8Tyhy4P7QW1xP6gNXwhejF06G9qsKR5u1RI4CGA+gA0ALgKo+PV/N/z6/YPAw61aYmgz+16QXw47f+AAac/XTkG2evAaMvJWPPjYn2XNqzvu+St6DvptvffN4KUMg5cYBi97U/L83ZrUAX8e1VfWvJox8Q7cNqAXAIYuu2PoEsPQZbyrQxfA/eDVuB+0DqcHL8YuA9zbKg6vdOyAFpeCgFUAXgXwl1//dxXQ4lIQXunYAfe2iqv7jgTJPfuMiIxj9eA16aGn8fJr76FFSCsgHdXnVTrQIqQVXn7tPdw84TnZ983gpQyDlxgGL3tS87w99+SD+PD/peKa4Eivr6+uCY7Eh/8vFVPvugUAQ5fdMXSJYegynrfQ5cH9IFmVk4NXoNkLkGP3yaa2OiXQm6HNmmJos6b4qbAIn57KRUFZOcIDA3BbdHPT35N9cVsmGvXsYeoa5PC1s4DIuY5/v0vRBq8ka4dQDGh89EehzV1bv0M4LLXDkJG3YsjIW/HTwb34PG0RLhScQ1h4E9yaMgnXto//9dYFQhu8zp0aKt7gde7Rxmc3eHsyjyja4O3Zd0log7f3ZLjQBu+w1E72Bu98q25CG7ygxO6KN3hx/btyg2cjakKXZxbeNqAXbhvQC7uPHMc7X6/DuQuFaBIWintGD+Y1uhyEoUsM56Dx6gpdHtwPqsf9oD627T+Lnh2dd8adLWKXk1wbGoInr21j9jKIyGRWD14AcG37eDwx/ZVabxvfgsHLCAxeYhi87EGL0HWlLm3iMOePd3m9PUOXvTF0ieH8M56c0HUl7gfJipwYvPg2RtLEkaD4+m9kMfyXATKb1d/SKIfo5oBvaVSGb2kUw7c0WpvWoasuDF32xtAlhqHLeKKhy8m4H7Q/p72lkbGLiEzBv1zcGLzEMHgpOI7BiyyEoUsMQ5eC4xi6yCAMXeRETgpejF1kCwwj5GQMXmIYvBQcx+BFFsDQJYahS8FxDF1kkLzn7zN7CT6H+0HjOCV4MXYREVkAg5cYBi8FxzF4kYkYusQwdCk4jqGLDMLQRb7ACcGLsYuIyCIYvMQweCk4jsGLTMDQJYahS8FxDF1kEIYu8iV2D16Oil12vCgeEdGVGLzEMHgpOI7BiwzE0CWGoUvBcQxdZBCGLvJFdg5ejopdREROwOAlhsFLwXEMXmQAhi4xDF0KjmPoIoMwdJEvs2vwYuwiIksqLDiFfds/M3sZpmHwEsPgpeA4Bi/N5F0uRvqJU5rfr50xdIlh6FJwHEOXIpxX4hi6iMwJXqdOncInn3yi+HjGLiKylNPHs/HFwon459T2+N/ih8xejqkYvMQweCk4jsFLlf35F/H05iwM+/o7PLdtjyb36QQMXWIYuhQcx9AljPNKGYYuoipGBa+srCzcdeedaNWyJe69917F98PYRUSWcXhXOt5/eSAub1+OORXlkP9S07kYvMQweCk4jsFLke9OncGdGZux9/gpzJIkzqtfMXSJYehScBxDlzDOK2UYuohqeu9bfe9/5cqV6NO7NzYvW4ZZZWWq5hVjFxFZwunj2fhs/jgMLy9BdkU5pgBoZvaiLILBSwyDl4LjGLyE7M+/iMc3/ojkigrslMB59SuGLjEMXQqOY+gSxnlFRFq6e5B+952VlYU7brsNycXF2FlWpnpeMXYRkSVs+moO4irKkCZJCDF7MRbE4CWGwUvBcQxesi3cdxixUgXSAM6rXzF0iWHoUnAcQ5cinFfKNfvLf8xeApGl9OzYVNf7n/nyy4gpK8PHGu0HhWLXP/7xDyQlJSE8PBzh4eHo27cvvv76aw2WQUS+rLDgFPZvX45HK8o1eyHmxHnF4CWGwUvBcQxe9cq7XIxVx3ORKmm3cbT7vGLoEsPQpeA4hi5FOK/UY/AictM7dJ06dQrLli1DalmZZvNKKHa1bNkSr7zyCrZu3YqtW7di6NChGDNmDHbt4sffEpFyv+xbj/KKckzQ8D6dOq8YvMQweCk4jsGrTlvyzqFUkjivfsXQJYahS8FxDF2KcV5pg8GLfJ3eoQsA1q5di9KyMk3nlVDsuuWWW3DjjTeiY8eO6NixI1566SU0atQImzZt0nBJyrUp2Wv2EohIgZLiCwC0vYaE1eeVGgxeYhi8FBzH4FWrwtJyAJxXAEOXKIYuBccxdKnCeaUdBi/yVUaELgC4cEH7/aDia3aVl5dj6dKlKCwsRN++fWu9XXFxMQoKCqp9ERFdKSg4DACQp9P9O3FeMXiJYfBScByDl1ehrgAAnFcMXWIYuhQcx9ClGueVthi8yNcYFboAICxM+/2gcOzKyspCo0aNEBwcjIcffhgrVqxAly5dar39zJkzERERUfnVqlUrVQsmIue5ptNABPgHYInG9+v0ecXgJYbBS8FxDF41XN+sCVx+fj49rxi6xDB0KTiOoUsTnFfaY/AiX2Fk6AKAIUOGwBUYqOm8Eo5dnTp1wo4dO7Bp0yb88Y9/xMSJE7F79+5abz99+nTk5+dXfh09elTVgsk3JcQr3+CS9YWGR6PjdXfgTf8AFGl4v74wrxi8xDB4KTiOwauaZg2CMTKuOeb5wSfnFUOXGIYuBccxdGnG1+eVXhi8zMH9oHGMDl0AEB0djbFjx2JeYKBm80o4dgUFBaF9+/bo1asXZs6ciW7duuH111+v9fbBwcGVn9bh+SIiuvovrD43Po3j/oFI8fPTbMD5yrxi8BLD4KXgOAavah7o1BYn/PyRAu02kHaYVwxdYhi6FBzH0KU5X51XemPwIqcyI3R5TH/2WeQEBmKcRvtBxdfs8pAkCcXFxRoshezMjh8OwH8dsJaouASMeeRjrA4IQoJ/AOYCOK3xYzh5XjF4iWHwUnAcg1eljhGN8Hrfbsjw90eSH3xiXjF0iWHoUnAcQ5cufHFeGeX675x9UX5R3A/an5mhCwASExOx/NNPkREcjKTAQNXzSih2Pfvss1i/fj2OHDmCrKwsPPfcc1i7di1+//vfq1gCEZFb264j8Ptn16Nhz9/iGf8AtFdxX744rxi8xDB4KTiOwavSgOhIfJjcG/Fx0Zjm5+foecXQJYahS8FxDF268qV5ZTQGL3IKs0OXx6hRo7Bp82bckJKCaYGBquaVUOw6deoU7r77bnTq1AnDhg3DDz/8gP/9738YMWKEiiUQycPy7hui4hJw8/2L8PCsQxg9cYHi+/HVecXgJYbBS8FxDF6VOkY0wpzeiVgzeiBe6ln7xZnrY+V5xdAlhqFLwXEMXYbwhXllFgYv43A/qA+rhC6PxMREvP/BBzh2/DjeeecdxffjJ0mSpOG66lVQUICIiAj866t8NAyV9+KjS4uzsm4n59TJ0EPb6/zxi9syZT3W4TXG/gWlVNuh9b9IbdSzR723KWx3Xb23ORIUX+9tdp9U9wcpe6/yF6BX03pYyv19amdqn7+r1fd8Fl8qwOtPtEB+fr4p13fwzKvNtwxBI1eg4Y+vhtINqp6bU4AbVCviBlVMbRvUi6Vl6P3FWkfNK4YuMZwjCo7jHDGFVeaVWY+vly0D+pi9BK+4H6wd94PWC11XUzMvVF+zi6xLzmAzmh0HAJEd8QwvMTzDS8FxPMPL0Ri6xDB0KTiOoYscxopneHE/SHWxeuhSi7FLISsODl+gVX3X4xRYrc968gU8FVlfDF5iGLwUHMfg5UgMXWIYuhQcx9BFDmXF4OU03A9qw+mhC2DssoQzJaVIP30Gn57MRfrpMzhTUmr2kojIIbQOXqfO5SNt7Q945+t1SFv7A06dywfA4OUEDF5inBq8nBS68s+ewuaMj/Dtf/+NzRkfIf/sKa+3Z+hShqFLDEOX71ASvLgfJCP5QugCAHtdhMZhDhYWYdHxE8g4cw6lFRWV33f5+yM5sgkmxcWifWiIiSu0poT4hpq+V5vksdO/VFB1x7/fpWgDW5K1o3Lzmn34KOYs/Qor1m9FaVlZ5W1cgYG4fWAvPD3+RiRAbPPa1u+Q7A1sfIsCoUjSuVNDxRvYzj3a+OwGdk/mEUUb2D37LgltYPeeDBfawB6W2snewJ5v1U1oAxuU2F3xBjauf1dHbWCdErqO/ZSF/34wE9u+XYaysqoNY2CgCz0HjcVNd01Hy2sTATB0KcXQJcZJc4Lkuf67TbKu4cX9oDLcDyrnK6EL4JldNci5OJ8WNp47j/uy9+BgaBhmvfoqcnNzUV5ejtzcXMx69VUcDA3Dfdl7sPHceUPW40v41jlr4fNhDDVneKVvzcLgx1/Ctp9zMWv27OrzavZsbPs5F4MffwnpW7N4hpcD8AwvMU45w8spoSt7y0q8NLkPTh/ZjNmzZ1WbV7Nnz8LpI5vx0uQ+yN6ykqFLIYYuMQxdvqu+M7y4HzSPr+4/fCl0AYxdpjhYWIRp+w8hecQIZO3ahSlTpiAqKgr+/v6IiorClClTkLVrF5JHjMC0/YdwsLDI7CVbjq8OKCI1lLzg3pWTh/EvvoXkocOwMyvL67zamZWF5KHDMP4v85B9+CiDlwMweImxe/BySug69lMW5r1wB4YNTUZW1k7vr6+ydmLY0GS89fwdOH08W2jtHgxdCo5j6CIfVVvw4n5QPe4Hxfha6AJsELuc+GkNi46fQEzLlkhbtgwhId5PSw0JCUHasmWIadkS7x7PMXiF+tLqOVUy4DgUrYnPi3FEX3jP/WYrYmLj8HFaWp3z6uO0NMTExuHVpV8B4DW8nIDBS0xMn/o/bt2KnBK6AOC/H8xEXGwM0tI+rvv1VdrHiIuNwQ8r/yb7MTwYuhQcx9BFPs5b8OJ+kPtBI/li6AJsELuc5kxJKTLOnMPkxx6rdbB5hISEIPXRR7HmzFmc5UUKvfLFYUWkltwX4LkXCvFZ1kGkPvqorHn1SGoqlq/fitxz7o0Kg5f9MXg5m5NCV/7ZU9j27TJMnpwqa15NnvwI9mcuR2FBruzHYuhScBxDFxGA6sGL+0FtcT9YN18NXYCDYlebkr2GP2bboeIvErfnF6C0ogITJkyQdfsJEyagtKIC2woE//JXsDa7kjvgOAitjc+PseS8EF9/8DhKy8rF5lVZGb7dWTWPGbzsj8HLmZwUugBg349rUVZWKjSvystKcfTAelm3Z+hScBxDF1E1nuDF/aD2uB/0zpdDF+Cg2GUXheXlAIBmzZrJur3ndoVl5bqtSStmBEeP+gaXUYONn1iojq/9BWS2+l6QXywuASA+ry4UVY8cDF72x+DlLE4LXQBwuegCAPF5VXKp/hnC0KXgOIYuIq+u/24T94M64X6wOl8PXYDFY5fW1+sKPbRd0/tTtIaAAABAXl6erNt7bhcaGKDbmpwiIb5hjSHm7Xskzsih7XnO+LwZo64X5o2CgwCIz6uwkJrPHYOX/TF4OYMTQxcANAgJAyA+r4Ia1v37jaFLwXEMXUR1SnppJgDuB/XA/aAbQ5ebpWOXE10XEQ6Xvz+WLFki6/ZLliyBy98fPcPFXvxbnZ4fPMBY4hxdOvI5NEJtL9AHto+DKzBAbF4FBmJQkvcLdTN42R+Dl705NXQBQKduQxAY6BKaVwGBLrTqMLDW2zB0KTiOoYuoXkOGDIHLJTavuB8U48v7QYauKoxdBosMciE5sgneeuMNFBXV/RGyRUVFmPfmmxga2RRNg1wGrZCIfJG3F+rNw0IxJrE95r35pqx5NX/ePNwxsBeaN6n9xRiDl/0xeNmTk0MXAEQ0jUbPQWPx1lvzZM2rt96aj4497kBoeHOvt2HoUnAcQxeRLNHR0Rg7dizmzZM3r7gfJLkYuqpj7FJJyYX/JsXFIufYMaSMHVvrgCsqKkLK2LHIOXYME+NidF8TEZG3F+xPDuuFnBPHMS4lpc55NS4lBTknjuNP42+s93EYvOyPwctenB66PG66azqOn8hBSsq4ul9fpYzD8RM5uGHUU15vw9Cl4DiGLiIh06dPR05ODsaNq2decT9IMjF01WTZ2KXnaY1max8aglc6tkNGejoSu3bF3LlzkZubi4qKCuTm5mLu3LlI7NoVGenpeKVjO7QPrfsjaY2gx/XOnPocW+WihERKXP3CvWtMM7z3hxuR8c1qJCUkeJ1XSQkJyPhmNZY+n4qEtq1kPQ6Dl/0xeNmDr4QuAGh5bSJSX1yO1d9kICEhyeu8SkhIwupvMnDrgx8iKi6hxn0wdCk4jqGLSFhiYiKWL1+OjIwMJCV5n1fcD9qX0ftBhi7vLBu7RJj5qQ9K9W3SGP9J6IwOhRcx7emnER0djYCAAERHR2Pa00+jQ+FF/CehM/o2aWz2UsnHMd75nqtfwA+Pb43Vj6bguiZBmDb1merzauozuK5JEFY/moLBwWKfEsTgZX8MXtbmS6HLIyB8EO56ei0aNOuFZ6ZOqzavnpk6DQ2a9cJdT69F2y4jahzL0KXgOIYuIsVGjRqFTZs24YYbbsC0adO4HyRFGLpqF2j2AnxZ+9AQ/F/HdphScg22FRSgsKwcoYEB6Bkervg92WafstqmZC+OBHm/ODUR2cfx73dV2yh3jWmGf//+N5h5axHWHzqGC5dLENYgCAPbtURUWNW/NpZk7RDaJDc++qPQJrmt3yHZG+X4FgVCG+XOnRoKR5jKY3u08dmN8p7MI4o2ynv2XRLaKO89GS60UT4stZO9UT7fqpvQRjkosbuqjbIRfDF0ef78RsUl4OZ730FhwSwcPbAeJZcKENQwHK06DOQ1urxg6BLD0EVaSkxMxPvvv4/XXnsNa9euxY/PTuN+kGRj6KqbJc/sstvpjGoHStMgF0Y0i8RtLZpjRLNIn7r4oN2eayJf4u0FfVRYCO7o3hET+yTgju4dq4UuD9FNBM/wsj+e4WUtvhy6rhQa3hzxPX+LpAH3IL7nbxm6vGDoEsPQRXpp3rw5xo0bh5cO/sT9IMnC0FU/S8YuIrtzylv/nPLzIOWUvrBn8PI9DF7WENNH+b+mOyl0yT6WoUv8OIYuIl1d/90ms5dAGtBzH8XQJY/lYhfLrnJKzjBr1LOHDishIidh8BLD4KXgOAYv0zF0+RaGLjEMXWQ0pcGL+0HnY+iSz3KxywqU/IE3+73RdubUwMmzoshJGLzEMHgpOI7ByzQMXb6FoUsMQxeZxZfO8OJ+UB6GLjG2j112/CRGp+Nz4gyMdXQ1Bi8xDF4KjrNz8OqSJHR7q2Do8i0MXWIYushsdg1e3A9qj6FLnKVil92Lrplnd9n9zDK7P/e1YTByBjXXwHEaBi8xDF4KjrNx8LIbhi7fwtAlhqGLrEJu8OJ+0Jq02A8ydCljqdhFRNbASFeTmk83cxoGLzEMXgqOY/DSHUOXb2HoEsPQRVZj1zO8SD2GLuUsE7ucUnLNKOp2r/geTvk9cDW7hSO7rddIDF5VGLzEMHgpOI7BSzcMXb6FoUsMQxdZVV3Bi/tBa1O6v2LoUscysUsJvheY7IIByTkYvKoweIlh8FJwHIOX5hi6fAtDlxiGLrI6nuFlX6L7QYYu9SwRu5xWcI0s61at+EpDpNN+L9gNo5w8DF5VGLzEMHgpOI7BSzMMXb6FoUsMQxfZxdXBi/tB52Ho0obpscupv5mNGDpWHWxqOfX3hNVDktXXZzUMXlUYvMQweCk4jsFLNYYu38LQJYahi+zGE7y4H7QXOfsthi7tmB67jBJ6aLvZS9CUFoOtUc8eGqyERFg1KFl1XVbH4FWFwUsMg5eC4xi8FGPo8i0MXWIYusiutHhLI/eDxqtr38XQpS1TY5eaYqv39bq0+IOvV2k3q+CLBkM1z5FTaz7gHnBWiUtWWotdMXhVYfASw+Cl4DgGL2EMXb6FoUsMQ5e5ln5v9grsr9lf/mPo43E/qA1vezCGLu2ZFrs6RTv3N++VtA5TTj1V1RsnDzjA/LOpzH58J2HwqsLgJYbBS8FxDF6yMXT5FoYuMQxd5usUfRbb9jv79b4RjA5eRvKV/SBDlz5s+TZGu30KY9uhXTWJVL4UunyFGWdW8WwufTB4VWHwEsPgpeA4Bq96MXT5FoYuMQxd1sLgpZ6Tg5fTMXTpx5axy66UxiqtYpkZ1IZJp9d8D0+A0itC6X3/5MbgVYXBSwyDl4LjGLxqxdDlWxi6xDB0WRODl3pWDV7cD9bu7kFmr8DZGLsM5glXcuKVnSOXlpw84Ly5MkwpjVNa3Acpw+BVhcFLDIOXguMsFLzyWyYI3bdegrokCd2eocveGLrEMHS5xfSJN3sJXjF4qWfV4KWWE/eDDF36E4pdM2fOxPXXX4+wsDA0b94ct912G/bt26fX2rwy8i2Men86xZXhy9uXnoz85A0tnjMnDji5rg5Xcr7I3HnF4FWFwUsMg5eC4ywUvJQya14xdNkbQ5cYhi43ta9R9J5XDF7qyQ1e3A+ah6HLGEKxa926dUhNTcWmTZuQnp6OsrIyjBw5EoWFhXqtj6iSkwYc6c/secXgVYXBSwyDl4LjbB68zJhXDF32xtAlhqHLTYvXJkbMKwYv9XiGl3UxdBnHT5IkSenBp0+fRvPmzbFu3ToMGiTvWSsoKEBERATWbjuMRo3EXhCpKcKiH5PqcXFbpuLHtDKlJb+w3XWKjjsSpN3p0jxzyXdcKizAQzdGID8/H+HhYvPiamrm1akV89HgJ2X/askX2FWUvsjW8/pDADfyVmTHjfyFixfR8fpBlphX4aF1/zowdNmbHf98XI2hy3hX/h1ccLkYrZ77p+nzqr79IC/crV7e8/fV+mPcDxqPoUucZ14omVeqrtmVn58PAGjatPbfbMXFxSgoKKj2ZTSloYu0o+XbT51Q9Ml4aueVaHDx4BleVXiGlxie4aXgOJuf4eWh5+srhi57Y+gSw9DlpudrET3nFc/wUs9KZ3j5+n6Qoct4imOXJEl48sknMWDAACQk1H5R1pkzZyIiIqLyq1WrVooez8hrdTmdmvdnWyUc2nHAkXm0mlcMXuoxeIlh8FJwnM2Dl56vrxi67I2hSwxDl5uer0GM2A8yeKnnLXhxP2gshi5zKI5dkydPxs6dO/Hhhx/Webvp06cjPz+/8uvo0aMAgDWrvsSZvFylD28YIy/c53RaB0s7DTgyl9p59el3W3HqnPtfLhm81GPwEsPgpeA4GwcvLefVlRi67I2hSwxDl5verz2M2g8yeKlnlTO8fHE/yNBlHkXX7Hr00Ufx6aef4ttvv0Xbtm2FjvW85xIAXC4Xho66Ffc8+ATad+pS6zFq/1Corc9Ou26X2oCn9H3aHlq+Xxuw73u2qX5aXLNLs3kVGIjbB/bC0+NvRELbVopfhPMFeBVew0sMN/oKjjNwo6/FNbv0mlcMXfZmh9//9WHoMl5df8dqcc0uo/eDAK/hpQXPNby4HzQGQ5d6hl2zS5IkTJ48GcuXL8eaNWuEB9uVDh06hFmzZuHAnh2453ejsHH9GsX3RfI58Uy1Li3O2qLqk7E0n1ezZ2Pbz7kY/PhLSN+axTO8NMAzvMTwDC8Fx9nkDC8959WKn+V/QhpDl/UwdIlh6HLT+62LZuwH25TsxZnsDYofi9ya/eU/3A8ahKFLG0u/V36sUOxKTU3FkiVL8MEHHyAsLAwnT57EyZMncemS+AuYZs2aYcqUKcjauRPJycl45tGJOLhvd43bmX1WF+DMQGQmva6/ZrUBR+bSY17tzMpC8tBhGP+Xecg+fJTBSwMMXmIYvBQcZ4Pgpee8uv+xp7Fn/4F6j2Posh6GLjEMXW56v8Ywez/I4KVegzGTzV6C4/eDDF3aeO9bdccLxa5//OMfyM/Px5AhQxATE1P59dFHHyleQEhICNLSPkZMbAwWLXi92o/xovTWZJWLEnpjlQFH5tNrXn2cloaY2Di8uvQrALyGlxYYvMQweCk4zuLBS995FYM3Fy6q87YMXdbD0CWGocvNiNcWVtgPMnippzZ4cT9YO4YubagNXYCCtzF6+5o0aZKqRYSEhCD1kUfwzcrPcPbMaVX3pRcnnN1lpZ+DniHT7AFH1qDnvHokNRXL129F7jn3JoHBSz0GLzEMXgqO0zl4/SwpfyuPrvPqkVR8uXI18s54/7uRoct6GLrEMHS5GfWawir7QQYv9cw+w8uJ+0GGLm1oEboAFZ/GqLUJEyagtLQU235wvymTZ3U5nxMHHGmrU7Q1n8cJEyagtKwM3+6s+j3M4KUeg5cYBi8Fx+kcvKzI8/pqw+atNX6Moct6GLrEMHS5OeW1hOh+kMFLPQYv7TB0aePq0KVmP2iZ2NWsWTMAQGHhBcuGLiudGSXKqmvXe8AxetmXlZ87z7y6UFR9M8fgpR6DlxgGLwXH+VjwqpxXF6tfqJ6hy3oYusQwdLk56TWEkv0gg5d6Tg9eRuwpGLq0cXXoUvvcWSZ25eXlAQBCQ8M0u08rv5fY7uz0a2vlaEI12SFSeuZVWEjNDQaDl3oMXmIYvBQc50PBq3JeNQqt/B5Dl/UwdIlh6HJz2msHpftBBi/1lAQv7gfdGLq0cWXo0mo/aJnYtWTJErhcLtzWI9rspdTJqmdI1cXqazbiTD6rxxNys8vztGTJErgCAzEoKd7rjzN4qcfgJYbBS8FxPhK8PK+v+vXuBYChy4oYusQwdLmpec0Q1CVJw5VoR81+kMFLPTPP8LLrfpChSxtXhy6tWCJ2FRUVYd78+bh1xBBERTYxezn1sno8upJd1mrXAUfascvzU1RUhPnz5uGOgb3QvEntG0EGL/UYvMQweCk4zuHBq6ioCPPnz8PNo4ajWWRThi4LYugSw9Dlpip0KXx9ojct9oMMXuoxeMnH0KUNvUIXYIHYVVRUhJSUcTh54gQev+9uze5X79Mq7RCR9F6j1r/GdhtwpB27PC9FRUUYl5KCnBPH8afxN9Z7ewYv9Ri8xDB4KTjOocGral7l4NEHJjF0WRBDlxiGLjenhi6t9oMMXuqJBC9f3Q8ydGlDz9AFmBi7Tp8+jblz5yIxKQlrM9Zg0WsvoUtH+S/ErMDKwcvKa6uLXQYc+RbPvEpKSkRGRgaWPp+KhLatZB3L4KUeg5cYBi8FxzkoeF09r/79xhw06PAb2cczdBmDoUsMQ5eb00KXXvtBBi/1eIZX7Ri6tHH1xej1YFrsat++PaZNm4Yena7F/5b8C0P732DWUlSxYlSy4ppEWPXTOEkfdoiPnnmV1LkTvly6CNffPkHoeAYv9Ri8xDB4KTjOIcHr6nnVpr/8syQYuozB0CWGocvNaaEL0Hc/yOClntODlxIMXdrQ+lMXa+MnSZKkyz3XoqCgABEREXhh5lsY37eNLtfoMuOTIS5uyzT8Mb0xI3QVtrtOl/s9EuT94t9a2X2yqa73T/Wrb7BdvFiAIT3bIj8/H+Hhxm8sPfPq7y/PwLBBA9AssvrvGaNe7POFfhWlL/ZFX+ifb9VN6PZ8i5j1GB0UCi8WYETvVpaaV/x9aT0MXWL495+b1qGroPASom9/xPR5ZcR+0Mxg4xSXP3ur3tv4wn6QoUsboqFLzX7QtDO7Jg3paIuL0cvVqGcPU8+oMvvx9aB30bfDGUVOZqdf/xtHDK0RugDxIMIzvNTjGV5ieIaXguNsfoaXZ14xdFkPQ5cYhi43J57R5WHEflBOqKG6OfkML7n7EYYubRh1RpeH6Reo15oZZ3VdyejoZIXIpeevuVUGHFFtGLyMx+AlhsFLwXE2D14MXdbD0CWGocvNyaFLT1fvTRi81KsveDl5P8jQpQ0jrtF1NcfFLqvwRCitY5Re92tlVn3PNinntMjI4GU8Bi8xDF4KjhOMOvtPWSN4/Sy1lX1bhi5jMHSJYehyY+jSFoOXek4+w6s2DF3a8Ba6jNgPOip2mX1WV12ujlRKv6xK7197PQecHcNL9t5L1b7sxI6/3nIweBmPwUsMg5eC41TEHatj6DIGQ5cYhi43hi7l6tqTMHipV1fwctp+kKFLG2aFLsBBscvKoctX2HnAWdHVQauuuCVyW9IPg5fxGLzEMHgpOM6BwYuhyxgMXWIYutwYupSTsxdh8FLPqcHrSgxd2jDjrYtXckzsIt+g14CzwtlGekUqK4UvK/w6643By3gMXmIYvBQc56DgxdBlDIYuMQxdbgxdxmDwUs+Jb2n07FMYurRRW+gycj/oiNjFs7qsw4jnwmlneBkZoqwSvexG5Bo4AIOXGRi8xDB4KTjOAcGLocsYDF1iGLrcjAxd+S0TFD+WVYnuQRi81KsteNl5P8jQpQ2zz+jysH3sYuiyHrsOOKPPOjIzPDF6iRP5dDOAwcsMDF5iGLwUHGfj4MXQZQyGLjEMXW5Ghi7R1yd2oHTvweClnpOCV8+OTTW9P19lldAF2Dx2MXRZlx0HnJGsEpqMXIcT3sLI4GV9DF5iGLwUHGfD4MXQZQyGLjEMXW4MXeqo3XMweKnnhODF0KWN+kKX0ftB28Yuhi7r43PknVVCl4fV1mN1DF7Wx+AlhsFLwXE2Cl4MXcZg6BLD0OXG0KWOVnsNBi/1zAxeajF0acNKZ3R52DJ22eEPDbnZ7RM59K7NVg1LVl2XVTF4WR+DlxgGLwXH2SB4MXQZg6FLDEOXG0OXOlrvMRi81DMreKnZDzJ0acOKoQuwYexi6LIfKw84I1k9KOm5Pie8hfFqDF7Wx+AlhsFLwXEWDl4MXcZg6BLD0OXG0KWOXnsLBi/17BS8GLq0ITd0mbEftFXsYuiyLysOOCNZPXR52GWdVsHgZX0MXmIYvBQcZ8HgxdBlDIYuMQxdbgxd6ui9p2DwUs8OwYuhSxtWPaPLwzaxi6HL/qw04Ii0wuBlfQxeYhi8FBxnoeC1/xRDlxEYusQwdLkxdKlj1H6QwUs9Kwcvhi5tWD10ATaJXQxdzmGH51LrUyztdraU3dZrBQxe1sfgJYbBS9y+A/abnQxdyjB0iWHocmPoUsfoPQSDl3pWvGg9Q5c27BC6AIvHrtBD220RR0iMns8pz+7ShpbBy4nX6/KGwcv6GLzEMHg5G0OXMgxdYhi63Bi6lDNzP8jgpZ4Zwau2/SBDlzaUhC6z9oOWjV2MXM6m519cVgpePEvKtzB4WR+DlxgGL2di6FKGoUsMQ5cbQ5dyVtgPMnipV1fwMmo/yNClDbuc0eVhydhlhcFGxuBzTXYgcg0cBi/rY/ASw+DlLAxdyjB0iWHocrNq6PpZaiu4GuNZaY/A4KVebcEL0P+5ZujSht1CF2DB2GWlwUbG0OM5t8LZXXY/q8sK629dcsDsJVQS+XQzBi/rY/ASw+DlDAxdyjB0iWHocrNq6BJ9jWIGK+4HGbzUMzp4tSnZy9ClETNDl5r9oKVilxUHGxnDas+9r1xnisQweDkLg5cYBi97Y+hShqFLDEOXG0OXclbbE1yJwUs9I4NXZEI/Te/PV9nxjC4Py8QuKw82MobWvwescHaX3ak9u0tNNLTq88fg5SwMXmIYvOyJoUsZhi4xDF1uDF3K2WE/yOClnhHBi6FLG1MXXLL1ftD02MVPXKQrOSV4WeEtgKQfBi9nYfASw+BlLwxdyjB0iWHocmPoUsZu+0EGL/X0DF4MXdqYusD++1lTY5edhhoZh78vyKpndV2JwctZGLzEMHjZA0OXMgxdYhi63Bi6lLHr634GL/X0CF4MXdqwQujSYj9oWuwKObzDrIcmG7DrX3zkWxi8nIXBSwyDl7UxdCnD0CWGocuNoUsZu+8HGbzU0zJ4MXRpwwqhSyumv42RqDZaBS87nCVkZUa/JdNuzxeDl7MweIlh8LImhi5lGLrEMHS5MXT5NgYv9bQIXgxd2qgtdNl1Pygcu7799lvccsstiI2NhZ+fHz799FNNFkLkjZlneCm9mB6v12UdRs0rBi9nYfASw+ClDa3m1b4DDF1KMHSJYehy89XQxf1gdQxe6qkJXgxd2nDSGV0ewrGrsLAQ3bp1w1tv8Q81GUOL4GW3s4V8ldbPk5HzisHLWRi8xDB4qWf26yuGLgXHMXT5NF8NXYD588qKGLzUUxK8GLq0YaXQpeV+MFD0gNGjR2P06NGaLcDXFLa7TvgYXr/K/Wug5NfOaXZvO1Lrj3Xp2cawdcih5mNmtWL0vNp7Mlz2Zuaw1E5oI3O+VTehzUxQYndFm5m4/l25kfnV8e93KdrMlGTtENrIND76o9BGpq3fIdmbmfgWBUIhtnOnhorfAte5RxufDSZ7Mo/g2vimqu7DzNdXvvq8AQxdovj3g5svhy6A+8HaXP7srTqDDdWvwZjJtYbDq/eDDF3a0DN0mb0f1P2aXcXFxSgoKKj25WsK211X+aX2eF8OPr4c/XZvO1Jn6JJ7G6qbFvOKZ3g5C8/wEuPLZ3jt2/mLoY+n1esrhi4FxzF0+TRfD11K+NJ+kGd4qSfnDC+GLm1Y6YwuPegeu2bOnImIiIjKr1atWun9kJagZ5zy5fClJngZ8VZGra/XpSRg6RG9jLgOmRXealrbvBK9Bg6Dl7MweInx5eBlJC1eXzF0KTiOocunOSV07T8l/3WKFnxtP8jgpV5dwYuhSxuiocuO+0HdY9f06dORn59f+XX06FG9H9JURkcoX41evkBtsOJZXuLqmleib+1i8HIWBi8xDF76U/v6iqFLwXEMXT7NKaFL5PWJVnxxP3gme4PZy7A9b8GLbxPVhtPP6PLQPXYFBwcjPDy82pdTmRmdfCl4+cLbGbUKVQxeYuqbVwxevo3BSwyDl77UvL5i6FJwHEOXT2PoUsdX94MMXupdGbcYurThK6ELMCB2+QKrnF1lhTUYRWnwssJb5eqjdaCyQ/Cyw/PiweDl2xi8xDB4WQ9Dl4LjGLp8GkMXyVHbfpDBS70GYyYzdGnEyqFLj/2gcOy6ePEiduzYgR07dgAADh8+jB07duCXX4y9MKtVWC0wWW09JEavMGWH4KUHveYVg5dvY/ASw+AljxGvrxi6FBzH0OXTGLq8436wuvr2XwxeZAW3PLTH7CUYTjh2bd26FT169ECPHj0AAE8++SR69OiB559/XvPFWZ1Vw5JV16U1p72dUe8g5YvBS895xeDl2xi8xDB41U/v11cMXQqOY+jyaQxdteN+sIrcfReDF5nJE7p8bT8oHLuGDBkCSZJqfC1atEiH5ZFSvhK8yDhGfAKH1vSeVwxevo3BSwyDV930nFcMXQqOY+jyaQxddeN+UBkGLzKDlmd02W0/yGt2KWSHmGSHNaql5OwuK14fyqjKbsWab8XnQwSDl29j8BLD4GU8hi4FxzF0+TSGLpJLyV6LwYuM5C10+dJ+kLFLATtFJDut1Yq6tDgr+7Z2Kd1WHHB2x+Dl2xi8xDB4GYehS8FxDF0+jaGL5FKzx2LwIiPUdUaXr+wHGbvI9ux+7S5fGTZOx+Dl2xi8xDB46Y+hS8FxDF0+jaGLjMTgRXryxYvRe8PYJciOZ0rZcc1kbyJnxDkJg5dvY/ASw+ClH4YuBccxdPk0hi4SodXeisGL9GCV0GWF/SBjF/kkq1wnyqyzung2mX4YvHwbg5cYBi/t7dv5i9lLMA1DlxiGLjeGLjITgxdpSSR0+cJ+kLHrV6fOFyDtu214J30D0r7bhlPna76Q4RlS1mX3tzL6KqtER63pHbxO553BZ1+txPtpK/DZVytxOu9Mrbdn8DIeg5cYBi9nKy7Kw4lDX+OXvctw4tDXKC7K0+VxGLrEMHS5+Wro2nfAHteZNZpZ+0EGL9KCVc7oEqXnfjBQt3u2iewjJzDnk1VYsfFHlJaVVX7fFRiI2/t2w9O/HYmENrEmrlAbhe2uYxCianZvO4IuPduYvQzH2rPvktAmau/J8Ho3UYf278LihXOxdtVnKC0trfy+y+XCzSOH4dEH70Hnjh1qHHe+VTehTVRQYndFm6i4/l25gfrV8e93KdpElWTtENpANT76o9AGqq3fIdmbqPgWBUIbqM6dGgqH3spje7Tx6bfeOVHB2f04tGMhTh5ehfLyqnkVEOBCi7Yj0a77Awhv2lGTx2LoEsM57earoUvpnHYyK+wHz2RvQGRCP10fg5xLaehy+n7Qp8/sSs/cg8HT5mLb8bOYNXs2cnNzUV5ejtzcXMyaPRvbjp/F4GlzkZ5pz0rqa+wW83zh1FFfp+UZXpu+W40H7xyOQ3szMWvWrOrzatYs7Ny7HzePn4SM77z/6yDP8DIez/ASwzO8nCP36HfY+PmdcJXvxZw51efVnDmz4Crfi42f34nco9+pfiyGLjEMXW4MXeRhpf0gz/AiJex6RpcRfDZ2ZR85gfGz/oPkocOwMysbU6ZMQVRUFPz9/REVFYUpU6ZgZ1Y2kocOw/hZ/8GW8gizl0xEJlFzDRwtgteh/bvw3ON3Izk5GVk7d3qfVzuzkJycjPsfexp79h/wet8MXsZj8BLD4GV/BWf3I/ObxzFieDJ2ZXufV7uyd2LE8GRkfvM4Cs7uV/xYDF1iGLrcGLrIw4r7QQYvEsHQVTefjV1zPlmFmNhYfJyWhpCQEK+3CQkJwcdpaYiJjcXr/3nP4BVqj9cco6vx7DL51LzFSm3wWrxwLmJiY5CW9rGMeRWDNxcuqvW+GbyMx+AlhsHL3g7tWIi4uFik1fP6Ki0tDXFxsTi0Y6Gix2HoEsPQ5cbQRVey6n6QwYvk0Cp0OXk/6JOx69T5AqzY+CNSJz9a62DzCAkJwSOpk/F5egZOnzln0ArJCGZeHN2uQyV7r2+/WDIjeJ3Ny8XaVZ9hcmqqvHn1SCq+XLkaeWdq/7hfBi/jMXiJYfCyp+KiPJw8vAqPPSpvXj06+RGcPLwKxZdq/5ANbxi6xDB0uTF00ZWsvh9k8KK6mHlGl532gz4Zu77NPoDSsjJMmDBB1u0nTJiA0tIyfL/FXteE8kV2u24X2Y/RwWv7lvUoLS0VnFel2LB5a523Y/AyHoOXGAYv+zmTswXl5WLzqry8FGdObJb9GAxdYhi63Bi66Gp22A8yeJE3fOuifJaPXRe3ZVb70uQ+LxUDAJo1aybr9p7bXSgs1OTxfZEezyPZm5ln1qllZPD66UQ5AAXz6mL984rBy3gMXmIYvOylrNQ9d0Tnlee4+jB0iWHocmPosj9f3g8yeNGVnBa69N4PWjJ21TXMtBhyjRoGAwDy8vJk3d5zu7DQUFWPawVGX7dLz+fRSux0OufV7PqWSrMZFbwahIQBUDCvGsmbVwxexmPwEsPgZR+BLvfcEZ1XnuPqwtAlhqHLjaHLvrgfrMLgRYC+ocup+0HLxS65g0vNgBuU0AGuwEAsWbJE1u2XLFkClysQ/a/nBd5FyHmOnBa95HDqMPFFRgSvTt2GIDDQJTivXOjXu5fstTB4GY/BSwyDlz1ExlyPgACxeRUQ4EJkbO86b8fQJYahy42hy764H6yJwcu3Oe2MLqNYKnaJDiylAy66cThu79sN8956E0VFRXXetqioCPPnzcOtI5IRFdlE0eP5IqOeS2943S4ykt7BK6JpNHoOGou33pona17Nmz8fySNvw4Wm1wuthcHLeAxeYhi8rC84pBlatB2JN96UN6/efGs+WrQdieCGkbXejqFLDEOXG0OXfXE/WDsGL9/E0KWcZWKX0kGl9LinfzsSOSdOYFxKSq0DrqioCONSUpCTk4PH77tb0eP4IqOfSyKz6R28brprOo6fyEFKyrg651VKyjjknMjB3Q9MASD+oprBy3gMXmIYvKyvXfcHcPz4CaTU8/oqJSUFx4+dQLvuD9R6XwxdYhi63Bi67Iv7wfoxePkWhi51LBG7zIgcCW1isXTqfchY8w2SEhMwd+5c5ObmoqKiArm5uZg7dy6SEhOQseYbLHrtJXTpKPYXFBH5Fj2DV8trE5H64nJ8syYDiYlJXudVYlISMjIy8NLr76Fdx6oX+gxe1sfgJYbBy9rCm3ZEj2GvI311BromeJ9XXROSkJ6egR7DX0d4045e74ehSwxDlxtDl31xPygfg5dvYOhSzxKxSy2lw3FEj85Y98qT6NUyEtOmTkV0dDQCAgIQHR2NaVOnolfLSPzv/YUY2v8GjVfsXGr/ouLZXWRnegavhOtH4bm3NqF52xswdeq06vNq2jS0j78OCz5cjT4Dhtc4lsHL+hi8xDB4WVvzVgPQ99YPURoQj2eeqT6vnnlmGkoD4tF3zIdo3mqA1+MZusQwdLkxdPk2X9sPMng5G0OXNvwkSZKMfMCCggJERETg1AezER7SULO40ahnD1XH556/gG+zD+DCpcsIa9gAgxI6oHnjMMM/vdAoel3XyirPp9zn7UhQfL232X2yab23Efk0RqteoL5LzzaybpcQX/9mokuLs/XeRs5HzV64WIi2fUciPz8f4eFiL/604JlXoyZthiuokezj1GzE5WzWCs7lYu+Pa3G5sADtYgPRo/dANI2Mqvc4kY0aYNxmjRu1Kko3a3pu1ACxzZqRGzU1gVlrpSUXsXJRb9vNK70VXzqDMyc2o6y0EIGuUETG9uY1uq7C0KUeQ5eYnT9kW2JecT+oXmRCP7OXQBozM3Q5bT8YKHRrB2veOAxjB1h3kNmBlc7KCj20XdZfTG1K9soKXlqxaugibe3JPKJ4w7Zn36V6N23hTZqj95Bxlf/dNFLepu2w1E5ow3a+VTehTVtQYndFm7a4/l25YfvV8e93Kdq0lWTtENqwNT76o9CGra3fIdmbtvgWBUIbts6dGiresHXu0cZSwYtqCm4Yidh2o2XdlqFLDOemG0OXGM7M2tlxP3gmewODl4PwjC5tmfo2Ri3jiJVCC6nH55MAIOTwDrOXoJjeF62/ksgLZL6l0fr4lkYxfEujMzB0iWHocmPoEmPF0MX9oHp8S6MzMHR5p2Y/6IhrdhGRvcg5ZdUJGLzEMHhVYfASw+BlbwxdYhi63Bi6xFgxdJF2GLzszRdDlxH7QdNiV+GOnWY9NNmEr/7rDDkLg5cYBq8qDF5iGLzsiaFLDEOXG0OXGKuGLu4HtcXgZU++GLqMwjO7SBMMU0S1Y/ASw+BVhcFLDIOXvTB0iWHocmPoEmPV0EX6YPCyF4YufTkqdjG4EJFVMXiJYfCqwuAlhsHLHhi6xDB0uTF0ifHF0MX9IIOXXTB06c9RsYuIyMoYvMQweFVh8BLD4GVtDF1iGLrcGLrE+GLooioMXtbG0GUMxi6yNP7rDDkNg5cYBq8qDF5iGLysiaFLDEOXG0OXGIYuAhi8rIqhyziMXUREBmPwEsPgVYXBSwyDl7UwdIlh6HJj6BLD0EVXYvCyFoYuYzF21aGw3XVmL4FUCD203ewl2MrubUfMXoJPYfASw+BVhcFLDIOXNTB0iWHocmPoEsPQpT0n7AcZvKxh6oJL6NKzjdnLqJPT9oOMXTaWm3cWK/63Gu998jlW/G81cvPOmr0kIhLgS8Hr1Ll8pK39Ae98vQ6fnSlG7oVCoccAGLyuxOAlhsHLXHYLXafzzuCzr1bi/bQV+OyrlTidd4ahywQMXWIYunyT3P0gg5e5pi5Q9uea1Ak0ewEkbvf+Q/j7v9/DF6vXorS0tPL7LpcLtwwfgifuvxtdOor9RUxkNaGHtkNs22JPezKPKN4I7tl3SWgzuPdkuOzN4GGpnVC4ON+qm9fNYPbho5iz9CusWL8VpWVlld93BQZiTGI7PDmsF7rGNJP9OHH9u3Ij+Kvj3+9StBksydohtBFsfPRHoY1gW79DsjeD8S0KhDaCnTs1VLwR7NyjDTeDJrBT6Nqz/wDeXPAOvlz1TfXXV4GBuH1gLzw9/kYktG1V730zdKnH0CWGs833KNkPnsnegMiEfkYv1ecxdCmndj/IM7tsZs33P+A3Ex7Cj/sOYdasWcjNzUV5eTlyc3Mxa9Ys/LjvEH4z4SGs+f4Hw9ak90XkeZF6cjqnnuGVvjULgx9/Cdt+zsWs2bOrz6vZs5GZX4rhb6Zh9d6fhR6HZ3hV4RleYniGl7HsFLoyvtuAm8dPws69+2u+vpo9G9t+zsXgx19C+tasOu+boUs9hi4xDF2+R81+kGd4GYuhy1yKYtf8+fPRtm1bNGjQAD179sT69eu1Xhd5sXv/IUya8hySk5OxMysLU6ZMQVRUFPz9/REVFYUpU6ZgZ1YWkpOTMWnKc9i9v/bNhhPef26ULi20eXuo094DbRd2mVdOC17Zh49i/F/mIXnosLrn1bDhuHvxV9iVkyf0OAxeVRi8xFg5eNllXslhp9C1Z/8B3P/Y0+7XVzvrmFdDh2H8X+Yh+/BRr/fN0KUeQ5cYM0OXk+aVnWixH2TwMoa30MX9oLGEY9dHH32EJ554As899xwyMzMxcOBAjB49Gr/88ose66Mr/P3f7yEmJgYfp6UhJCTE621CQkLwcVoaYmJi8Pp/3jN4hUTWYrd55aTgNWfpV4iJjZM3r+Ja4rU1W4UeA2DwuhKDlxgrBi+7zau62Cl0AcCbC95BTKzM11excXh16Vc1fpyhSz2GLjFmhi4nzSu70Wo/yOClL57RZQ3CsWvu3Lm47777cP/996Nz5874+9//jlatWuEf//iHHusT5tS3vOXmncUXq9cidfLkWgebR0hICB5JTcXn6Rk4feacQSsksh6rzytvnBC8TuedwYrvtgnNq093HsLpC0WyH8ODwasKg5cYqwUvO84rb+wWuk7nncGXq75Baqr8ebV8/Vbknqt6fIYu9Ri6xJj91kWrzyvuB+XtBxm89MHQZR1CF6gvKSnBtm3bMG3atGrfHzlyJDZs8P6Hpbi4GMXFxZX/nZ+fDwC4UFwiulbZKoq0+Q1WdFH808L0svq7DSgtLcWYMWNQUFD/C7wxY8bgqaeewur1G3Dz8CFeb1Ou0a9ToY7PpYfS51TOc3gxqP5fz0uFdf9RKb5U//pKSy7WexuzFV+S82tRWu9tLl6s+34ulNT/vJQXXcKFossAAEmS6r391bScV2UGP3c7f8hGp6RrFB27Y0cBOnWQv2nMPAR0jJa3acxGFFr7Ha73dt98+52iebXmYgVGu4rrvf3Vwnq2R86mvcLHOdG+tT8ipk+8+IFbfkBQlyTZN/ffuwn5LRNk374ZfsTPUltZt23VqAD7T8nfMLZpCew7oOzviGvjm2LfTm3ORPDMCV+bV1fqlHSNrL9HahzXoaGsv1s8OkYXoFDgp9na7zAu1PJjSufVyi07MaZ/T5Ts3il/IVfh3HKL6ROPgsvisx8Agrok4XKh/D//+S0TgIvyfvO4Z5b838/uuSX/9krnlhYzyyrzivtBMbrsBz+cgwY3PaTxSn3X8+/U/fuO+8EqRuwHIQk4fvy4BED6/vvvq33/pZdekjp27Oj1mBdeeEECwC9+8Ytfir8OHTokMqo4r/jFL36Z9sV5xS9+8csuX5xX/OIXv+zypWReCZ3Z5eHn51ftvyVJqvE9j+nTp+PJJ5+s/O/z58+jdevW+OWXXxAREaHk4Q1VUFCAVq1a4ejRowgPFzs12SxcszHstma7rRdw/8vfNddcg6ZNmyq+D84ra+OajWG3NdttvQDnlSg7PsdcszHstma7rRfgvBJlx+eYazaG3dZst/UC6uaVUOxq1qwZAgICcPLkyWrfz83NRXR0tNdjgoODERwcXOP7ERERtvkFBoDw8HBbrRfgmo1itzXbbb0A4O8v/sGxnFf2WS/ANRvFbmu223oBzitRdnyOuWZj2G3NdlsvwHklyo7PMddsDLut2W7rBZTNK6EjgoKC0LNnT6Snp1f7fnp6Ovr16yf84EREeuG8IiK74LwiIrvgvCIiuxB+G+OTTz6Ju+++G7169ULfvn2xYMEC/PLLL3j44Yf1WB8RkWKcV0RkF5xXRGQXnFdEZAfCset3v/sdzpw5g7/85S/IyclBQkICvvrqK7Ru3VrW8cHBwXjhhRe8nspqRXZbL8A1G8Vua7bbegH1a+a8sj6u2Rh2W7Pd1gtwXomy23oBrtkodluz3dYLcF6Jstt6Aa7ZKHZbs93WC6hbs58kKfkMRyIiIiIiIiIiIusRv8oXERERERERERGRRTF2ERERERERERGRYzB2ERERERERERGRYzB2ERERERERERGRYzB2ERERERERERGRYxgau+bPn4+2bduiQYMG6NmzJ9avX2/kwwv59ttvccsttyA2NhZ+fn749NNPzV5SvWbOnInrr78eYWFhaN68OW677Tbs27fP7GXV6R//+AeSkpIQHh6O8PBw9O3bF19//bXZy5Jt5syZ8PPzwxNPPGH2Umo1Y8YM+Pn5Vftq0aKF2cuq1/HjxzFhwgRERkYiJCQE3bt3x7Zt2wx7fM4rfXFeGY/zSj+cV/JxXhmD80p/nFfKcF7pi/PKeJxX+lE7rwyLXR999BGeeOIJPPfcc8jMzMTAgQMxevRo/PLLL0YtQUhhYSG6deuGt956y+ylyLZu3TqkpqZi06ZNSE9PR1lZGUaOHInCwkKzl1arli1b4pVXXsHWrVuxdetWDB06FGPGjMGuXbvMXlq9tmzZggULFiApKcnspdSra9euyMnJqfzKysoye0l1OnfuHPr37w+Xy4Wvv/4au3fvxt/+9jc0btzYkMfnvNIf55WxOK/0w3klhvPKGJxXxuC8EsN5pT/OK2NxXulHk3klGaR3797Sww8/XO178fHx0rRp04xagmIApBUrVpi9DGG5ubkSAGndunVmL0VIkyZNpH//+99mL6NOFy5ckDp06CClp6dLgwcPlh5//HGzl1SrF154QerWrZvZyxAydepUacCAAaY9PueV8Tiv9MN5pS/OK+U4r4zFeaUtzitxnFfG47zSD+eVvrSYV4ac2VVSUoJt27Zh5MiR1b4/cuRIbNiwwYgl+KT8/HwAQNOmTU1eiTzl5eVYunQpCgsL0bdvX7OXU6fU1FTcdNNNGD58uNlLkeXAgQOIjY1F27ZtMX78ePz0009mL6lOn3/+OXr16oWUlBQ0b94cPXr0wMKFCw15bM4rc3Be6YfzSl+cV76H80o/nFf64rzyPZxX+uG80pcW88qQ2JWXl4fy8nJER0dX+350dDROnjxpxBJ8jiRJePLJJzFgwAAkJCSYvZw6ZWVloVGjRggODsbDDz+MFStWoEuXLmYvq1ZLly7F9u3bMXPmTLOXIssNN9yAxYsXY+XKlVi4cCFOnjyJfv364cyZM2YvrVY//fQT/vGPf6BDhw5YuXIlHn74YTz22GNYvHix7o/NeWU8ziv9cF7pj/PKt3Be6YfzSn+cV76F80o/nFf602JeBeq4vhr8/Pyq/bckSTW+R9qYPHkydu7cie+++87spdSrU6dO2LFjB86fP49PPvkEEydOxLp16yw54I4ePYrHH38cq1atQoMGDcxejiyjR4+u/P+JiYno27cv2rVrh3fffRdPPvmkiSurXUVFBXr16oWXX34ZANCjRw/s2rUL//jHP/CHP/zBkDVwXhmH80ofnFfG4LzyLZxX+uC8MgbnlW/hvNIH55UxtJhXhpzZ1axZMwQEBNSo9rm5uTXqPqn36KOP4vPPP0dGRgZatmxp9nLqFRQUhPbt26NXr16YOXMmunXrhtdff93sZXm1bds25ObmomfPnggMDERgYCDWrVuHN954A4GBgSgvLzd7ifUKDQ1FYmIiDhw4YPZSahUTE1PjL7fOnTsbcgFTzitjcV7ph/PKGJxXvoPzSj+cV8bgvPIdnFf64bwyhhbzypDYFRQUhJ49eyI9Pb3a99PT09GvXz8jluATJEnC5MmTsXz5cqxZswZt27Y1e0mKSJKE4uJis5fh1bBhw5CVlYUdO3ZUfvXq1Qu///3vsWPHDgQEBJi9xHoVFxdjz549iImJMXspterfv3+Nj0nev38/Wrdurftjc14Zg/NKf5xXxuC8cj7OK/1xXhmD88r5OK/0x3llDE3mlarL2wtYunSp5HK5pP/85z/S7t27pSeeeEIKDQ2Vjhw5YtQShFy4cEHKzMyUMjMzJQDS3LlzpczMTOnnn382e2m1+uMf/yhFRERIa9eulXJyciq/ioqKzF5araZPny59++230uHDh6WdO3dKzz77rOTv7y+tWrXK7KXJZvVP33jqqaektWvXSj/99JO0adMm6eabb5bCwsIs+2dPkiRp8+bNUmBgoPTSSy9JBw4ckN5//30pJCREWrJkiSGPz3mlP84rc3BeaY/zSgznlTE4r/THeSWO80p/nFfm4LzSnhbzyrDYJUmSNG/ePKl169ZSUFCQdN1111n6I1AzMjIkADW+Jk6caPbSauVtvQCkd955x+yl1eree++t/D0RFRUlDRs2zFaDTZKsP9x+97vfSTExMZLL5ZJiY2OlO+64Q9q1a5fZy6rXF198ISUkJEjBwcFSfHy8tGDBAkMfn/NKX5xX5uC80gfnlXycV8bgvNIf55UynFf64rwyB+eVPtTOKz9JkiT554ERERERERERERFZlyHX7CIiIiIiIiIiIjICYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGYxcRERERERERETkGY5eFbNiwATNmzMD58+fNXoosJ06cwIwZM7Bjxw7DH3v37t2YMWMGjhw5UuPHJk2ahDZt2hi+JsB+zyGR0V5++WV8+umnutz3kSNH4Ofnh0WLFik6XnRtixcvxvjx49GpUyf4+/ubNnfqmsUzZsyAn5+f8YtC3XOayA6cMq9ycnLw5z//GX379kWzZs0QHh6Onj17YsGCBSgvL1f0+EpxXhGZy8/PDzNmzBA6xtufzfnz53udX2pnm1pvvvkm2rdvj6CgIPj5+Zm6J/vqq69q/bVu06YNJk2aZOh6fJJEljFnzhwJgHT48GGzlyLLli1bJADSO++8Y/hjp6WlSQCkjIyMGj928OBBafv27YavSZLs9xwSGS00NFSaOHGiLvd9+fJlaePGjVJubq6i40XXNnz4cCkhIUGaMGGC1L59e6l169aKHletumbx0aNHpY0bNxq/KKnuOU1kB06ZV1988YXUqlUr6bnnnpP++9//SqtWrZKmTJki+fv7S/fcc4+ix1eK84rIXBs3bpSOHj0qdIy3P5tdu3aVBg8eXOO2amebGpmZmRIA6f7775fWr18vbdy4USorKzN8HR6pqalSbbll+/bt0sGDBw1eke8JNCuykXGKiooQEhJi9jIM065dO7OXQEQauHTpEho0aCD7X/qDg4PRp08fnVdVZeXKlfD3d58gffPNNyM7O9uwx5arZcuWaNmypdnLIHI8K8+r/v3749ChQ3C5XJXfGzFiBEpKSjBv3jy8+OKLaNWqlSFrqQvnFZH+lMwdkT+bRr8Wu9KuXbsAAA888AB69+5tyhrk6tGjh9lL8A1m1zZye+GFFyQANb48/8K0dOlSacSIEVKLFi2kBg0aSPHx8dLUqVOlixcvVrufiRMnSqGhodLOnTulESNGSI0aNZL69OkjSZIknTt3Trr33nulJk2aSKGhodKNN94oHTp0SAIgvfDCC9XuZ//+/dKdd94pRUVFSUFBQVJ8fLz01ltvVf54RkaG1/VefT9Xy8nJkR588EEpLi5OcrlcUps2baQZM2ZIpaWl1W43f/58KSkpSQoNDZUaNWokderUSZo+fbokSZL0zjvveH1sz78STpw4scYZFgCk1NRU6e2335Y6duwoNWjQQOrZs6e0ceNGqaKiQpo9e7bUpk0bKTQ0VEpOTpYOHDhQ7fhVq1ZJt956qxQXFycFBwdL7dq1kx588EHp9OnTsp9Dz/PYp08fKSQkRAoNDZVGjhxp2llo5Gx79uyRxo8fLzVv3lwKCgqSWrVqJd19993S5cuXK2+TlZUl3XrrrVLjxo2l4OBgqVu3btKiRYuq3Y/nz/r/b+++w6Mq0zaA3zOZSZk0khBSKaGEAElQKVJECBAQESvBhm2VXaUKFkDdtayKFFmV4q51VVbQsOpnXQhSFRCkJrTQO4SQQBqElPP9ESdkkplkyulz/65rrt0MZ2aeCeTxzJ3nfc/nn38uPPfcc0JMTIwQHBwsDBo0SNi7d6/NsVu3bhWGDx9e2zdiYmKEm2++ufY3iPZ+Pqy/FbT+XC9btkx45JFHhObNmwsAhEuXLgn79+8XHn74YaF9+/ZCQECAEBsbK9xyyy3Czp07bV7/8OHDDSYGrD+XOTk5wj333COEhIQILVq0EB555BHhwoULtcc1Vpszhg8f7vJk18WLF4WnnnpKaNOmjWA2m4XY2Fhh0qRJDfr6l19+KfTs2VMICQkRAgIChISEhNppjKZ6sfX919W6dWth+PDhwnfffSdcc801tf9N+e677wRBqPm7SEpKEiwWi9CjRw9h8+bNNo/fvHmzcPfddwutW7cW/P39hdatWwv33HOPcOTIkdpjmurTgiAIWVlZwsCBA4Xg4GAhICBA6NOnj7BixQqXvoekD+xXNeTqV1affPKJAEBYv359k8eyX7FfUdOc/fkXhJrPZVOmTBESEhIEX19fITIyUhg2bJiwZ8+e2mNOnjwpZGRkCEFBQUJISIgwatQoYcOGDQ3+ffbv399uD3D0maju57XS0tLan20/Pz8hLCxM6Natm/D555/XHlP/Z7N169YNfl6sr2OvtwmCIKxbt04YOHCgEBQUJAQEBAi9e/cWvv/+e5tjrD+LK1euFB5//HEhIiJCCA8PF+644w7h5MmTDr7rV78H9WuyTr+2bt3a7iRs/e+bK/8NEQRB+Omnn4SBAwfW9rukpCTh9ddfFwSh5ntvr69YV//Yq+no0aPC/fffb/P5e86cOUJVVVXtMdbv7+zZs4U333yz9vNrr169FJuMVTNOdqnEY489hoKCAsybNw9fffUVYmJiAACdO3cGAOzfvx8333wznnzySQQGBmLv3r2YOXMmNm3ahJUrV9o815UrV3DrrbfiL3/5C6ZNm4bKykpUV1djxIgR+P333/HSSy/huuuuw4YNG3DTTTc1qGX37t3o06cPWrVqhTfffBPR0dFYtmwZJk6ciPz8fLz44ou47rrr8PHHH+ORRx7BCy+8gOHDhwNAo6n/mTNn0LNnTxiNRvztb39Du3btsGHDBrz66qs4cuQIPv74YwDAkiVLMHbsWEyYMAFz5syB0WjEgQMHsHv3bgDA8OHD8frrr+O5557DggULcN111wFoeqLr+++/x7Zt2/DGG2/AYDBg6tSpGD58OB566CEcOnQI8+fPx8WLFzFlyhTcdddd2L59e+1vaA8ePIjevXvjscceQ2hoKI4cOYK5c+fihhtuQHZ2Nsxmc5N/h6+//jpeeOGF2u/ZlStXMHv2bPTr1w+bNm2qPY7IUzt27MANN9yA5s2b45VXXkGHDh1w+vRpfPvtt7hy5Qr8/Pywb98+9OnTBy1atMA777yDiIgILFq0CA8//DDOnj2LZ5991uY5n3vuOfTt2xcffPABioqKMHXqVIwYMQJ79uyBj48PSktLkZ6ejoSEBCxYsABRUVE4c+YMVq1aheLiYgDAhg0bMHDgQKSlpeGvf/0rACAkJMTmdf70pz9h+PDh+Oyzz1BaWgqz2YxTp04hIiICb7zxBiIjI1FQUIBPPvkE119/PbZt24aOHTs2+T256667cPfdd+PRRx9FdnY2pk+fDgD46KOPnK5NTGVlZejfvz9OnDiB5557Dqmpqdi1axf+9re/ITs7GytWrIDBYMCGDRtw99134+6778ZLL70Ef39/HD16tLbvu9OLgZp/I9OnT8fzzz+P0NBQvPzyy7jzzjsxffp0/Pzzz3j99ddr++Qtt9yCw4cPIyAgAEDNfhwdO3bEPffcg/DwcJw+fRrvvvsuevTogd27d6N58+ZN9ulFixbhwQcfxG233YZPPvkEZrMZ//rXvzB06FAsW7YMgwYNkupbTyrDftWQXP1q5cqVMJlMSExMbPQ49iv2K3KOsz//xcXFuOGGG3DkyBFMnToV119/PUpKSrB27VqcPn0aSUlJuHTpEgYPHoxTp05hxowZSExMxA8//IC7775b1JqnTJmCzz77DK+++iquvfZalJaWIicnB+fPn3f4mK+//hojR45EaGgoFi5cCKBmosuRNWvWID09Hampqfjwww/h5+eHhQsXYsSIEVi8eHGD9/TYY49h+PDh+Pzzz3H8+HE888wzGD16dIPPvHUtXLgQixcvxquvvoqPP/4YSUlJiIyMdPG7UaOp/4YAwIcffogxY8agf//++Oc//4kWLVogNze3dsr/r3/9K0pLS7F06VJs2LCh9rmtnw/rO3fuHPr06YMrV67g73//O9q0aYPvv/8eTz/9NA4ePFj7fbZasGABkpKS8NZbb9W+3s0334zDhw8jNDTUrfetS0qnbXSVs/s9VVdXCxUVFcKaNWsEAMKOHTtq/8yaIn/00Uc2j/nhhx8EAMK7775rc/+MGTMaJPxDhw4V4uPjhYsXL9ocO378eMHf318oKCgQBMH1Pbv+8pe/CEFBQcLRo0dt7p8zZ44AQNi1a1ft6zRr1qzR52psbwVHv8WIjo62+Q3kN998IwAQrrnmGqG6urr2/rfeeksAYPe3MIJw9ft/9OhRAYDwf//3f7V/5ujv8NixY4LJZBImTJhgc39xcbEQHR0tjBo1qtH3S+SKgQMHCs2aNWt0v4R77rlH8PPzE44dO2Zz/7BhwwSLxVI7RWD9LdfNN99sc9yXX34pAKj9LdLvv/8uABC++eabRmtztM+M9bd5Dz74YJPvr7KyUrhy5YrQoUMHYfLkybX3NzYpMWvWLJvnGDt2rODv72/zs+/J/jyuTnbNmDFDMBqNDaYQli5dKgAQfvzxR0EQrvbHulMd9TXWix1NSgQEBAgnTpyovW/79u0CACEmJkYoLS2tvd/aJ7/99luHr19ZWSmUlJQIgYGBwttvv117v6M+XVpaKoSHhwsjRoywub+qqkro2rWr0LNnT4evRfrDfvVx7X1y9StBEIRly5YJRqPRpiZH2K/Yr8g9jn7+X3nlFQGAkJWV5fCx7777boPPGYIgCGPGjBF1sis5OVm4/fbbG30f9n42He3ZZa+39erVS2jRooVQXFxce19lZaWQnJwsxMfH1/Y2a28dO3aszXPOmjVLACCcPn260Tqtj6/fq1yd7GrqvyHFxcVCSEiIcMMNN9j05foa27Orfk3Tpk0TAAi//fabzXFPPPGEYDAYhH379gmCcPX7m5KSYrMf2aZNmwQAwuLFix3W4414NUaNOHToEO677z5ER0fDx8cHZrMZ/fv3BwDs2bOnwfF33XWXzddr1qwBAIwaNcrm/nvvvdfm68uXL+Pnn3/GHXfcAYvFgsrKytrbzTffjMuXL2Pjxo1uvYfvv/8eaWlpiI2NtXneYcOG2dTYs2dPXLhwAffeey/+7//+D/n5+W69Xn1paWkIDAys/bpTp04AgGHDhtnssWG9/+jRo7X35eXl4fHHH0fLli1hMplgNpvRunVrAPa///UtW7YMlZWVePDBB23eu7+/P/r374/Vq1eL8RaJUFZWhjVr1mDUqFGN/kZr5cqVGDRoUIN9Wh5++GGUlZXZ/BYKAG699Vabr1NTUwFc/Tlp3749wsLCMHXqVPzzn/+sncR0Vf3eBQCVlZV4/fXX0blzZ/j6+sJkMsHX1xf79+936ufPUf2XL19GXl6eW3V66vvvv0dycjKuueYam54wdOhQGAyG2p7Qo0cPADW9+8svv8TJkydFef1rrrkGcXFxtV9b+96AAQNs9ni01w9LSkowdepUtG/fHiaTCSaTCUFBQSgtLXXq72P9+vUoKCjAQw89ZPPeq6urcdNNN2Hz5s0oLS0V5X2SurFf2Sd1v9q6dStGjRqFXr16YcaMGU0ez37FfkXOcfbn/6effkJiYiIGDx7s8LlWrVqF4ODgBv3gvvvuE7Xmnj174qeffsK0adOwevVqXLp0SdTnLy0txW+//YaRI0ciKCio9n4fHx888MADOHHiBPbt22fzmKZ6uNSaev3169ejqKgIY8eOFe0KsitXrkTnzp0b7DX28MMPQxCEBlNtw4cPr50ys1cj1eAyRg0oKSlBv3794O/vj1dffRWJiYmwWCw4fvw47rzzzgZNyWKxNBhnP3/+PEwmE8LDw23uj4qKanBcZWUl5s2bh3nz5tmtx93w6ezZs/juu+9sNki197wPPPAAKisr8f777+Ouu+5CdXU1evTogVdffRXp6eluvTaABu/d19e30fsvX74MAKiursaQIUNw6tQp/PWvf0VKSgoCAwNRXV2NXr16OfUfhbNnzwK4eiJYn3WTayJPFRYWoqqqqsllIefPn7c7Sh0bG1v753VFRETYfG0dV7f++w8NDcWaNWvw2muv4bnnnkNhYSFiYmIwZswYvPDCCw5/7uuzV9OUKVOwYMECTJ06Ff3790dYWBiMRiMee+wxp0/KmqpfbmfPnsWBAwea7Ic33ngjvvnmG7zzzjt48MEHUV5eji5duuD5559v8MsKV7jbD4GaE+2ff/4Zf/3rX9GjRw+EhITAYDDg5ptvdqkfjhw50uExBQUFNr+cIH1iv7JPyn61bds2pKeno0OHDvjxxx8bXXpkxX7FfkXOcfbn/9y5c2jVqlWjz3X+/PkGn9MAIDo6WtSa33nnHcTHx+OLL77AzJkz4e/vj6FDh2L27Nno0KGDx89fWFgIQRBE7eFSa+r1z507B6DpJdiuOH/+PNq0adPgfrV+j7SCYZcGrFy5EqdOncLq1atrp7kA4MKFC3aPt5cwR0REoLKyEgUFBTYnB2fOnLE5LiwsrDZpHzdunN3nT0hIcONdAM2bN0dqaipee+01u39u/WEGgEceeQSPPPIISktLsXbtWrz44ou45ZZbkJubWztRJZecnBzs2LED//73v/HQQw/V3n/gwAGnn6N58+YAgKVLl8peP3mX8PBw+Pj44MSJE40eFxERgdOnTze4/9SpUwCu/pt1RUpKCpYsWQJBELBz5078+9//xiuvvIKAgABMmzbNqeew17+s+6W8/vrrNvfn5+ejWbNmLtepBs2bN0dAQEDtHjz2/tzqtttuw2233Yby8nJs3LgRM2bMwH333Yc2bdqgd+/ecpUMALh48SK+//57vPjiizZ/p+Xl5SgoKHDqOazvbd68eQ6v2GTvBJ/0h/1KXtu2bcPgwYPRunVrLF++3Ol9Xdiv2K/IOc7+/EdGRjrV9zZt2tTg/vqf3QDA398fFy9ebHC/MwMKgYGBePnll/Hyyy/j7NmztVNeI0aMwN69e5t8fFOsgZ/YPdwV/v7+KC8vb3B/fn6+W69tnURu6u/QFVL8d44AjpOoiKNE1noyVf+3b//617+cfm5rSPbFF1/Y3L9kyRKbry0WC9LS0rBt2zakpqaie/fuDW7WJNnVBPmWW25BTk4O2rVrZ/d564ZdVoGBgRg2bBief/55XLlypfaSsnKm1658/x3VNXToUJhMJhw8eNDue+/evbtE1ZO3CQgIQP/+/ZGZmdnoSc6gQYNqg/S6Pv30U1gsFo8uG20wGNC1a1f84x//QLNmzbB169baP/Pz83P559ZgMDT4+fvhhx9EWyJj5U5t7rrllltw8OBBRERE2O0H9n675+fnh/79+2PmzJkAaj64Wu8H5OuHgiA0+Pv44IMPUFVV1aBee3X17dsXzZo1w+7dux32Q+uEBukb+5X7XK1t+/btGDx4MOLj45GVlYWwsDCnH8t+xX5FznH253/YsGHIzc1tdMP1tLQ0FBcX49tvv7W5//PPP29wbJs2bZCbm2sT6Jw/fx7r1693qf6oqCg8/PDDuPfee7Fv3z6UlZU5PNbZHhQYGIjrr78eX331lc3x1dXVWLRoEeLj45u8SIan2rRpg507d9rcl5ub22D5pLP69OmD0NBQ/POf/4QgCA6Pc6XfDRo0CLt377b5bxBQ8985g8GAtLQ0t2r1dpzsUpGUlBQAwNtvv42HHnoIZrMZHTt2RJ8+fRAWFobHH38cL774IsxmM/7zn/9gx44dTj/3TTfdhL59++Kpp55CUVERunXrhg0bNuDTTz8FYLuM7u2338YNN9yAfv364YknnkCbNm1QXFyMAwcO4LvvvqttzO3atUNAQAD+85//oFOnTggKCkJsbKzd0AoAXnnlFWRlZaFPnz6YOHEiOnbsiMuXL+PIkSP48ccf8c9//hPx8fEYM2YMAgIC0LdvX8TExODMmTOYMWMGQkNDa5cBJicnAwDee+89BAcHw9/fHwkJCQ1GOsWQlJSEdu3aYdq0aRAEAeHh4fjuu++QlZXV4FhHf4dt2rTBK6+8gueffx6HDh3CTTfdhLCwMJw9exabNm2q/a0KkRisVwq9/vrrMW3aNLRv3x5nz57Ft99+i3/9618IDg7Giy++WLuP3t/+9jeEh4fjP//5D3744QfMmjXL5Su5fP/991i4cCFuv/12tG3bFoIg4KuvvsKFCxdslh+npKRg9erV+O677xATE4Pg4OAmr052yy234N///jeSkpKQmpqKLVu2YPbs2aKOj7tT2+7du2v3+jlz5gzKysqwdOlSADVXYW3sCqtPPvkk/vvf/+LGG2/E5MmTkZqaiurqahw7dgzLly/HU089heuvvx5/+9vfcOLECQwaNAjx8fG4cOEC3n77bZt9G13txZ4ICQnBjTfeiNmzZ6N58+Zo06YN1qxZgw8//LDB1EpjfXrevHl46KGHUFBQgJEjR6JFixY4d+4cduzYgXPnzuHdd98VvXZSJ/Yr97hS2759+2r3Bnrttdewf/9+7N+/v/bP27Vr1+ieaexX7FfkHGd//p988kl88cUXuO222zBt2jT07NkTly5dwpo1a3DLLbcgLS0NDz74IP7xj3/gwQcfxGuvvVa79HjZsmUNXveBBx7Av/71L4wePRpjxozB+fPnMWvWLKeu0nr99dfjlltuQWpqKsLCwrBnzx589tln6N27t82eePVZp2O/+OILtG3bFv7+/rWfg+qbMWMG0tPTkZaWhqeffhq+vr5YuHAhcnJysHjxYtH2vXLkgQcewOjRozF27FjcddddOHr0KGbNmuX21RqDgoLw5ptv4rHHHsPgwYMxZswYREVF4cCBA9ixYwfmz58P4OrnwpkzZ2LYsGHw8fFBamqq3YB88uTJ+PTTTzF8+HC88soraN26NX744QcsXLgQTzzxhOSBoG4ptTM+2Td9+nQhNjZWMBqNNleFWb9+vdC7d2/BYrEIkZGRwmOPPSZs3bq1wdUuHnroISEwMNDucxcUFAiPPPKI0KxZM8FisQjp6enCxo0bBQA2V6QRhJorPfzpT38S4uLiBLPZLERGRgp9+vQRXn31VZvjFi9eLCQlJQlms7nB1T3sOXfunDBx4kQhISFBMJvNQnh4uNCtWzfh+eefr71S4ieffCKkpaUJUVFRgq+vrxAbGyuMGjWqwdUR33rrLSEhIUHw8fGx+T44uvLIuHHjGrxHAMLs2bNt7rdeiSMzM7P2vt27dwvp6elCcHCwEBYWJmRkZAjHjh2z+54d/R0KQs2VgtLS0oSQkBDBz89PaN26tTBy5EhhxYoVjX7fiFy1e/duISMjQ4iIiBB8fX2FVq1aCQ8//LBw+fLl2mOys7OFESNGCKGhoYKvr6/QtWvXBlfIsvfzIAgNr7azd+9e4d577xXatWsnBAQECKGhoULPnj2Ff//73zaP2759u9C3b1/BYrEIAGqvguPoCjqCIAiFhYXCo48+KrRo0UKwWCzCDTfcIKxbt67BVXQau7rZuXPnbJ7T+np1r5zqqDZHrM9t79ZULxQEQSgpKRFeeOEFoWPHjoKvr68QGhoqpKSkCJMnTxbOnDkjCIIgfP/998KwYcOEuLg4wdfXV2jRooVw8803C+vWrbN5Lke92NHVzYYPH96gHmf75IkTJ4S77rpLCAsLE4KDg4WbbrpJyMnJsXu1I0d9WhAEYc2aNcLw4cOF8PBwwWw2C3FxccLw4cMb/Fsj/WO/qiFVv7I+3tHNmatqs1+xX1HTnP35tx47adIkoVWrVoLZbBZatGghDB8+XNi7d2/tMdZ/v0FBQUJwcLBw1113CevXr7f7c/vJJ58InTp1Evz9/YXOnTsLX3zxhVNXY5w2bZrQvXt3ISwsTPDz8xPatm0rTJ48WcjPz689xt7P5pEjR4QhQ4YIwcHBAoDa17HX2wRBENatWycMHDhQCAwMFAICAoRevXoJ3333nc0xjnqrtbfXv1pqfY4eX11dLcyaNUto27at4O/vL3Tv3l1YuXKlw6sxNvXfEKsff/xR6N+/vxAYGChYLBahc+fOwsyZM2v/vLy8XHjssceEyMhIwWAw2PRxez3o6NGjwn333SdEREQIZrNZ6NixozB79myhqqqqQS31P78KQsO/WxIEgyA0MntHuvf555/j/vvvx6+//oo+ffooXQ4RERERERHZceTIESQkJODjjz/Gww8/rHQ5RKrGZYxeZPHixTh58iRSUlJgNBqxceNGzJ49GzfeeCODLiIiIiIiIiLSBYZdXiQ4OBhLlizBq6++itLSUsTExODhhx/Gq6++qnRpRERERERERESi4DJGIiIiIiIiIiLSDWPTh9g6efIkRo8ejYiICFgsFlxzzTXYsmWLFLUREXmE/YqItIL9ioi0gv2KiLTApWWMhYWF6Nu3L9LS0vDTTz+hRYsWOHjwYIPL9xIRKY39ioi0gv2KiLSC/YqItMKlZYzTpk3Dr7/+inXr1klZExGRx9iviEgr2K+ISCvYr4hIK1wKuzp37oyhQ4fixIkTWLNmDeLi4jB27FiMGTPG4WPKy8tRXl5e+3V1dTUKCgoQEREBg8HgWfVEpGuCIKC4uBixsbEwGl1bdc1+RURyYr8iIq1gvyIirfCkX0FwgZ+fn+Dn5ydMnz5d2Lp1q/DPf/5T8Pf3Fz755BOHj3nxxRcFALzxxhtvbt+OHz/uSqtiv+KNN94Uu7Ff8cYbb1q5sV/xxhtvWrm5069cmuzy9fVF9+7dsX79+tr7Jk6ciM2bN2PDhg12H1M/yb948SJatWqF/UveQrAlwNmXbqD6YK7bj/VmxnaJsrxOSZQ8r6Mnp82tlC5BdUpLinHTjdfgwoULCA0NdemxYvarOV8cR4AlxKnXTYwqcqlOvWhtOKx0CSSxo0KC0iWoWmlJMW4f2FnxfnXg/ZcRbPEHAFxYv9mNd6Juu7/IUboEWXS+O1npEmw069ND6RJIRMVll9F+zIuK9ytXzq+IvMGn839RugTVqawsw5afM9zqVy5tUB8TE4POnTvb3NepUyf897//dfgYPz8/+Pn5Nbg/2BKAkEAPwq6Ahs9JTTN68D13hSEoSJbX0YNT5tYAAH7HHHNnxF3MfhVgCUFAoHMnY8dLQpAU7X2BV7CB/4L1LhnncFhop3QZqqd0vwq2+CPE4o/CdRsR7Gd2uRY1y1m0E4FGH6XLkIXa/u5C/ghQSV+U7leunF8R6dmHc9cAAEzmQIUrUS93+pVLYVffvn2xb98+m/tyc3PRunVrl19Yjwpz9jd5TFhyBxkqIa2wBl0kPiX71d4z3hd4HRbaIcFwUOkyiDSJ51dNy1m0U+kSZJWzaCeSR6cqXQZRA+xXROKyBl0kPpfCrsmTJ6NPnz54/fXXMWrUKGzatAnvvfce3nvvPanq0wxngq66xykVelXv3wtjhyRFXpuuYsglPaX71d4zNb+p9KbQi4GX/iUYDnK6SwJi96vCdRtFrlA53hZyEamd0udXRHrBkEt6Lm1n36NHD3z99ddYvHgxkpOT8fe//x1vvfUW7r//fqnqU73CnP1OB11iPI607ZS5NYMumailX1lDL2/BIET/GGiKT8x+pad9urw96FLL+w/r10vpEkhF1HJ+RaRVH85dw6BLJi5NdgHALbfcgltuuUWKWjRHjLCqMGe/7pY2Fkd3UroEVWG4pRy19CtvW9bICS8i16mlX6mFWoIeImqI/YrINQy3lOFy2EU1xJzK0mPg5c0YbpE93raskYGXvnE5I0mJQRcREWkZwy11YNilEnIGXty3y3MMtMRx5EJ4o39eVqK/FuVNU14MvPSNgRdJgUEXERGpHcMsbdDfJ0kZSLXXFie8lMUASx5NBVzewJumvBh4EZGzGHSpD/frIiJvwhBLXxh2uUjqTeUZeMmH4Za8GHI15C2hFwMv/eJ0F4mFQZd9OYt2Inl0qtJlEBHpDoMt/WPYpUJyBF7eupSRAZf8GHI1zRtCL2sgwtBLf6x/pwy9yF0MuoiISC4MubwHwy4XSD3VRdJhyKUMBl2u8ZbQi4GXPnHKi0hfuISRiPSEIZf3YdilUkotZzxbcBHrduxBcdllBFv80a9rJ0SFh8peh1gYcimHQZf7rKEXoM/gS6zA61z+eazf9DtKSssQFGhBn57dEdk8QoQKyV0MvMhV3jLVVVhdiR1XSnFJqEaAwYiuvoEIM/I0nIjU52LBWezbsRqXy4rhbwlGx64DEBoepXRZbmPI5b34X1knKTHVJWfglXPoOOZ8/h2+XrsJFVXVtfebfYy448aeePq+EUhu21KWWsTCoEsZDLnEpddpL0+WNe7J3Y95//oI3y9bgYqqqtr7zT4+uGXoYEz4y5/QKZF7Hyql7t8pgy9qjDcEXYcrL2NJ6TmsKy9CZZ37TQD6+YXgnsBIJJj8lSrPIU51EXmfE4ey8cN/XseWtUtRWXW1Y5l8TOh240gMv/85xLdNUbBC1zHo8m5GpQsg5VTv3wsAyNq8EwPGvoQtazdhZlU18gBUAcgDMLOqGlvWbsKAsS8ha7N2TkoZdCmDQZd09p4JsZn40gtXw5BVv6zHLRkPYOeyFZhZVVWvX1Vh57IVuCXjAaz6Zb0U5ZKLEgwHa29EdXlD0PV7eQkmFRzC0fIizAJs+tUsAEfLizCp4BB+Ly9x+BzcnJ6I5JCzeRlee6Inzq1dillVlbb9qqoS59YuxWtP9ETO5mUKV+o8Bl3EsMsJSu7VJfVr5xw6jnv/+jbSKiuxs6oakwFEouYfRiSAyQB2VlUjrbIS9/71beQcOi5pPWJg0KUMBl3ysIZeegq+DgvtnAq99uTux2PjpiCtogI7q6oc9KsqpFVU4LFxU7Anl/ssqknd4Kv+jbyLNwRdhysv45WLxzAQArIBu/0qG8BACHjl4jEcrrysYLW2ONVF5F1OHMrGghdux6DKcmRXVdrvV1WVGFRZjgUv3I4Th7IVrbcpH85dw6CLAHAZoyZIuZxxzuffIaa6Cl8KAiwOjrEA+FIQkFpdhTc//w4fvzBWklo8xZBLOQy6lKG3vb2aWto4718fIabKyX5VVYV5732EhXNmSFMsiUqNgReXYErDG4IuAFhSeg6xEJAJNNqvMgGkQMCS0nOYHqqt7SKISB9++M/riKuuRGYT51eZgoCU6kr8+PkM/PmFz+Us0WkMuaguTnY1Qc9XYDxbVIKv127CuKpqh43NygJgbFU1vlq7CXmF2v9QTeJh0KUOepr4shcynMs/j++XrcC4qion+1UVvv/fCuSfL5CkRtK/xibROJlGjSmsrsS68iKMh+Ogy8oCYByAdeVFKKyubOJo6XGqi8i7XCw4iy1rl2J8VaVz/aqqEr+vyURRYZ4c5RF5hGGXRkgRuq3LPYaKqmqMdvL40QAqqqqxbsce0WvxFKe6lMGgS530EHzVX9q4ftPvqKiqcrFfVWH9pt8lqY+orgTDQbQ2HFa6DNXzlqmuHVdKUQm41K8qAey8UipdUUREduzbsRqVVZWu9auqSuzdsVrCqtzDqS6qj8sYvVjJ5SsAgOZOHm89rqj0kiT1uItBlzIYdGmD1q/maA28SkrLALjer4pL+OGRSA28JegCgEtCzVWtXe1XZUJ1o8dJjVNdRN7nclkxANf71eVSdZ1XMugiezjZ1Qi1LWEUu54gf18AQL6Tx1uPCwkMELUOTzDoInKO1qe9ygITALjer4KDAiWph4ic501BFwAEGGpOr13tVxaD7Wk5r8RIRFLztwQDcL1f+Qeq53ySQRc5wrDLi/VLbAWz0YBFTh6/CIDZx4h+XTtJWZbTGHQph1Nd2qbF4Ou6Hv1g9vFxsV/5oE/P7lKWRUTUQFffQJgAl/qVCUCqr3LhPKe6iLxTx64DYPIxudavfExI6jpAwqqcx6CLGsOwS2PEnO6KCgnC7dd1wgKjAWVNHFsGYKGPEXfe2BMtwrTzAZnEx6BLX7QSfIU3b4EBQ27HfB8fJ/uVD265aTCaR/DfK5GSvG2qCwDCjCb08wvBfMCpfrUAQD+/EIQZldldhEEXkfcKDY9CtxtHYr6Pybl+5WNC9/4ZCAlrIUd5RB5h2OWA2pYwSuWpYX1w2mjEKIPjwKsMwCiDAaeNPnjqvhFylucQp7qUwaBL39QefD34l6dw2seEDGf6lY8PJvz5T3KWR0T1eGPQZXVPYCROwYAMOA68ygBkADgFA+4JjLT5My5hJCK5DL//OZw0Nn1+lWEw4KSPCTffN13O8hziVBc1RbNhl7FDktIlKEbMIC45rgU+fyIDq0wmpPoYMRdAHoDqP/53LoBUHyNWmUxY/PdJSG7bUrTXdheDLiLpqTH4apfYBa/N/xyrzL5I8fGx269SfExYafbFq/MXo1NiB0XrJSLvlWDyx99CW2ElDEgB7PcrACthwN9CWyHB5K9InZzqIqL4tikY9+o3+NnkhxQfk8Pzq5/Nfhj3928Q3zZF0XoBBl3kHM2GXSSewV3aYvXCl9D9xp6Y5mNEFAAfAFEApvkY0f3Gnli98CWk9+BvGb0Zp7q8l5qCr143DMZ7X65C+6F3YJqPT71+5YP2Q2/He1+uQq8bBuOw0K72RkTy8uapLqvufkF4O7wtWvuFYCpg06+mAmjtF4K3w9uiu1+QIvUx6CIiq+QeQ/H8u5vQon8GpvqYbPuVjwkt+mfg+YWbkNxjqMKVEjlPmc0ByGOFOfsRlize1EJy25b4+IWxmFk4Gut27EFR6SWEBAagX9dOqtqji1NdymDQRVZ1A6+kaGUuO90usQtemvMhJk5/A9s2rUNpaTECA4Nxbc9+CI+ItPuY+oFXguGgHKUSeSUGXVclmPwxPbQlHq+uxM4rpSgTqmExGJHqG+hwjy4uYSQiJcS3TcGfX/gc94x7C3t3rMbl0iL4B4YgqesAVe3RxakuchbDLju8Zb+uuqr374WxQxJahIXgrgHXK10OEWmA0sFXeEQkBg27063H2pv2YgBGRFIJM5rQ3z9U6TJq6WGqqzjR9fcQnLtRgkqI9CUkrAV6DhildBlEHmPYRZrBqS5lcKqLnKF08CUGR8sdGYIROY9TXZ6RY6pLq0GXO+FWY8/B4ItIezjVRa5g2KVhYi9lJKqPQRe5Qw/BV11N7fnFMIyoBoMu9dNS0CVGuOXM8zP0IiLSJ4ZdpAmc6iLSJr0FX/aoZQN8hm5E2ib1VJcWgi6pAy5Hr8nAi0j9ONVFrmLYVY/W9utS23RXcXQnpUsgkXCqi8TmDcGXkpwJ3RiIkVQ41eUZb9+UXomQq/7rM/AiItIXhl2kepzqItIfBl/KqBuIMfgi8h5qnOpSOuCqj4EXkXpxqovcYVS6AFKP6v17lS6BVIJTXSSnvWdCbMIvksdhoZ1qlmCStnGqyzPetnyxOLGX6oIuK7XWRUREruNklw6obSmjmDjVJT8GXaQUTnspwxp4cdKL3MGgS93UEnRpKUTihBeRunCqi9zFsIuIiFSHwZf8DgvtGHgRyUzKqS41BF1aCrmIiEhfGHaRanGqS36c6iI1YvAlHwZe5ApOdXlGz0GX1kMuTncRqQOnusgTDLvq0NqVGOvS81JGIiIrBl/SY+BFJD29Bl1aD7mIiEg/GHaRKnGqS36c6iKtYfBFpBxOdamTUkGXHkMuTncRKYtTXeQphl1ERKR5DL7ExekuIulIffVFOekx5CIiIn0wunLwSy+9BIPBYHOLjo6WqjZykZaXYZKy9DjVxX7lvfaeCam9EWmB1voVp7rcp5fli8WJvbwi6PKG9+gqrfUrIvJeLk92denSBStWrKj92sfHR9SCSFnV+/fC2CFJ0Rq4hJHEwn5FnPhyH6e75MV+pX96CrrIu7FfkdS4hJHE4HLYZTKZXErvy8vLUV5eXvt1UZF4HzaMHZJQvX+vaM9H5I30ONVlpaZ+Rcpj8EVqppV+xaku9ZEr6GLIRVZa6VdE5N1cWsYIAPv370dsbCwSEhJwzz334NChQ40eP2PGDISGhtbeWrZs6XaxUuISQM8VR3fy+Dk41UVi0mu/Is9xqSOpDfuVvkk11SVH0OUtSxbJeexXJCVOdZFYDIIgCM4e/NNPP6GsrAyJiYk4e/YsXn31Vezduxe7du1CRESE3cfYS/JbtmyJM9/+CyGBAR6/AbEmu/QUdoUld/Do8e4uY2TYpT1qn+oqKynC/QPDcfHiRYSEuBZKiNmvFnx3EQGB3hWK7Nl3SZTn6dTR8z4vJ058NcSljM4pLilBYo8bFe9X+8aNRLCf2aP34ginutyj1aCLAddVersqY1HZZUTdP1XxfuWN51fUNIZdVFdlRSl+WzbcrX7l0jLGYcOG1f7/lJQU9O7dG+3atcMnn3yCKVOm2H2Mn58f/Pz8XCqKiMhT7FeuESvccvZ51RqCcakjKYH9Sr8YdJHesF8RkVa4vGdXXYGBgUhJScH+/fqZiiLlcKpLXmqf6hIb+1VDUgVc7ry2GsMvBl+kFDX2K051uU6LQRdDLnKVGvsVaRenukhMHoVd5eXl2LNnD/r16ydWPUREkmC/ukrJkMsRtYdfDL5ITuxX2qe1oIshF7mL/YqI1MqlDeqffvpprFmzBocPH8Zvv/2GkSNHoqioCA899JBU9ZEb9LT/GEnDG6a62K8a2rPvkiqDLnustaqxZm/b3P6w0E7pEnRP7f2KU12uYdBFeqb2fkVEZOXSZNeJEydw7733Ij8/H5GRkejVqxc2btyI1q25/Iw8wyWMJDb2K1tqC4xcVbd+NU19WQMvTnuRJ9iv9ENLQRdDLucVJ/bS3Sb17mK/IqlwCSOJzaWwa8mSJVLVQUQkKvarq7QedNWnxuCLyxzJE2ruV5zqUh6DLlITNfcrIqK6PNqzi4i0xxuWMNJVegu66rO+P7WEXgCnvYi8lRRTXWIHXQy5iIjIWzDsgj73uCrM2Y+w5A6yvV5xdCe3H8sljETS0HvQVRenvUgqzY7vcOo4Y6n3/LxRQwy6iIjcxyWMJAWGXURehFNd3sObgq76OO1F7nI22PIWXMLoHLUHXQy5iIjIGzHsIiLSGW8OuupS87QXQy91YLhFnmLQRUREpE4Mu2R2bMPh2v/fqneCgpWoA5cwEpEc1DbtxdBLOQy4nMOprqYx6CIi8hyXMJJUNB92GTskoXr/XqXLaFLdkMvefQy+SGpcwugdONXVOLVNe+09E8LASyYMuUhMag66GHIRERHpIOxSO3shl6PjGHgREclHLdNeWpjyOiy0Q4LhoNJluIUhF4lJipALYNBFREQkNoZdEnI26Kp7vJiBl9xXZHQVlzDKR+1TXQdPGu3ef7nM/v1EYlJT6KXmwEtrGHK5j0sY7VNz0MWQi4i0iEsYSUoMu1SGE17kTRyFXOQetS5h3LPtiMM/63RtG9nqaIoaQi8tTHmpHUMukgKDLiIiIm1h2CURV6e66j9WS4FXcXQnpUsgDWLQpU+NBVuuHK9kCKaW0IuBl2sYcpFUGHQRERFpD8MuCXgSdBGJTY1LGLUWdCVGFeF4SYjSZaiaqyGXq8+nRPildOjFwMt5DLrEwyWMthh02dqHLh49viN2iVQJERFR4xh2qZTWprtcxf26vJfWgi4ra+hgXWZGNcQOuZx5HbmDLyVDLwZeTWPQRVJRa9AlZ8jlabjV2PMx+CLShl7z02v//8bxWaI9L/frIqkx7BKZmFNdeg+8yPtoNeiqKym6iIHXH+QKuhp7XTmDrz37LjHwUhGGXCQlbw26xA63nHkthl5E6lQ35CLSIoZdRDqmpiWMegi6rLw98FIq5LLHWotcoZdSU14MvGwx6JIGlzDW8MagS86Qy9FrM/QiUg8GXaQHDLtEJMVeXZ5OdxXm7EdYcgcRK/IclzB6Hz0FXVbeuqxRTUFXXd4QejHwqsGgi6QiVcgFqDPoUjLgsoehF5E6yBF0cQkjycHrw67CnP1Kl0Cka3oMuury9ikvtVEi9GLgJR8GXSQVbwq61BZy1bcPXRh4SaRw3UYAQHF5hcKVkBpxmov0xuvDLnLe2YKLWLdjD4rLLiPY4o9+XTvB0pmXvFYrNSxh1HvQZaWWwKtTx4DaiSMpqHWqy57tv/6O86c3IzLaH77+wWiZ2A+BIVGSvJbcU17eGngx6CKpKB10nb1QhLU5B1ByqRxBAX64Mbk9oprV/DdFzKBL7SFXXQy8xGUNuYgccTboulhwFvt2rMblsmL4W4LRsesAhIZLc35F5CmGXSKRYglj3edWcqP6nEPHMWfx9/h67WZUVFbW3m82mXDLkEGYOOZhdE5sr1h9RGrgrcsa1aaoIBcHt7+PM4eXo6rq6m+ufUxmJF57B64f+jQi45IleW2lNrD3Bgy6SCpKBl05R09h9n9X4OsNOxqcX93RuyvGTpmCziLUoaWQqy4ua/QcQy5yhjNBV+7FEryfewTLvm2Jysqr51cmkxndbhyJ4fdNR3zbFCnLJHKZYmMXJVGJoj2XsUOSaM9FtrI278SACa9gy5GzmDlrFvLy8lBVVYW8vDzMnDULO/fux/D7/oRVv2xw6vm4X5f38Japrvr0OnGjhamuvOO/YMO398JctRezZ8+06VezZ83Epfwt+Hz2ABzeLd5ls+uTcrKuLm8KVRl0ycMbN6dXMujK2rYH/ae9hS0nC+yeX205WYib73sMK39xP6zYhy6aDbrIM4XrNjLoIqc4E3T9cvY87l37O/b6+GPWLNvzq1mzZuLckU14bXwv5Gxe5tRrcr8ukot3fhrVICknxxzJOXQc9740D2kDB2FndjYmT56MyMhIGI1GREZGYvLkydiZnY20tDT86cmp2J17QPYayT41LGH0ZnoNvNSsqCAX236ehPTBadiVs9Nuv9qVsxODBw3At+/di3MncySrhYGXeBh0kRSSR6cqPtF1z6yP/zi/ymni/GqaW+dXegq59PRepMaQi1zh7ETXpE3ZSEtPx85du+z2q+zsnRg0MA0LXrwTJw5ly1A5kXMYdolAiSBKDnMWf4+Y2Dh8mZkJi8Vi9xiLxYIvMzMRExODeR/8W94CSbW8daqrLqUCL29dRndw+/uIi4tFZhP9KjMzE7GxMcha8rKk9cgVeBGRa6QMuQDn9uia/d8ViImNdfr86p0PPnX69fU6zaXH9yQ2hlzkrF7z053eo+v93COIjYtHZubSJs6vvkRcbAx+XPyGmKUSeYSfSMmuswUX8fXazRg3frzDxmZlsVgwdtw4fLd8Jc6dL3B4HJcwkrdJii7SxZSX2pcwlpfl48zh5Zg4YZxT/WrC+LE4c3g5tq/fImldcgReep7u4lQXiU0NQdfZC0X4esMOjBs/QbTzK0C/IVdden9/nmDQRc5y5YqL+ZfLsfxUHsZNnOhUvxo3bix+X5OJosI8h8dxCSPJSdGwqzi6k5IvrzlyTpCt27EHFZWVGD16tFPHjx49GhUVFVi/WdoPj9Q0pZcwcqqrIbkDL2+b7jp/ejOqqipc6ldVVRU4f2oT9mw7ImmYp5cJrwTDQVlfj0EXiU0NQRcArM054Ob51VaHx3hTCKTUew3OVWeYxGWL5ApXgi4A2JxfiIqqapf6VWVlBfbuWO1GdUTi46dSsqu47DIAoHnz5k4dbz2uuKRUspqItEwPE15qVVlR03dc7VfWxwHSTq9JHXjpbbqLQReJTS1BFwCUXCoHIN75lTcFXWSLIRe5wtWgCwBKK6oAuN6vLpfynJfUwaR0AVqnxf26zhaVYF3uMZRcvoIgf1/0S2yFqJAgm2OCLf4AgPz8fERGRjb5nPn5+TWPCwoUv2DSDE51NS4puki2YKJTxwBdTBWVl+Xj/OnNqKwohckciIiYHvCz2J50mcw1fcfVfmV9nNWebUfQ6do24hRez559l7xu4o5IDeQMus5eKMLanAMouVSOoAA/3JjcHlHNbHt+UIAfAM/Pr7w55NqHLuiIXUqXoSgGXeQKe0FX/uVybM4vRGlFFQLNPujRPAzN/f1sjgk0+9Qc62K/8g/U1y/hSLsUD7uKozsh+MwepcvQtcKc/QhL7oCck3l4838b8M22vaiorKr9c7PJB7dfm4SnbuqN5LgWAIB+XTvB5OODp556CjfeeCOCg4PRpUsX7Nq1C8XFxQgODsaAAQMQFRUFAFi0aBHMZjP69Ohmtwbu1yUPpZcwUtPkDLzE0unaNrLv21VUkIuD29/HmcPLUVVVUXu/j48Z0QlD0O6aMQgJTwQARMT0gNFocqlf+fiYERHbs8HrMvBSllanuq5kb8eVy+VKl0F1SB1yAVeDrpyjpzD7vyvw9YYdqKisrP1zs8mEO3p3xTN3DUZy61gAwI3J7d08v7qu9nm9OegiBl3kmvpBV+7FEryfewTLT+Whoqq69n6zjxFDYltgTGIbJIbWDEH0aB4Gk9HgUr8ymcxI6jrAbi3cr4vkpnjYRa45tuEwWvVOcPlxK3Ydwn3vfYWY2DjMnDUbo0ePRkREBM6fP49FixZhwbx5GDjrU3z+5zsR6xOIOYu/hyAI+Oyzz/DZZ58BAAwGAwRBqH1Os9mMkSNH4sknn8TCBQswYshAREYwbPFWnOpynnVJo9Shl1anu/KO/4JtP09CXFwsZs+e2aBfvTNvATZ8ey+uHfQ2/ANb4OD29wE436/mzV+I6IQh8AuIsPv6Wgy89p4J0fxSWbUHXVeytytdAjlJzqAra9se3DPrY8TExmLmrFkNz6/mz0P/aW9hybOPICY8FLP/uwKCC/2q/vkVg64a3jrdxaCLXFE/6Prl7HlM2pSN2Lh4zJw9p2G/eucd3Lv2d7zdMwUt/P3wfu4RCDA43a8WLFiI7v0zEBLWQtb3SeQIwy4vsCf/Iu77ejXSBg1ucJnryMhITJ48GX/5y18wKiMD97z3FSD8F3HxLTF7jp0muGABTp8+jY8++ginTp3CggULcMMNN8BoMOCDN19T8F0SaY8Wp7ykVlSQi20/T0L64DRkNtKvMjIysDxrAiAA8S3jMWeOnRDfQb+qrjag7x1vNlqHlIEXaQcDLu2Re6LrnlkfI23goCbPr0a98REAAXEtW2K2vQ+ZjZxfffjm3xlyEYMuclr9kOvQt8dwRCjHM8aTSEtPR2bm0kbOr0ZiwvIsAALiXfw8CBjx8HNfy/tmiRrBUQwPaGW/rgVb9yImNq7BiVhdFosFX2ZmIio6BhHNI7EzOxuTJ09GZGQkjEZjbRPcuXMn0tLS8Kc//QmDBw/Gzp07MXjwYBgMBpnfFdWn5BJGTnW5T+ppHK0tmzu4/X3ExcU2CLrqslgsyMzMRFxsNFq0iMCunJ2u9Sujc/1KqqWbWpy2k5qaprquZG+vvZG2yBl0AcDs/65ATGysc+dXMX+cX+10/fzqCNpJ/r60yJsCQAZd5Ky6Qdehb4/h0LfHAACZQiFi4+IbBF111ZxfLUV0XBwiWkRh565dkpxfEclFFZ9Qi6M7efwcxg5JIlSiP+fKLuPHgycxbsIEh43NymKxYOLEicg7dw4lJSUOj/nyyy8RExODN954AxaLBUuXLkVcXBzmffBvu4/hfl1EjdPC8jM5ppzKy/Jx5vByTJwwzul+dc6NfhUfH//H0semaSnw4pSgZxhwaZscG9HX34z+6w07MG68tOdXsXHx+M8Hc0V9L/VtO2xx+kbyY9BFzqofdFkVCpX4VSjBuIkTpT2/iovDj4vfsPsY7tdFSlBF2EWucWWibMPJc6ioqsLo0aOdOn706NGoqKjA6tWrHR5jsVgwduxYZGZmIi8vr+brcePw3fKVOHe+wOnaiOiqpOgiyUIvrUx3nT+9GVVVFZL3qwnjx+LM4eUov3TeqdeRe3N+b6P0VBdDLu2T84qLVmtzDqCislLyfjVu3FisWv41Cs/nuVt+A54EWAy+5MWgi5zlKOgCgGzhEiqFaln61e9rMlFUKF6/IvIEwy6dK71ScxWz5s2bO3W89biiosY/dNdvgtav12/e4n6xpElcwiguNQdeUk93VVaUApCnX1VVVeD8qU1O1yZF4MXljMpiyKUPSgRdAFByqebqm3L0q8qKCmzbvM7Jih0TO6RSQ/DlTUsZiRyxBl11ly3WdQk1V12UpV9VVmDvjtVOVk4kLW5QryPnyi5jw8lzKL1SgUBfM3rHRSLQ1wwAyM/PR2RkZJPPkZ+fDwAICWl8OUz9Jmj9urik1O36yX1K7tdF4vOGjevLy/Jx/vRmVFaUwmQORERMD5jMgQDk61fWcI2Uo8RUFwMu/ZAr6Dp7oQhrcw6g5FI5ggL8cGNyewQF+AGQr1+VlhS79R7kCqK2Hbbg2oQyWV5LbsG5ykxXcaqLnFE36AJqlixmC5dwCdUIgBEphgAE/DHfIle/ulyq/u05yDt4NJIxY8YMGAwGPPnkkx4XIsa+Xd5qT/5FjF/+G67/5EeMW7YRz67agnHLNuL6T37Ed/uPw2Q0YtGiRU4916JFi2A2mzFgwIBGj6vfBK1fBwcF2hzH/bpILcTsV3KQYsJLDdNdRQW52LbyGaxcPAhbf34KO9f+DVt/fgorFw/CqYM/wWg0ydavrOGas7SwnFHvIamntBJ0KdGv5NjcXSzJo1NlCbpyjp7CQ3M/RYcxL+PBNz/B2IVL8OCbn6DDmJex9NdtMPn4yNavAoOCXapfiYkrpae89ERLQZfWzq/0pG7QdUQox+zqM3ik+ghmVZ/GvOqzmFV9Go9UH8E6oRg+MMjWr/wDbc9FuF8XKcXtsGvz5s147733kJqqnZMjPVp99Axu/2oVciqMmDl7NvLy8lBVVYW8vDzMnD0b+wUzDEYDZs+cibKyxn/jVlZWhvnz5yMjIwMtWrRo9Nj6TdD6dZ8e3cR6a6QBWlnCqNV+JcU+Xkru35V3/Bds+PZemKv2YvbsmTb9avbsmfA3HIbBYMDMmbMl71c+PmZExPYU6625TQtLGRMMByV5XjmnurS0ZFGr/Uoucl1xMWvbHvSf9ha2nCzAzFmzbM+vZs3CrrxiGAwGzJ4l/fmVyWzGtT36OVW7GgInpV9f67QUdLFfKadu0LWluhRPCydxvGU4Zr05x6ZfzXpzDs62ioDBaJTl86DJZEZS1wFivEUij7n1SbWkpAT3338/3n//fYSFhYldkya4skm8VK+/J/8i/rJsI9IGp2NnjoNLw+bsQvqQIcjLP4ebhg512ODKysqQMXIkjh07hkmTJjX62mVlZVi4cGFtEywrK8PCBQswYshAREZwOR2pix76ldoCL3emu4oKcrHt50lIH5yGXTk77farXTk7MWRoOs6dy8PQJvrVyJEZbverefMXIjphCPwCIlx+H1qY7iJbWgm5AH30KynJFXTlHD2Fe2Z9jLSBg7AzO8f++VV2Ts351bn8ps+vMtw/v1qwYCHShtyBsIjGP3QC6gqZ5KyF+3Ypg/1KGb3mpzeY6JqBsxg4NB3Zu+1/HszevRtDhg5B3rlzTZ5fefJ5cMGChejePwMhYU33KyI5uBV2jRs3DsOHD8fgwYObPLa8vBxFRUU2NxLHgq17ERMXhy8zMx1eRtZisSAzcylat2qFDRvWIzU5GXPnzkVeXh6qq6uRl5eHuXPnIjU5Gat+XgGT0YhXXn650SY4atQonD59GtOmTav5OiMDp0+fxoTHHpbw3ZIj3K+rcXrpV2oLvFx1cPv7iIuLRWYT/WppZiZatWqF9es3oEtyqt1+1SU5FVkrVgIGH7zsYr/KyMjAyROn0O6aMVK+XWqCXFNdWgq6AOX7lZqXMsoVdAHA7P+uQExsrBPnV5l/nF9tQGqKg/OrlBSsXLkKJh8fl8+vMjJG4dTp07j/sSmN1q2GaS571FiT2mlpqkvpfuWN7F1xMVMoRGzLeGQuXdp4v1q6FK1atcKG9euR2qWL/X7VpQtWZmXBBwJefvkll/vVyVOncfO908R/40RucjnsWrJkCbZu3YoZM2Y4dfyMGTMQGhpae2vZsqXDY7lvl/MKKirw48GTGDdhosPGZmWxWDBu/AQYYECXZr6YNvVZREVFwcfHB1FRUZg29Vl0i7Rg1dSH8OXfJ2HVyp+RmpJivwmmpmLVqlX46KOPkJWVhdSUlJqv35qJzontZXr3pAZaWMIoZb9SglRXanSHK9Nd5WX5OHN4OSZOGOdUv5owYTxgMOCykIBnn51m06+efXYaKnyS0Oe2JeiWPh9ZK1Y5DMXq96suyanIylqFawe/jZDwRLffu9jTXVpYyqhFWgu69NavxCRn0HX2QhG+3rAD48ZPcPL8ajwMBiC5RTCmTbPtV9OmTUPXTh3wv8Uf4pN5s7Fq1Sqkpjp3fpWSkoqVq1bh1bc+R7vEZIc1qD1QUnt95B72K/nZC7oKhUr8KpRg/CTnPg+OnzABBgAJV0ox7dlnbPvVs88gqeoylvTvjvm9umJVVpbjUMxOv/p55SqMe/krxLdNkfLbQOQSgyAIgrMHHz9+HN27d8fy5cvRtWtXAMCAAQNwzTXX4K233rL7mPLycpSXl9d+XVRUhJYtW+LAb6sQHBTU4PjgM3tcfAtXVe/f6/JjCnP2u/VaSi9jXFFQgBcPH0JeXp5TV9XIy8tDVFQUPnnsdvRLbI11uUdRfPkKgv190S+xNVqE1GzUbOyQhJxDx/Hmku/x1ZrNqKisrH0Og8GAuv9czGYzRgwZiAmPPWw36JJzc/q6001tmhXI9rpqoNRklxxh1+WyIrxwfzNcvHixySvC1Cdmv8radByBQera+Fusjcg9DVqcCX5OHfwJW39+yuV+dd2gNxER2xPnT226etXG2J42yw+LCnJxcPv7OHN4OaqqKmrvr9+vfHzMiE4YgnbXjPEo6LLydKP+Bs8n4qSd2KGoFHt2ST3ZpUTQVXS5HC2f/6fi/WrfuJEI9jO7/T4AIGfRTo8eLxa5Js2sQRcAZP6yFQ+++YnL/er9N19Dnx7XYf3mrSguKUVwUCD69LjOZnuH3bkH8M4Hn+K75StRUeG4X5nMZqQNuQP3PzZF00FXXVJfqbEjdkn23HJdjVHOqa7i8gp0XLBU8X614LuLCAhU1/mVGtkLugBgbXUxZlWfdrlfvdkzBT2bh2FTfiFKKyoRaDahZ/MwRPj71h6be7EE7+cewfJTeaioqq69v0G/MpnRvX8Gbr53mt2gi5vTk6cqK0rx27LhbvUrkysHb9myBXl5eejW7eom5FVVVVi7di3mz5+P8vJy+Pj42DzGz88Pfn5+LhVFTSurrgJw9RKvTbEeV3z5ClqEBOKu7p0dHpvctiU+fu4JzHzifqzbsQdFpZcQEhiATm3isefICeSZmv1xEtdN8T267AU93hx80VV671dJ0UWiBF6dOgZ4FHh1urZNk4FXZUUpANf7VWVFKfwCIhDbbpjDY0PCE3HtwNkovzTNJhQLDuuA4sL9DkMyUo4egy5Pqa1fJY9OVTzwUiLoAoCSSzUfyF0+vyopRWREOG67yfGSrs6J7fHPWa/g71OfxNLNeSgtKUZgUDAS2nfC4QN7ar++tke/Jvfo0lLQBdTUK3XgpWVaWr6otn6ld46CLgC4hJoQytV+VVpRiQh/XwyLj3J4bGJoEGb3SMa0y1fwn+7jcLm0CP6BIYhr0wUnj+yq/Tqp6wDu0UWq5VLYNWjQIGRnZ9vc98gjjyApKQlTp05t0NjkZuyQ5NZ0l5oVVFRga3ExyqqrYDH64LrgYISbzbAYa77X+fn5TiX51kvBBtdJ7JvSIiwEdw243ua+zm3iVLHc1NlppiMXwnUdeOl5qstTau9XYlBb4FVelo/zpzdfDZhiesDP0hwmc83kqKv9yvo4Z9gLxYLDpVtavWfbEdGnu8hzWgy6AHX2K6UCL7lCrnOll7AjKBQlWRsQFOCHG5PbI6pZCIICaj6Qu3x+FeR8vyqI6IeBN9nel9De8S8h69JayCWXfegiyXSXXFNdWqLGfqVX1qAr/3I5vv/fAVxCNQJgRIohAGEGEwL+2JHI1X4VaHY+Aojw90XPAaNs7otrw4tCkDa4FHYFBwcjOdl2nDowMBAREREN7ndXcXQnj5Yy6sXBS2X47MwZrL5wARXVV0dHzUYjBjRrhlsjmsNsNGLRokWYPHlyk8+3aNEimE0+6Jco39JCqbga8Og98CL75OhXamBdsuZp6OVJ4HXuZA62rXy5wVJC69LBlh1HwsfH7FK/8vExIyK2p1v1aNGefZdkv2iAEqSc6tJq0AWot1/JGXjJFXLtOXcB8zbvxg8HTtps1WA2mXBH7674U3ovmE0m186vzGb06XGdlGUD0H7Qxeku+7Q01QWot1/pTa/56ci9WIJ/rNqNX4USVApXPw+aDEb0NQRhKEJgMrj4edDHiJ7NefVM8g7qH8/wQr9dvIi/5ObiYEgIZs6Zg7y8PFRVVSEvLw8z58zBwZAQPHv4EJItFiyY947DK2VYlZWVYeH8+bjjuqTavbm0yt1JJl6xkPROjH2a3AlbDu/OwuezB8C3ai9mz55p069mz54Jc9VebMkai2YtrsU78xY41a/mzV+I6IQhXHaoAlLs1yUFLQddaidHCCVX0LX6yGmM+GIFsq8YMXPWLNvzq1mzsOVkAe58/QP0TmqDBfPnOXd+tWABRgwZ6PS2Dvvg3kSE1oMuInJer/np+OXsedyz6nccbxmOWW/afh6c9eYcHG8ZhldwBp3gj/lvO/l5cN48DIltYbM3F5GeeRx2rV692uFmhOS6g5fK8PyRw0hLT0f2rl2YPHkyIiMjYTQaERkZicmTJyN71y6kpadj9+XLOHn8BEZlZDR+adiMDJw+dRJThvZu9LWNHZKarM/ZJYxSbE7vaWDFwIv03q/kDrzOnczBt+/di8GD0pCTs9Nuv9qVsxPpg9NwMX8nThw/gYwm+lVGRgZOnjiFdteM8fi9EGmZmvpV8uhU0QMp63PKOdH12Pe/Im1wOnZm59jtVzuzc5A2cBA27z+GkyecPL86fRoTH3vQqRoYdOnrvYhBa1NdjqipX2mddaJr4oZsDByajuzdDj4P7t6NgUPTkWsox4ljx5ExcmTj51cjR+LUyRMYk9hGtvfCzelJaZzsUpnPzpxBTHw8MpcudXgJWYvFgsylSxEXH4/rosOxakUWUpMdXBo2uQtWrcjC53++E8lx3DxQT4GXnt4LiScpusjj0MvZwOu3ZXMQFxuDzMwvG+9XmZmIj49DaIuuyFqxCl2SU+32qy7JqcjKWoVrB78tyhUTSV2kWsLIqS75eBpOyR1w1TVv827ExMfjy8zMRvvVl5mZiI2Lw/Ud22DVyp+RmpJs//wqJQWrVq3CR2+9YfeK1PUx6CKiplj36PrHqt2Ibenk58GW8UiCH1Yuy0JKZ/ufB1M6d8bKZcvxds8UJIYGyfmWiBTl0p5dclH7vl3HNhyW5HkLKiqw+sIFzHzhBYeNzcpisWDchAmY9uyzWDTiBizefRjTnn0WTz31VO0xZh8fDG8Xh3fvTEOfLm0lqVkuYgY73MPLM1rYnJ4837y+qT28SovOInfb15g9a6ZT/WrC+LF49tlp6DHsfRzfm4lnn51m06+s+3v1vu1NBl3kNAZdylAirPLEudJL+OHAScycNcupfjV23HhMmzoV37/4OD7K2oBpU6fanl+ZzRgxZCA+fPPvDLrcwL27auhlqovEteX/DuFXoQSzJr3k3OfBiRMx9eln8HfE4n/HCzD16Wds+pV1f68phnjcEMXtIci7qDLs8lZbi4tRUV2N0aNHO3X86NGj8dRTT+H8pXLMG3I9Xiy7jA0nz6HkSgWCfM3oHReJ5hZ/iauWHieYSGmtDYcRbHDuN2GHhXYSV+M8KQOv47nrUFVZ4XK/unLpPK4dOBvll6bh/KlNV6/cGNuTe3SRSxh0kbPWn8hDRWWly/0q72IJ/j35Qcx6pBjLCwwoLilFcFAg+vS4jnt0EZGoes1Px6FvjyFbuIRKwfXPgxcMVXjGGI0xQiWyhUsoQzUsf1y5sZmBH/nJO/FfvoqUVVcBAJo3b+7U8dbjSq7UXP2sucUfIzq0lKY4neF0F0nF0YbeSoVgnl6t0VHgdeVyMQDX+1VlRSkAwC8gArHthrlVkxp0uraN0iVoipRXYSRqitC6FYD1Lver4rLLAICAnum4zY3XdTfoIiLvYg26AOASaq666Gq/Kvvjcc0MJvQzBEtQJZH2cD2SiliMPgCA/Px8p463Hhfka5asJqVxqov0IsFw0OYmN0/28erUMaDBPl6+/jUnUq72K5NZ21eE9SZqvhIjp7rIWWH9eiEowA+A6/0q2OKP4sRebr2uJ0GXN0x1aeE9BudKt8yQSxjJyrpPl1XAHx/PXe1XFn6sJ2qAPxUqcl1wMMxGIxYtWuTU8YsWLYLZxwe94yIlrsw1UlyJUQpaDtKUqp37dYlHieBLzI3rWyb2g4/J7FK/8jGZERHb06MaiNTi5K+7bG6nN+5VuiSqJ6xfTVB1Y3J7mE0m186vTCZcd+vdbr0ugy4icoZ1oss61QUAKYYAmAyufR40GWqWK0ph4/gstx7HKzGSGqj2k2txdCe3HmfskCRyJeI6WFaGt44dw98PH8Jbx47hYJ1LxIabzRjQrBnmv/OOw0vHWpWVlWHBvHkY3i5OF/ty2aPlMIrIFXJOfblytcYTh3Lw+fxJ+GDGQ/h8/iScOJRTG3gFhkQh8do7MG/eAqf61fz5C5F47Z24pk83zS8BlKJ+Z6+AqUViL2FUcqqrbrhF6rL3XCH+umoLJv60AX9dtQUnW7Wq/bOoZiG4o3dXLJg/z6l+tXDBfIwYOsjpfbnqYtClHK0sG+VUF504lIOVY1ph/Ne/4V9VeThSfbn2z8IMJvQ1BGH+205+HnznHfQ1BHFfLiI7+FMhk9WFBXjnxHGc/WN/LavMc3mI8jVjYnxLDAgLxwPR0fhLbi4yRo50eLnZsrIyZIwcidMnTuClRF61zBPcu4vUyBp4SbnPV2Ob129Z+18sfncKCs4es7l/xVfvIDyqFe59Yi4sUTfj+qFP4/PZA5CRMQqZmV867lcZo3Dy1Gncd//VqwN1urYN9mw7Iup7IpIKwy31+iH3OF5auxUni2w/FH64bSZaRoZh1iN34PbeXfHMXYPRf9pbGJWRgS8zMx32q1EZGTh9+gw+fPNVud4CAAZdRN7A0fnVd8IFRAomPGaIRF9jMDIMYXj6+AmnPg+eOn4CUwzxcr0FIk1R7WSXnnxy6hSeP3wIZy0VwFAAzwD42x//OxQ4a6nA84cP4ZNTp9AuwILX2iRgVVYWUrp0wdy5c5GXl4fq6mrk5eVh7ty5SOnSBauysvBamwS0C+DJEZFeST3tZW/K6/tFr2HByyNRUH7Mbr8qKD+GBS+PxMHNcxEZl4xb/7wYK35eheTkVLv9Kjk5FSt+XoVb/7wYkXHJNq/V6do2mpvy0lq9eiP3VBenuNTt7d92YcwPv+CkscxuvzpeUYh7Z3+EmZnLkdw6FkuefQSrVv6M1JRku/0qNSUZq1atwkdvvYHOie1drodXXiQiR5o6vzoXWokZwml8UXUebQx+mI4orFyWhZTODj4Pdu6MlcuWYzqi0cbgp+ybI1Ipr5/sCkvugMKc/ZI9/+rCArx35hTQHkAGAN86fxgIoDeAbgAygfcOnELrAH8MCAvHvxIT8dmZs5j6zDN46qmr0xAmoxFpzcLwUmKi7EGXu0tL3SHnEkZOd5EWSDntZZ3y2rL2v/jq4xec6ldfffwCxrVKws133IWg0NX4bdmbeHbqNJt+5WMyI/HaO3Hf/U81CLrqsgZIap/0YtDlOq1ehZEBl/r9kHscM9fvdKpfvbT4B3SMj8LtvbtizRtPYs5XKzBt6lSbfmU2mTBi6CB8+OarDLpksu2wBdcmNL5MS2+4hNE7uXJ+9dmB84iv9kVfYzDmCHHIPF6AqU/X+zxoMKKvIQhTDPEMuogaoeqwqzi6E4LP7FG6DI+8c+I4EIqGja0u3z/+fCEw78QJDAgLR7sAC15KSMCkinhsLS5GaXUVAo0+uC44GGFm/V59kZrGvcy8W90pLzGDr6ToIjz97hSX+tWSd59Ctxvvwo0DeyAy7mOUFs3E8f3rcOVSEXwDQtCyQz8EhrRwugY1h15aCbo8vQiBlRqvxCjXVBeDLm14ae1Wl/rV1I+/xu29uyK5dSz+PflBzHqkGGt3HUBx2WUEW/xx3a13u7VHF6CNvaK2bM6ze3+3Hs73aCJyz1ezRrvUrz64eA59EYw2Bj88Y4jGGKES2cIllKEaFtRsRs89uoiaxp8SCR0sK6vZoysNjhublS+AnsCZ5Vdw+FIZEv6Y2gozmzEo3PNwozBnP8KSO3j8PHJgmKNOvBKj+og57XUwdzcK8o4BQ+B0vzq//ChOHtmFuDZd/thgvbVL4ZYjagu9pA669Lw5vdYw6NKGvecKa/boGgqn+9Wx5YXYfew0OreKAQC0aBaMkX2vBQAUJ/ZyuxY1b0jvKOCydwxDLyJphL/WC6fLLgP94HS/Ore8Ekery9HaWDO11cxgQj9DsMe1HPr2GNre2qrpA4l0gp9eJfRdfn7N/0l18gF/HPftuXxJ6iHHGLCRlomxr9e3Sz8BBLjcr9b88IHN3WIGN9Y9vZScqtLKRJfeST3Vxb25tOU/OYdq/o+L/erjrA0N/sibgy5Pjte74FzxlxtyCaN36TU/Hb3mp2PpkVM1d7jYr5YJFyWpi8ibcLJLQsVVlTX/x9nzmT+OK7I+ToNOmVsrXQKR1/Jk0quk+ELN/3GxX5UVNdzvzhp47dl3yeU6HJF72oshl+e0sl8XQy7tuXj5Ss3/cbFfFZbY7g+lx6DLk9Bqy+Y8Tnh5gZxFO1FaXaV0GbrVa366zdenDl2o+T8u9qsSaPfv6MO5a5QugQgAwy5JBfv88e0tQ83mg0354xwsxId/LUTkPndCr6DgZjX/x8V+ZQlxPBXZqWOAqIEXYBtCSRF8yR1ycQlj0+S+AiOpW1i/Xmix7ySw54jL/Sos6OqnTQZdjp9Dq4FXRzC4bkzOop1Kl6B79YOuQ98eQyB8ar5wsV8FWR9HRG5T/TJGd64AaOyQJEElrhvRvHnN/3H2vy1/HHdrZHNJ6tECJZcTcikj6Y0ryxtvHfkQYIDL/ar/8McaPaxTxwDJAp26yxw9We6ohuWSYtDz5vRS4VSXtoT1qwmoHknvXXOHi/3K+jgGXfI9F9VQegkjgy5pWZcs1nXo22MAgKGGkJo7XOxXQw2hIlVH5L04QiShdhYLonzNOPtbRc3lZBvblPAKgE1AtK9v7eb0RPUxkCN3ODPp1S6xM6JiWuLsb8ed7lcRUa0R18a5D35STHnZfR0NBVac6lIWgy7tsIZcVsmtY9EyMgzHfyt0ul+1igxD51Yxugu6GExRUxh0Sad+wAVcDbms2hj9ESmYcO63Sqf7VaTBVLs5PRG5T/WTXVo3Mb4lcBFAJmoamD1X/vjzi8CE+HjZaiMi79LUpNekZ193qV/d88SbLr2+lFNepC5i7dcl1RJGBl3aUT/ospr1yB0u9auZj9yhWNAlFSmDLoZo+sCgSxr2JrmAhkGX1WOGSJf61WOGSJEqJfJuDLskNiAsHH+OjgUOAFgIYD2AEgDVf/zv+j/uPwD8OToWA8I4uaMkTk6RN3AUeg0Yciv+PPEF5/rVxBfQ7ca73Hp9hl6c6iJqSli/Xg6DLgC4vXdXvHTvcKf61Uv3Dsegh/7idi2eBl1STHUxjKKmMOgSn6OQC3AcdAFAX2MwHkCEU/**************************************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", "text/plain": ["<Figure size 1200x800 with 8 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_constrained_opt(pbounds, target_function, optimizer);"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}, "vscode": {"interpreter": {"hash": "49851069de08cc5bbf068d7713ecb1523f4cab708013d75e8e72826d85a7e48d"}}}, "nbformat": 4, "nbformat_minor": 2}