#!/usr/bin/env python3
"""Test optimagic PyGAD integration with n_cores."""

import numpy as np
import sys
import os

# Add the optimagic source to the path
sys.path.insert(0, 'optimagic_/src')

# Import only what we need to avoid dependency issues
from optimagic.optimizers.pygad_new import Pygad
from optimagic.optimization.internal_optimization_problem import InternalOptimizationProblem
from optimagic.optimization.algo_options import AlgoOptions
from optimagic.parameters import Bounds
from optimagic.typing import Direction
from optimagic.batch_evaluators import joblib_batch_evaluator


def square_function(params):
    """Simple square function to minimize: sum(x^2)"""
    return np.sum(params**2)


def create_test_problem():
    """Create a simple test problem for PyGAD"""
    
    # Create bounds
    bounds = Bounds(
        lower=np.array([-5.0, -5.0, -5.0]),
        upper=np.array([5.0, 5.0, 5.0])
    )
    
    # Create a simple converter (identity)
    class SimpleConverter:
        def params_to_internal(self, params):
            return np.array(params)
        
        def params_from_internal(self, internal):
            return internal
    
    # Create internal problem
    problem = InternalOptimizationProblem(
        fun=square_function,
        jac=None,
        fun_and_jac=None,
        converter=SimpleConverter(),
        solver_type="scalar",
        direction=Direction.MINIMIZE,
        bounds=bounds,
        numdiff_options=None,
        error_handling="raise",
        error_penalty_func=None,
        batch_evaluator=joblib_batch_evaluator,
        linear_constraints=None,
        nonlinear_constraints=None,
        logger=None,
    )
    
    return problem


def test_pygad_single_core():
    """Test PyGAD with single core"""
    print("Testing PyGAD with n_cores=1...")
    
    problem = create_test_problem()
    x0 = np.array([2.0, 3.0, 1.0])
    
    optimizer = Pygad(
        population_size=20,
        stopping_maxiter=5,  # Just a few generations for testing
        n_cores=1,
        seed=42
    )
    
    try:
        result = optimizer._solve_internal_problem(problem, x0)
        print(f"✓ Single core test passed. Best value: {result.fun:.6f}")
        print(f"  Best params: {result.x}")
        return True
    except Exception as e:
        print(f"✗ Single core test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pygad_multi_core():
    """Test PyGAD with multiple cores"""
    print("\nTesting PyGAD with n_cores=2...")
    
    problem = create_test_problem()
    x0 = np.array([2.0, 3.0, 1.0])
    
    optimizer = Pygad(
        population_size=20,
        stopping_maxiter=5,  # Just a few generations for testing
        n_cores=2,
        seed=42
    )
    
    try:
        result = optimizer._solve_internal_problem(problem, x0)
        print(f"✓ Multi core test passed. Best value: {result.fun:.6f}")
        print(f"  Best params: {result.x}")
        return True
    except Exception as e:
        print(f"✗ Multi core test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Testing optimagic PyGAD integration...")
    print("Expected optimal solution: [0, 0, 0] with value 0")
    print("=" * 60)
    
    # Test single core first
    single_core_ok = test_pygad_single_core()
    
    # Test multi core
    multi_core_ok = test_pygad_multi_core()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print(f"Single core (n_cores=1): {'PASS' if single_core_ok else 'FAIL'}")
    print(f"Multi core (n_cores=2):  {'PASS' if multi_core_ok else 'FAIL'}")
    
    if single_core_ok and not multi_core_ok:
        print("\n🐛 BUG CONFIRMED: Multi-core mode fails while single-core works")
    elif single_core_ok and multi_core_ok:
        print("\n✅ Both modes work correctly")
    else:
        print("\n❌ Both modes have issues")
