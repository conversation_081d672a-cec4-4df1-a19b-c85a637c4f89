name: Coverage

on:
  pull_request:
    paths:
      - src/**
      - extern/**
      - tests/**
      - pyproject.toml
      - noxfile.py
      - CMakeLists.txt
      - .github/workflows/coverage.yml
  push:
    branches:
      - main
      - develop
      - beta/*
    paths:
      - src/**
      - extern/**
      - tests/**
      - pyproject.toml
      - noxfile.py
      - CMakeLists.txt
      - .github/workflows/coverage.yml

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  coverage:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
    - uses: hendrikmuhs/ccache-action@v1.2
      with:
        create-symlink: true
    - uses: rui314/setup-mold@v1
    - uses: actions/setup-python@v5
      with:
        python-version: "3.12"
    # without this, the Qt tests abort because a lib is missing
    - uses: tlambert03/setup-qt-libs@v1
    - uses: astral-sh/setup-uv@v6
    - run: uv pip install --system nox
    - run: nox -s cov
    - uses: AndreMiras/coveralls-python-action@develop
