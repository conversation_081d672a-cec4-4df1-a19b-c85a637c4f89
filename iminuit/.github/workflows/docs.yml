name: Docs

on:
  pull_request:
  push:
    branches:
      - main
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

env:
  CMAKE_ARGS: -DCMAKE_CXX_COMPILER_LAUNCHER=ccache
  # Latest Ju<PERSON><PERSON> requires this to acknowledge deprecation
  JUPYTER_PLATFORM_DIRS: 1

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
        fetch-depth: 0  # needed to get correct ROOT version
    # must come after checkout
    - uses: hendrikmuhs/ccache-action@v1.2
      with:
        create-symlink: true
    - uses: actions/setup-python@v5
      with:
        python-version: "3.11"
    - run: sudo apt-get install pandoc
    - uses: astral-sh/setup-uv@v6
    - run: uv pip install --system nox
    - run: nox -s doc
    - uses: actions/upload-pages-artifact@v3
      with:
        path: './build/html'

  deploy:
    if: github.event_name == 'workflow_dispatch' || github.ref_name == 'main'
    needs: build
    # Set permissions to allow deployment to GitHub Pages
    permissions:
      contents: read
      pages: write
      id-token: write

    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}

    runs-on: ubuntu-latest

    steps:
    - uses: actions/configure-pages@v5
    - uses: actions/deploy-pages@v4
