name: Release

on:
  push:
    branches:
      - main
  workflow_dispatch:
  pull_request:
    paths:
      - .github/workflows/release.yml

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

env:
  # Latest <PERSON><PERSON><PERSON> requires this to acknowledge deprecation
  JUPYTER_PLATFORM_DIRS: 1

jobs:
  release_check:
    runs-on: ubuntu-latest
    outputs:
      tag: ${{ steps.tag.outputs.tag }}
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          fetch-depth: 0
      - uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - if: ${{ github.ref == 'refs/heads/main' }}
        run: |
          pip install packaging
          python .ci/release_check.py

      - id: tag
        run: echo "tag=$(python version.py)" >> $GITHUB_OUTPUT

  wheels:
    needs: release_check
    name: ${{ matrix.py }} ${{ matrix.os }} ${{ matrix.arch }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-13, macos-14, ubuntu-24.04-arm, windows-11-arm]
        py: [cp39, cp310, cp311, cp312, cp313]
        exclude:
          - os: ubuntu-24.04-arm
            py: cp39
          - os: windows-11-arm
            py: cp39
          - os: windows-11-arm
            py: cp310

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      - uses: astral-sh/setup-uv@v6

      - uses: pypa/cibuildwheel@v3.0
        env:
          CIBW_BUILD: ${{ matrix.py }}-*

      - uses: actions/upload-artifact@v4
        with:
          name: wheel-${{ matrix.py }}-${{ matrix.os }}
          path: ./wheelhouse/*.whl

  sdist:
    needs: release_check
    name: source package
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
      # without this, the Qt tests abort because a lib is missing
      - uses: tlambert03/setup-qt-libs@v1

      - run: pipx run build --sdist

      - run: python -m pip install --upgrade pip setuptools
      - run: python -m pip install -v $(echo dist/iminuit-*)[test]
      - run: python -m pytest

      - uses: actions/upload-artifact@v4
        with:
          name: sdist
          path: dist/*.tar.gz

  upload:
    if: ${{ github.ref == 'refs/heads/main' }}
    needs: [wheels, sdist]
    runs-on: ubuntu-latest

    environment:
      name: pypi
      url: https://pypi.org/project/iminuit/

    permissions:
      id-token: write
      attestations: write

    steps:
      - uses: actions/download-artifact@v4
        with:
          pattern: "*"
          merge-multiple: true
          path: dist

      - name: Generate artifact attestation for sdist and wheels
        uses: actions/attest-build-provenance@db473fddc028af60658334401dc6fa3ffd8669fd # v2.3.0
        with:
          subject-path: "dist/iminuit-*"

      - uses: pypa/gh-action-pypi-publish@release/v1

  release:
    if: ${{ github.ref == 'refs/heads/main' }}
    needs: [release_check, upload]
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: softprops/action-gh-release@v2
        with:
          name: v${{ needs.release_check.outputs.tag }}
          tag_name: v${{ needs.release_check.outputs.tag }}
          target_commitish: ${{ github.ref_name }}
          generate_release_notes: true
