# This CITATION.cff file was generated with cffinit.
# Visit https://bit.ly/cffinit to generate yours today!

cff-version: 1.2.0
title: scikit-hep/iminuit
message: >-
  If you use this software, please cite it using the
  metadata from this file and the MINUIT paper at
  https://doi.org/10.1016/0010-4655(75)90039-9.

  This software includes source code under other
  licenses, see LICENSE in the repository for details.
type: software
authors:
  - given-names: Hans
    family-names: <PERSON><PERSON><PERSON><PERSON>
    email: hans.dem<PERSON><PERSON>@gmail.com
    affiliation: TU Dortmund
    orcid: 'https://orcid.org/0000-0003-3337-3850'
  - given-names: Piti
    family-names: Ongmongkolkul
  - given-names: Christoph
    family-names: Deil
  - given-names: <PERSON>
    family-names: <PERSON>hreiner
  - given-names: Matthew
    family-names: Feickert
  - given-names: <PERSON>
    family-names: Burr
  - given-names: <PERSON>
    family-names: Watson
  - given-names: Fabian
    family-names: Rost
  - given-names: <PERSON>
    family-names: Pearce
  - given-names: <PERSON><PERSON>
    family-names: Geiger
  - given-names: <PERSON>
    family-names: <PERSON><PERSON><PERSON><PERSON>b
  - given-names: <PERSON>an
    family-names: <PERSON><PERSON>
  - given-names: <PERSON>.
    family-names: Wiedemann
  - given-names: <PERSON>
    family-names: <PERSON><PERSON><PERSON>
  - given-names: <PERSON>
    family-names: <PERSON>
  - given-names: <PERSON>
    family-names: <PERSON>otleff
  - given-names: <PERSON>
    family-names: Eschle
  - given-names: <PERSON>
    family-names: Neste
  - given-names: Marco <PERSON>
    family-names: Gorelli
  - given-names: Max
    family-names: Baak
  - given-names: Michael
    family-names: Eliachevitch
  - given-names: Omar
    family-names: Zapata
identifiers:
  - type: doi
    value: 10.5281/zenodo.7695764
repository-code: 'https://github.com/scikit-hep/iminuit'
url: 'https://scikit-hep.org/iminuit/'
abstract: >-
  iminuit is a Jupyter-friendly Python interface for the
  Minuit2 C++ library maintained by CERN's ROOT team.
keywords:
  - Python
  - C++
  - fitting
  - optimization
  - statistics
  - data analysis
  - Scikit-HEP
license: MIT
