/pypy*
/py[0-9]*
/gammapy
/probfit
/iminuit-dev
/root-*
/dev
/venv
/out
/.venv
/.vs

/build
/dist
/_build
/_skbuild

__pycache__
*.pyxbldc
*.root
*.so
*.pyc
*.o
*.pkl

CMakeCache.txt
CMakeFiles/

pip-wheel-metadata

bench/*.svg

.benchmarks
.DS_Store
.idea
.vscode
.cache
.project
.pydevproject
.settings
.coverage*
.ipynb_checkpoints
.eggs
.pytest_cache
.mypy_cache
.ruff_cache
.nox

Untitled*.ipynb
Untitled*.py

Pipfile
iminuit.egg-info
archive
env
MANIFEST

htmlcov
cover
coverage.xml

tests/fig/*.svg

doc/index.rst

testcase

*venv*

uv.lock
