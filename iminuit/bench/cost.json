{"machine_info": {"node": "MacBook-Pro-2.local", "processor": "i386", "machine": "x86_64", "python_compiler": "Clang 13.0.0 (clang-1300.0.29.3)", "python_implementation": "CPython", "python_implementation_version": "3.8.12", "python_version": "3.8.12", "python_build": ["default", "Oct 22 2021 18:39:35"], "release": "21.3.0", "system": "<PERSON>", "cpu": {"python_version": "3.8.12.final.0 (64 bit)", "cpuinfo_version": [8, 0, 0], "cpuinfo_version_string": "8.0.0", "arch": "X86_64", "bits": 64, "count": 8, "arch_string_raw": "x86_64", "vendor_id_raw": "GenuineIntel", "brand_raw": "Intel(R) Core(TM) i7-8569U CPU @ 2.80GHz", "hz_advertised_friendly": "2.8000 GHz", "hz_actual_friendly": "2.8000 GHz", "hz_advertised": [2800000000, 0], "hz_actual": [2800000000, 0], "l2_cache_size": 262144, "stepping": 10, "model": 142, "family": 6, "flags": ["1gbpage", "3dnowprefetch", "abm", "acapmsr", "acpi", "adx", "aes", "apic", "avx", "avx1.0", "avx2", "bmi1", "bmi2", "clflush", "clflushopt", "clfsh", "clfsopt", "cmov", "cx16", "cx8", "de", "ds", "ds_cpl", "dscpl", "dtes64", "dts", "em64t", "erms", "est", "f16c", "fma", "fpu", "fpu_csds", "fxsr", "ht", "htt", "ibrs", "intel_pt", "invpcid", "ipt", "l1df", "lahf", "lahf_lm", "lzcnt", "mca", "mce", "mdclear", "mmx", "mon", "monitor", "movbe", "mpx", "msr", "mtrr", "osxsave", "pae", "pat", "pbe", "pcid", "pclmulqdq", "pdcm", "pge", "pni", "popcnt", "prefetchw", "pse", "pse36", "rdrand", "rdrnd", "rdseed", "rdtscp", "rdwrfsgs", "seglim64", "sep", "sgx", "smap", "smep", "ss", "ssbd", "sse", "sse2", "sse3", "sse4.1", "sse4.2", "sse4_1", "sse4_2", "ssse3", "stibp", "syscall", "tm", "tm2", "tpr", "tsc", "tsc_thread_offset", "tscdeadline", "tsci", "tsctmr", "tsxfa", "vme", "vmx", "x2apic", "xd", "xsave", "xtpr"], "l2_cache_line_size": 256, "l2_cache_associativity": 6}}, "commit_info": {"id": "5b76d81819cfde26c08db4516f87ad8d5ddc905b", "time": "2022-03-14T18:52:11+01:00", "author_time": "2022-03-14T18:52:11+01:00", "dirty": true, "project": "iminuit", "branch": "develop"}, "benchmarks": [{"group": null, "name": "test_UnbinnedNLL[False-10]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-10]", "params": {"log": false, "n": 10}, "param": "False-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 5.613999999987129e-06, "max": 2.6232000000625533e-05, "mean": 6.807627819539115e-06, "stddev": 3.8941006015191716e-06, "rounds": 266, "median": 5.8319999998168726e-06, "iqr": 1.0900000102509466e-07, "q1": 5.782999999759397e-06, "q3": 5.892000000784492e-06, "iqr_outliers": 29, "stddev_outliers": 15, "outliers": "15;29", "ld15iqr": 5.6440000006929836e-06, "hd15iqr": 6.056000000143058e-06, "ops": 146894.04687045614, "total": 0.0018108289999974048, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-31]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-31]", "params": {"log": false, "n": 31}, "param": "False-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 6.01999999982894e-06, "max": 0.00012303500000054868, "mean": 6.816884017796628e-06, "stddev": 2.323222675723695e-06, "rounds": 44731, "median": 6.545000000635071e-06, "iqr": 3.320000008244506e-07, "q1": 6.399999999295858e-06, "q3": 6.732000000120308e-06, "iqr_outliers": 1712, "stddev_outliers": 690, "outliers": "690;1712", "ld15iqr": 6.01999999982894e-06, "hd15iqr": 7.231999999746108e-06, "ops": 146694.58910982363, "total": 0.304926039000061, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-100]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-100]", "params": {"log": false, "n": 100}, "param": "False-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 7.054000000117355e-06, "max": 0.00016630500000047732, "mean": 8.018752700972623e-06, "stddev": 3.2906121861149325e-06, "rounds": 45354, "median": 7.604999999522022e-06, "iqr": 3.2199999999704687e-07, "q1": 7.461000000041906e-06, "q3": 7.783000000038953e-06, "iqr_outliers": 2576, "stddev_outliers": 880, "outliers": "880;2576", "ld15iqr": 7.054000000117355e-06, "hd15iqr": 8.267000000117264e-06, "ops": 124707.67428439418, "total": 0.36368250999991236, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-316]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-316]", "params": {"log": false, "n": 316}, "param": "False-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 9.546000000248966e-06, "max": 0.00017028099999993884, "mean": 1.109931439856433e-05, "stddev": 4.326686668004807e-06, "rounds": 38483, "median": 1.0506999999826405e-05, "iqr": 6.220000008383408e-07, "q1": 1.0136999999410534e-05, "q3": 1.0759000000248875e-05, "iqr_outliers": 1857, "stddev_outliers": 1125, "outliers": "1125;1857", "ld15iqr": 9.546000000248966e-06, "hd15iqr": 1.1693999999451421e-05, "ops": 90095.65492886187, "total": 0.4271349159999511, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-1000]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-1000]", "params": {"log": false, "n": 1000}, "param": "False-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.6870999999696323e-05, "max": 0.00020322700000008354, "mean": 2.1278980280460123e-05, "stddev": 1.0796045995011676e-05, "rounds": 15974, "median": 1.8531000000265863e-05, "iqr": 1.0599999997751297e-06, "q1": 1.794599999982438e-05, "q3": 1.900599999959951e-05, "iqr_outliers": 2115, "stddev_outliers": 1376, "outliers": "1376;2115", "ld15iqr": 1.6870999999696323e-05, "hd15iqr": 2.060899999989374e-05, "ops": 46994.73315073614, "total": 0.33991043100007, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-3162]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-3162]", "params": {"log": false, "n": 3162}, "param": "False-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 3.918599999952477e-05, "max": 0.000341052999999647, "mean": 5.476941792159598e-05, "stddev": 2.465966861807974e-05, "rounds": 16070, "median": 4.4873000000222873e-05, "iqr": 8.62499999954025e-06, "q1": 4.2888000000296245e-05, "q3": 5.1512999999836495e-05, "iqr_outliers": 2870, "stddev_outliers": 2113, "outliers": "2113;2870", "ld15iqr": 3.918599999952477e-05, "hd15iqr": 6.445400000032464e-05, "ops": 18258.36457549228, "total": 0.8801445460000474, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-10000]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-10000]", "params": {"log": false, "n": 10000}, "param": "False-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00012126399999967674, "max": 0.0005189279999999741, "mean": 0.0001688479985113522, "stddev": 5.476428721666543e-05, "rounds": 2687, "median": 0.0001410760000002398, "iqr": 5.87212500005041e-05, "q1": 0.00013086899999947832, "q3": 0.00018959024999998242, "iqr_outliers": 155, "stddev_outliers": 413, "outliers": "413;155", "ld15iqr": 0.00012126399999967674, "hd15iqr": 0.0002777659999999571, "ops": 5922.486548946369, "total": 0.4536945720000034, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-31622]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-31622]", "params": {"log": false, "n": 31622}, "param": "False-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00036192599999917974, "max": 0.0010299439999998938, "mean": 0.00047745339502761915, "stddev": 0.0001255235368462855, "rounds": 1086, "median": 0.0004199184999995609, "iqr": 0.00013283600000058016, "q1": 0.0003908599999995488, "q3": 0.000523696000000129, "iqr_outliers": 85, "stddev_outliers": 168, "outliers": "168;85", "ld15iqr": 0.00036192599999917974, "hd15iqr": 0.0007232020000005335, "ops": 2094.445259818821, "total": 0.5185143869999944, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-100000]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-100000]", "params": {"log": false, "n": 100000}, "param": "False-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0011009369999985807, "max": 0.002529974999999851, "mean": 0.001402640759219157, "stddev": 0.00030394511879178475, "rounds": 461, "median": 0.0012860190000001381, "iqr": 0.00032229700000208084, "q1": 0.001183275749998991, "q3": 0.0015055727500010718, "iqr_outliers": 32, "stddev_outliers": 73, "outliers": "73;32", "ld15iqr": 0.0011009369999985807, "hd15iqr": 0.00200464300000025, "ops": 712.940924771568, "total": 0.6466173900000314, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-316227]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-316227]", "params": {"log": false, "n": 316227}, "param": "False-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.004103880000000615, "max": 0.00699744199999941, "mean": 0.004840556282258055, "stddev": 0.00042142557346933887, "rounds": 124, "median": 0.0048290335000000795, "iqr": 0.0005937444999997155, "q1": 0.0045202749999999625, "q3": 0.005114019499999678, "iqr_outliers": 1, "stddev_outliers": 41, "outliers": "41;1", "ld15iqr": 0.004103880000000615, "hd15iqr": 0.00699744199999941, "ops": 206.5878262102374, "total": 0.6002289789999988, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[False-1000000]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[False-1000000]", "params": {"log": false, "n": 1000000}, "param": "False-1000000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.02014899499999956, "max": 0.03069126699999991, "mean": 0.02306327500000005, "stddev": 0.0031518522647303528, "rounds": 32, "median": 0.02149305200000029, "iqr": 0.00429467100000025, "q1": 0.02059766350000025, "q3": 0.0248923345000005, "iqr_outliers": 0, "stddev_outliers": 7, "outliers": "7;0", "ld15iqr": 0.02014899499999956, "hd15iqr": 0.03069126699999991, "ops": 43.35897655471731, "total": 0.7380248000000016, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-10]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-10]", "params": {"log": true, "n": 10}, "param": "True-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 9.861000000555009e-06, "max": 0.00011064799999971342, "mean": 1.2904325369988345e-05, "stddev": 6.2280154244536975e-06, "rounds": 9460, "median": 1.1257000000597372e-05, "iqr": 8.969999996111255e-07, "q1": 1.0861499999847979e-05, "q3": 1.1758499999459104e-05, "iqr_outliers": 1496, "stddev_outliers": 734, "outliers": "734;1496", "ld15iqr": 9.861000000555009e-06, "hd15iqr": 1.3104999998958533e-05, "ops": 77493.39630924877, "total": 0.12207491800008974, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-31]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-31]", "params": {"log": true, "n": 31}, "param": "True-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.0087999999797148e-05, "max": 0.0008933570000007052, "mean": 1.4521875886642965e-05, "stddev": 1.048082927260062e-05, "rounds": 31439, "median": 1.158200000084264e-05, "iqr": 1.1149999994408688e-06, "q1": 1.1069999999779867e-05, "q3": 1.2184999999220736e-05, "iqr_outliers": 6311, "stddev_outliers": 2651, "outliers": "2651;6311", "ld15iqr": 1.0087999999797148e-05, "hd15iqr": 1.3857999999089543e-05, "ops": 68861.62695549459, "total": 0.45655325600016816, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-100]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-100]", "params": {"log": true, "n": 100}, "param": "True-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.0066999999835957e-05, "max": 0.00014689099999998234, "mean": 1.3911768780707706e-05, "stddev": 7.284360644200518e-06, "rounds": 21365, "median": 1.1793999998843674e-05, "iqr": 1.0229999993782712e-06, "q1": 1.1591999999893687e-05, "q3": 1.2614999999271959e-05, "iqr_outliers": 3185, "stddev_outliers": 1637, "outliers": "1637;3185", "ld15iqr": 1.0066999999835957e-05, "hd15iqr": 1.4150000000157092e-05, "ops": 71881.58571085229, "total": 0.29722493999982014, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-316]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-316]", "params": {"log": true, "n": 316}, "param": "True-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.1033000001603455e-05, "max": 0.00014430300000078944, "mean": 1.517401692263753e-05, "stddev": 7.712991478504646e-06, "rounds": 21096, "median": 1.2793000001565247e-05, "iqr": 1.4230000004999965e-06, "q1": 1.2477999999482847e-05, "q3": 1.3900999999982844e-05, "iqr_outliers": 3203, "stddev_outliers": 1738, "outliers": "1738;3203", "ld15iqr": 1.1033000001603455e-05, "hd15iqr": 1.603599999988603e-05, "ops": 65902.12763689083, "total": 0.32011106099996134, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-1000]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-1000]", "params": {"log": true, "n": 1000}, "param": "True-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.186399999930643e-05, "max": 0.0001897890000002178, "mean": 1.5743009087816723e-05, "stddev": 7.027698432300104e-06, "rounds": 17606, "median": 1.3716999999857649e-05, "iqr": 1.3730000016920485e-06, "q1": 1.3452999999330473e-05, "q3": 1.4826000001022521e-05, "iqr_outliers": 1916, "stddev_outliers": 1396, "outliers": "1396;1916", "ld15iqr": 1.186399999930643e-05, "hd15iqr": 1.688700000102017e-05, "ops": 63520.25806641278, "total": 0.27717141800010126, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-3162]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-3162]", "params": {"log": true, "n": 3162}, "param": "True-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.5538999999620273e-05, "max": 0.00014090600000038478, "mean": 1.96098519289649e-05, "stddev": 7.033629237581259e-06, "rounds": 16330, "median": 1.7546000000479012e-05, "iqr": 1.7870000004194253e-06, "q1": 1.715600000018469e-05, "q3": 1.8943000000604115e-05, "iqr_outliers": 1613, "stddev_outliers": 1318, "outliers": "1318;1613", "ld15iqr": 1.5538999999620273e-05, "hd15iqr": 2.1632000001048368e-05, "ops": 50994.775667986636, "total": 0.3202288819999968, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-10000]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-10000]", "params": {"log": true, "n": 10000}, "param": "True-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.8184999999680826e-05, "max": 0.00013907000000124015, "mean": 3.572925095727153e-05, "stddev": 1.084553273841856e-05, "rounds": 3395, "median": 3.183599999978526e-05, "iqr": 3.561500000248685e-06, "q1": 3.0721249999920985e-05, "q3": 3.428275000016967e-05, "iqr_outliers": 537, "stddev_outliers": 385, "outliers": "385;537", "ld15iqr": 2.8184999999680826e-05, "hd15iqr": 3.9652000001666465e-05, "ops": 27988.272163776848, "total": 0.12130080699993684, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-31622]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-31622]", "params": {"log": true, "n": 31622}, "param": "True-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 6.362099999890347e-05, "max": 0.00022118799999937266, "mean": 8.053470712206149e-05, "stddev": 2.237386259623731e-05, "rounds": 1376, "median": 7.234949999901374e-05, "iqr": 1.333300000005977e-05, "q1": 6.82505000000333e-05, "q3": 8.158350000009307e-05, "iqr_outliers": 173, "stddev_outliers": 170, "outliers": "170;173", "ld15iqr": 6.362099999890347e-05, "hd15iqr": 0.00010197200000128248, "ops": 12417.00672586245, "total": 0.1108157569999566, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-100000]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-100000]", "params": {"log": true, "n": 100000}, "param": "True-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00017283899999931407, "max": 0.0006448110000008, "mean": 0.0002660068870056153, "stddev": 8.887120161713912e-05, "rounds": 354, "median": 0.00023938700000059043, "iqr": 0.00010831899999885763, "q1": 0.0001986719999997888, "q3": 0.00030699099999864643, "iqr_outliers": 14, "stddev_outliers": 60, "outliers": "60;14", "ld15iqr": 0.00017283899999931407, "hd15iqr": 0.000481723000000045, "ops": 3759.301164179597, "total": 0.0941664379999878, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-316227]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-316227]", "params": {"log": true, "n": 316227}, "param": "True-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0007201549999997781, "max": 0.0021617169999998964, "mean": 0.0010889014239999569, "stddev": 0.0002952186780363007, "rounds": 125, "median": 0.0010181800000008678, "iqr": 0.0003412147499983753, "q1": 0.0008715085000003953, "q3": 0.0012127232499987706, "iqr_outliers": 6, "stddev_outliers": 27, "outliers": "27;6", "ld15iqr": 0.0007201549999997781, "hd15iqr": 0.0017415899999999596, "ops": 918.3567749655543, "total": 0.1361126779999946, "iterations": 1}}, {"group": null, "name": "test_UnbinnedNLL[True-1000000]", "fullname": "bench/test_cost.py::test_UnbinnedNLL[True-1000000]", "params": {"log": true, "n": 1000000}, "param": "True-1000000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.006835566999999543, "max": 0.015650539000001018, "mean": 0.00975040879411762, "stddev": 0.0021127750091034783, "rounds": 34, "median": 0.009763373000000186, "iqr": 0.002199320999999088, "q1": 0.008179027000000616, "q3": 0.010378347999999704, "iqr_outliers": 2, "stddev_outliers": 11, "outliers": "11;2", "ld15iqr": 0.006835566999999543, "hd15iqr": 0.015237973000001404, "ops": 102.55980247754287, "total": 0.33151389899999906, "iterations": 1}}, {"group": null, "name": "test_custom[False-10]", "fullname": "bench/test_cost.py::test_custom[False-10]", "params": {"log": false, "n": 10}, "param": "False-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 9.966000000360964e-06, "max": 0.00024564900000001444, "mean": 1.3333629878563186e-05, "stddev": 8.059901903783004e-06, "rounds": 17705, "median": 1.1383000000364518e-05, "iqr": 7.669999995130183e-07, "q1": 1.0903999999811731e-05, "q3": 1.167099999932475e-05, "iqr_outliers": 2760, "stddev_outliers": 1442, "outliers": "1442;2760", "ld15iqr": 9.966000000360964e-06, "hd15iqr": 1.2824999998883868e-05, "ops": 74998.33197018054, "total": 0.23607191699996122, "iterations": 1}}, {"group": null, "name": "test_custom[False-31]", "fullname": "bench/test_cost.py::test_custom[False-31]", "params": {"log": false, "n": 31}, "param": "False-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.0072000000249659e-05, "max": 0.00015143200000089507, "mean": 1.2920464623972505e-05, "stddev": 6.0667386601100956e-06, "rounds": 28296, "median": 1.1435999999420687e-05, "iqr": 1.1170000000504388e-06, "q1": 1.0959999999116121e-05, "q3": 1.207699999916656e-05, "iqr_outliers": 3148, "stddev_outliers": 1756, "outliers": "1756;3148", "ld15iqr": 1.0072000000249659e-05, "hd15iqr": 1.375699999961455e-05, "ops": 77396.59749887088, "total": 0.365597466999926, "iterations": 1}}, {"group": null, "name": "test_custom[False-100]", "fullname": "bench/test_cost.py::test_custom[False-100]", "params": {"log": false, "n": 100}, "param": "False-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.1143000000046754e-05, "max": 0.00018102599999991753, "mean": 1.3956544709495123e-05, "stddev": 8.850763497969071e-06, "rounds": 27332, "median": 1.1990999999156315e-05, "iqr": 4.919999998520552e-07, "q1": 1.1842999999345238e-05, "q3": 1.2334999999197294e-05, "iqr_outliers": 2873, "stddev_outliers": 1668, "outliers": "1668;2873", "ld15iqr": 1.1143000000046754e-05, "hd15iqr": 1.3072999999863555e-05, "ops": 71650.97241580613, "total": 0.3814602799999207, "iterations": 1}}, {"group": null, "name": "test_custom[False-316]", "fullname": "bench/test_cost.py::test_custom[False-316]", "params": {"log": false, "n": 316}, "param": "False-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.4291999999471727e-05, "max": 0.0003033289999994082, "mean": 1.824192360940663e-05, "stddev": 1.128592405055234e-05, "rounds": 20369, "median": 1.542300000068053e-05, "iqr": 5.450000011286704e-07, "q1": 1.5228000000089281e-05, "q3": 1.577300000121795e-05, "iqr_outliers": 3139, "stddev_outliers": 1608, "outliers": "1608;3139", "ld15iqr": 1.4411000000436047e-05, "hd15iqr": 1.659399999986988e-05, "ops": 54818.7801578305, "total": 0.3715697420000037, "iterations": 1}}, {"group": null, "name": "test_custom[False-1000]", "fullname": "bench/test_cost.py::test_custom[False-1000]", "params": {"log": false, "n": 1000}, "param": "False-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.0483999998432978e-05, "max": 0.0006008140000002271, "mean": 2.8404916538409974e-05, "stddev": 1.940288907234837e-05, "rounds": 20141, "median": 2.2700999998903626e-05, "iqr": 1.0439999993394622e-06, "q1": 2.248500000057163e-05, "q3": 2.3528999999911093e-05, "iqr_outliers": 4157, "stddev_outliers": 1463, "outliers": "1463;4157", "ld15iqr": 2.095600000018294e-05, "hd15iqr": 2.5103000000470388e-05, "ops": 35205.17297235393, "total": 0.5721034240001153, "iterations": 1}}, {"group": null, "name": "test_custom[False-3162]", "fullname": "bench/test_cost.py::test_custom[False-3162]", "params": {"log": false, "n": 3162}, "param": "False-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 4.402500000111331e-05, "max": 0.0010989180000002818, "mean": 6.062804127025641e-05, "stddev": 4.098725536298938e-05, "rounds": 7778, "median": 4.5823000000666525e-05, "iqr": 1.1557000000550488e-05, "q1": 4.53880000002016e-05, "q3": 5.694500000075209e-05, "iqr_outliers": 1255, "stddev_outliers": 716, "outliers": "716;1255", "ld15iqr": 4.402500000111331e-05, "hd15iqr": 7.437300000034952e-05, "ops": 16494.017933754218, "total": 0.4715649050000543, "iterations": 1}}, {"group": null, "name": "test_custom[False-10000]", "fullname": "bench/test_cost.py::test_custom[False-10000]", "params": {"log": false, "n": 10000}, "param": "False-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00011805799999997646, "max": 0.000288777999999823, "mean": 0.00014026708187140824, "stddev": 3.206076021385848e-05, "rounds": 171, "median": 0.00012519699999913314, "iqr": 2.349775000087817e-05, "q1": 0.00012187674999974973, "q3": 0.0001453745000006279, "iqr_outliers": 19, "stddev_outliers": 24, "outliers": "24;19", "ld15iqr": 0.00011805799999997646, "hd15iqr": 0.00018273300000082315, "ops": 7129.256463157646, "total": 0.02398567100001081, "iterations": 1}}, {"group": null, "name": "test_custom[False-31622]", "fullname": "bench/test_cost.py::test_custom[False-31622]", "params": {"log": false, "n": 31622}, "param": "False-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0003327609999992376, "max": 0.0011477549999998615, "mean": 0.0003945496537657045, "stddev": 8.637925549902671e-05, "rounds": 956, "median": 0.0003578080000004036, "iqr": 4.9915000000844145e-05, "q1": 0.00034959099999998244, "q3": 0.0003995060000008266, "iqr_outliers": 109, "stddev_outliers": 104, "outliers": "104;109", "ld15iqr": 0.0003327609999992376, "hd15iqr": 0.0004750000000015575, "ops": 2534.535236454244, "total": 0.3771894690000135, "iterations": 1}}, {"group": null, "name": "test_custom[False-100000]", "fullname": "bench/test_cost.py::test_custom[False-100000]", "params": {"log": false, "n": 100000}, "param": "False-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0010441270000001168, "max": 0.0024914440000003424, "mean": 0.0013609152873899988, "stddev": 0.0003018179004937743, "rounds": 341, "median": 0.0012594489999990799, "iqr": 0.00035873650000262103, "q1": 0.0011328457499990563, "q3": 0.0014915822500016773, "iqr_outliers": 19, "stddev_outliers": 51, "outliers": "51;19", "ld15iqr": 0.0010441270000001168, "hd15iqr": 0.0020326839999995627, "ops": 734.7995935278439, "total": 0.4640721129999896, "iterations": 1}}, {"group": null, "name": "test_custom[False-316227]", "fullname": "bench/test_cost.py::test_custom[False-316227]", "params": {"log": false, "n": 316227}, "param": "False-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0037355100000020514, "max": 0.011600158000000249, "mean": 0.005870520244680857, "stddev": 0.0019264995299903547, "rounds": 94, "median": 0.0053474695000002015, "iqr": 0.00284201499999881, "q1": 0.004240069000001512, "q3": 0.007082084000000322, "iqr_outliers": 1, "stddev_outliers": 25, "outliers": "25;1", "ld15iqr": 0.0037355100000020514, "hd15iqr": 0.011600158000000249, "ops": 170.3426541976543, "total": 0.5518289030000005, "iterations": 1}}, {"group": null, "name": "test_custom[False-1000000]", "fullname": "bench/test_cost.py::test_custom[False-1000000]", "params": {"log": false, "n": 1000000}, "param": "False-1000000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.01992837400000269, "max": 0.030484014999998976, "mean": 0.023060005529411728, "stddev": 0.0026836474675817536, "rounds": 34, "median": 0.022698909500000752, "iqr": 0.0037107550000001766, "q1": 0.020900592000000273, "q3": 0.02461134700000045, "iqr_outliers": 1, "stddev_outliers": 8, "outliers": "8;1", "ld15iqr": 0.01992837400000269, "hd15iqr": 0.030484014999998976, "ops": 43.36512403366759, "total": 0.7840401879999988, "iterations": 1}}, {"group": null, "name": "test_custom[True-10]", "fullname": "bench/test_cost.py::test_custom[True-10]", "params": {"log": true, "n": 10}, "param": "True-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 8.789999998981557e-06, "max": 0.0007441660000004902, "mean": 1.3133151607740057e-05, "stddev": 1.2985262274528562e-05, "rounds": 21272, "median": 9.6799999980135e-06, "iqr": 5.479999991564455e-07, "q1": 9.512000001876686e-06, "q3": 1.0060000001033131e-05, "iqr_outliers": 3515, "stddev_outliers": 1608, "outliers": "1608;3515", "ld15iqr": 8.789999998981557e-06, "hd15iqr": 1.0882999998074183e-05, "ops": 76143.18557098262, "total": 0.2793684009998465, "iterations": 1}}, {"group": null, "name": "test_custom[True-31]", "fullname": "bench/test_cost.py::test_custom[True-31]", "params": {"log": true, "n": 31}, "param": "True-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 9.032000001241158e-06, "max": 0.000202774000001682, "mean": 1.2148806264562728e-05, "stddev": 9.895840143008478e-06, "rounds": 31734, "median": 9.932000001100505e-06, "iqr": 4.620000026989146e-07, "q1": 9.676999997765279e-06, "q3": 1.0139000000464193e-05, "iqr_outliers": 3591, "stddev_outliers": 2027, "outliers": "2027;3591", "ld15iqr": 9.032000001241158e-06, "hd15iqr": 1.0833000001042592e-05, "ops": 82312.61394931735, "total": 0.3855302179996336, "iterations": 1}}, {"group": null, "name": "test_custom[True-100]", "fullname": "bench/test_cost.py::test_custom[True-100]", "params": {"log": true, "n": 100}, "param": "True-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 9.170999998531215e-06, "max": 0.0002294269999971732, "mean": 1.2380379749494696e-05, "stddev": 1.0329272028596635e-05, "rounds": 33372, "median": 9.99099999887676e-06, "iqr": 4.6000000253343387e-07, "q1": 9.734999999011507e-06, "q3": 1.019500000154494e-05, "iqr_outliers": 4550, "stddev_outliers": 2308, "outliers": "2308;4550", "ld15iqr": 9.170999998531215e-06, "hd15iqr": 1.0885999998322404e-05, "ops": 80772.96659989892, "total": 0.413158033000137, "iterations": 1}}, {"group": null, "name": "test_custom[True-316]", "fullname": "bench/test_cost.py::test_custom[True-316]", "params": {"log": true, "n": 316}, "param": "True-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 9.731000002233259e-06, "max": 0.00029858699999962823, "mean": 1.367896212417522e-05, "stddev": 1.0813466337242634e-05, "rounds": 25478, "median": 1.0877999997660481e-05, "iqr": 7.020000012403216e-07, "q1": 1.0460999998684883e-05, "q3": 1.1162999999925205e-05, "iqr_outliers": 4276, "stddev_outliers": 2161, "outliers": "2161;4276", "ld15iqr": 9.731000002233259e-06, "hd15iqr": 1.2219000002033908e-05, "ops": 73104.96154036946, "total": 0.34851259699973625, "iterations": 1}}, {"group": null, "name": "test_custom[True-1000]", "fullname": "bench/test_cost.py::test_custom[True-1000]", "params": {"log": true, "n": 1000}, "param": "True-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.1400999998301131e-05, "max": 0.0003645020000000443, "mean": 1.8334365914677316e-05, "stddev": 1.4686574395030505e-05, "rounds": 21986, "median": 1.2191999999799918e-05, "iqr": 6.534000000613105e-06, "q1": 1.1879000002323892e-05, "q3": 1.8413000002936997e-05, "iqr_outliers": 2787, "stddev_outliers": 1954, "outliers": "1954;2787", "ld15iqr": 1.1400999998301131e-05, "hd15iqr": 2.8219000000717642e-05, "ops": 54542.38257563433, "total": 0.40309936900009546, "iterations": 1}}, {"group": null, "name": "test_custom[True-3162]", "fullname": "bench/test_cost.py::test_custom[True-3162]", "params": {"log": true, "n": 3162}, "param": "True-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.4678000002987801e-05, "max": 0.00021816099999938388, "mean": 2.0741297327134935e-05, "stddev": 1.5083305725205552e-05, "rounds": 18108, "median": 1.5502000000111593e-05, "iqr": 1.0165000023931725e-06, "q1": 1.5300999997691633e-05, "q3": 1.6317500000084806e-05, "iqr_outliers": 3633, "stddev_outliers": 1610, "outliers": "1610;3633", "ld15iqr": 1.4678000002987801e-05, "hd15iqr": 1.784299999840755e-05, "ops": 48212.99189861878, "total": 0.3755834119997594, "iterations": 1}}, {"group": null, "name": "test_custom[True-10000]", "fullname": "bench/test_cost.py::test_custom[True-10000]", "params": {"log": true, "n": 10000}, "param": "True-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.8541999999021073e-05, "max": 0.0002257589999992149, "mean": 3.8309295480477375e-05, "stddev": 2.1328575645327324e-05, "rounds": 2633, "median": 2.947999999847184e-05, "iqr": 4.855249998492184e-06, "q1": 2.9189000001927923e-05, "q3": 3.4044250000420107e-05, "iqr_outliers": 555, "stddev_outliers": 267, "outliers": "267;555", "ld15iqr": 2.8541999999021073e-05, "hd15iqr": 4.1689999999761085e-05, "ops": 26103.325249340734, "total": 0.10086837500009693, "iterations": 1}}, {"group": null, "name": "test_custom[True-31622]", "fullname": "bench/test_cost.py::test_custom[True-31622]", "params": {"log": true, "n": 31622}, "param": "True-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 6.303400000007287e-05, "max": 0.0003410009999988972, "mean": 7.764404535792864e-05, "stddev": 3.0996005951667394e-05, "rounds": 1411, "median": 6.483900000020526e-05, "iqr": 3.5792500030495944e-06, "q1": 6.404674999860305e-05, "q3": 6.762600000165264e-05, "iqr_outliers": 307, "stddev_outliers": 170, "outliers": "170;307", "ld15iqr": 6.303400000007287e-05, "hd15iqr": 7.309700000135422e-05, "ops": 12879.287721165661, "total": 0.10955574800003731, "iterations": 1}}, {"group": null, "name": "test_custom[True-100000]", "fullname": "bench/test_cost.py::test_custom[True-100000]", "params": {"log": true, "n": 100000}, "param": "True-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00017040299999848685, "max": 0.0005319950000028939, "mean": 0.00021622023185481127, "stddev": 6.987675584364205e-05, "rounds": 496, "median": 0.00017799749999980463, "iqr": 6.552499999834538e-05, "q1": 0.00017415550000166036, "q3": 0.00023968050000000574, "iqr_outliers": 41, "stddev_outliers": 69, "outliers": "69;41", "ld15iqr": 0.00017040299999848685, "hd15iqr": 0.00034965300000067145, "ops": 4624.914104575955, "total": 0.1072452349999864, "iterations": 1}}, {"group": null, "name": "test_custom[True-316227]", "fullname": "bench/test_cost.py::test_custom[True-316227]", "params": {"log": true, "n": 316227}, "param": "True-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0007048860000011814, "max": 0.0013792859999988138, "mean": 0.0008437566850828471, "stddev": 0.00012339195355524246, "rounds": 181, "median": 0.0007973080000027721, "iqr": 0.00012779849999944304, "q1": 0.0007587457500006778, "q3": 0.0008865442500001208, "iqr_outliers": 13, "stddev_outliers": 34, "outliers": "34;13", "ld15iqr": 0.0007048860000011814, "hd15iqr": 0.0010797920000022998, "ops": 1185.1757949648857, "total": 0.15271995999999533, "iterations": 1}}, {"group": null, "name": "test_custom[True-1000000]", "fullname": "bench/test_cost.py::test_custom[True-1000000]", "params": {"log": true, "n": 1000000}, "param": "True-1000000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.007021717000000649, "max": 0.010763501999999647, "mean": 0.008429458762711945, "stddev": 0.0008034515838174789, "rounds": 59, "median": 0.008447413999999043, "iqr": 0.0012559467500015131, "q1": 0.007796427249999738, "q3": 0.009052374000001251, "iqr_outliers": 0, "stddev_outliers": 20, "outliers": "20;0", "ld15iqr": 0.007021717000000649, "hd15iqr": 0.010763501999999647, "ops": 118.63157862798272, "total": 0.4973380670000047, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-10]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-10]", "params": {"fastmath": false, "parallel": false, "n": 10}, "param": "False-False-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 5.569999999011088e-07, "max": 9.442200000009393e-05, "mean": 7.384942069935835e-07, "stddev": 1.3178564893478023e-06, "rounds": 151562, "median": 6.510000005732763e-07, "iqr": 4.4000000087862645e-08, "q1": 6.270000021402211e-07, "q3": 6.710000022280838e-07, "iqr_outliers": 6690, "stddev_outliers": 904, "outliers": "904;6690", "ld15iqr": 5.620000003148107e-07, "hd15iqr": 7.379999971135476e-07, "ops": 1354106.7628289312, "total": 0.1119276590003615, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-31]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-31]", "params": {"fastmath": false, "parallel": false, "n": 31}, "param": "False-False-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 6.08050000039384e-07, "max": 8.525150000160409e-06, "mean": 7.301177386555848e-07, "stddev": 3.304085264139116e-07, "rounds": 75234, "median": 6.574500000411376e-07, "iqr": 3.0949999896279223e-08, "q1": 6.448500000644231e-07, "q3": 6.757999999607023e-07, "iqr_outliers": 6112, "stddev_outliers": 4451, "outliers": "4451;6112", "ld15iqr": 6.08050000039384e-07, "hd15iqr": 7.222499998960075e-07, "ops": 1369642.1098347555, "total": 0.0549296779500134, "iterations": 20}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-100]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-100]", "params": {"fastmath": false, "parallel": false, "n": 100}, "param": "False-False-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 6.763500000062095e-07, "max": 1.0546700000091392e-05, "mean": 8.538209473740091e-07, "stddev": 4.178100397458519e-07, "rounds": 62953, "median": 7.458000000681864e-07, "iqr": 5.410000003536197e-08, "q1": 7.22100000061232e-07, "q3": 7.76200000096594e-07, "iqr_outliers": 6993, "stddev_outliers": 5168, "outliers": "5168;6993", "ld15iqr": 6.763500000062095e-07, "hd15iqr": 8.574499998914575e-07, "ops": 1171205.7464455054, "total": 0.053750590100036806, "iterations": 20}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-316]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-316]", "params": {"fastmath": false, "parallel": false, "n": 316}, "param": "False-False-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 8.770000000879463e-07, "max": 4.2351599999790325e-05, "mean": 1.135184090152709e-06, "stddev": 8.640068702973347e-07, "rounds": 195313, "median": 9.75399999703086e-07, "iqr": 5.05999999234064e-08, "q1": 9.439999999472093e-07, "q3": 9.945999998706157e-07, "iqr_outliers": 25154, "stddev_outliers": 6912, "outliers": "6912;25154", "ld15iqr": 8.770000000879463e-07, "hd15iqr": 1.0705999997639992e-06, "ops": 880914.3897226823, "total": 0.2217162102000466, "iterations": 5}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-1000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-1000]", "params": {"fastmath": false, "parallel": false, "n": 1000}, "param": "False-False-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.934000000147762e-06, "max": 0.00015526800000031926, "mean": 2.591447944351279e-06, "stddev": 3.1300665431034445e-06, "rounds": 197942, "median": 2.162000001248998e-06, "iqr": 9.599999728493458e-08, "q1": 2.1100000004992125e-06, "q3": 2.205999997784147e-06, "iqr_outliers": 25596, "stddev_outliers": 4300, "outliers": "4300;25596", "ld15iqr": 1.9669999993254805e-06, "hd15iqr": 2.3499999990406195e-06, "ops": 385884.65656034293, "total": 0.5129563890007809, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-3162]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-3162]", "params": {"fastmath": false, "parallel": false, "n": 3162}, "param": "False-False-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 4.73999999783814e-06, "max": 0.00010552999999902113, "mean": 5.680130016329278e-06, "stddev": 3.55400793831634e-06, "rounds": 121877, "median": 5.089000001845534e-06, "iqr": 7.30000024873334e-08, "q1": 5.058999999363323e-06, "q3": 5.132000001850656e-06, "iqr_outliers": 29499, "stddev_outliers": 3435, "outliers": "3435;29499", "ld15iqr": 4.94999999745005e-06, "hd15iqr": 5.242000000293956e-06, "ops": 176052.30815583322, "total": 0.6922772060001634, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-10000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-10000]", "params": {"fastmath": false, "parallel": false, "n": 10000}, "param": "False-False-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.5317000002568193e-05, "max": 0.0001715979999978856, "mean": 1.798729320889319e-05, "stddev": 7.312460171572777e-06, "rounds": 49712, "median": 1.6140999999691985e-05, "iqr": 1.1899999918796311e-07, "q1": 1.6093000002825875e-05, "q3": 1.6212000002013838e-05, "iqr_outliers": 9907, "stddev_outliers": 3368, "outliers": "3368;9907", "ld15iqr": 1.5931000000080076e-05, "hd15iqr": 1.6390999999060796e-05, "ops": 55594.80175180471, "total": 0.8941843200004982, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-31622]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-31622]", "params": {"fastmath": false, "parallel": false, "n": 31622}, "param": "False-False-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 5.250899999964531e-05, "max": 0.00023098600000537317, "mean": 5.8762171728307075e-05, "stddev": 1.6905532008580644e-05, "rounds": 16299, "median": 5.324499999659338e-05, "iqr": 5.850000022178392e-07, "q1": 5.287199999770564e-05, "q3": 5.345699999992348e-05, "iqr_outliers": 3407, "stddev_outliers": 1483, "outliers": "1483;3407", "ld15iqr": 5.250899999964531e-05, "hd15iqr": 5.4335000001515255e-05, "ops": 17017.750886124537, "total": 0.9577646369996771, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-100000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-100000]", "params": {"fastmath": false, "parallel": false, "n": 100000}, "param": "False-False-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0001557339999962437, "max": 0.0004167609999967681, "mean": 0.0001776748460486473, "stddev": 3.571705864297936e-05, "rounds": 4391, "median": 0.00016488499999667283, "iqr": 6.429500002624877e-06, "q1": 0.00016334399999529126, "q3": 0.00016977349999791613, "iqr_outliers": 818, "stddev_outliers": 500, "outliers": "500;818", "ld15iqr": 0.0001557339999962437, "hd15iqr": 0.0001797769999996035, "ops": 5628.258711006286, "total": 0.7801702489996103, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-316227]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-316227]", "params": {"fastmath": false, "parallel": false, "n": 316227}, "param": "False-False-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006041039999971076, "max": 0.001561467999998456, "mean": 0.000725377717692312, "stddev": 0.00016024841360708203, "rounds": 1300, "median": 0.0006513955000002625, "iqr": 0.00013348199999896337, "q1": 0.0006258485000003589, "q3": 0.0007593304999993222, "iqr_outliers": 128, "stddev_outliers": 172, "outliers": "172;128", "ld15iqr": 0.0006041039999971076, "hd15iqr": 0.0009613699999988512, "ops": 1378.5921122327284, "total": 0.9429910330000055, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-False-1000000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-False-1000000]", "params": {"fastmath": false, "parallel": false, "n": 1000000}, "param": "False-False-1000000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.004517882000001805, "max": 0.006456591999999262, "mean": 0.005539401096491291, "stddev": 0.0003119458719998638, "rounds": 228, "median": 0.005589803999999532, "iqr": 0.0003723320000048602, "q1": 0.005370785499998476, "q3": 0.005743117500003336, "iqr_outliers": 6, "stddev_outliers": 59, "outliers": "59;6", "ld15iqr": 0.004819979000004082, "hd15iqr": 0.006456591999999262, "ops": 180.52493086904457, "total": 1.2629834500000143, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-10]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-10]", "params": {"fastmath": false, "parallel": true, "n": 10}, "param": "False-True-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.2930000014298457e-06, "max": 0.00013977399999731688, "mean": 2.9654290337888466e-06, "stddev": 3.2213711052195733e-06, "rounds": 144907, "median": 2.494000000297092e-06, "iqr": 1.3000000365082087e-07, "q1": 2.4289999984716815e-06, "q3": 2.5590000021225023e-06, "iqr_outliers": 19688, "stddev_outliers": 2877, "outliers": "2877;19688", "ld15iqr": 2.2930000014298457e-06, "hd15iqr": 2.754999997023333e-06, "ops": 337219.3327190594, "total": 0.4297114249992404, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-31]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-31]", "params": {"fastmath": false, "parallel": true, "n": 31}, "param": "False-True-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.2199999989425123e-06, "max": 0.00012292000000257985, "mean": 2.6337453622649823e-06, "stddev": 1.564083121951044e-06, "rounds": 185220, "median": 2.53599999666676e-06, "iqr": 2.0050000060223283e-07, "q1": 2.428500000206668e-06, "q3": 2.629000000808901e-06, "iqr_outliers": 1983, "stddev_outliers": 1334, "outliers": "1334;1983", "ld15iqr": 2.2199999989425123e-06, "hd15iqr": 2.9299999937393295e-06, "ops": 379687.42701079295, "total": 0.48782231599872006, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-100]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-100]", "params": {"fastmath": false, "parallel": true, "n": 100}, "param": "False-True-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.303999998787276e-06, "max": 0.00012516999999689915, "mean": 2.8794110737327086e-06, "stddev": 2.6350071267249425e-06, "rounds": 173672, "median": 2.5570000019570216e-06, "iqr": 1.4500000133921276e-07, "q1": 2.506999997820003e-06, "q3": 2.651999999159216e-06, "iqr_outliers": 9865, "stddev_outliers": 2496, "outliers": "2496;9865", "ld15iqr": 2.303999998787276e-06, "hd15iqr": 2.8699999958803346e-06, "ops": 347293.23962196737, "total": 0.500073079997307, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-316]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-316]", "params": {"fastmath": false, "parallel": true, "n": 316}, "param": "False-True-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.651000002629189e-06, "max": 0.0007230800000002091, "mean": 3.612061695701183e-06, "stddev": 4.688857260435486e-06, "rounds": 156348, "median": 2.889999997535142e-06, "iqr": 1.5100000183565498e-07, "q1": 2.8560000018273968e-06, "q3": 3.0070000036630518e-06, "iqr_outliers": 16901, "stddev_outliers": 4247, "outliers": "4247;16901", "ld15iqr": 2.651000002629189e-06, "hd15iqr": 3.2339999975761202e-06, "ops": 276850.1992062119, "total": 0.5647386219994885, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-1000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-1000]", "params": {"fastmath": false, "parallel": true, "n": 1000}, "param": "False-True-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 3.2270000005496513e-06, "max": 0.00014222000000074786, "mean": 3.86599585170078e-06, "stddev": 2.652394058101699e-06, "rounds": 139334, "median": 3.5559999957968103e-06, "iqr": 2.2399999721756103e-07, "q1": 3.4370000037142745e-06, "q3": 3.6610000009318355e-06, "iqr_outliers": 4063, "stddev_outliers": 2235, "outliers": "2235;4063", "ld15iqr": 3.2270000005496513e-06, "hd15iqr": 3.997000000310891e-06, "ops": 258665.56467205385, "total": 0.5386646660008765, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-3162]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-3162]", "params": {"fastmath": false, "parallel": true, "n": 3162}, "param": "False-True-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 5.1769999984685455e-06, "max": 0.00012922499999490356, "mean": 6.23081878861576e-06, "stddev": 3.2334501468683276e-06, "rounds": 112664, "median": 5.766000001017346e-06, "iqr": 2.8999999557299816e-07, "q1": 5.681000004642556e-06, "q3": 5.971000000215554e-06, "iqr_outliers": 5431, "stddev_outliers": 2834, "outliers": "2834;5431", "ld15iqr": 5.246999997154944e-06, "hd15iqr": 6.405999997127765e-06, "ops": 160492.55064632688, "total": 0.701988968000606, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-10000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-10000]", "params": {"fastmath": false, "parallel": true, "n": 10000}, "param": "False-True-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.1323000002505523e-05, "max": 0.0002611179999973956, "mean": 1.3936605927854537e-05, "stddev": 6.55507154859e-06, "rounds": 61641, "median": 1.2569999995548642e-05, "iqr": 6.752500070206224e-07, "q1": 1.2448999996195198e-05, "q3": 1.312425000321582e-05, "iqr_outliers": 4883, "stddev_outliers": 3371, "outliers": "3371;4883", "ld15iqr": 1.1436999997727071e-05, "hd15iqr": 1.4137999997387851e-05, "ops": 71753.48181448827, "total": 0.8590663259988816, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-31622]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-31622]", "params": {"fastmath": false, "parallel": true, "n": 31622}, "param": "False-True-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 3.0582000000833887e-05, "max": 0.00016446400000091899, "mean": 3.4908025296270915e-05, "stddev": 9.43187804471102e-06, "rounds": 25063, "median": 3.224499999987529e-05, "iqr": 1.5230000016686063e-06, "q1": 3.2120000000190885e-05, "q3": 3.364300000185949e-05, "iqr_outliers": 2143, "stddev_outliers": 1659, "outliers": "1659;2143", "ld15iqr": 3.0582000000833887e-05, "hd15iqr": 3.5932000002958375e-05, "ops": 28646.707784608658, "total": 0.874899838000438, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-100000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-100000]", "params": {"fastmath": false, "parallel": true, "n": 100000}, "param": "False-True-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 9.072600000337161e-05, "max": 0.000300341000006199, "mean": 0.00010426488576224787, "stddev": 2.267235254179405e-05, "rounds": 7143, "median": 9.734900000069047e-05, "iqr": 6.862499994042537e-06, "q1": 9.504225000256383e-05, "q3": 0.00010190474999660637, "iqr_outliers": 833, "stddev_outliers": 595, "outliers": "595;833", "ld15iqr": 9.072600000337161e-05, "hd15iqr": 0.00011222599999882732, "ops": 9590.956655151096, "total": 0.7447640789997365, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-316227]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-316227]", "params": {"fastmath": false, "parallel": true, "n": 316227}, "param": "False-True-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0002935079999986101, "max": 0.001218880999999783, "mean": 0.0003769477158383957, "stddev": 0.00010447618990144026, "rounds": 1932, "median": 0.00032598900000024855, "iqr": 0.00011434200000337569, "q1": 0.00030775849999642446, "q3": 0.00042210049999980015, "iqr_outliers": 111, "stddev_outliers": 270, "outliers": "270;111", "ld15iqr": 0.0002935079999986101, "hd15iqr": 0.0005952469999996879, "ops": 2652.887809058161, "total": 0.7282629869997805, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[False-True-1000000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[False-True-1000000]", "params": {"fastmath": false, "parallel": true, "n": 1000000}, "param": "False-True-1000000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0010692270000021153, "max": 0.0016584909999934894, "mean": 0.0014053659056599997, "stddev": 0.00010938705043474735, "rounds": 742, "median": 0.0014287605000014025, "iqr": 0.0001535330000095314, "q1": 0.0013364219999942861, "q3": 0.0014899550000038175, "iqr_outliers": 1, "stddev_outliers": 235, "outliers": "235;1", "ld15iqr": 0.0011212009999965744, "hd15iqr": 0.0016584909999934894, "ops": 711.5584603074398, "total": 1.0427815019997198, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-10]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-10]", "params": {"fastmath": true, "parallel": false, "n": 10}, "param": "True-False-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 4.2525000019111305e-07, "max": 6.327250000026652e-06, "mean": 5.276509057261997e-07, "stddev": 2.5985263902639337e-07, "rounds": 107262, "median": 4.5859999993069777e-07, "iqr": 2.835000003642566e-08, "q1": 4.5375000006231404e-07, "q3": 4.821000000987397e-07, "iqr_outliers": 10751, "stddev_outliers": 6206, "outliers": "6206;10751", "ld15iqr": 4.2525000019111305e-07, "hd15iqr": 5.246500002442644e-07, "ops": 1895192.4258023659, "total": 0.05659689145000069, "iterations": 20}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-31]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-31]", "params": {"fastmath": true, "parallel": false, "n": 31}, "param": "True-False-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 5.998500000714558e-07, "max": 8.326300000049968e-06, "mean": 7.364908693010957e-07, "stddev": 3.1494067713892395e-07, "rounds": 70597, "median": 6.688500000961994e-07, "iqr": 4.175000007933254e-08, "q1": 6.566999999790823e-07, "q3": 6.984500000584148e-07, "iqr_outliers": 4606, "stddev_outliers": 3911, "outliers": "3911;4606", "ld15iqr": 5.998500000714558e-07, "hd15iqr": 7.611999997436669e-07, "ops": 1357790.0849591773, "total": 0.051994045900049815, "iterations": 20}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-100]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-100]", "params": {"fastmath": true, "parallel": false, "n": 100}, "param": "True-False-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 6.781499997998708e-07, "max": 6.672919999992644e-05, "mean": 8.573905086876151e-07, "stddev": 4.852130215400721e-07, "rounds": 66819, "median": 7.606000000492941e-07, "iqr": 4.069999981481942e-08, "q1": 7.39200000055007e-07, "q3": 7.798999998698264e-07, "iqr_outliers": 6747, "stddev_outliers": 4465, "outliers": "4465;6747", "ld15iqr": 6.792500002461566e-07, "hd15iqr": 8.410500001332366e-07, "ops": 1166329.6827610508, "total": 0.05728997639999992, "iterations": 20}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-316]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-316]", "params": {"fastmath": true, "parallel": false, "n": 316}, "param": "True-False-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 8.33833333047096e-07, "max": 0.0006090733333330434, "mean": 1.1376584217867735e-06, "stddev": 1.7193758544463329e-06, "rounds": 174856, "median": 9.371666666406023e-07, "iqr": 5.416666700360417e-08, "q1": 9.09666666141599e-07, "q3": 9.638333331452031e-07, "iqr_outliers": 29951, "stddev_outliers": 7628, "outliers": "7628;29951", "ld15iqr": 8.33833333047096e-07, "hd15iqr": 1.0451666661026593e-06, "ops": 878998.4593349568, "total": 0.1989264009999456, "iterations": 6}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-1000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-1000]", "params": {"fastmath": true, "parallel": false, "n": 1000}, "param": "True-False-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.9029999975828105e-06, "max": 0.0001145579999928259, "mean": 2.5136941765871796e-06, "stddev": 2.748865927021821e-06, "rounds": 190404, "median": 2.117999997608422e-06, "iqr": 9.200000761211413e-08, "q1": 2.078999997934261e-06, "q3": 2.171000005546375e-06, "iqr_outliers": 31316, "stddev_outliers": 3712, "outliers": "3712;31316", "ld15iqr": 1.940999993621517e-06, "hd15iqr": 2.3099999992837184e-06, "ops": 397820.8683117097, "total": 0.47861742599890533, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-3162]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-3162]", "params": {"fastmath": true, "parallel": false, "n": 3162}, "param": "True-False-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 4.888999995955601e-06, "max": 0.0016173559999970166, "mean": 6.67323438642645e-06, "stddev": 7.125385296796051e-06, "rounds": 117814, "median": 5.28900000063004e-06, "iqr": 7.889999977805928e-07, "q1": 5.217999998308187e-06, "q3": 6.00699999608878e-06, "iqr_outliers": 18184, "stddev_outliers": 5567, "outliers": "5567;18184", "ld15iqr": 4.888999995955601e-06, "hd15iqr": 7.190999994577396e-06, "ops": 149852.3717425584, "total": 0.7862004360024457, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-10000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-10000]", "params": {"fastmath": true, "parallel": false, "n": 10000}, "param": "True-False-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 1.590200000123332e-05, "max": 0.00020341500000142787, "mean": 2.001629543258794e-05, "stddev": 9.9266641487268e-06, "rounds": 45540, "median": 1.691299999606599e-05, "iqr": 8.679999865535137e-07, "q1": 1.6245000011849697e-05, "q3": 1.711299999840321e-05, "iqr_outliers": 8686, "stddev_outliers": 5031, "outliers": "5031;8686", "ld15iqr": 1.590200000123332e-05, "hd15iqr": 1.8414999999549764e-05, "ops": 49959.294584148156, "total": 0.9115420940000547, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-31622]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-31622]", "params": {"fastmath": true, "parallel": false, "n": 31622}, "param": "True-False-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 5.195599999296974e-05, "max": 0.00026434200000835517, "mean": 6.409864415755342e-05, "stddev": 2.298564850533202e-05, "rounds": 6042, "median": 5.50729999986288e-05, "iqr": 2.561000002287983e-06, "q1": 5.28730000013411e-05, "q3": 5.543400000362908e-05, "iqr_outliers": 1359, "stddev_outliers": 798, "outliers": "798;1359", "ld15iqr": 5.195599999296974e-05, "hd15iqr": 5.9315999990872115e-05, "ops": 15600.954016156977, "total": 0.3872840079999378, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-100000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-100000]", "params": {"fastmath": true, "parallel": false, "n": 100000}, "param": "True-False-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00015974299999754749, "max": 0.0005250009999997474, "mean": 0.00019991409436661905, "stddev": 5.6284982081303065e-05, "rounds": 3497, "median": 0.00017131999999264735, "iqr": 4.23897500105852e-05, "q1": 0.0001695609999927683, "q3": 0.0002119507500033535, "iqr_outliers": 396, "stddev_outliers": 515, "outliers": "515;396", "ld15iqr": 0.00015974299999754749, "hd15iqr": 0.0002756389999944986, "ops": 5002.148563703153, "total": 0.6990995880000668, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-316227]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-316227]", "params": {"fastmath": true, "parallel": false, "n": 316227}, "param": "True-False-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0005987210000029108, "max": 0.001858225999995966, "mean": 0.000793979701890818, "stddev": 0.0001924912937560062, "rounds": 899, "median": 0.0007148859999972501, "iqr": 0.00027998899999914784, "q1": 0.0006326717500009238, "q3": 0.0009126607500000716, "iqr_outliers": 10, "stddev_outliers": 155, "outliers": "155;10", "ld15iqr": 0.0005987210000029108, "hd15iqr": 0.0013555880000097886, "ops": 1259.4780415904288, "total": 0.7137877519998455, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-False-1000000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-False-1000000]", "params": {"fastmath": true, "parallel": false, "n": 1000000}, "param": "True-False-1000000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.004206049000003986, "max": 0.006451630000000819, "mean": 0.005544679136364101, "stddev": 0.0003724681762288862, "rounds": 220, "median": 0.005616597000006607, "iqr": 0.00047781199999974433, "q1": 0.005313022999999362, "q3": 0.005790834999999106, "iqr_outliers": 4, "stddev_outliers": 58, "outliers": "58;4", "ld15iqr": 0.004679873000000612, "hd15iqr": 0.006451630000000819, "ops": 180.35308724027368, "total": 1.2198294100001021, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-10]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-10]", "params": {"fastmath": true, "parallel": true, "n": 10}, "param": "True-True-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.3770000012746095e-06, "max": 8.785100000352486e-05, "mean": 2.869558150655784e-06, "stddev": 2.5634970718002163e-06, "rounds": 43843, "median": 2.5719999996454135e-06, "iqr": 6.899998084008985e-08, "q1": 2.5320000105466534e-06, "q3": 2.600999991386743e-06, "iqr_outliers": 4040, "stddev_outliers": 700, "outliers": "700;4040", "ld15iqr": 2.428999991366254e-06, "hd15iqr": 2.7049999999917418e-06, "ops": 348485.70668326365, "total": 0.12581003799920154, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-31]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-31]", "params": {"fastmath": true, "parallel": true, "n": 31}, "param": "True-True-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.389000002267494e-06, "max": 0.0002836330000093312, "mean": 2.96322942001911e-06, "stddev": 2.5824245072081864e-06, "rounds": 164881, "median": 2.6139999960150817e-06, "iqr": 1.2000000992884452e-07, "q1": 2.566999995678998e-06, "q3": 2.6870000056078425e-06, "iqr_outliers": 13447, "stddev_outliers": 2621, "outliers": "2621;13447", "ld15iqr": 2.389000002267494e-06, "hd15iqr": 2.867999995714854e-06, "ops": 337469.65160515683, "total": 0.4885802300021709, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-100]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-100]", "params": {"fastmath": true, "parallel": true, "n": 100}, "param": "True-True-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.4869999890597683e-06, "max": 0.00017010599999878195, "mean": 3.2954506166353633e-06, "stddev": 3.4040333843371385e-06, "rounds": 170766, "median": 2.7100000039581573e-06, "iqr": 1.610000168739134e-07, "q1": 2.6609999963511655e-06, "q3": 2.822000013225079e-06, "iqr_outliers": 19746, "stddev_outliers": 4008, "outliers": "4008;19746", "ld15iqr": 2.4869999890597683e-06, "hd15iqr": 3.063999997721112e-06, "ops": 303448.63763153413, "total": 0.5627509200003544, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-316]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-316]", "params": {"fastmath": true, "parallel": true, "n": 316}, "param": "True-True-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.4320000022726163e-06, "max": 0.000343454999992332, "mean": 3.290745510603073e-06, "stddev": 3.3747928953426736e-06, "rounds": 161213, "median": 2.7200000118909884e-06, "iqr": 1.4900000167017424e-07, "q1": 2.663999993046673e-06, "q3": 2.812999994716847e-06, "iqr_outliers": 19819, "stddev_outliers": 3690, "outliers": "3690;19819", "ld15iqr": 2.4419999959945926e-06, "hd15iqr": 3.0369999990398355e-06, "ops": 303882.50831852894, "total": 0.5305109560008532, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-1000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-1000]", "params": {"fastmath": true, "parallel": true, "n": 1000}, "param": "True-True-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.6120000029550283e-06, "max": 0.00016679300000532749, "mean": 3.390966656527658e-06, "stddev": 3.3982203598919475e-06, "rounds": 151424, "median": 2.901000001998e-06, "iqr": 1.5599999869664316e-07, "q1": 2.8150000019877552e-06, "q3": 2.9710000006843984e-06, "iqr_outliers": 17510, "stddev_outliers": 3331, "outliers": "3331;17510", "ld15iqr": 2.6120000029550283e-06, "hd15iqr": 3.205000012940218e-06, "ops": 294901.1598433108, "total": 0.513473734998044, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-3162]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-3162]", "params": {"fastmath": true, "parallel": true, "n": 3162}, "param": "True-True-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 3.030999991437966e-06, "max": 0.0018878150000034566, "mean": 4.191692589570426e-06, "stddev": 7.0054428267911025e-06, "rounds": 150626, "median": 3.3860000030472293e-06, "iqr": 1.4500000133921276e-07, "q1": 3.3240000050227536e-06, "q3": 3.4690000063619664e-06, "iqr_outliers": 26754, "stddev_outliers": 4427, "outliers": "4427;26754", "ld15iqr": 3.106999997726234e-06, "hd15iqr": 3.6869999888722305e-06, "ops": 238567.11307698314, "total": 0.631377887996635, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-10000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-10000]", "params": {"fastmath": true, "parallel": true, "n": 10000}, "param": "True-True-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 4.261999990262666e-06, "max": 0.00016315700000291145, "mean": 5.513823705609758e-06, "stddev": 4.4255046361270606e-06, "rounds": 128189, "median": 4.741000012131735e-06, "iqr": 2.499999993688107e-07, "q1": 4.5860000028596914e-06, "q3": 4.836000002228502e-06, "iqr_outliers": 13608, "stddev_outliers": 4658, "outliers": "4658;13608", "ld15iqr": 4.261999990262666e-06, "hd15iqr": 5.2119999907063175e-06, "ops": 181362.34551398535, "total": 0.7068115469984093, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-31622]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-31622]", "params": {"fastmath": true, "parallel": true, "n": 31622}, "param": "True-True-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 9.149999996793667e-06, "max": 0.00016548899999691002, "mean": 1.1977842146838918e-05, "stddev": 7.249386427116504e-06, "rounds": 31675, "median": 9.897999987629191e-06, "iqr": 3.599999871539694e-07, "q1": 9.802000008107825e-06, "q3": 1.0161999995261795e-05, "iqr_outliers": 5640, "stddev_outliers": 2571, "outliers": "2571;5640", "ld15iqr": 9.262999995485188e-06, "hd15iqr": 1.0703999990369084e-05, "ops": 83487.49196564681, "total": 0.37939815000112276, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-100000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-100000]", "params": {"fastmath": true, "parallel": true, "n": 100000}, "param": "True-True-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 2.2526999998717656e-05, "max": 0.00038498199999992266, "mean": 3.00487217428664e-05, "stddev": 1.4126930086363842e-05, "rounds": 25383, "median": 2.4766999999314976e-05, "iqr": 1.6147499977137159e-06, "q1": 2.4354999997200366e-05, "q3": 2.5969749994914082e-05, "iqr_outliers": 5157, "stddev_outliers": 2812, "outliers": "2812;5157", "ld15iqr": 2.2526999998717656e-05, "hd15iqr": 2.839399999743364e-05, "ops": 33279.285839751275, "total": 0.7627267039991779, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-316227]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-316227]", "params": {"fastmath": true, "parallel": true, "n": 316227}, "param": "True-True-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 7.126100000220958e-05, "max": 0.0013527760000044964, "mean": 0.00010187946724624525, "stddev": 4.637387629720374e-05, "rounds": 6442, "median": 7.959999999940237e-05, "iqr": 3.755599999522019e-05, "q1": 7.624799999916831e-05, "q3": 0.0001138039999943885, "iqr_outliers": 571, "stddev_outliers": 925, "outliers": "925;571", "ld15iqr": 7.126100000220958e-05, "hd15iqr": 0.0001703669999955082, "ops": 9815.520507022035, "total": 0.6563075280003119, "iterations": 1}}, {"group": null, "name": "test_numba_sum_logpdf[True-True-1000000]", "fullname": "bench/test_cost.py::test_numba_sum_logpdf[True-True-1000000]", "params": {"fastmath": true, "parallel": true, "n": 1000000}, "param": "True-True-1000000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00032303599999750077, "max": 0.0008806449999951838, "mean": 0.00041048534172643224, "stddev": 7.492473906070616e-05, "rounds": 1668, "median": 0.0003826869999912219, "iqr": 7.027049999663859e-05, "q1": 0.0003624620000053369, "q3": 0.0004327325000019755, "iqr_outliers": 128, "stddev_outliers": 271, "outliers": "271;128", "ld15iqr": 0.00032303599999750077, "hd15iqr": 0.0005382989999986876, "ops": 2436.1405837152874, "total": 0.684689549999689, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-10]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-10]", "params": {"numba": false, "log": false, "n": 10}, "param": "False-False-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0017344800000103078, "max": 0.0041282650000056265, "mean": 0.0021414910851063726, "stddev": 0.00034513590580946964, "rounds": 376, "median": 0.0020513134999973204, "iqr": 0.0003378595000000928, "q1": 0.00189626299999901, "q3": 0.0022341224999991027, "iqr_outliers": 30, "stddev_outliers": 66, "outliers": "66;30", "ld15iqr": 0.0017344800000103078, "hd15iqr": 0.002772308999993811, "ops": 466.9643534613771, "total": 0.805200647999996, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-31]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-31]", "params": {"numba": false, "log": false, "n": 31}, "param": "False-False-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0008828080000000682, "max": 0.0020847910000014735, "mean": 0.0010923882206673027, "stddev": 0.00020940821009605564, "rounds": 929, "median": 0.0010156419999987065, "iqr": 0.00022649024999665812, "q1": 0.0009411497500018129, "q3": 0.001167639999998471, "iqr_outliers": 56, "stddev_outliers": 129, "outliers": "129;56", "ld15iqr": 0.0008828080000000682, "hd15iqr": 0.00150755599999286, "ops": 915.4254697008121, "total": 1.0148286569999243, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-100]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-100]", "params": {"numba": false, "log": false, "n": 100}, "param": "False-False-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0009083310000050915, "max": 0.0019698910000016667, "mean": 0.0010831048155651986, "stddev": 0.00019561254309250845, "rounds": 938, "median": 0.0009991424999995502, "iqr": 0.00022228399998880377, "q1": 0.0009399099999995997, "q3": 0.0011621939999884034, "iqr_outliers": 47, "stddev_outliers": 131, "outliers": "131;47", "ld15iqr": 0.0009083310000050915, "hd15iqr": 0.001495941999991146, "ops": 923.2716775228888, "total": 1.0159523170001563, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-316]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-316]", "params": {"numba": false, "log": false, "n": 316}, "param": "False-False-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0011275840000024573, "max": 0.0026859690000122782, "mean": 0.0013931383106435605, "stddev": 0.0002711524370633534, "rounds": 808, "median": 0.0013117589999893653, "iqr": 0.00027960450000108494, "q1": 0.0011993314999969584, "q3": 0.0014789359999980434, "iqr_outliers": 55, "stddev_outliers": 115, "outliers": "115;55", "ld15iqr": 0.0011275840000024573, "hd15iqr": 0.0019012909999958083, "ops": 717.8038191613939, "total": 1.1256557549999968, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-1000]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-1000]", "params": {"numba": false, "log": false, "n": 1000}, "param": "False-False-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.001243679000012321, "max": 0.003187733999993725, "mean": 0.0015580237327586924, "stddev": 0.00029879129198301976, "rounds": 696, "median": 0.0014769430000001194, "iqr": 0.00033460450000433184, "q1": 0.0013272039999989715, "q3": 0.0016618085000033034, "iqr_outliers": 40, "stddev_outliers": 123, "outliers": "123;40", "ld15iqr": 0.001243679000012321, "hd15iqr": 0.002164116999992416, "ops": 641.8387467239438, "total": 1.08438451800005, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-3162]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-3162]", "params": {"numba": false, "log": false, "n": 3162}, "param": "False-False-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0014341560000019626, "max": 0.0033118289999976014, "mean": 0.0018275347154604706, "stddev": 0.0003623601156891203, "rounds": 608, "median": 0.0017206160000000637, "iqr": 0.0003861669999949413, "q1": 0.001543353000002412, "q3": 0.0019295199999973534, "iqr_outliers": 48, "stddev_outliers": 100, "outliers": "100;48", "ld15iqr": 0.0014341560000019626, "hd15iqr": 0.0025115819999967925, "ops": 547.1852280234453, "total": 1.1111411069999662, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-10000]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-10000]", "params": {"numba": false, "log": false, "n": 10000}, "param": "False-False-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0032603289999997287, "max": 0.00596613300000115, "mean": 0.0040838711266375175, "stddev": 0.0005379164641770408, "rounds": 229, "median": 0.00399909300000445, "iqr": 0.0007170995000080893, "q1": 0.0036451789999922823, "q3": 0.004362278500000372, "iqr_outliers": 3, "stddev_outliers": 70, "outliers": "70;3", "ld15iqr": 0.0032603289999997287, "hd15iqr": 0.005591271999989544, "ops": 244.86570927211326, "total": 0.9352064879999915, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-31622]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-31622]", "params": {"numba": false, "log": false, "n": 31622}, "param": "False-False-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.012078066000000831, "max": 0.018781547999992654, "mean": 0.013415765839285245, "stddev": 0.001054360041583573, "rounds": 56, "median": 0.013215752000000691, "iqr": 0.0010431765000049609, "q1": 0.012766638999991642, "q3": 0.013809815499996603, "iqr_outliers": 2, "stddev_outliers": 8, "outliers": "8;2", "ld15iqr": 0.012078066000000831, "hd15iqr": 0.016091967000008367, "ops": 74.53916623020585, "total": 0.7512828869999737, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-100000]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-100000]", "params": {"numba": false, "log": false, "n": 100000}, "param": "False-False-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.028532187999999792, "max": 0.03257806400000618, "mean": 0.030168462875001634, "stddev": 0.0010069044608002923, "rounds": 32, "median": 0.029816428499998437, "iqr": 0.0012382594999920116, "q1": 0.029552801000008344, "q3": 0.030791060500000356, "iqr_outliers": 0, "stddev_outliers": 9, "outliers": "9;0", "ld15iqr": 0.028532187999999792, "hd15iqr": 0.03257806400000618, "ops": 33.14719759317356, "total": 0.9653908120000523, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-False-316227]", "fullname": "bench/test_cost.py::test_minuit_custom[False-False-316227]", "params": {"numba": false, "log": false, "n": 316227}, "param": "False-False-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.1348924870000019, "max": 0.14091586800000755, "mean": 0.13745938849999995, "stddev": 0.002217329713866885, "rounds": 8, "median": 0.13775680399999857, "iqr": 0.0037236874999990732, "q1": 0.1352264424999987, "q3": 0.13895012999999778, "iqr_outliers": 0, "stddev_outliers": 3, "outliers": "3;0", "ld15iqr": 0.1348924870000019, "hd15iqr": 0.14091586800000755, "ops": 7.274875953634846, "total": 1.0996751079999996, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-10]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-10]", "params": {"numba": false, "log": true, "n": 10}, "param": "False-True-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.001266528000002154, "max": 0.002670139999992216, "mean": 0.001511692147388548, "stddev": 0.00029771554978151555, "rounds": 536, "median": 0.001384672500002182, "iqr": 0.00022811350000750963, "q1": 0.0013347789999968995, "q3": 0.0015628925000044092, "iqr_outliers": 62, "stddev_outliers": 70, "outliers": "70;62", "ld15iqr": 0.001266528000002154, "hd15iqr": 0.0019159369999925957, "ops": 661.5103490002924, "total": 0.8102669910002618, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-31]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-31]", "params": {"numba": false, "log": true, "n": 31}, "param": "False-True-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0008195010000093816, "max": 0.00237106100000517, "mean": 0.0010072394748206137, "stddev": 0.00023668967424665633, "rounds": 695, "median": 0.0008981460000114794, "iqr": 0.0001717067499953373, "q1": 0.0008661062500046057, "q3": 0.001037812999999943, "iqr_outliers": 85, "stddev_outliers": 97, "outliers": "97;85", "ld15iqr": 0.0008195010000093816, "hd15iqr": 0.001296014000004675, "ops": 992.8125584812857, "total": 0.7000314350003265, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-100]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-100]", "params": {"numba": false, "log": true, "n": 100}, "param": "False-True-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0007900579999926549, "max": 0.002130019999995625, "mean": 0.0009970345534532489, "stddev": 0.00022869089032350124, "rounds": 1057, "median": 0.0008880030000000261, "iqr": 0.00017210600000083787, "q1": 0.000867814250000265, "q3": 0.0010399202500011029, "iqr_outliers": 108, "stddev_outliers": 133, "outliers": "133;108", "ld15iqr": 0.0007900579999926549, "hd15iqr": 0.0013007349999867301, "ops": 1002.9742665752958, "total": 1.0538655230000842, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-316]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-316]", "params": {"numba": false, "log": true, "n": 316}, "param": "False-True-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0009590579999922966, "max": 0.002438382999997657, "mean": 0.0011908449200866893, "stddev": 0.0002753196635742493, "rounds": 926, "median": 0.0010627799999980425, "iqr": 0.00026227499999720294, "q1": 0.001015735000009954, "q3": 0.001278010000007157, "iqr_outliers": 73, "stddev_outliers": 129, "outliers": "129;73", "ld15iqr": 0.0009590579999922966, "hd15iqr": 0.0016742699999952038, "ops": 839.73990494682, "total": 1.1027223960002743, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-1000]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-1000]", "params": {"numba": false, "log": true, "n": 1000}, "param": "False-True-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0008996469999971168, "max": 0.002533948000007058, "mean": 0.001122879915151448, "stddev": 0.0002661797058956045, "rounds": 495, "median": 0.0009918009999978494, "iqr": 0.00022886399999322293, "q1": 0.0009561847500023646, "q3": 0.0011850487499955875, "iqr_outliers": 47, "stddev_outliers": 72, "outliers": "72;47", "ld15iqr": 0.0008996469999971168, "hd15iqr": 0.001557597000001465, "ops": 890.5671804318679, "total": 0.5558255579999667, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-3162]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-3162]", "params": {"numba": false, "log": true, "n": 3162}, "param": "False-True-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0007653779999969856, "max": 0.002001369999987901, "mean": 0.0009433106150064317, "stddev": 0.00022681224343272042, "rounds": 813, "median": 0.0008336240000090811, "iqr": 0.0001813799999972332, "q1": 0.0008093120000012277, "q3": 0.000990691999998461, "iqr_outliers": 86, "stddev_outliers": 118, "outliers": "118;86", "ld15iqr": 0.0007653779999969856, "hd15iqr": 0.001264914000003614, "ops": 1060.096201708895, "total": 0.7669115300002289, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-10000]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-10000]", "params": {"numba": false, "log": true, "n": 10000}, "param": "False-True-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0011349509999973861, "max": 0.002723068000008766, "mean": 0.0014135167107025514, "stddev": 0.00029743002774712714, "rounds": 598, "median": 0.0012958754999985445, "iqr": 0.0002950410000011061, "q1": 0.001206702999994036, "q3": 0.001501743999995142, "iqr_outliers": 46, "stddev_outliers": 86, "outliers": "86;46", "ld15iqr": 0.0011349509999973861, "hd15iqr": 0.0019505309999914289, "ops": 707.4553787927814, "total": 0.8452829930001258, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-31622]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-31622]", "params": {"numba": false, "log": true, "n": 31622}, "param": "False-True-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0024581879999914236, "max": 0.004871679000004292, "mean": 0.003061960565350218, "stddev": 0.00046932590081302344, "rounds": 329, "median": 0.0028868889999955627, "iqr": 0.0006882747500007724, "q1": 0.002695703249997905, "q3": 0.0033839779999986774, "iqr_outliers": 2, "stddev_outliers": 82, "outliers": "82;2", "ld15iqr": 0.0024581879999914236, "hd15iqr": 0.004499139999992963, "ops": 326.5881381087032, "total": 1.0073850260002217, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-100000]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-100000]", "params": {"numba": false, "log": true, "n": 100000}, "param": "False-True-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0048042859999952725, "max": 0.008324477999991586, "mean": 0.005830165062893427, "stddev": 0.000842987564016931, "rounds": 159, "median": 0.0056534279999880255, "iqr": 0.0014428974999987076, "q1": 0.005083976250002564, "q3": 0.006526873750001272, "iqr_outliers": 0, "stddev_outliers": 57, "outliers": "57;0", "ld15iqr": 0.0048042859999952725, "hd15iqr": 0.008324477999991586, "ops": 171.52173038197213, "total": 0.9269962450000548, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[False-True-316227]", "fullname": "bench/test_cost.py::test_minuit_custom[False-True-316227]", "params": {"numba": false, "log": true, "n": 316227}, "param": "False-True-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.022271719000002577, "max": 0.0275136759999981, "mean": 0.024295345702703235, "stddev": 0.0013508909801599129, "rounds": 37, "median": 0.023977650999995603, "iqr": 0.0018118857500049046, "q1": 0.023361240249997195, "q3": 0.0251731260000021, "iqr_outliers": 0, "stddev_outliers": 9, "outliers": "9;0", "ld15iqr": 0.022271719000002577, "hd15iqr": 0.0275136759999981, "ops": 41.160146977811245, "total": 0.8989277910000197, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-10]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-10]", "params": {"numba": true, "log": false, "n": 10}, "param": "True-False-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006247840000099814, "max": 0.0016997040000035213, "mean": 0.0007364066695156964, "stddev": 0.00013824279332687748, "rounds": 1053, "median": 0.0006690249999934395, "iqr": 0.0001570274999949106, "q1": 0.0006363307500016901, "q3": 0.0007933582499966008, "iqr_outliers": 50, "stddev_outliers": 136, "outliers": "136;50", "ld15iqr": 0.0006247840000099814, "hd15iqr": 0.0010297870000073317, "ops": 1357.9453329199991, "total": 0.7754362230000282, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-31]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-31]", "params": {"numba": true, "log": false, "n": 31}, "param": "True-False-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004155829999916705, "max": 0.0014971849999909637, "mean": 0.0005144389983598676, "stddev": 0.00012006889660948242, "rounds": 1829, "median": 0.00045067599999981667, "iqr": 0.00011892400000590442, "q1": 0.0004417722499958643, "q3": 0.0005606962500017687, "iqr_outliers": 109, "stddev_outliers": 270, "outliers": "270;109", "ld15iqr": 0.0004155829999916705, "hd15iqr": 0.0007393000000064376, "ops": 1943.8650708600942, "total": 0.9409089280001979, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-100]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-100]", "params": {"numba": true, "log": false, "n": 100}, "param": "True-False-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004419530000063787, "max": 0.0011699809999896615, "mean": 0.000541588804844375, "stddev": 0.00012835751210447753, "rounds": 1445, "median": 0.00047369000000685446, "iqr": 0.0001397294999954113, "q1": 0.00045465125000276885, "q3": 0.0005943807499981801, "iqr_outliers": 78, "stddev_outliers": 229, "outliers": "229;78", "ld15iqr": 0.0004419530000063787, "hd15iqr": 0.0008043440000022883, "ops": 1846.4192595106335, "total": 0.782595823000122, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-316]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-316]", "params": {"numba": true, "log": false, "n": 316}, "param": "True-False-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0005664549999977453, "max": 0.0013976510000048847, "mean": 0.0006828348768269093, "stddev": 0.0001516300455717845, "rounds": 1437, "median": 0.0006034110000001647, "iqr": 0.0001802785000037943, "q1": 0.000572579999996492, "q3": 0.0007528585000002863, "iqr_outliers": 62, "stddev_outliers": 223, "outliers": "223;62", "ld15iqr": 0.0005664549999977453, "hd15iqr": 0.001024354000008998, "ops": 1464.4828990676883, "total": 0.9812337180002686, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-1000]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-1000]", "params": {"numba": true, "log": false, "n": 1000}, "param": "True-False-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0007363069999968275, "max": 0.0021546970000088095, "mean": 0.0009467173763704371, "stddev": 0.0002562169552203185, "rounds": 821, "median": 0.0008324180000016668, "iqr": 0.00023939500000125236, "q1": 0.0007829792500011479, "q3": 0.0010223742500024002, "iqr_outliers": 64, "stddev_outliers": 107, "outliers": "107;64", "ld15iqr": 0.0007363069999968275, "hd15iqr": 0.0013873550000056412, "ops": 1056.2814467754251, "total": 0.7772549660001289, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-3162]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-3162]", "params": {"numba": true, "log": false, "n": 3162}, "param": "True-False-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.001129839000000743, "max": 0.0024871709999985114, "mean": 0.0014112488782049412, "stddev": 0.00025772909993452935, "rounds": 468, "median": 0.0013277479999942443, "iqr": 0.00030073650000161933, "q1": 0.0012109054999953628, "q3": 0.0015116419999969821, "iqr_outliers": 27, "stddev_outliers": 88, "outliers": "88;27", "ld15iqr": 0.001129839000000743, "hd15iqr": 0.0019648090000004004, "ops": 708.5922373040004, "total": 0.6604644749999125, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-10000]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-10000]", "params": {"numba": true, "log": false, "n": 10000}, "param": "True-False-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0028462549999943576, "max": 0.005148325999996928, "mean": 0.003433433589905115, "stddev": 0.0004043515901989944, "rounds": 317, "median": 0.0033321929999914346, "iqr": 0.0006182132500072157, "q1": 0.0031049022499907153, "q3": 0.003723115499997931, "iqr_outliers": 2, "stddev_outliers": 91, "outliers": "91;2", "ld15iqr": 0.0028462549999943576, "hd15iqr": 0.004794410000002358, "ops": 291.2536310415824, "total": 1.0883984479999214, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-31622]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-31622]", "params": {"numba": true, "log": false, "n": 31622}, "param": "True-False-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.01161169799999584, "max": 0.014282819999991148, "mean": 0.013063198207316943, "stddev": 0.0006028859771067187, "rounds": 82, "median": 0.013191876000000491, "iqr": 0.0005937240000122301, "q1": 0.012847469999996974, "q3": 0.013441194000009205, "iqr_outliers": 7, "stddev_outliers": 22, "outliers": "22;7", "ld15iqr": 0.011996899999999755, "hd15iqr": 0.014282819999991148, "ops": 76.55093217829928, "total": 1.0711822529999893, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-100000]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-100000]", "params": {"numba": true, "log": false, "n": 100000}, "param": "True-False-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.03272018700000956, "max": 0.03617866599999786, "mean": 0.03391822135483881, "stddev": 0.0007924347345359764, "rounds": 31, "median": 0.03377913299999591, "iqr": 0.0007900052500069421, "q1": 0.03340289824999587, "q3": 0.034192903500002814, "iqr_outliers": 2, "stddev_outliers": 7, "outliers": "7;2", "ld15iqr": 0.03272018700000956, "hd15iqr": 0.03596168900000407, "ops": 29.48267804312029, "total": 1.051464862000003, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-False-316227]", "fullname": "bench/test_cost.py::test_minuit_custom[True-False-316227]", "params": {"numba": true, "log": false, "n": 316227}, "param": "True-False-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.11665477299999338, "max": 0.1410759789999929, "mean": 0.12520272274999478, "stddev": 0.00810219504828115, "rounds": 8, "median": 0.12357879749998801, "iqr": 0.010663498500001367, "q1": 0.11885160949999829, "q3": 0.12951510799999966, "iqr_outliers": 0, "stddev_outliers": 3, "outliers": "3;0", "ld15iqr": 0.11665477299999338, "hd15iqr": 0.1410759789999929, "ops": 7.987046751345843, "total": 1.0016217819999582, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-10]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-10]", "params": {"numba": true, "log": true, "n": 10}, "param": "True-True-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.000514675000005127, "max": 0.001299927000019352, "mean": 0.0006025002745481143, "stddev": 0.0001336977721421544, "rounds": 1548, "median": 0.000543470499991372, "iqr": 0.00010868399998287259, "q1": 0.0005201235000100723, "q3": 0.0006288074999929449, "iqr_outliers": 153, "stddev_outliers": 211, "outliers": "211;153", "ld15iqr": 0.000514675000005127, "hd15iqr": 0.0007927520000237109, "ops": 1659.7502810268716, "total": 0.9326704250004809, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-31]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-31]", "params": {"numba": true, "log": true, "n": 31}, "param": "True-True-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0003904719999923145, "max": 0.0009447790000081113, "mean": 0.00044771645565514363, "stddev": 9.136295565787653e-05, "rounds": 1556, "median": 0.0004084115000040356, "iqr": 6.77675000133604e-05, "q1": 0.0003944184999937761, "q3": 0.0004621860000071365, "iqr_outliers": 168, "stddev_outliers": 214, "outliers": "214;168", "ld15iqr": 0.0003904719999923145, "hd15iqr": 0.0005640329999891946, "ops": 2233.556500702436, "total": 0.6966468049994035, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-100]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-100]", "params": {"numba": true, "log": true, "n": 100}, "param": "True-True-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00039467700000273, "max": 0.0010728569999969295, "mean": 0.0004754076685305402, "stddev": 0.00010500805596300418, "rounds": 1967, "median": 0.0004204650000190213, "iqr": 0.00010018425001590003, "q1": 0.0004143244999923468, "q3": 0.0005145087500082468, "iqr_outliers": 145, "stddev_outliers": 320, "outliers": "320;145", "ld15iqr": 0.00039467700000273, "hd15iqr": 0.0006664209999769355, "ops": 2103.457866152952, "total": 0.9351268839995726, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-316]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-316]", "params": {"numba": true, "log": true, "n": 316}, "param": "True-True-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004328759999907561, "max": 0.0010588549999965835, "mean": 0.0005392366092147661, "stddev": 0.00011845867843780345, "rounds": 1172, "median": 0.00047436049999305396, "iqr": 0.0001410179999936645, "q1": 0.00045806500000367123, "q3": 0.0005990829999973357, "iqr_outliers": 47, "stddev_outliers": 191, "outliers": "191;47", "ld15iqr": 0.0004328759999907561, "hd15iqr": 0.00081333899998981, "ops": 1854.4734962564867, "total": 0.6319853059997058, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-1000]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-1000]", "params": {"numba": true, "log": true, "n": 1000}, "param": "True-True-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00043979399998761437, "max": 0.0013313549999907082, "mean": 0.0005350957644579634, "stddev": 0.00013075402531963296, "rounds": 1660, "median": 0.00047016950000511315, "iqr": 0.00013194699998564374, "q1": 0.0004477095000083864, "q3": 0.0005796564999940301, "iqr_outliers": 105, "stddev_outliers": 242, "outliers": "242;105", "ld15iqr": 0.00043979399998761437, "hd15iqr": 0.0007777449999935016, "ops": 1868.8243608374873, "total": 0.8882589690002192, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-3162]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-3162]", "params": {"numba": true, "log": true, "n": 3162}, "param": "True-True-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00046590800002377364, "max": 0.0010717620000093575, "mean": 0.0005515500011639458, "stddev": 0.00011455680487290001, "rounds": 1719, "median": 0.0004965059999904042, "iqr": 0.00012479274998611345, "q1": 0.00047282225000344624, "q3": 0.0005976149999895597, "iqr_outliers": 94, "stddev_outliers": 270, "outliers": "270;94", "ld15iqr": 0.00046590800002377364, "hd15iqr": 0.000787972000011905, "ops": 1813.0722471030408, "total": 0.9481144520008229, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-10000]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-10000]", "params": {"numba": true, "log": true, "n": 10000}, "param": "True-True-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0007520370000122512, "max": 0.0018421880000119017, "mean": 0.0009149970662489864, "stddev": 0.0001907750093467647, "rounds": 1117, "median": 0.000823462999989033, "iqr": 0.0002045154999876786, "q1": 0.0007893734999910862, "q3": 0.0009938889999787648, "iqr_outliers": 71, "stddev_outliers": 159, "outliers": "159;71", "ld15iqr": 0.0007520370000122512, "hd15iqr": 0.001302224999989221, "ops": 1092.8996790115202, "total": 1.0220517230001178, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-31622]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-31622]", "params": {"numba": true, "log": true, "n": 31622}, "param": "True-True-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0021368330000086644, "max": 0.003999949000018432, "mean": 0.0026084402773717845, "stddev": 0.00037383043636695523, "rounds": 411, "median": 0.0025082939999947484, "iqr": 0.000464154500001257, "q1": 0.0023317954999839685, "q3": 0.0027959499999852255, "iqr_outliers": 11, "stddev_outliers": 128, "outliers": "128;11", "ld15iqr": 0.0021368330000086644, "hd15iqr": 0.0035078079999948386, "ops": 383.37086291566595, "total": 1.0720689539998034, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-100000]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-100000]", "params": {"numba": true, "log": true, "n": 100000}, "param": "True-True-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.004990885000012213, "max": 0.007896475999984887, "mean": 0.005972209641024269, "stddev": 0.0005900366215827988, "rounds": 156, "median": 0.005957894000005126, "iqr": 0.0007728875000054813, "q1": 0.005514118499988285, "q3": 0.006287005999993767, "iqr_outliers": 3, "stddev_outliers": 54, "outliers": "54;3", "ld15iqr": 0.004990885000012213, "hd15iqr": 0.007816135000012991, "ops": 167.44221320209618, "total": 0.9316647039997861, "iterations": 1}}, {"group": null, "name": "test_minuit_custom[True-True-316227]", "fullname": "bench/test_cost.py::test_minuit_custom[True-True-316227]", "params": {"numba": true, "log": true, "n": 316227}, "param": "True-True-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.01949758699998938, "max": 0.027939285999991625, "mean": 0.021289639764706506, "stddev": 0.0017427119903350153, "rounds": 51, "median": 0.020739848999994592, "iqr": 0.0015701012499675926, "q1": 0.020108637500015902, "q3": 0.021678738749983495, "iqr_outliers": 5, "stddev_outliers": 9, "outliers": "9;5", "ld15iqr": 0.01949758699998938, "hd15iqr": 0.02410944599998288, "ops": 46.971203414055786, "total": 1.0857716280000318, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[10]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[10]", "params": {"n": 10}, "param": "10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006447549999961666, "max": 0.0015930299999808994, "mean": 0.0007842420807876833, "stddev": 0.0001565645130149, "rounds": 1015, "median": 0.0006999699999994391, "iqr": 0.0001715167499725112, "q1": 0.0006756515000105878, "q3": 0.000847168249983099, "iqr_outliers": 61, "stddev_outliers": 147, "outliers": "147;61", "ld15iqr": 0.0006447549999961666, "hd15iqr": 0.0011045630000126039, "ops": 1275.1164780594431, "total": 0.7960057119994985, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[31]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[31]", "params": {"n": 31}, "param": "31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004511540000180503, "max": 0.001408961999999292, "mean": 0.0005259686660196399, "stddev": 0.00010568538228575217, "rounds": 1545, "median": 0.00047737200000597113, "iqr": 9.500899999892454e-05, "q1": 0.0004575827500090668, "q3": 0.0005525917500079913, "iqr_outliers": 135, "stddev_outliers": 227, "outliers": "227;135", "ld15iqr": 0.0004511540000180503, "hd15iqr": 0.0006951860000015131, "ops": 1901.2539426876422, "total": 0.8126215890003436, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[100]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[100]", "params": {"n": 100}, "param": "100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00045346900000708956, "max": 0.0013854369999819482, "mean": 0.000554866174465344, "stddev": 0.00013699137067027308, "rounds": 1731, "median": 0.00048286000000530294, "iqr": 0.00011051450000110208, "q1": 0.0004782202499953314, "q3": 0.0005887347499964335, "iqr_outliers": 179, "stddev_outliers": 264, "outliers": "264;179", "ld15iqr": 0.00045346900000708956, "hd15iqr": 0.0007564699999988989, "ops": 1802.2363698121922, "total": 0.9604733479995105, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[316]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[316]", "params": {"n": 316}, "param": "316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004975850000050741, "max": 0.0015226000000154727, "mean": 0.0005956076732426517, "stddev": 0.00014400037339921823, "rounds": 1622, "median": 0.0005278789999891842, "iqr": 0.00012384700002598947, "q1": 0.0005052779999914492, "q3": 0.0006291250000174387, "iqr_outliers": 144, "stddev_outliers": 219, "outliers": "219;144", "ld15iqr": 0.0004975850000050741, "hd15iqr": 0.0008150140000111605, "ops": 1678.9575502876341, "total": 0.966075645999581, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[1000]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[1000]", "params": {"n": 1000}, "param": "1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004520509999963451, "max": 0.0012797220000209109, "mean": 0.0005659766371160534, "stddev": 0.00014320245033526549, "rounds": 1692, "median": 0.0004979195000061054, "iqr": 0.000115224500021327, "q1": 0.00047633049999262767, "q3": 0.0005915550000139547, "iqr_outliers": 203, "stddev_outliers": 252, "outliers": "252;203", "ld15iqr": 0.0004520509999963451, "hd15iqr": 0.0007654270000045926, "ops": 1766.8573831872677, "total": 0.9576324700003624, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[3162]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[3162]", "params": {"n": 3162}, "param": "3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00040641699999355296, "max": 0.0014257770000085657, "mean": 0.000509190936292067, "stddev": 0.00014181213943027505, "rounds": 1915, "median": 0.00043356099999414255, "iqr": 0.00011474950000689432, "q1": 0.00042845974999039527, "q3": 0.0005432092499972896, "iqr_outliers": 186, "stddev_outliers": 275, "outliers": "275;186", "ld15iqr": 0.00040641699999355296, "hd15iqr": 0.0007153869999854123, "ops": 1963.8998433122338, "total": 0.9751006429993083, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[10000]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[10000]", "params": {"n": 10000}, "param": "10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00043939399998293993, "max": 0.0011787109999943368, "mean": 0.000532945294524735, "stddev": 0.00011164931986661854, "rounds": 1735, "median": 0.00048139100002231316, "iqr": 0.00010011325001357818, "q1": 0.00046285599998441285, "q3": 0.000562969249997991, "iqr_outliers": 167, "stddev_outliers": 290, "outliers": "290;167", "ld15iqr": 0.00043939399998293993, "hd15iqr": 0.0007132650000016838, "ops": 1876.3651922131537, "total": 0.924660086000415, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[31622]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[31622]", "params": {"n": 31622}, "param": "31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.000692094999976689, "max": 0.0016611030000035498, "mean": 0.0008298776604989758, "stddev": 0.0001619579016758851, "rounds": 1081, "median": 0.0007468939999739632, "iqr": 0.00018415425002871189, "q1": 0.0007206242499862014, "q3": 0.0009047785000149133, "iqr_outliers": 50, "stddev_outliers": 177, "outliers": "177;50", "ld15iqr": 0.000692094999976689, "hd15iqr": 0.0011827239999888661, "ops": 1204.996890022001, "total": 0.8970977509993929, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[100000]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[100000]", "params": {"n": 100000}, "param": "100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0009025119999819253, "max": 0.0018434059999776764, "mean": 0.001093183470524098, "stddev": 0.00018948897883235276, "rounds": 916, "median": 0.0010175925000055486, "iqr": 0.00024105599999302285, "q1": 0.0009530935000015006, "q3": 0.0011941494999945235, "iqr_outliers": 29, "stddev_outliers": 141, "outliers": "141;29", "ld15iqr": 0.0009025119999819253, "hd15iqr": 0.0015616629999897214, "ops": 914.7595321035878, "total": 1.0013560590000736, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_sum_logpdf_parallel_fastmath[316227]", "fullname": "bench/test_cost.py::test_minuit_numba_sum_logpdf_parallel_fastmath[316227]", "params": {"n": 316227}, "param": "316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0022540200000094046, "max": 0.004824810000002344, "mean": 0.0028190955069646068, "stddev": 0.00044183862244312174, "rounds": 359, "median": 0.0027121580000084577, "iqr": 0.0005951160000137179, "q1": 0.0024769562499855624, "q3": 0.0030720722499992803, "iqr_outliers": 10, "stddev_outliers": 96, "outliers": "96;10", "ld15iqr": 0.0022540200000094046, "hd15iqr": 0.00398796899997933, "ops": 354.72370394315794, "total": 1.012055287000294, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[10]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[10]", "params": {"n": 10}, "param": "10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006169539999802964, "max": 0.0013400910000029853, "mean": 0.0007354356790245874, "stddev": 0.00013036012440391771, "rounds": 1025, "median": 0.0006735409999976127, "iqr": 0.00014174899999375157, "q1": 0.0006452930000051538, "q3": 0.0007870419999989053, "iqr_outliers": 60, "stddev_outliers": 158, "outliers": "158;60", "ld15iqr": 0.0006169539999802964, "hd15iqr": 0.0010006649999922956, "ops": 1359.7382184752116, "total": 0.7538215710002021, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[31]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[31]", "params": {"n": 31}, "param": "31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00045015799997827344, "max": 0.0012866180000230543, "mean": 0.0005365436358944543, "stddev": 0.00012836141443097246, "rounds": 1744, "median": 0.00047820700000045235, "iqr": 0.00010994799998798044, "q1": 0.0004574595000121917, "q3": 0.0005674075000001721, "iqr_outliers": 136, "stddev_outliers": 221, "outliers": "221;136", "ld15iqr": 0.00045015799997827344, "hd15iqr": 0.0007330510000258528, "ops": 1863.7813089198849, "total": 0.9357321009999282, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[100]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[100]", "params": {"n": 100}, "param": "100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00046435999999516753, "max": 0.0014027260000091246, "mean": 0.0005565170250606008, "stddev": 0.00013389062863757158, "rounds": 1237, "median": 0.0004945350000014059, "iqr": 0.00011656950001537325, "q1": 0.0004753962499890463, "q3": 0.0005919657500044195, "iqr_outliers": 84, "stddev_outliers": 146, "outliers": "146;84", "ld15iqr": 0.00046435999999516753, "hd15iqr": 0.0007677450000187491, "ops": 1796.890220727941, "total": 0.6884115599999632, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[316]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[316]", "params": {"n": 316}, "param": "316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004767350000065562, "max": 0.0010502730000041538, "mean": 0.0005557864736837383, "stddev": 9.341290116534625e-05, "rounds": 1615, "median": 0.0005062649999842961, "iqr": 6.809850000877304e-05, "q1": 0.000500083499993309, "q3": 0.0005681820000020821, "iqr_outliers": 217, "stddev_outliers": 259, "outliers": "259;217", "ld15iqr": 0.0004767350000065562, "hd15iqr": 0.0006703820000097949, "ops": 1799.2521361162785, "total": 0.8975951549992374, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[1000]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[1000]", "params": {"n": 1000}, "param": "1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004586219999964669, "max": 0.001172613999983696, "mean": 0.0005524427758412109, "stddev": 0.00011277084009062057, "rounds": 1664, "median": 0.0005010604999853285, "iqr": 9.5973499981028e-05, "q1": 0.0004800764999970397, "q3": 0.0005760499999780677, "iqr_outliers": 179, "stddev_outliers": 269, "outliers": "269;179", "ld15iqr": 0.0004586219999964669, "hd15iqr": 0.0007208290000164652, "ops": 1810.1422332426894, "total": 0.919264778999775, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[3162]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[3162]", "params": {"n": 3162}, "param": "3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00038899599999808743, "max": 0.001106687999993028, "mean": 0.0004695797848955093, "stddev": 0.00010632279095631717, "rounds": 1920, "median": 0.000415307500006179, "iqr": 7.069250000313332e-05, "q1": 0.00040906750000146985, "q3": 0.00047976000000460317, "iqr_outliers": 260, "stddev_outliers": 275, "outliers": "275;260", "ld15iqr": 0.00038899599999808743, "hd15iqr": 0.0005861009999819089, "ops": 2129.563563351688, "total": 0.9015931869993778, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[10000]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[10000]", "params": {"n": 10000}, "param": "10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004541559999893252, "max": 0.001393481999997448, "mean": 0.000576431029412094, "stddev": 0.00016425272729781457, "rounds": 816, "median": 0.00048226399999862224, "iqr": 0.00015625100000704606, "q1": 0.00047502050000503004, "q3": 0.0006312715000120761, "iqr_outliers": 66, "stddev_outliers": 127, "outliers": "127;66", "ld15iqr": 0.0004541559999893252, "hd15iqr": 0.0008694260000083887, "ops": 1734.812924661441, "total": 0.4703677200002687, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[31622]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[31622]", "params": {"n": 31622}, "param": "31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006344500000068365, "max": 0.0016135670000210212, "mean": 0.0007772023883264803, "stddev": 0.00017813848871515344, "rounds": 1285, "median": 0.0006859529999871938, "iqr": 0.0001660530000222593, "q1": 0.0006688909999894577, "q3": 0.000834944000011717, "iqr_outliers": 110, "stddev_outliers": 185, "outliers": "185;110", "ld15iqr": 0.0006344500000068365, "hd15iqr": 0.0010847110000042903, "ops": 1286.6661438769652, "total": 0.9987050689995272, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[100000]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[100000]", "params": {"n": 100000}, "param": "100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0008010539999929733, "max": 0.0022210260000008475, "mean": 0.0010672760456803586, "stddev": 0.0002877242619050517, "rounds": 1007, "median": 0.0009236479999970015, "iqr": 0.0003022327500019628, "q1": 0.00088111500000565, "q3": 0.0011833477500076128, "iqr_outliers": 74, "stddev_outliers": 160, "outliers": "160;74", "ld15iqr": 0.0008010539999929733, "hd15iqr": 0.0016387120000160849, "ops": 936.9647187785687, "total": 1.074746978000121, "iterations": 1}}, {"group": null, "name": "test_minuit_numba_handtuned_parallel_fastmath[316227]", "fullname": "bench/test_cost.py::test_minuit_numba_handtuned_parallel_fastmath[316227]", "params": {"n": 316227}, "param": "316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0018499210000015864, "max": 0.004200620999995408, "mean": 0.0022775608804356355, "stddev": 0.00037805549420293413, "rounds": 460, "median": 0.002145488500005399, "iqr": 0.0002891255000037063, "q1": 0.002060043000000178, "q3": 0.0023491685000038842, "iqr_outliers": 36, "stddev_outliers": 55, "outliers": "55;36", "ld15iqr": 0.0018499210000015864, "hd15iqr": 0.0027852329999973335, "ops": 439.06619954269985, "total": 1.0476780050003924, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[10]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[10]", "params": {"n": 10}, "param": "10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00028420900000014626, "max": 0.0008445459999961713, "mean": 0.0003479935256570441, "stddev": 8.193483762795691e-05, "rounds": 2397, "median": 0.00031339099999172504, "iqr": 4.8590500014711324e-05, "q1": 0.0002999247500028446, "q3": 0.0003485152500175559, "iqr_outliers": 348, "stddev_outliers": 321, "outliers": "321;348", "ld15iqr": 0.00028420900000014626, "hd15iqr": 0.00042177200000992343, "ops": 2873.616680402048, "total": 0.8341404809999347, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[31]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[31]", "params": {"n": 31}, "param": "31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0002551640000092448, "max": 0.0010490439999841783, "mean": 0.0003081439760683613, "stddev": 7.846546720246586e-05, "rounds": 2340, "median": 0.0002789315000057968, "iqr": 3.9101999988133684e-05, "q1": 0.0002670349999931432, "q3": 0.00030613699998127686, "iqr_outliers": 340, "stddev_outliers": 287, "outliers": "287;340", "ld15iqr": 0.0002551640000092448, "hd15iqr": 0.0003652430000045115, "ops": 3245.23624559888, "total": 0.7210569039999655, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[100]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[100]", "params": {"n": 100}, "param": "100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.000267385000000786, "max": 0.0008799059999944348, "mean": 0.0003238323639140989, "stddev": 8.990359949664036e-05, "rounds": 2616, "median": 0.0002826215000055754, "iqr": 6.883249997713392e-05, "q1": 0.0002701845000103731, "q3": 0.000339016999987507, "iqr_outliers": 271, "stddev_outliers": 367, "outliers": "367;271", "ld15iqr": 0.000267385000000786, "hd15iqr": 0.00044231399999716814, "ops": 3088.017478899249, "total": 0.8471454639992828, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[316]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[316]", "params": {"n": 316}, "param": "316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0002733890000001793, "max": 0.0008168150000074093, "mean": 0.0003298011725664967, "stddev": 7.497849278677978e-05, "rounds": 2486, "median": 0.00029053299999759474, "iqr": 5.076999997299936e-05, "q1": 0.0002866750000123375, "q3": 0.00033744499998533684, "iqr_outliers": 324, "stddev_outliers": 355, "outliers": "355;324", "ld15iqr": 0.0002733890000001793, "hd15iqr": 0.0004142679999858956, "ops": 3032.12991093406, "total": 0.8198857150003107, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[1000]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[1000]", "params": {"n": 1000}, "param": "1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00030172299997843766, "max": 0.0008848319999970045, "mean": 0.0003798759886840202, "stddev": 9.481835510859718e-05, "rounds": 2386, "median": 0.00032567249999715386, "iqr": 0.00010039599999345228, "q1": 0.0003187549999950079, "q3": 0.00041915099998846017, "iqr_outliers": 154, "stddev_outliers": 376, "outliers": "376;154", "ld15iqr": 0.00030172299997843766, "hd15iqr": 0.000570160000023634, "ops": 2632.43803185412, "total": 0.9063841090000722, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[3162]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[3162]", "params": {"n": 3162}, "param": "3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.00035379999999918255, "max": 0.0008948260000067876, "mean": 0.0004093662588432514, "stddev": 8.74659813714349e-05, "rounds": 1781, "median": 0.0003644489999885536, "iqr": 6.840875002467328e-05, "q1": 0.00035715699998917216, "q3": 0.00042556575001384545, "iqr_outliers": 229, "stddev_outliers": 289, "outliers": "289;229", "ld15iqr": 0.00035379999999918255, "hd15iqr": 0.0005282380000153353, "ops": 2442.8002513585407, "total": 0.7290813069998308, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[10000]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[10000]", "params": {"n": 10000}, "param": "10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006233320000035292, "max": 0.0016100530000073832, "mean": 0.0007169718373211462, "stddev": 0.00014036103764686689, "rounds": 1254, "median": 0.0006401134999975966, "iqr": 0.00014293599997472484, "q1": 0.0006274890000099731, "q3": 0.0007704249999846979, "iqr_outliers": 97, "stddev_outliers": 214, "outliers": "214;97", "ld15iqr": 0.0006233320000035292, "hd15iqr": 0.000985245000009627, "ops": 1394.754923340287, "total": 0.8990826840007173, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[31622]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[31622]", "params": {"n": 31622}, "param": "31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0017984259999934693, "max": 0.0031719259999931637, "mean": 0.0022157871553666916, "stddev": 0.00030705215548166345, "rounds": 354, "median": 0.0021333315000049424, "iqr": 0.0004940940000039973, "q1": 0.0019429609999974673, "q3": 0.0024370550000014646, "iqr_outliers": 0, "stddev_outliers": 121, "outliers": "121;0", "ld15iqr": 0.0017984259999934693, "hd15iqr": 0.0031719259999931637, "ops": 451.30688549122385, "total": 0.7843886529998088, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[100000]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[100000]", "params": {"n": 100000}, "param": "100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.004520018999983222, "max": 0.008775570000011612, "mean": 0.005380861803278567, "stddev": 0.0006082198985246072, "rounds": 183, "median": 0.005286898999997902, "iqr": 0.0005016057499744875, "q1": 0.00504591650001629, "q3": 0.005547522249990777, "iqr_outliers": 10, "stddev_outliers": 30, "outliers": "30;10", "ld15iqr": 0.004520018999983222, "hd15iqr": 0.006321036999992202, "ops": 185.84383627743398, "total": 0.9846977099999776, "iterations": 1}}, {"group": null, "name": "test_minuit_cfunc_sum_logpdf[316227]", "fullname": "bench/test_cost.py::test_minuit_cfunc_sum_logpdf[316227]", "params": {"n": 316227}, "param": "316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.01902835599997843, "max": 0.023534320999999636, "mean": 0.02005255944680737, "stddev": 0.0008857244094260912, "rounds": 47, "median": 0.019740104000021574, "iqr": 0.0009997244999908617, "q1": 0.01945873849999913, "q3": 0.02045846299998999, "iqr_outliers": 2, "stddev_outliers": 10, "outliers": "10;2", "ld15iqr": 0.01902835599997843, "hd15iqr": 0.02198451499998555, "ops": 49.86894578982102, "total": 0.9424702939999463, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-10]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-10]", "params": {"log": false, "n": 10}, "param": "False-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0009500509999895712, "max": 0.0021751660000006723, "mean": 0.001163103485426069, "stddev": 0.0002163732143692876, "rounds": 892, "median": 0.001063496999975655, "iqr": 0.0002513774999783891, "q1": 0.0009969680000097014, "q3": 0.0012483454999880905, "iqr_outliers": 43, "stddev_outliers": 151, "outliers": "151;43", "ld15iqr": 0.0009500509999895712, "hd15iqr": 0.0016263640000033774, "ops": 859.768724391432, "total": 1.0374883090000537, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-31]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-31]", "params": {"log": false, "n": 31}, "param": "False-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006618170000081136, "max": 0.0013512219999824993, "mean": 0.0007760414820512503, "stddev": 0.00012780869805370098, "rounds": 1170, "median": 0.0007192424999971081, "iqr": 0.00013883399998348978, "q1": 0.0006903830000055677, "q3": 0.0008292169999890575, "iqr_outliers": 64, "stddev_outliers": 184, "outliers": "184;64", "ld15iqr": 0.0006618170000081136, "hd15iqr": 0.0010405230000003485, "ops": 1288.5909105744934, "total": 0.9079685339999628, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-100]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-100]", "params": {"log": false, "n": 100}, "param": "False-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006952620000220122, "max": 0.0014686420000202816, "mean": 0.0008397515719299828, "stddev": 0.00015518254979093076, "rounds": 855, "median": 0.0007648550000283194, "iqr": 0.00018832924998690714, "q1": 0.0007313414999998713, "q3": 0.0009196707499867784, "iqr_outliers": 30, "stddev_outliers": 160, "outliers": "160;30", "ld15iqr": 0.0006952620000220122, "hd15iqr": 0.001216068000019277, "ops": 1190.82837523212, "total": 0.7179875940001352, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-316]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-316]", "params": {"log": false, "n": 316}, "param": "False-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0008643740000024991, "max": 0.001995728999986568, "mean": 0.0010439097798508438, "stddev": 0.00020806135760371145, "rounds": 804, "median": 0.0009451905000048555, "iqr": 0.00019835750001107044, "q1": 0.0009114770000024919, "q3": 0.0011098345000135623, "iqr_outliers": 67, "stddev_outliers": 125, "outliers": "125;67", "ld15iqr": 0.0008643740000024991, "hd15iqr": 0.0014084320000051775, "ops": 957.9371889234358, "total": 0.8393034630000784, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-1000]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-1000]", "params": {"log": false, "n": 1000}, "param": "False-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0010225190000028306, "max": 0.0026591009999776816, "mean": 0.0012434683702419432, "stddev": 0.0002772054801049433, "rounds": 867, "median": 0.0011282440000002225, "iqr": 0.00020816350001950923, "q1": 0.0010788477500014437, "q3": 0.001287011250020953, "iqr_outliers": 97, "stddev_outliers": 111, "outliers": "111;97", "ld15iqr": 0.0010225190000028306, "hd15iqr": 0.0016033560000039415, "ops": 804.2022008211022, "total": 1.0780870769997648, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-3162]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-3162]", "params": {"log": false, "n": 3162}, "param": "False-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.001305465999990929, "max": 0.0028778389999786214, "mean": 0.0015743717098670453, "stddev": 0.00031583569333025095, "rounds": 679, "median": 0.0014391480000028878, "iqr": 0.000270756250003501, "q1": 0.0013738869999997405, "q3": 0.0016446432500032415, "iqr_outliers": 71, "stddev_outliers": 96, "outliers": "96;71", "ld15iqr": 0.001305465999990929, "hd15iqr": 0.0020530059999828154, "ops": 635.1740149627367, "total": 1.0689983909997238, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-10000]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-10000]", "params": {"log": false, "n": 10000}, "param": "False-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.003588719000021001, "max": 0.006967398000000458, "mean": 0.004346541895197051, "stddev": 0.0006198113371817329, "rounds": 229, "median": 0.004060890000005202, "iqr": 0.0009249262500006239, "q1": 0.003895881749997443, "q3": 0.004820807999998067, "iqr_outliers": 4, "stddev_outliers": 47, "outliers": "47;4", "ld15iqr": 0.003588719000021001, "hd15iqr": 0.006245973999995158, "ops": 230.06795381519382, "total": 0.9953580940001245, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-31622]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-31622]", "params": {"log": false, "n": 31622}, "param": "False-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.012513622000000169, "max": 0.01677481799998759, "mean": 0.014605974288135655, "stddev": 0.0009040166266761113, "rounds": 59, "median": 0.014678333000006205, "iqr": 0.0013619759999912162, "q1": 0.01391518524999924, "q3": 0.015277161249990456, "iqr_outliers": 0, "stddev_outliers": 19, "outliers": "19;0", "ld15iqr": 0.012513622000000169, "hd15iqr": 0.01677481799998759, "ops": 68.46513490115439, "total": 0.8617524830000036, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-100000]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-100000]", "params": {"log": false, "n": 100000}, "param": "False-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.032298643999979504, "max": 0.037699446000004855, "mean": 0.03393516844827728, "stddev": 0.0009654127119240813, "rounds": 29, "median": 0.03381888599997751, "iqr": 0.0006604002500054662, "q1": 0.03351382150000859, "q3": 0.034174221750014055, "iqr_outliers": 2, "stddev_outliers": 6, "outliers": "6;2", "ld15iqr": 0.03256087400001206, "hd15iqr": 0.037699446000004855, "ops": 29.46795450637479, "total": 0.984119885000041, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[False-316227]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[False-316227]", "params": {"log": false, "n": 316227}, "param": "False-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.12719934200001148, "max": 0.13136711900000364, "mean": 0.12872532262500158, "stddev": 0.0013783641940866545, "rounds": 8, "median": 0.12850404099999935, "iqr": 0.0015317520000053264, "q1": 0.12779113349999704, "q3": 0.12932288550000237, "iqr_outliers": 0, "stddev_outliers": 3, "outliers": "3;0", "ld15iqr": 0.12719934200001148, "hd15iqr": 0.13136711900000364, "ops": 7.768479267386787, "total": 1.0298025810000127, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-10]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-10]", "params": {"log": true, "n": 10}, "param": "True-10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0013268200000027264, "max": 0.0027703669999823433, "mean": 0.0015558359545456246, "stddev": 0.0002398404314984047, "rounds": 550, "median": 0.0014608724999902734, "iqr": 0.0002616170000067086, "q1": 0.0013864539999985936, "q3": 0.0016480710000053023, "iqr_outliers": 32, "stddev_outliers": 81, "outliers": "81;32", "ld15iqr": 0.0013268200000027264, "hd15iqr": 0.002041799000011224, "ops": 642.7412845668847, "total": 0.8557097750000935, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-31]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-31]", "params": {"log": true, "n": 31}, "param": "True-31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0008524710000017421, "max": 0.0023565159999918706, "mean": 0.001070813328124783, "stddev": 0.0002734924517447499, "rounds": 960, "median": 0.0009472470000133626, "iqr": 0.00023539750000622917, "q1": 0.0009057919999975184, "q3": 0.0011411895000037475, "iqr_outliers": 83, "stddev_outliers": 127, "outliers": "127;83", "ld15iqr": 0.0008524710000017421, "hd15iqr": 0.0014967170000090846, "ops": 933.8695865424164, "total": 1.0279807949997917, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-100]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-100]", "params": {"log": true, "n": 100}, "param": "True-100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0008614839999836477, "max": 0.002530946999996786, "mean": 0.0010834494570856042, "stddev": 0.00024397184166669071, "rounds": 1002, "median": 0.0009715845000073386, "iqr": 0.00025357599997732905, "q1": 0.0009158640000066498, "q3": 0.0011694399999839789, "iqr_outliers": 62, "stddev_outliers": 141, "outliers": "141;62", "ld15iqr": 0.0008614839999836477, "hd15iqr": 0.0015542489999802456, "ops": 922.9779879994801, "total": 1.0856163559997754, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-316]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-316]", "params": {"log": true, "n": 316}, "param": "True-316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0009960589999877811, "max": 0.003216543999997157, "mean": 0.001349540059023644, "stddev": 0.00036189012498504935, "rounds": 881, "median": 0.0012199869999847124, "iqr": 0.00043317675000054123, "q1": 0.0010779427499869598, "q3": 0.001511119499987501, "iqr_outliers": 34, "stddev_outliers": 123, "outliers": "123;34", "ld15iqr": 0.0009960589999877811, "hd15iqr": 0.0021940579999863985, "ops": 740.9931949137347, "total": 1.1889447919998304, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-1000]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-1000]", "params": {"log": true, "n": 1000}, "param": "True-1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0009383240000033766, "max": 0.0024742819999801213, "mean": 0.0012518140994157772, "stddev": 0.0003125229195674502, "rounds": 513, "median": 0.0011416589999839744, "iqr": 0.00033568399999239773, "q1": 0.0010188847500032239, "q3": 0.0013545687499956216, "iqr_outliers": 36, "stddev_outliers": 75, "outliers": "75;36", "ld15iqr": 0.0009383240000033766, "hd15iqr": 0.0018581129999972745, "ops": 798.8406589019096, "total": 0.6421806330002937, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-3162]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-3162]", "params": {"log": true, "n": 3162}, "param": "True-3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0007931719999874076, "max": 0.0022585739999954058, "mean": 0.0010533879853709153, "stddev": 0.00027790488354070586, "rounds": 957, "median": 0.0009487769999907414, "iqr": 0.00028883100001309003, "q1": 0.0008526302500015959, "q3": 0.001141461250014686, "iqr_outliers": 68, "stddev_outliers": 130, "outliers": "130;68", "ld15iqr": 0.0007931719999874076, "hd15iqr": 0.0015812840000251072, "ops": 949.3178333981886, "total": 1.008092301999966, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-10000]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-10000]", "params": {"log": true, "n": 10000}, "param": "True-10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0011688759999799458, "max": 0.002916024999990441, "mean": 0.001509200726087309, "stddev": 0.0003141009604528609, "rounds": 690, "median": 0.0014224404999936269, "iqr": 0.00037800399999809997, "q1": 0.0012661419999915324, "q3": 0.0016441459999896324, "iqr_outliers": 32, "stddev_outliers": 108, "outliers": "108;32", "ld15iqr": 0.0011688759999799458, "hd15iqr": 0.002214564000013297, "ops": 662.6023846361103, "total": 1.0413485010002432, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-31622]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-31622]", "params": {"log": true, "n": 31622}, "param": "True-31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0024861429999987195, "max": 0.004981973000013795, "mean": 0.0030995408549860473, "stddev": 0.0004135482973252876, "rounds": 331, "median": 0.0030435200000056284, "iqr": 0.0005594307500089712, "q1": 0.0027622864999941044, "q3": 0.0033217172500030756, "iqr_outliers": 4, "stddev_outliers": 110, "outliers": "110;4", "ld15iqr": 0.0024861429999987195, "hd15iqr": 0.004232782000002544, "ops": 322.6284300758157, "total": 1.0259480230003817, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-100000]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-100000]", "params": {"log": true, "n": 100000}, "param": "True-100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.004802079000000958, "max": 0.013973354000000882, "mean": 0.006184715731707305, "stddev": 0.00131114337082781, "rounds": 164, "median": 0.005878464999995003, "iqr": 0.001183939500009501, "q1": 0.005392125499994904, "q3": 0.006576065000004405, "iqr_outliers": 8, "stddev_outliers": 16, "outliers": "16;8", "ld15iqr": 0.004802079000000958, "hd15iqr": 0.00848663800002214, "ops": 161.6889188412137, "total": 1.014293379999998, "iterations": 1}}, {"group": null, "name": "test_minuit_UnbinnedNLL[True-316227]", "fullname": "bench/test_cost.py::test_minuit_UnbinnedNLL[True-316227]", "params": {"log": true, "n": 316227}, "param": "True-316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.02254614800000354, "max": 0.03322082799999748, "mean": 0.025007343810812387, "stddev": 0.002478186561594418, "rounds": 37, "median": 0.024372866999982534, "iqr": 0.0023638517500330636, "q1": 0.023315686249979706, "q3": 0.02567953800001277, "iqr_outliers": 3, "stddev_outliers": 4, "outliers": "4;3", "ld15iqr": 0.02254614800000354, "hd15iqr": 0.030401066000024457, "ops": 39.98825335330622, "total": 0.9252717210000583, "iterations": 1}}, {"group": null, "name": "test_RooFit[10]", "fullname": "bench/test_cost.py::test_RooFit[10]", "params": {"n": 10}, "param": "10", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006122679999975844, "max": 0.0009010500000101729, "mean": 0.0007050892857145234, "stddev": 0.00010758060558618865, "rounds": 14, "median": 0.0006504324999951905, "iqr": 0.0001380970000184334, "q1": 0.0006222669999829122, "q3": 0.0007603640000013456, "iqr_outliers": 0, "stddev_outliers": 3, "outliers": "3;0", "ld15iqr": 0.0006122679999975844, "hd15iqr": 0.0009010500000101729, "ops": 1418.2600987712071, "total": 0.009871250000003329, "iterations": 1}}, {"group": null, "name": "test_RooFit[31]", "fullname": "bench/test_cost.py::test_RooFit[31]", "params": {"n": 31}, "param": "31", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0004892839999968146, "max": 0.0015550309999809997, "mean": 0.0005778007936199647, "stddev": 0.00010770752459401756, "rounds": 1536, "median": 0.0005270945000006577, "iqr": 0.00010376050002491866, "q1": 0.0005090164999899116, "q3": 0.0006127770000148303, "iqr_outliers": 121, "stddev_outliers": 230, "outliers": "230;121", "ld15iqr": 0.0004892839999968146, "hd15iqr": 0.0007688059999964025, "ops": 1730.7002881303192, "total": 0.8875020190002658, "iterations": 1}}, {"group": null, "name": "test_RooFit[100]", "fullname": "bench/test_cost.py::test_RooFit[100]", "params": {"n": 100}, "param": "100", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0006665639999994255, "max": 0.0012955530000056115, "mean": 0.0007882812602365763, "stddev": 0.00012215875083451973, "rounds": 1099, "median": 0.0007301910000023781, "iqr": 0.00015063824998406972, "q1": 0.0007054642500179398, "q3": 0.0008561025000020095, "iqr_outliers": 41, "stddev_outliers": 184, "outliers": "184;41", "ld15iqr": 0.0006665639999994255, "hd15iqr": 0.0010834070000100837, "ops": 1268.5827387294269, "total": 0.8663211049999973, "iterations": 1}}, {"group": null, "name": "test_RooFit[316]", "fullname": "bench/test_cost.py::test_RooFit[316]", "params": {"n": 316}, "param": "316", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0011175200000081986, "max": 0.00218862200000558, "mean": 0.0012993575508343548, "stddev": 0.00017844516878629842, "rounds": 659, "median": 0.0012324679999835553, "iqr": 0.0001928562499955433, "q1": 0.0011757307499991043, "q3": 0.0013685869999946476, "iqr_outliers": 37, "stddev_outliers": 98, "outliers": "98;37", "ld15iqr": 0.0011175200000081986, "hd15iqr": 0.0016609360000074957, "ops": 769.6111046246442, "total": 0.8562766259998398, "iterations": 1}}, {"group": null, "name": "test_RooFit[1000]", "fullname": "bench/test_cost.py::test_RooFit[1000]", "params": {"n": 1000}, "param": "1000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0023462180000137778, "max": 0.0036021269999935157, "mean": 0.0027190710953850436, "stddev": 0.0002960786807391795, "rounds": 325, "median": 0.002624744999991435, "iqr": 0.00035847350000750566, "q1": 0.0025032794999972907, "q3": 0.0028617530000047964, "iqr_outliers": 12, "stddev_outliers": 93, "outliers": "93;12", "ld15iqr": 0.0023462180000137778, "hd15iqr": 0.0034125629999834928, "ops": 367.7726565139303, "total": 0.8836981060001392, "iterations": 1}}, {"group": null, "name": "test_RooFit[3162]", "fullname": "bench/test_cost.py::test_RooFit[3162]", "params": {"n": 3162}, "param": "3162", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0070529690000000755, "max": 0.009920332999996617, "mean": 0.008050764816514068, "stddev": 0.0005535654462759453, "rounds": 109, "median": 0.008048930999990489, "iqr": 0.000883497000010891, "q1": 0.007585992499997474, "q3": 0.008469489500008365, "iqr_outliers": 1, "stddev_outliers": 42, "outliers": "42;1", "ld15iqr": 0.0070529690000000755, "hd15iqr": 0.009920332999996617, "ops": 124.21180133703047, "total": 0.8775333650000334, "iterations": 1}}, {"group": null, "name": "test_RooFit[10000]", "fullname": "bench/test_cost.py::test_RooFit[10000]", "params": {"n": 10000}, "param": "10000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.0241574460000038, "max": 0.02698788999998669, "mean": 0.02565058709090677, "stddev": 0.0007058901130612385, "rounds": 33, "median": 0.025619065999990198, "iqr": 0.000895872749978821, "q1": 0.025234184500000367, "q3": 0.02613005724997919, "iqr_outliers": 0, "stddev_outliers": 12, "outliers": "12;0", "ld15iqr": 0.0241574460000038, "hd15iqr": 0.02698788999998669, "ops": 38.98546245573084, "total": 0.8464693739999234, "iterations": 1}}, {"group": null, "name": "test_RooFit[31622]", "fullname": "bench/test_cost.py::test_RooFit[31622]", "params": {"n": 31622}, "param": "31622", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.06533745100000488, "max": 0.07018297199999779, "mean": 0.06782916825000267, "stddev": 0.0017636218885062626, "rounds": 12, "median": 0.06757253799999319, "iqr": 0.0033303670000179864, "q1": 0.06616526499999509, "q3": 0.06949563200001307, "iqr_outliers": 0, "stddev_outliers": 5, "outliers": "5;0", "ld15iqr": 0.06533745100000488, "hd15iqr": 0.07018297199999779, "ops": 14.742919982657472, "total": 0.8139500190000319, "iterations": 1}}, {"group": null, "name": "test_RooFit[100000]", "fullname": "bench/test_cost.py::test_RooFit[100000]", "params": {"n": 100000}, "param": "100000", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.23750098700000422, "max": 0.2524962930000072, "mean": 0.24434892280000325, "stddev": 0.00610208358833709, "rounds": 5, "median": 0.24614085199999636, "iqr": 0.00932900350000665, "q1": 0.2386875890000013, "q3": 0.24801659250000796, "iqr_outliers": 0, "stddev_outliers": 2, "outliers": "2;0", "ld15iqr": 0.23750098700000422, "hd15iqr": 0.2524962930000072, "ops": 4.09250832187416, "total": 1.2217446140000163, "iterations": 1}}, {"group": null, "name": "test_RooFit[316227]", "fullname": "bench/test_cost.py::test_RooFit[316227]", "params": {"n": 316227}, "param": "316227", "extra_info": {}, "options": {"disable_gc": false, "timer": "perf_counter", "min_rounds": 5, "max_time": 1.0, "min_time": 5e-06, "warmup": false}, "stats": {"min": 0.7698972659999868, "max": 0.7902549499999907, "mean": 0.7843643233999898, "stddev": 0.008318801216263792, "rounds": 5, "median": 0.7876884430000075, "iqr": 0.008084058500003266, "q1": 0.7812198569999822, "q3": 0.7893039154999855, "iqr_outliers": 0, "stddev_outliers": 1, "outliers": "1;0", "ld15iqr": 0.7698972659999868, "hd15iqr": 0.7902549499999907, "ops": 1.2749177520788968, "total": 3.9218216169999494, "iterations": 1}}], "datetime": "2022-03-15T15:06:02.637206", "version": "3.4.1"}