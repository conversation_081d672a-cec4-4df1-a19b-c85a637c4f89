{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [".benchmarks/Darwin-CPython-3.11-64bit/0003_a7f9e06fd44a98dc1caf03c110caf57e8dbf69c3_20231221_103807_uncommited-changes.json\n", "benchmark results\n", "  2023-12-21T10:48:12.673733\n", "  Intel(R) Core(TM) i7-8569U CPU @ 2.80GHz\n", "\n", "UnbinnedNLL\n", "UnbinnedNLL_log\n", "nll_numba_stats\n", "nll_scipy.stats\n", "nll_numba\n", "minuit\n", "minuit_numba\n", "minuit_log\n", "minuit_log_numba\n", "minuit_parallel_fastmath\n", "minuit_parallel_fastmath_log\n", "minuit_cfunc\n", "minuit_UnbinnedNLL\n", "minuit_UnbinnedNLL_log\n", "RooFit_legacy\n", "RooFit_legacy-parallel\n", "RooFit_cpu\n", "RooFit_cpu-parallel\n", "RooFit_cpu-implicitmt\n", "RooFit_codegen\n", "RooFit_codegen_no_grad\n"]}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"469.19952pt\" height=\"353.99952pt\" viewBox=\"0 0 469.19952 353.99952\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T10:53:57.574600</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 353.99952 \n", "L 469.19952 353.99952 \n", "L 469.19952 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 51.**********.44327 \n", "L 461.99952 316.44327 \n", "L 461.99952 22.318125 \n", "L 51.378125 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m3751853e3d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3751853e3d\" x=\"70.042734\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(61.242734 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m3751853e3d\" x=\"152.99657\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(144.19657 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m3751853e3d\" x=\"235.950407\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(227.150407 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m3751853e3d\" x=\"318.904244\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(310.104244 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m3751853e3d\" x=\"401.85808\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- $\\mathdefault{10^{5}}$ -->\n", "      <g transform=\"translate(393.05808 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"md867d505bf\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"51.639529\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"57.193022\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"62.003676\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"66.246974\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"95.014327\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"109.621772\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"119.98592\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"128.024977\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"134.593366\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"140.146859\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"144.957513\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"149.200811\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"177.968163\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"192.575609\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"202.939757\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"210.978814\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"217.547202\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"223.100695\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"227.91135\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"232.154648\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"260.922\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"275.529446\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"285.893593\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"293.93265\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"300.501039\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"306.054532\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"310.865186\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"315.108484\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"343.875837\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"358.483282\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"368.84743\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_37\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"376.886487\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_38\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"383.454875\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_39\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"389.008368\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_40\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"393.819023\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_41\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"398.062321\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_42\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"426.829673\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_43\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"441.437119\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_44\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"451.801266\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_45\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#md867d505bf\" x=\"459.840324\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- number of data points -->\n", "     <g transform=\"translate(200.969291 344.719832) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6e\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"63.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"126.757812\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"224.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"287.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"349.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"390.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"422.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"483.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"518.457031\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"550.244141\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"613.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"675\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"714.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"775.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"807.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"870.751953\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"931.933594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"959.716797\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1023.095703\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1062.304688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_46\">\n", "      <defs>\n", "       <path id=\"m64bd2825ca\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m64bd2825ca\" x=\"51.378125\" y=\"292.035412\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{10^{-5}}$ -->\n", "      <g transform=\"translate(20.878125 295.834631) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" transform=\"translate(186.855469 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#m64bd2825ca\" x=\"51.378125\" y=\"213.737565\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{-4}}$ -->\n", "      <g transform=\"translate(20.878125 217.536783) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(186.855469 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m64bd2825ca\" x=\"51.378125\" y=\"135.439717\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{-3}}$ -->\n", "      <g transform=\"translate(20.878125 139.238936) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#m64bd2825ca\" x=\"51.378125\" y=\"57.14187\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{10^{-2}}$ -->\n", "      <g transform=\"translate(20.878125 60.941089) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_50\">\n", "      <defs>\n", "       <path id=\"me299a200fa\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"315.605413\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"309.405691\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"304.163902\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"299.623257\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"295.618125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"268.465411\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"254.677845\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"244.895411\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"237.307565\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_59\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"231.107844\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"225.866055\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_61\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"221.32541\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"217.320278\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_63\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"190.167564\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"176.379998\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_65\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"166.597563\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_66\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"159.009718\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_67\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"152.809997\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_68\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"147.568207\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_69\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"143.027563\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"139.02243\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_71\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"111.869717\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"98.08215\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_73\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"88.299716\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_74\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"80.711871\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_75\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"74.51215\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_31\">\n", "     <g id=\"line2d_76\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"69.27036\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_32\">\n", "     <g id=\"line2d_77\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"64.729716\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_33\">\n", "     <g id=\"line2d_78\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"60.724583\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_34\">\n", "     <g id=\"line2d_79\">\n", "      <g>\n", "       <use xlink:href=\"#me299a200fa\" x=\"51.378125\" y=\"33.57187\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- runtime / sec -->\n", "     <g transform=\"translate(14.798438 202.362729) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2f\" d=\"M 1625 4666 \n", "L 2156 4666 \n", "L 531 -594 \n", "L 0 -594 \n", "L 1625 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-72\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"41.113281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"104.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"167.871094\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"207.080078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"234.863281\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"332.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"393.798828\"/>\n", "      <use xlink:href=\"#DejaVuSans-2f\" x=\"425.585938\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"459.277344\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"491.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"543.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"604.6875\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 70.042734 192.527223 \n", "L 110.803072 192.213946 \n", "L 152.99657 191.416239 \n", "L 194.447531 188.463987 \n", "L 235.950407 185.719047 \n", "L 277.424162 173.951585 \n", "L 318.904244 146.468039 \n", "L 360.380277 120.127067 \n", "L 401.85808 82.826308 \n", "L 443.334911 35.68745 \n", "\" clip-path=\"url(#p1c4c23718c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 70.042734 303.073945 \n", "L 110.803072 299.369175 \n", "L 152.99657 292.767505 \n", "L 194.447531 280.89398 \n", "L 235.950407 258.412016 \n", "L 277.424162 225.722564 \n", "L 318.904244 186.795632 \n", "L 360.380277 148.584127 \n", "L 401.85808 108.658015 \n", "L 443.334911 66.052378 \n", "\" clip-path=\"url(#p1c4c23718c)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.**********.44327 \n", "L 51.378125 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 461.99952 316.44327 \n", "L 461.99952 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 51.**********.44327 \n", "L 461.99952 316.44327 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 51.378125 22.318125 \n", "L 461.99952 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- <PERSON><PERSON> vs. <PERSON><PERSON><PERSON><PERSON> -->\n", "    <g transform=\"translate(206.725697 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-4e\" d=\"M 628 4666 \n", "L 1478 4666 \n", "L 3547 763 \n", "L 3547 4666 \n", "L 4159 4666 \n", "L 4159 0 \n", "L 3309 0 \n", "L 1241 3903 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-4e\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"74.804688\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"138.183594\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"235.595703\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"299.072266\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"360.351562\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"392.138672\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"451.318359\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"503.417969\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"535.205078\"/>\n", "     <use xlink:href=\"#DejaVuSans-53\" x=\"566.992188\"/>\n", "     <use xlink:href=\"#DejaVuSans-63\" x=\"630.46875\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"685.449219\"/>\n", "     <use xlink:href=\"#DejaVuSans-50\" x=\"713.232422\"/>\n", "     <use xlink:href=\"#DejaVuSans-79\" x=\"773.535156\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"line2d_82\">\n", "     <path d=\"M 60.378125 35.416562 \n", "L 70.378125 35.416562 \n", "L 80.378125 35.416562 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- handwritten NLL scipy.stats -->\n", "     <g transform=\"translate(88.378125 38.916562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4c\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 531 \n", "L 3531 531 \n", "L 3531 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-68\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"188.037109\"/>\n", "      <use xlink:href=\"#DejaVuSans-77\" x=\"251.513672\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"333.300781\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"374.414062\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"402.197266\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"441.40625\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"480.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"542.138672\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"605.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-4e\" x=\"637.304688\"/>\n", "      <use xlink:href=\"#DejaVuSans-4c\" x=\"712.109375\"/>\n", "      <use xlink:href=\"#DejaVuSans-4c\" x=\"767.822266\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"823.535156\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"855.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"907.421875\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"962.402344\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"990.185547\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"1053.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"1098.591797\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1130.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1182.478516\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1221.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1282.966797\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1322.175781\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_83\">\n", "     <path d=\"M 60.378125 50.094687 \n", "L 70.378125 50.094687 \n", "L 80.378125 50.094687 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- handwritten NLL numba_stats -->\n", "     <g transform=\"translate(88.378125 53.594687) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-5f\" d=\"M 3263 -1063 \n", "L 3263 -1509 \n", "L -63 -1509 \n", "L -63 -1063 \n", "L 3263 -1063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-68\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"124.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"188.037109\"/>\n", "      <use xlink:href=\"#DejaVuSans-77\" x=\"251.513672\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"333.300781\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"374.414062\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"402.197266\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"441.40625\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"480.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"542.138672\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"605.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-4e\" x=\"637.304688\"/>\n", "      <use xlink:href=\"#DejaVuSans-4c\" x=\"712.109375\"/>\n", "      <use xlink:href=\"#DejaVuSans-4c\" x=\"767.822266\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"823.535156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"855.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"918.701172\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"982.080078\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"1079.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1142.96875\"/>\n", "      <use xlink:href=\"#DejaVuSans-5f\" x=\"1204.248047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1254.248047\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1306.347656\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1345.556641\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1406.835938\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1446.044922\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p1c4c23718c\">\n", "   <rect x=\"51.378125\" y=\"22.318125\" width=\"410.621395\" height=\"294.125145\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"469.19952pt\" height=\"353.99952pt\" viewBox=\"0 0 469.19952 353.99952\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T10:53:58.121446</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 353.99952 \n", "L 469.19952 353.99952 \n", "L 469.19952 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 51.**********.44327 \n", "L 461.99952 316.44327 \n", "L 461.99952 22.318125 \n", "L 51.378125 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mf958483f97\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf958483f97\" x=\"70.042734\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(61.242734 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mf958483f97\" x=\"152.99657\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(144.19657 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mf958483f97\" x=\"235.950407\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(227.150407 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mf958483f97\" x=\"318.904244\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(310.104244 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mf958483f97\" x=\"401.85808\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- $\\mathdefault{10^{5}}$ -->\n", "      <g transform=\"translate(393.05808 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m7caead6045\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"51.639529\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"57.193022\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"62.003676\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"66.246974\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"95.014327\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"109.621772\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"119.98592\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"128.024977\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"134.593366\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"140.146859\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"144.957513\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"149.200811\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"177.968163\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"192.575609\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"202.939757\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"210.978814\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"217.547202\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"223.100695\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"227.91135\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"232.154648\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"260.922\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"275.529446\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"285.893593\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"293.93265\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"300.501039\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"306.054532\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"310.865186\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"315.108484\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"343.875837\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"358.483282\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"368.84743\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_37\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"376.886487\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_38\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"383.454875\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_39\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"389.008368\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_40\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"393.819023\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_41\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"398.062321\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_42\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"426.829673\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_43\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"441.437119\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_44\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"451.801266\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_45\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#m7caead6045\" x=\"459.840324\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- number of data points -->\n", "     <g transform=\"translate(200.969291 344.719832) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6e\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"63.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"126.757812\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"224.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"287.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"349.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"390.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"422.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"483.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"518.457031\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"550.244141\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"613.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"675\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"714.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"775.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"807.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"870.751953\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"931.933594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"959.716797\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1023.095703\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1062.304688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_46\">\n", "      <defs>\n", "       <path id=\"m778f6ef58e\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m778f6ef58e\" x=\"51.378125\" y=\"250.306654\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{10^{-3}}$ -->\n", "      <g transform=\"translate(20.878125 254.105873) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#m778f6ef58e\" x=\"51.378125\" y=\"172.123588\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{-2}}$ -->\n", "      <g transform=\"translate(20.878125 175.922807) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m778f6ef58e\" x=\"51.378125\" y=\"93.940522\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{-1}}$ -->\n", "      <g transform=\"translate(20.878125 97.739741) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.855469 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_49\">\n", "      <defs>\n", "       <path id=\"m080e2734dc\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"304.954272\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"291.186918\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"281.418824\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"273.842102\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"267.65147\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"262.417364\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"257.883376\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"253.884115\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"226.771206\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"213.003852\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_59\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"203.235758\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"195.659036\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_61\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"189.468404\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"184.234298\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_63\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"179.70031\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"175.701049\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_65\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"148.58814\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_66\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"134.820785\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_67\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"125.052692\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_68\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"117.47597\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_69\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"111.285337\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"106.051232\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_71\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"101.517244\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"97.517983\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_73\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"70.405074\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_74\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"56.637719\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_75\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"46.869626\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_31\">\n", "     <g id=\"line2d_76\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"39.292904\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_32\">\n", "     <g id=\"line2d_77\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"33.102271\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_33\">\n", "     <g id=\"line2d_78\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"27.868166\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_34\">\n", "     <g id=\"line2d_79\">\n", "      <g>\n", "       <use xlink:href=\"#m080e2734dc\" x=\"51.378125\" y=\"23.334178\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- runtime / sec -->\n", "     <g transform=\"translate(14.798438 202.362729) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2f\" d=\"M 1625 4666 \n", "L 2156 4666 \n", "L 531 -594 \n", "L 0 -594 \n", "L 1625 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-72\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"41.113281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"104.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"167.871094\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"207.080078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"234.863281\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"332.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"393.798828\"/>\n", "      <use xlink:href=\"#DejaVuSans-2f\" x=\"425.585938\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"459.277344\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"491.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"543.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"604.6875\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_80\">\n", "    <path d=\"M 70.042734 265.239687 \n", "L 110.803072 262.295493 \n", "L 152.99657 262.014637 \n", "L 194.447531 253.169108 \n", "L 235.950407 233.846805 \n", "L 277.424162 196.890082 \n", "L 318.904244 158.459522 \n", "L 360.380277 119.303967 \n", "L 401.85808 78.339784 \n", "L 443.334911 35.68745 \n", "\" clip-path=\"url(#p5b071f147f)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_81\">\n", "    <path d=\"M 70.042734 278.780667 \n", "L 110.803072 274.531818 \n", "L 152.99657 271.605135 \n", "L 194.447531 260.387296 \n", "L 235.950407 237.567669 \n", "L 277.424162 198.92766 \n", "L 318.904244 160.155358 \n", "L 360.380277 119.489801 \n", "L 401.85808 79.716333 \n", "L 443.334911 39.008912 \n", "\" clip-path=\"url(#p5b071f147f)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 70.042734 303.073945 \n", "L 110.803072 292.84633 \n", "L 152.99657 284.138082 \n", "L 194.447531 266.458737 \n", "L 235.950407 241.447379 \n", "L 277.424162 201.611774 \n", "L 318.904244 160.204858 \n", "L 360.380277 119.814634 \n", "L 401.85808 77.475611 \n", "L 443.334911 38.377098 \n", "\" clip-path=\"url(#p5b071f147f)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.**********.44327 \n", "L 51.378125 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 461.99952 316.44327 \n", "L 461.99952 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 51.**********.44327 \n", "L 461.99952 316.44327 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 51.378125 22.318125 \n", "L 461.99952 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- Overhead -->\n", "    <g transform=\"translate(227.380698 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-4f\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1225 4090 567 \n", "Q 3503 -91 2522 -91 \n", "Q 1538 -91 948 565 \n", "Q 359 1222 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-4f\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"78.710938\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"137.890625\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"199.414062\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"238.777344\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"302.15625\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"363.679688\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"424.958984\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"line2d_83\">\n", "     <path d=\"M 60.378125 35.416562 \n", "L 70.378125 35.416562 \n", "L 80.378125 35.416562 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- iminuit+UnbinnedNLL -->\n", "     <g transform=\"translate(88.378125 38.916562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2b\" d=\"M 2944 4013 \n", "L 2944 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 2944 1741 \n", "L 2944 0 \n", "L 2419 0 \n", "L 2419 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "L 2419 2272 \n", "L 2419 4013 \n", "L 2944 4013 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-55\" d=\"M 556 4666 \n", "L 1191 4666 \n", "L 1191 1831 \n", "Q 1191 1081 1462 751 \n", "Q 1734 422 2344 422 \n", "Q 2950 422 3222 751 \n", "Q 3494 1081 3494 1831 \n", "L 3494 4666 \n", "L 4128 4666 \n", "L 4128 1753 \n", "Q 4128 841 3676 375 \n", "Q 3225 -91 2344 -91 \n", "Q 1459 -91 1007 375 \n", "Q 556 841 556 1753 \n", "L 556 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4e\" d=\"M 628 4666 \n", "L 1478 4666 \n", "L 3547 763 \n", "L 3547 4666 \n", "L 4159 4666 \n", "L 4159 0 \n", "L 3309 0 \n", "L 1241 3903 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4c\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 531 \n", "L 3531 531 \n", "L 3531 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-69\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"125.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"152.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"216.357422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"279.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.519531\"/>\n", "      <use xlink:href=\"#DejaVuSans-2b\" x=\"346.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-55\" x=\"430.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"503.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"567.089844\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"630.566406\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"658.349609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"721.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"785.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"846.630859\"/>\n", "      <use xlink:href=\"#DejaVuSans-4e\" x=\"910.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-4c\" x=\"984.912109\"/>\n", "      <use xlink:href=\"#DejaVuSans-4c\" x=\"1040.625\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_84\">\n", "     <path d=\"M 60.378125 50.094687 \n", "L 70.378125 50.094687 \n", "L 80.378125 50.094687 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- iminuit+numba -->\n", "     <g transform=\"translate(88.378125 53.594687) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-69\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"125.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"152.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"216.357422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"279.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.519531\"/>\n", "      <use xlink:href=\"#DejaVuSans-2b\" x=\"346.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"430.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"493.896484\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"557.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"654.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"718.164062\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_85\">\n", "     <path d=\"M 218.0125 35.416562 \n", "L 228.0125 35.416562 \n", "L 238.0125 35.416562 \n", "\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- iminuit+numba.cfunc -->\n", "     <g transform=\"translate(246.0125 38.916562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-69\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"125.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"152.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"216.357422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"279.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.519531\"/>\n", "      <use xlink:href=\"#DejaVuSans-2b\" x=\"346.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"430.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"493.896484\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"557.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"654.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"718.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"779.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"811.230469\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"866.210938\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"901.416016\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"964.794922\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"1028.173828\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p5b071f147f\">\n", "   <rect x=\"51.378125\" y=\"22.318125\" width=\"410.621395\" height=\"294.125145\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"469.19952pt\" height=\"353.99952pt\" viewBox=\"0 0 469.19952 353.99952\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T10:53:58.788049</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 353.99952 \n", "L 469.19952 353.99952 \n", "L 469.19952 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 51.**********.44327 \n", "L 461.99952 316.44327 \n", "L 461.99952 22.318125 \n", "L 51.378125 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m8c9a763743\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8c9a763743\" x=\"70.042734\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(61.242734 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m8c9a763743\" x=\"152.99657\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(144.19657 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m8c9a763743\" x=\"235.950407\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(227.150407 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m8c9a763743\" x=\"318.904244\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(310.104244 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m8c9a763743\" x=\"401.85808\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- $\\mathdefault{10^{5}}$ -->\n", "      <g transform=\"translate(393.05808 331.041707) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m5004dceba4\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"51.639529\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"57.193022\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"62.003676\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"66.246974\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"95.014327\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"109.621772\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"119.98592\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"128.024977\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"134.593366\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"140.146859\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"144.957513\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"149.200811\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"177.968163\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"192.575609\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"202.939757\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"210.978814\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"217.547202\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"223.100695\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"227.91135\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"232.154648\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"260.922\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"275.529446\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"285.893593\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"293.93265\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"300.501039\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"306.054532\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"310.865186\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"315.108484\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"343.875837\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"358.483282\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"368.84743\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_37\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"376.886487\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_38\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"383.454875\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_39\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"389.008368\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_40\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"393.819023\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_41\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"398.062321\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_42\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"426.829673\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_43\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"441.437119\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_44\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"451.801266\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_45\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#m5004dceba4\" x=\"459.840324\" y=\"316.44327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- number of data points -->\n", "     <g transform=\"translate(200.969291 344.719832) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6e\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"63.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"126.757812\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"224.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"287.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"349.169922\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"390.283203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"422.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"483.251953\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"518.457031\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"550.244141\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"613.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"675\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"714.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"775.488281\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"807.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"870.751953\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"931.933594\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"959.716797\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1023.095703\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1062.304688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_46\">\n", "      <defs>\n", "       <path id=\"m5023f058b7\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5023f058b7\" x=\"51.378125\" y=\"291.647699\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- $\\mathdefault{10^{-3}}$ -->\n", "      <g transform=\"translate(20.878125 295.446918) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#m5023f058b7\" x=\"51.378125\" y=\"217.732692\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{-2}}$ -->\n", "      <g transform=\"translate(20.878125 221.531911) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(186.855469 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m5023f058b7\" x=\"51.378125\" y=\"143.817686\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{-1}}$ -->\n", "      <g transform=\"translate(20.878125 147.616904) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.855469 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#m5023f058b7\" x=\"51.378125\" y=\"69.902679\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(26.778125 73.701898) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_50\">\n", "      <defs>\n", "       <path id=\"m4e646e552f\" d=\"M 0 0 \n", "L -2 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"313.898333\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"308.045651\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"303.097279\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"298.810804\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"295.029865\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"269.397065\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"256.381278\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"247.146431\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"239.983327\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_59\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"234.130644\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"229.182272\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_61\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"224.895797\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"221.114858\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_63\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"195.482058\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"182.466272\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_65\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"173.231424\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_66\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"166.06832\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_67\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"160.215637\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_68\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"155.267265\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_69\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"150.98079\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"147.199851\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_71\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"121.567051\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"108.551265\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_73\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"99.316417\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_74\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"92.153313\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_75\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"86.300631\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_31\">\n", "     <g id=\"line2d_76\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"81.352258\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_32\">\n", "     <g id=\"line2d_77\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"77.065783\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_33\">\n", "     <g id=\"line2d_78\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"73.284844\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_34\">\n", "     <g id=\"line2d_79\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"47.652045\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_35\">\n", "     <g id=\"line2d_80\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"34.636258\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_36\">\n", "     <g id=\"line2d_81\">\n", "      <g>\n", "       <use xlink:href=\"#m4e646e552f\" x=\"51.378125\" y=\"25.40141\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_11\">\n", "     <!-- runtime / sec -->\n", "     <g transform=\"translate(14.798438 202.362729) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2f\" d=\"M 1625 4666 \n", "L 2156 4666 \n", "L 531 -594 \n", "L 0 -594 \n", "L 1625 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-72\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"41.113281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"104.492188\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"167.871094\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"207.080078\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"234.863281\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"332.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"393.798828\"/>\n", "      <use xlink:href=\"#DejaVuSans-2f\" x=\"425.585938\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"459.277344\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"491.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"543.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"604.6875\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_82\">\n", "    <path d=\"M 70.042734 303.073945 \n", "L 110.803072 299.006129 \n", "L 152.99657 279.382076 \n", "L 194.447531 253.341617 \n", "L 235.950407 216.682128 \n", "L 277.424162 181.564536 \n", "L 318.904244 144.958969 \n", "L 360.380277 110.261712 \n", "L 401.85808 68.308685 \n", "L 443.334911 35.68745 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_83\">\n", "    <path d=\"M 70.042734 101.656085 \n", "L 110.803072 106.630249 \n", "L 152.99657 106.786371 \n", "L 194.447531 107.840311 \n", "L 235.950407 107.694455 \n", "L 277.424162 109.393092 \n", "L 318.904244 109.040769 \n", "L 360.380277 105.038044 \n", "L 401.85808 86.580726 \n", "L 443.334911 64.118533 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_84\">\n", "    <path d=\"M 70.042734 279.342625 \n", "L 110.803072 298.051167 \n", "L 152.99657 298.898398 \n", "L 194.447531 291.808574 \n", "L 235.950407 269.387228 \n", "L 277.424162 245.202797 \n", "L 318.904244 209.064437 \n", "L 360.380277 167.980904 \n", "L 401.85808 135.426533 \n", "L 443.334911 94.297738 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_85\">\n", "    <path d=\"M 70.042734 302.188689 \n", "L 110.803072 297.840988 \n", "L 152.99657 301.501561 \n", "L 194.447531 290.090176 \n", "L 235.950407 272.942091 \n", "L 277.424162 241.401172 \n", "L 318.904244 204.991996 \n", "L 360.380277 168.856576 \n", "L 401.85808 132.496727 \n", "L 443.334911 94.128285 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_86\">\n", "    <path d=\"M 70.042734 156.753813 \n", "L 110.803072 157.203667 \n", "L 152.99657 152.828878 \n", "L 194.447531 154.883443 \n", "L 235.950407 153.93712 \n", "L 277.424162 151.014079 \n", "L 318.904244 140.394867 \n", "L 360.380277 117.028486 \n", "L 401.85808 86.292823 \n", "L 443.334911 51.016319 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <path d=\"M 70.042734 217.128154 \n", "L 110.803072 215.894742 \n", "L 152.99657 216.085062 \n", "L 194.447531 213.592707 \n", "L 235.950407 209.178305 \n", "L 277.424162 196.950845 \n", "L 318.904244 173.589163 \n", "L 360.380277 146.816331 \n", "L 401.85808 106.275445 \n", "L 443.334911 74.589522 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <path d=\"M 70.042734 283.884156 \n", "L 110.803072 286.224091 \n", "L 152.99657 288.487811 \n", "L 194.447531 284.513345 \n", "L 235.950407 271.570597 \n", "L 277.424162 240.132234 \n", "L 318.904244 205.001963 \n", "L 360.380277 168.608724 \n", "L 401.85808 130.828547 \n", "L 443.334911 91.871275 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke: #e377c2; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_89\">\n", "    <path d=\"M 70.042734 227.4555 \n", "L 110.803072 233.528728 \n", "L 152.99657 245.10517 \n", "L 194.447531 254.479356 \n", "L 235.950407 250.907194 \n", "L 277.424162 236.409797 \n", "L 318.904244 216.196117 \n", "L 360.380277 197.079695 \n", "L 401.85808 160.53466 \n", "L 443.334911 131.787912 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #7f7f7f; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_90\">\n", "    <path d=\"M 70.042734 217.615738 \n", "L 110.803072 223.732803 \n", "L 152.99657 235.378547 \n", "L 194.447531 241.585321 \n", "L 235.950407 246.050955 \n", "L 277.424162 232.769287 \n", "L 318.904244 216.706599 \n", "L 360.380277 192.302318 \n", "L 401.85808 162.369036 \n", "L 443.334911 122.76283 \n", "\" clip-path=\"url(#pc2c51d252e)\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #bcbd22; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 51.**********.44327 \n", "L 51.378125 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 461.99952 316.44327 \n", "L 461.99952 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 51.**********.44327 \n", "L 461.99952 316.44327 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 51.378125 22.318125 \n", "L 461.99952 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- <PERSON><PERSON><PERSON><PERSON> vs. iminuit+numba -->\n", "    <g transform=\"translate(179.249448 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-52\" d=\"M 2841 2188 \n", "Q 3044 2119 3236 1894 \n", "Q 3428 1669 3622 1275 \n", "L 4263 0 \n", "L 3584 0 \n", "L 2988 1197 \n", "Q 2756 1666 2539 1819 \n", "Q 2322 1972 1947 1972 \n", "L 1259 1972 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "L 2053 4666 \n", "Q 2853 4666 3247 4331 \n", "Q 3641 3997 3641 3322 \n", "Q 3641 2881 3436 2590 \n", "Q 3231 2300 2841 2188 \n", "z\n", "M 1259 4147 \n", "L 1259 2491 \n", "L 2053 2491 \n", "Q 2509 2491 2742 2702 \n", "Q 2975 2913 2975 3322 \n", "Q 2975 3731 2742 3939 \n", "Q 2509 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-46\" d=\"M 628 4666 \n", "L 3309 4666 \n", "L 3309 4134 \n", "L 1259 4134 \n", "L 1259 2759 \n", "L 3109 2759 \n", "L 3109 2228 \n", "L 1259 2228 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2b\" d=\"M 2944 4013 \n", "L 2944 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 2944 1741 \n", "L 2944 0 \n", "L 2419 0 \n", "L 2419 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "L 2419 2272 \n", "L 2419 4013 \n", "L 2944 4013 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-52\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"126.164062\"/>\n", "     <use xlink:href=\"#DejaVuSans-46\" x=\"187.345703\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"237.615234\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"265.398438\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"304.607422\"/>\n", "     <use xlink:href=\"#DejaVuSans-76\" x=\"336.394531\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"395.574219\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"447.673828\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"479.460938\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"511.248047\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"539.03125\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"636.443359\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"664.226562\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"727.605469\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"790.984375\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"818.767578\"/>\n", "     <use xlink:href=\"#DejaVuSans-2b\" x=\"857.976562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"941.765625\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"1005.144531\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"1068.523438\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"1165.935547\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"1229.412109\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"line2d_91\">\n", "     <path d=\"M 58.875125 33.229123 \n", "L 67.205125 33.229123 \n", "L 75.535125 33.229123 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- Roo<PERSON><PERSON> [legacy] -->\n", "     <g transform=\"translate(82.199125 36.144623) scale(0.0833 -0.0833)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-5b\" d=\"M 550 4863 \n", "L 1875 4863 \n", "L 1875 4416 \n", "L 1125 4416 \n", "L 1125 -397 \n", "L 1875 -397 \n", "L 1875 -844 \n", "L 550 -844 \n", "L 550 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-5d\" d=\"M 1947 4863 \n", "L 1947 -844 \n", "L 622 -844 \n", "L 622 -397 \n", "L 1369 -397 \n", "L 1369 4416 \n", "L 622 4416 \n", "L 622 4863 \n", "L 1947 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-46\" x=\"187.345703\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"237.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"265.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"304.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"336.394531\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"375.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"403.191406\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"464.714844\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"528.191406\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"589.470703\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"644.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"703.630859\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_92\">\n", "     <path d=\"M 58.875125 45.456002 \n", "L 67.205125 45.456002 \n", "L 75.535125 45.456002 \n", "\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- RooFit [legacy, parallel] -->\n", "     <g transform=\"translate(82.199125 48.371502) scale(0.0833 -0.0833)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2c\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 256 \n", "L 897 -744 \n", "L 494 -744 \n", "L 750 256 \n", "L 750 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-46\" x=\"187.345703\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"237.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"265.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"304.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"336.394531\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"375.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"403.191406\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"464.714844\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"528.191406\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"589.470703\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"644.451172\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"703.630859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"735.417969\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"767.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"830.681641\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"891.960938\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"933.074219\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"994.353516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"1022.136719\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1049.919922\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"1111.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"1139.226562\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_93\">\n", "     <path d=\"M 58.875125 57.68288 \n", "L 67.205125 57.68288 \n", "L 75.535125 57.68288 \n", "\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- Roo<PERSON><PERSON> [CPU] -->\n", "     <g transform=\"translate(82.199125 60.59838) scale(0.0833 -0.0833)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-50\" d=\"M 1259 4147 \n", "L 1259 2394 \n", "L 2053 2394 \n", "Q 2494 2394 2734 2622 \n", "Q 2975 2850 2975 3272 \n", "Q 2975 3691 2734 3919 \n", "Q 2494 4147 2053 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2053 4666 \n", "Q 2838 4666 3239 4311 \n", "Q 3641 3956 3641 3272 \n", "Q 3641 2581 3239 2228 \n", "Q 2838 1875 2053 1875 \n", "L 1259 1875 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-55\" d=\"M 556 4666 \n", "L 1191 4666 \n", "L 1191 1831 \n", "Q 1191 1081 1462 751 \n", "Q 1734 422 2344 422 \n", "Q 2950 422 3222 751 \n", "Q 3494 1081 3494 1831 \n", "L 3494 4666 \n", "L 4128 4666 \n", "L 4128 1753 \n", "Q 4128 841 3676 375 \n", "Q 3225 -91 2344 -91 \n", "Q 1459 -91 1007 375 \n", "Q 556 841 556 1753 \n", "L 556 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-46\" x=\"187.345703\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"237.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"265.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"304.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"336.394531\"/>\n", "      <use xlink:href=\"#DejaVuSans-43\" x=\"375.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-50\" x=\"445.232422\"/>\n", "      <use xlink:href=\"#DejaVuSans-55\" x=\"505.535156\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"578.728516\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_94\">\n", "     <path d=\"M 58.875125 69.909758 \n", "L 67.205125 69.909758 \n", "L 75.535125 69.909758 \n", "\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- RooFit [CPU, ImplicitMT] -->\n", "     <g transform=\"translate(82.199125 72.825258) scale(0.0833 -0.0833)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4d\" d=\"M 628 4666 \n", "L 1569 4666 \n", "L 2759 1491 \n", "L 3956 4666 \n", "L 4897 4666 \n", "L 4897 0 \n", "L 4281 0 \n", "L 4281 4097 \n", "L 3078 897 \n", "L 2444 897 \n", "L 1241 4097 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-46\" x=\"187.345703\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"237.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"265.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"304.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"336.394531\"/>\n", "      <use xlink:href=\"#DejaVuSans-43\" x=\"375.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-50\" x=\"445.232422\"/>\n", "      <use xlink:href=\"#DejaVuSans-55\" x=\"505.535156\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"578.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"610.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-49\" x=\"642.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"671.794922\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"769.207031\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"832.683594\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"860.466797\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"888.25\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"943.230469\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"971.013672\"/>\n", "      <use xlink:href=\"#DejaVuSans-4d\" x=\"1010.222656\"/>\n", "      <use xlink:href=\"#DejaVuSans-54\" x=\"1096.501953\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"1157.585938\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_95\">\n", "     <path d=\"M 58.875125 82.136636 \n", "L 67.205125 82.136636 \n", "L 75.535125 82.136636 \n", "\" style=\"fill: none; stroke: #9467bd; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- Roo<PERSON><PERSON> [Codegen] -->\n", "     <g transform=\"translate(82.199125 85.052136) scale(0.0833 -0.0833)\">\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-46\" x=\"187.345703\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"237.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"265.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"304.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"336.394531\"/>\n", "      <use xlink:href=\"#DejaVuSans-43\" x=\"375.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"445.232422\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"506.414062\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"569.890625\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"631.414062\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"694.890625\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"756.414062\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"819.792969\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_96\">\n", "     <path d=\"M 198.534083 33.229123 \n", "L 206.864083 33.229123 \n", "L 215.194083 33.229123 \n", "\" style=\"fill: none; stroke: #8c564b; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- Roo<PERSON>it [CodegenNoGrad] -->\n", "     <g transform=\"translate(221.858083 36.144623) scale(0.0833 -0.0833)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4e\" d=\"M 628 4666 \n", "L 1478 4666 \n", "L 3547 763 \n", "L 3547 4666 \n", "L 4159 4666 \n", "L 4159 0 \n", "L 3309 0 \n", "L 1241 3903 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-47\" d=\"M 3809 666 \n", "L 3809 1919 \n", "L 2778 1919 \n", "L 2778 2438 \n", "L 4434 2438 \n", "L 4434 434 \n", "Q 4069 175 3628 42 \n", "Q 3188 -91 2688 -91 \n", "Q 1594 -91 976 548 \n", "Q 359 1188 359 2328 \n", "Q 359 3472 976 4111 \n", "Q 1594 4750 2688 4750 \n", "Q 3144 4750 3555 4637 \n", "Q 3966 4525 4313 4306 \n", "L 4313 3634 \n", "Q 3963 3931 3569 4081 \n", "Q 3175 4231 2741 4231 \n", "Q 1884 4231 1454 3753 \n", "Q 1025 3275 1025 2328 \n", "Q 1025 1384 1454 906 \n", "Q 1884 428 2741 428 \n", "Q 3075 428 3337 486 \n", "Q 3600 544 3809 666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-52\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"64.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"126.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-46\" x=\"187.345703\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"237.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"265.398438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"304.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"336.394531\"/>\n", "      <use xlink:href=\"#DejaVuSans-43\" x=\"375.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"445.232422\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"506.414062\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"569.890625\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"631.414062\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"694.890625\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"756.414062\"/>\n", "      <use xlink:href=\"#DejaVuSans-4e\" x=\"819.792969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"894.597656\"/>\n", "      <use xlink:href=\"#DejaVuSans-47\" x=\"955.779297\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"1033.269531\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1074.382812\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"1135.662109\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"1199.138672\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_97\">\n", "     <path d=\"M 198.534083 45.456002 \n", "L 206.864083 45.456002 \n", "L 215.194083 45.456002 \n", "\" style=\"fill: none; stroke: #e377c2; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- iminuit+numba -->\n", "     <g transform=\"translate(221.858083 48.371502) scale(0.0833 -0.0833)\">\n", "      <use xlink:href=\"#DejaVuSans-69\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"125.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"152.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"216.357422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"279.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.519531\"/>\n", "      <use xlink:href=\"#DejaVuSans-2b\" x=\"346.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"430.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"493.896484\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"557.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"654.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"718.164062\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_98\">\n", "     <path d=\"M 198.534083 57.68288 \n", "L 206.864083 57.68288 \n", "L 215.194083 57.68288 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #7f7f7f; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_20\">\n", "     <!-- iminuit+numba [parallel, fastmath] -->\n", "     <g transform=\"translate(221.858083 60.59838) scale(0.0833 -0.0833)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-69\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"125.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"152.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"216.357422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"279.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.519531\"/>\n", "      <use xlink:href=\"#DejaVuSans-2b\" x=\"346.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"430.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"493.896484\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"557.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"654.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"718.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"779.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"811.230469\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"850.244141\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"913.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"975\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1016.113281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"1077.392578\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"1105.175781\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1132.958984\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"1194.482422\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"1222.265625\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"1254.052734\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"1285.839844\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1321.044922\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1382.324219\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1434.423828\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"1473.632812\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1571.044922\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1632.324219\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"1671.533203\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"1734.912109\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_99\">\n", "     <path d=\"M 198.534083 69.909758 \n", "L 206.864083 69.909758 \n", "L 215.194083 69.909758 \n", "\" style=\"fill: none; stroke-dasharray: 9.6,2.4,1.5,2.4; stroke-dashoffset: 0; stroke: #bcbd22; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- iminuit+numba logpdf [parallel, fastmath] -->\n", "     <g transform=\"translate(221.858083 72.825258) scale(0.0833 -0.0833)\">\n", "      <use xlink:href=\"#DejaVuSans-69\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"27.783203\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"125.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"152.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"216.357422\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"279.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"307.519531\"/>\n", "      <use xlink:href=\"#DejaVuSans-2b\" x=\"346.728516\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"430.517578\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"493.896484\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"557.275391\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"654.6875\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"718.164062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"779.443359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"811.230469\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"839.013672\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"900.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"963.671875\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"1027.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"1090.625\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"1125.830078\"/>\n", "      <use xlink:href=\"#DejaVuSans-5b\" x=\"1157.617188\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"1196.630859\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1260.107422\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"1321.386719\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1362.5\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"1423.779297\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"1451.5625\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1479.345703\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"1540.869141\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"1568.652344\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"1600.439453\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"1632.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1667.431641\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"1728.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1780.810547\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"1820.019531\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1917.431641\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1978.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-68\" x=\"2017.919922\"/>\n", "      <use xlink:href=\"#DejaVuSans-5d\" x=\"2081.298828\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pc2c51d252e\">\n", "   <rect x=\"51.378125\" y=\"22.318125\" width=\"410.621395\" height=\"294.125145\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import json\n", "\n", "from pathlib import Path\n", "\n", "with open(\"root.json\") as f:\n", "    root_data = json.load(f)\n", "\n", "paths = []\n", "for p in Path(\".benchmarks\").rglob(\"*.json\"):\n", "    paths.append((p.stat().st_mtime, p))\n", "paths.sort()\n", "fn = paths[-1][1]\n", "\n", "print(fn)\n", "with open(fn) as f:\n", "    data = json.load(f)\n", "\n", "print(\n", "    f\"\"\"\\\n", "benchmark results\n", "  {data[\"datetime\"]}\n", "  {data[\"machine_info\"][\"cpu\"][\"brand_raw\"]}\n", "\"\"\"\n", ")\n", "\n", "variant = {}\n", "for d in (data, root_data):\n", "    # override ROOT results with root_data if any\n", "    for k in tuple(variant):\n", "        if k.startswith(\"RooFit\"):\n", "            del variant[k]\n", "    for b in d[\"benchmarks\"]:\n", "        params = b[\"params\"]\n", "        n = params[\"n\"]\n", "        n = int(n)\n", "        name = b[\"name\"]\n", "        name = name[name.find(\"_\") + 1 : name.find(\"[\")]\n", "        extra = []\n", "        for k, v in params.items():\n", "            if k in (\"n\", \"lib\", \"model\"):\n", "                continue\n", "            if isinstance(v, (bool, int)):\n", "                if v:\n", "                    extra.append(k)\n", "            else:\n", "                extra.append(v)\n", "        if extra:\n", "            name += \"_\" + \"_\".join(extra)\n", "        for key in (\"lib\", \"model\"):\n", "            if key in params:\n", "                name += f\"_{params[key]}\"\n", "        t = b[\"stats\"][\"min\"]\n", "        if name not in variant:\n", "            variant[name] = []\n", "        variant[name].append((n, t))\n", "\n", "for k in variant:\n", "    print(k)\n", "\n", "names = {\n", "    \"Numba vs. Sci<PERSON>y\": {\n", "        \"nll_scipy.stats\": \"handwritten NLL scipy.stats\",\n", "        \"nll_numba_stats\": \"handwritten NLL numba_stats\",\n", "    },\n", "    \"Overhead\": {\n", "        \"minuit_UnbinnedNLL\": \"iminuit+UnbinnedNLL\",\n", "        \"minuit_numba\": \"iminuit+numba\",\n", "        \"minuit_cfunc\": \"iminuit+numba.cfunc\",\n", "    },\n", "    \"RooFit vs. iminuit+numba\": {\n", "        \"RooFit_legacy\": \"RooFit [legacy]\",\n", "        \"RooFit_legacy-parallel\": \"RooFit [legacy, parallel]\",\n", "        \"RooFit_cpu\": \"RooFit [CPU]\",\n", "        \"RooFit_cpu-implicitmt\": \"RooFit [CPU, ImplicitMT]\",\n", "        # \"RooFit_cpu_NumCPU\": \"RooFit [CPU, parallel]\",\n", "        \"RooFit_codegen\": \"RooFit [Codegen]\",\n", "        \"RooFit_codegen_no_grad\": \"RooFit [CodegenNoGrad]\",\n", "        # \"RooFit_codegen_NumCPU\": \"RooFit [Codegen, parallel]\",\n", "        \"minuit\": \"iminuit+numba\",\n", "        \"minuit_parallel_fastmath\": \"iminuit+numba [parallel, fastmath]\",\n", "        \"minuit_parallel_fastmath_log\": \"iminuit+numba logpdf [parallel, fastmath]\",\n", "    },\n", "}\n", "\n", "for title, subnames in names.items():\n", "    plt.figure(constrained_layout=True)\n", "    plt.title(title)\n", "    for name in subnames:\n", "        if name not in variant:\n", "            continue\n", "        d = variant[name]\n", "        n, t = np.transpose(d)\n", "        ls = \"-\"\n", "        if (\n", "            \"parallel\" in name\n", "            and \"fastmath\" in name\n", "            or (\"NumCPU\" in name and \"BatchMode\" in name)\n", "        ):\n", "            ls = \"-.\"\n", "        elif \"parallel\" in name or \"NumCPU\" in name:\n", "            ls = \"--\"\n", "        elif \"fastmath\" in name or \"Batch<PERSON><PERSON>\" in name:\n", "            ls = \":\"\n", "        plt.plot(n, t, ls=ls, label=subnames[name])\n", "\n", "    plt.loglog()\n", "    plt.legend(\n", "        frameon=False,\n", "        fontsize=\"medium\" if len(subnames) < 4 else \"small\",\n", "        ncol=1 if len(subnames) < 3 else 2,\n", "    )\n", "    # plt.title(\"Fit of normal distribution with 2 parameters\")\n", "    plt.xlabel(\"number of data points\")\n", "    plt.ylabel(\"runtime / sec\")\n", "    fname = title.replace(\" \", \"_\").replace(\".\", \"_\").replace(\"__\", \"_\").lower()\n", "    plt.savefig(f\"{fname}.svg\")"]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "py39", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}