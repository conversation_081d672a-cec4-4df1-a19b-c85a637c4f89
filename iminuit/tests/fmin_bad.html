<table>
    <tr>
        <th colspan="2" style="text-align:center" title="Minimizer"> SciPy[L-BFGS-B] </th>
    </tr>
    <tr>
        <td style="text-align:left" title="Minimum value of function"> FCN = nan </td>
        <td style="text-align:center" title="Total number of function and (optional) gradient evaluations"> Nfcn = 100000, Ngrad = 200000 </td>
    </tr>
    <tr>
        <td style="text-align:left" title="Estimated distance to minimum and goal"> EDM = 1.23e-10 (Goal: 1e-05) </td>
        <td style="text-align:center" title="Total run time of algorithms"> time = 1.2 sec </td>
    </tr>
    <tr>
        <td style="text-align:center;{bad}"> INVALID Minimum </td>
        <td style="text-align:center;{bad}"> ABOVE EDM threshold (goal x 10) </td>
    </tr>
    <tr>
        <td style="text-align:center;{warn}"> SOME parameters at limit </td>
        <td style="text-align:center;{bad}"> ABOVE call limit </td>
    </tr>
    <tr>
        <td style="text-align:center;{bad}"> Hesse FAILED </td>
        <td style="text-align:center;{bad}"> ABOVE call limit </td>
    </tr>
</table>
