{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# SciPy minimizers and constraints\n", "\n", "The `Minuit` class can call SciPy minimizers implemented in `scipy.optimize.minimize` as alternatives to the standard Migrad minimizer to minimize the cost function. The SciPy minimizers may perform better or worse on some problems. You can give them a try when you are not satisfied with Mi<PERSON>.\n", "\n", "More importantly, the SciPy minimizers support additional features that Migrad lacks.\n", "\n", "* <PERSON>grad does not allow one to use an externally computed hessian matrix.\n", "* Migrad does not allow one to use additional constraints of the form $\\vec a \\leq f(\\vec x) \\leq \\vec b$ in the minimization, where $\\vec x$ is the parameter vector of length $m$, $f$ is an arbitrary function $\\mathcal{R}^m \\rightarrow \\mathcal{R}^k$ and $\\vec a, \\vec b$ are vector bounds with length $k$.\n", "\n", "SciPy comes with a variety of minimization algorithms and some of them support these features. The ability to use constraints is interesting for HEP applications. In particular, it allows us to ensure that a pdf as a function of the parameters is always positive. This can be ensured sometimes with suitable limits on the parameters, but not always.\n", "\n", "We demonstrate this on a common example of fit of an additive model with a signal and background pdf."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "from iminuit import Minuit\n", "from iminuit.cost import ExtendedUnbinnedNLL\n", "import numpy as np\n", "from numba_stats import norm, <PERSON><PERSON><PERSON>\n", "import matplotlib.pyplot as plt\n", "import joblib"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["The signal pdf is a Gaussian, the background is modelled with second degree Bernstein polynomials. We perform an extended maximum likelihood fit, where the full density model is given by the sum of the signal and background component."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["xrange = (0, 1)\n", "\n", "\n", "def model(x, b0, b1, b2, sig, mu, sigma):\n", "    beta = [b0, b1, b2]\n", "    bint = np.diff(bernstein.integral(xrange, beta, *xrange))\n", "    sint = sig * np.diff(norm.cdf(xrange, mu, sigma))[0]\n", "    return bint + sint, bernstein.density(x, beta, *xrange) + sig * norm.pdf(\n", "        x, mu, sigma\n", "    )"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["In searches for rare decays, it is common to fit models like this to small simulated samples that contain only background, to calculate the distribution of some test statistic (usually the likelihood ratio of S+B and B-only hypotheses). Here, for simplicity, we use the signal amplitude itself as the test statistic.\n", "\n", "We run one such fit. The mean and width of the Gaussian are fixed, only the signal amplitude and the background parameters are varied."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["rng = np.random.default_rng(2)\n", "x = rng.uniform(0, 1, size=35)\n", "\n", "cost = ExtendedUnbinnedNLL(x, model)\n", "n = len(x)\n", "m = Minuit(cost, b0=n, b1=n, b2=n, sig=0, mu=0.5, sigma=0.05)\n", "m.print_level = 0\n", "m.limits[\"b0\", \"b1\", \"b2\"] = (0, None)\n", "m.fixed[\"mu\", \"sigma\"] = True\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["In this example, the signal amplitude came out negative. This happens if the background has an underfluctuation where the signal is expected. This is not an issue if the sum of signal and background density is still positive everywhere where we evaluate it. As long as the total density is positive, individual components are allowed to be negative.\n", "\n", "There are, however, no restrictions in this example that prevent the sum of signal and background from becoming negative for some toy data sets. When that happens, the fit will fail, since the total density cannot mathematically become negative. Specifically, the logarithm of a negative number is taken in the calculation which results in a NaN."]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Migrad fit on toys\n", "\n", "We apply the fit many times on randomly sampled background-only data to observe this."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["@joblib.delayed\n", "def compute(itry):\n", "    rng = np.random.default_rng(itry)\n", "    x = rng.uniform(0, 1, size=35)\n", "    cost = ExtendedUnbinnedNLL(x, model)\n", "    m = Minuit(cost, b0=n, b1=n, b2=n, sig=0, mu=0.5, sigma=0.05)\n", "    m.limits[\"b0\", \"b1\", \"b2\"] = (0, None)\n", "    m.fixed[\"mu\", \"sigma\"] = True\n", "    m.migrad()\n", "    return m\n", "\n", "\n", "fits = joblib.Parallel(-1)(compute(i) for i in range(200))"]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["nfailed = sum(not m.valid for m in fits)\n", "plt.title(f\"{nfailed} fits failed ({nfailed / len(fits) * 100:.0f} %)\")\n", "plt.hist([m.values[\"sig\"] for m in fits], bins=10, range=(-10, 10))\n", "plt.xlabel(\"sig\");"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["The distribution of the signal amplitude looks fairly gaussian which is good, but the fit failed to converge in a few cases due to the problem just described. Simply discarding these cases is not acceptable, it would distort conclusions drawn from the distribution of the test statistic, which is commonly needed to set limits or to compute the p-value for an observed amplitude.\n", "\n", "We can repair this by placing a limit on the signal amplitude. This is a suitable solution, although it will bias the signal amplitude and change the shape of the distribution of the test statistic. \n", "\n", "An alternative is to perform a constrained minimization, which allows us to directly add a condition to the fit that the model density must be positive at each data point. We merely need to replace the call `m.migrad` with the call `m.scipy` and pass the (non-linear) constraint. An appropriate algorithm is automatically selected which performs a constrained minimization. The SciPy minimizers are fully integrated into Minuit, which means that Minuit computes an EDM value for the minimum and parameter uncertainties."]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["## SciPy constrained fit on toys \n", "\n", "We run SciPy with the constraint on the same simulated samples on which we ran Migrad before."]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["from scipy.optimize import NonlinearConstraint\n", "\n", "\n", "@joblib.delayed\n", "def compute(itry):\n", "    rng = np.random.default_rng(itry)\n", "    x = rng.uniform(0, 1, size=35)\n", "    cost = ExtendedUnbinnedNLL(x, model)\n", "    m = Minuit(cost, b0=n, b1=n, b2=n, sig=0, mu=0.5, sigma=0.05)\n", "    m.limits[\"b0\", \"b1\", \"b2\"] = (0, None)\n", "    m.fixed[\"mu\", \"sigma\"] = True\n", "    m.scipy(constraints=NonlinearConstraint(lambda *par: model(x, *par)[1], 0, np.inf))\n", "    return m\n", "\n", "\n", "fits_constrained = joblib.Parallel(-1)(compute(i) for i in range(200))"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["nfailed = sum(not m.valid for m in fits_constrained)\n", "plt.title(\n", "    f\"{nfailed} constrained fits failed ({nfailed / len(fits_constrained) * 100:.0f} %)\"\n", ")\n", "plt.hist(\n", "    [m.values[\"sig\"] for m in fits],\n", "    alpha=0.5,\n", "    bins=10,\n", "    range=(-10, 10),\n", "    label=fits[0].fmin.algorithm,\n", ")\n", "plt.hist(\n", "    [m.values[\"sig\"] for m in fits_constrained],\n", "    alpha=0.5,\n", "    bins=10,\n", "    range=(-10, 10),\n", "    label=fits_constrained[0].fmin.algorithm,\n", ")\n", "plt.xlabel(\"sig\")\n", "plt.legend();"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["There are no failures this time. \n", "\n", "For sig $\\gg 0$, the distributions are identical, as theoretically expected. In practice, there can be small bin migration effects due to finite precision of numerical algorithms. These are not of concern.\n", "\n", "Important are the differences for sig < 0, where <PERSON><PERSON> did not converge in a few cases and where therefore samples are missing. Those missing samples are recovered in the distribution produced by the constrained fit.\n", "\n", "This demonstrates that it is important to not discard failed fits, as this will in general distort the distribution of the test statistic."]}, {"cell_type": "markdown", "id": "15", "metadata": {}, "source": ["## Bonus: unconstrained SciPy fit\n", "\n", "The issues we describe here are of a principal mathematical nature. We should not expect that an unconstrained minimiser from SciPy does better than Mi<PERSON>, but let's test this assumption. The minimiser that SciPy uses when only box constraints are used is the L-BFGS-B method which is roughly comparable to Migrad. Let us see how well this algorithm does on the same toy samples."]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["@joblib.delayed\n", "def compute(itry):\n", "    rng = np.random.default_rng(itry)\n", "    x = rng.uniform(0, 1, size=35)\n", "    cost = ExtendedUnbinnedNLL(x, model)\n", "    m = Minuit(cost, b0=n, b1=n, b2=n, sig=0, mu=0.5, sigma=0.05)\n", "    m.limits[\"b0\", \"b1\", \"b2\"] = (0, None)\n", "    m.fixed[\"mu\", \"sigma\"] = True\n", "    m.scipy()\n", "    return m\n", "\n", "\n", "fits_bfgs = joblib.Parallel(-1)(compute(i) for i in range(200))"]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["nfailed = sum(not m.valid for m in fits_bfgs)\n", "plt.title(f\"{nfailed} BFGS fits failed ({nfailed / len(fits_bfgs) * 100:.0f} %)\")\n", "for f in (fits, fits_constrained, fits_bfgs):\n", "    plt.hist(\n", "        [m.values[\"sig\"] for m in f],\n", "        alpha=0.5,\n", "        bins=10,\n", "        range=(-10, 10),\n", "        label=f[0].fmin.algorithm,\n", "    )\n", "plt.xlabel(\"sig\")\n", "plt.legend();"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["In this example, the BFGS method actually failed less than Migrad, but it still fails in some cases, while the constrained fit did not fail at all."]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["## Speed comparison\n", "\n", "Since constrained fits are so useful, should you use them all the time? No.\n", "\n", "Constrained fits are more computationally expensive. Satisfying extra constrains generally slows down convergence. Let's compare the speed of the three minimisers tested here. We set the strategy to 0, to avoid computing the Hessian matrix automatically, since we want to measure only the time used by the minimiser."]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["m.strategy = 0"]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["%timeit -n3 m.reset(); m.migrad()"]}, {"cell_type": "code", "execution_count": null, "id": "22", "metadata": {}, "outputs": [], "source": ["%timeit -n3 m.reset(); m.scipy()"]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["%timeit -n3 m.reset(); m.scipy(constraints=NonlinearConstraint(lambda *par: model(x, *par)[1], 0, np.inf))"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["Migrad is the fastest, followed by the L-BFGS-B method. The constrained fit is much slower, since it has to do more work.\n", "\n", "Migrad is quite fast because of its smart stopping criterion. Migrad stops the fit as soon as the improvement of the fitted parameters become small compared to their uncertainties. Migrad was explicitly designed for statistical fits, where the cost function is a log-likelihood or least-squares function. Since it assumes that, it can stops the fit as soon as the parameter improvements become negligible compared to the parameter uncertainty, which is given by the inverse of its internal approximation of the Hessian matrix.\n", "\n", "The SciPy minimisers do not expect the cost function to be a log-likelihood or least-squares and thus cannot assume that the Hessian matrix has a special meaning. Instead they tend to optimise until they hit the limits of machine precision. You can also see this in the benchmark section of the documentation."]}], "metadata": {"kernelspec": {"display_name": "Python 3.8.14 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 5}