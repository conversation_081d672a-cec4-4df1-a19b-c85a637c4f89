{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Performance of cost functions\n", "\n", "This is not really a tutorial, but more of a benchmark of the builtin cost functions.\n", "\n", "We test the performance of the cost functions shipped with iminuit. We check that they produce unbiased results with proper variance. To do that, we generate normal distributed data many times and fit a normal distribution to each independent data set. The bias is computed from the averages of these reconstructed parameters. We also compute the mean of the estimated variance for each data set, which should converge to 1.\n", "\n", "Since we do the fit many times, we do not use implementations of the pdf and cdf of a normal distribution from `scipy.stats`, but Numba-accelerated versions from the `numba-stats` package. For the binned fits, we compute histograms of the data with $3 + n/10$ equidistant bins, where $n$ is the sample size.\n", "\n", "Disclaimer: This tutorial is targeted at experts, please read the code to understand what is going on."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Maximum-likelihood fits\n", "\n", "Here we check that the different maximum-likelihood cost functions produce asymptotically unbiased results with the expected variance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "import numpy as np\n", "from matplotlib import pyplot as plt\n", "from iminuit import Minuit\n", "from iminuit.cost import (\n", "    UnbinnedNLL,\n", "    BinnedNLL,\n", "    ExtendedUnbinnedNLL,\n", "    ExtendedBinnedNLL,\n", "    <PERSON>st<PERSON><PERSON><PERSON>,\n", ")\n", "from argparse import Namespace\n", "from numba_stats import norm\n", "import joblib"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["10 2 need to re-try [(True, True), (True, True), (True, True), (False, False)]\n", "10 9 need to re-try [(True, True), (True, True), (True, True), (False, False)]\n", "10 57 need to re-try [(True, True), (True, True), (False, False), (False, False)]\n", "10 85 need to re-try [(True, True), (True, True), (True, True), (False, True)]\n"]}], "source": ["n_tries = 100  # increase this to get less scattering\n", "\n", "n_pts = np.array((10, 30, 100, 300, 1000, 3000, 10000))\n", "\n", "truth = Namespace(mu=0, sigma=1)\n", "\n", "\n", "# function that runs random experiments with sample size n\n", "@joblib.delayed\n", "def compute(n):\n", "    rng = np.random.default_rng(n)\n", "    np.random.seed(n)\n", "    u_nll = []\n", "    b_nll = []\n", "    e_u_nll = []\n", "    e_b_nll = []\n", "    for i_try in range(n_tries):\n", "        while True:\n", "            k = 2 * rng.poisson(n)\n", "            x = rng.normal(truth.mu, truth.sigma, size=k)\n", "            x = x[np.abs(x) < 2]\n", "            x = x[:k]\n", "            xrange = np.array((-2.0, 2.0))\n", "            nh, xe = np.histogram(x, bins=3 + n // 10, range=xrange)\n", "            m = [\n", "                # model must be a normalized pdf\n", "                Minuit(\n", "                    UnbinnedNLL(\n", "                        x,\n", "                        lambda x, mu, sigma: (\n", "                            norm.pdf(x, mu, sigma)\n", "                            / np.diff(norm.cdf(xrange, mu, sigma))\n", "                        ),\n", "                    ),\n", "                    mu=truth.mu,\n", "                    sigma=truth.sigma,\n", "                ),\n", "                # model must be a function that returns the integral over the scaled pdf and the scaled pdf\n", "                Minuit(\n", "                    ExtendedUnbinnedNLL(\n", "                        x,\n", "                        lambda x, n, mu, sigma: (\n", "                            n * np.diff(norm.cdf(xrange, mu, sigma)),\n", "                            n * norm.pdf(x, mu, sigma),\n", "                        ),\n", "                    ),\n", "                    n=n,\n", "                    mu=truth.mu,\n", "                    sigma=truth.sigma,\n", "                ),\n", "                # model must be a normalized cdf up to an arbitrary additive constant (only differences are used)\n", "                Minuit(\n", "                    BinnedNLL(\n", "                        nh,\n", "                        xe,\n", "                        lambda x, mu, sigma: (\n", "                            norm.cdf(x, mu, sigma)\n", "                            / np.diff(norm.cdf(xrange, mu, sigma))\n", "                        ),\n", "                    ),\n", "                    mu=truth.mu,\n", "                    sigma=truth.sigma,\n", "                ),\n", "                # model must be a scaled cdf up to an arbitrary additive constant (only differences are used)\n", "                Minuit(\n", "                    ExtendedBinnedNLL(\n", "                        nh, xe, lambda x, n, mu, sigma: n * norm.cdf(x, mu, sigma)\n", "                    ),\n", "                    n=n,\n", "                    mu=truth.mu,\n", "                    sigma=truth.sigma,\n", "                ),\n", "            ]\n", "            for mi in m:\n", "                mi.limits[\"sigma\"] = (1e-3, None)\n", "                mi.limits[\"mu\"] = (-2, 2)\n", "                if \"n\" in mi.parameters:\n", "                    mi.limits[\"n\"] = (0, None)\n", "\n", "            # only accept a random data set when all fits converged ok\n", "            all_good = True\n", "            for mi in m:\n", "                mi.migrad()\n", "                mi.hesse()\n", "                if not mi.valid or not mi.accurate:\n", "                    all_good = False\n", "                    break\n", "            if all_good:\n", "                break\n", "            print(f\"{n} {i_try} need to re-try {[(mi.valid, mi.accurate) for mi in m]}\")\n", "\n", "        # store parameter deviations and estimated variances for each pseudo-experiment\n", "        u_nll.append(\n", "            (\n", "                m[0].values[\"mu\"] - truth.mu,\n", "                m[0].errors[\"mu\"] ** 2,\n", "                m[0].values[\"sigma\"] - truth.sigma,\n", "                m[0].errors[\"sigma\"] ** 2,\n", "            )\n", "        )\n", "        e_u_nll.append(\n", "            (\n", "                m[1].values[\"n\"] - n,\n", "                m[1].errors[\"n\"] ** 2,\n", "                m[1].values[\"mu\"] - truth.mu,\n", "                m[1].errors[\"mu\"] ** 2,\n", "                m[1].values[\"sigma\"] - truth.sigma,\n", "                m[1].errors[\"sigma\"] ** 2,\n", "            )\n", "        )\n", "        b_nll.append(\n", "            (\n", "                m[2].values[\"mu\"] - truth.mu,\n", "                m[2].errors[\"mu\"] ** 2,\n", "                m[2].values[\"sigma\"] - truth.sigma,\n", "                m[2].errors[\"sigma\"] ** 2,\n", "            )\n", "        )\n", "        e_b_nll.append(\n", "            (\n", "                m[3].values[\"n\"] - n,\n", "                m[3].errors[\"n\"] ** 2,\n", "                m[3].values[\"mu\"] - truth.mu,\n", "                m[3].errors[\"mu\"] ** 2,\n", "                m[3].values[\"sigma\"] - truth.sigma,\n", "                m[3].errors[\"sigma\"] ** 2,\n", "            )\n", "        )\n", "\n", "    # means over pseudo-experiments are computed here\n", "    return (\n", "        np.mean(u_nll, axis=0),\n", "        np.mean(e_u_nll, axis=0),\n", "        np.mean(b_nll, axis=0),\n", "        np.mean(e_b_nll, axis=0),\n", "    )\n", "\n", "\n", "unbinned_nll = []\n", "extended_unbinned_nll = []\n", "binned_nll = []\n", "extended_binned_nll = []\n", "\n", "result = joblib.Parallel(-1)(compute(n) for n in n_pts)\n", "\n", "for a, b, c, d in result:\n", "    unbinned_nll.append(a)\n", "    extended_unbinned_nll.append(b)\n", "    binned_nll.append(c)\n", "    extended_binned_nll.append(d)\n", "\n", "unbinned_nll = np.transpose(unbinned_nll)\n", "extended_unbinned_nll = np.transpose(extended_unbinned_nll)\n", "binned_nll = np.transpose(binned_nll)\n", "extended_binned_nll = np.transpose(extended_binned_nll)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We plot the measured bias as a point and the mean variance as an error bar. The deviations go down with $n^{-{1/2}}$, where $n$ is the sample size. We undo this for the plots by multiplying deviations with $n^{1/2}$."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"538.342187pt\" height=\"503.394375pt\" viewBox=\"0 0 538.**********.394375\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:07:22.278782</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 503.394375 \n", "L 538.**********.394375 \n", "L 538.342187 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 28.**********.918125 \n", "L 257.**********.918125 \n", "L 257.214915 22.318125 \n", "L 28.942188 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mb928dd431c\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"39.318221\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"108.491774\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"177.665328\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"246.838882\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"ma57b084673\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"32.614611\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"36.153012\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"60.141535\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"72.322393\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"80.96485\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"87.66846\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"93.145708\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"97.776655\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"101.788164\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"105.326566\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"129.315089\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"141.495947\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"150.138403\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"156.842013\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"162.319262\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"166.950209\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"170.961718\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"174.50012\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"198.488643\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"210.669501\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"219.311957\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"226.015567\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"231.492815\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"236.123763\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"240.135272\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"243.673673\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_31\">\n", "      <defs>\n", "       <path id=\"ma2d664a7f8\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"203.758125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(7.2 207.557344) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"163.438125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(7.2 167.237344) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"123.118125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(15.579688 126.917344) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"82.798125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(15.579688 86.597344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"42.478125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(15.579688 46.277344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 39.318221 162.271646 \n", "L 39.318221 91.849318 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 72.322393 141.554774 \n", "L 72.322393 105.747559 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 108.491774 135.718473 \n", "L 108.491774 101.922464 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 141.495947 136.456095 \n", "L 141.495947 103.115012 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 177.665328 143.038184 \n", "L 177.665328 109.70552 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.669501 140.107996 \n", "L 210.669501 106.983856 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 246.838882 139.267614 \n", "L 246.838882 106.156202 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_2\">\n", "    <path d=\"M 39.318221 445.048823 \n", "L 39.318221 -1 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 72.322393 142.410056 \n", "L 72.322393 105.361497 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 108.491774 140.163761 \n", "L 108.491774 106.812009 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 141.495947 140.835877 \n", "L 141.495947 108.320388 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 177.665328 137.918399 \n", "L 177.665328 105.378295 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.669501 141.222966 \n", "L 210.669501 109.025611 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 246.838882 142.164916 \n", "L 246.838882 109.986508 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_36\">\n", "    <path d=\"M 28.942188 143.278125 \n", "L 257.214915 143.278125 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_37\">\n", "    <path d=\"M 28.942188 102.958125 \n", "L 257.214915 102.958125 \n", "\" clip-path=\"url(#p7d93922c16)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_38\">\n", "    <defs>\n", "     <path id=\"mad74139515\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #1f77b4\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p7d93922c16)\">\n", "     <use xlink:href=\"#mad74139515\" x=\"39.318221\" y=\"127.060482\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"72.322393\" y=\"123.651166\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"108.491774\" y=\"118.820468\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"141.495947\" y=\"119.785554\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"177.665328\" y=\"126.371852\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"210.669501\" y=\"123.545926\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"246.838882\" y=\"122.711908\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_39\">\n", "    <defs>\n", "     <path id=\"m87aa2491f5\" d=\"M -3 3 \n", "L 3 3 \n", "L 3 -3 \n", "L -3 -3 \n", "z\n", "\" style=\"stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p7d93922c16)\">\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"39.318221\" y=\"117.519331\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"72.322393\" y=\"123.885777\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"108.491774\" y=\"123.487885\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"141.495947\" y=\"124.578133\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"177.665328\" y=\"121.648347\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"210.669501\" y=\"125.124288\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"246.838882\" y=\"126.075712\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 28.**********.918125 \n", "L 28.942188 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 257.**********.918125 \n", "L 257.214915 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 28.**********.918125 \n", "L 257.**********.918125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 28.942188 22.318125 \n", "L 257.214915 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- Unbinned NLL -->\n", "    <g transform=\"translate(101.221051 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-55\" d=\"M 556 4666 \n", "L 1191 4666 \n", "L 1191 1831 \n", "Q 1191 1081 1462 751 \n", "Q 1734 422 2344 422 \n", "Q 2950 422 3222 751 \n", "Q 3494 1081 3494 1831 \n", "L 3494 4666 \n", "L 4128 4666 \n", "L 4128 1753 \n", "Q 4128 841 3676 375 \n", "Q 3225 -91 2344 -91 \n", "Q 1459 -91 1007 375 \n", "Q 556 841 556 1753 \n", "L 556 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-4e\" d=\"M 628 4666 \n", "L 1478 4666 \n", "L 3547 763 \n", "L 3547 4666 \n", "L 4159 4666 \n", "L 4159 0 \n", "L 3309 0 \n", "L 1241 3903 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-4c\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 531 \n", "L 3531 531 \n", "L 3531 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-55\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"73.193359\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"136.572266\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"200.048828\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"227.832031\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"291.210938\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"354.589844\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"416.113281\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"479.589844\"/>\n", "     <use xlink:href=\"#DejaVuSans-4e\" x=\"511.376953\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"586.181641\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"641.894531\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 302.86946 223.918125 \n", "L 531.142187 223.918125 \n", "L 531.142187 22.318125 \n", "L 302.86946 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"313.245493\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"382.419047\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"451.592601\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"520.766154\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"306.541883\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"310.080285\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_37\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"334.068808\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_38\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"346.249666\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_39\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"354.892122\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_40\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"361.595732\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_41\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"367.072981\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_42\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"371.703928\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_43\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"375.715437\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_44\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"379.253839\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_45\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"403.242362\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_46\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"415.42322\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_47\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"424.065676\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_48\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"430.769286\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_49\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"436.246534\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_50\">\n", "     <g id=\"line2d_59\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"440.877482\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_51\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"444.888991\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_52\">\n", "     <g id=\"line2d_61\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"448.427392\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_53\">\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"472.415915\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_54\">\n", "     <g id=\"line2d_63\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"484.596773\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_55\">\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"493.23923\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_56\">\n", "     <g id=\"line2d_65\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"499.94284\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_57\">\n", "     <g id=\"line2d_66\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"505.420088\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_58\">\n", "     <g id=\"line2d_67\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"510.051035\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_59\">\n", "     <g id=\"line2d_68\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"514.062544\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_60\">\n", "     <g id=\"line2d_69\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"517.600946\" y=\"223.918125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"203.758125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_71\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"163.438125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"123.118125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_73\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"82.798125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_74\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"42.478125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_3\">\n", "    <path d=\"M 313.245493 164.48841 \n", "L 313.245493 88.65951 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 346.249666 142.627853 \n", "L 346.249666 105.122852 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 382.419047 136.199468 \n", "L 382.419047 102.287876 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 415.42322 136.490605 \n", "L 415.42322 103.120907 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 451.592601 143.027309 \n", "L 451.592601 109.691154 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 484.596773 140.097661 \n", "L 484.596773 106.973004 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 520.766154 139.267785 \n", "L 520.766154 106.156293 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_4\">\n", "    <path d=\"M 313.245493 175.809725 \n", "L 313.245493 60.828416 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 346.249666 144.527793 \n", "L 346.249666 102.691548 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 382.419047 140.555406 \n", "L 382.419047 106.793255 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 415.42322 140.891986 \n", "L 415.42322 108.296281 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 451.592601 137.914788 \n", "L 451.592601 105.365931 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 484.596773 141.218686 \n", "L 484.596773 109.020125 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 520.766154 142.163113 \n", "L 520.766154 109.984533 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_75\">\n", "    <path d=\"M 302.86946 143.278125 \n", "L 531.142187 143.278125 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_76\">\n", "    <path d=\"M 302.86946 102.958125 \n", "L 531.142187 102.958125 \n", "\" clip-path=\"url(#pa2abcce74e)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_77\">\n", "    <g clip-path=\"url(#pa2abcce74e)\">\n", "     <use xlink:href=\"#mad74139515\" x=\"313.245493\" y=\"126.57396\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"346.249666\" y=\"123.875353\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"382.419047\" y=\"119.243672\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"415.42322\" y=\"119.805756\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"451.592601\" y=\"126.359232\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"484.596773\" y=\"123.535332\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"520.766154\" y=\"122.712039\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_78\">\n", "    <g clip-path=\"url(#pa2abcce74e)\">\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"313.245493\" y=\"118.31907\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"346.249666\" y=\"123.60967\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"382.419047\" y=\"123.67433\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"415.42322\" y=\"124.594133\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"451.592601\" y=\"121.64036\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"484.596773\" y=\"125.119406\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"520.766154\" y=\"126.073823\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 302.86946 223.918125 \n", "L 302.86946 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 531.142187 223.918125 \n", "L 531.142187 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 302.86946 223.918125 \n", "L 531.142187 223.918125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 302.86946 22.318125 \n", "L 531.142187 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_7\">\n", "    <!-- Binned NLL -->\n", "    <g transform=\"translate(383.034574 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-42\" d=\"M 1259 2228 \n", "L 1259 519 \n", "L 2272 519 \n", "Q 2781 519 3026 730 \n", "Q 3272 941 3272 1375 \n", "Q 3272 1813 3026 2020 \n", "Q 2781 2228 2272 2228 \n", "L 1259 2228 \n", "z\n", "M 1259 4147 \n", "L 1259 2741 \n", "L 2194 2741 \n", "Q 2656 2741 2882 2914 \n", "Q 3109 3088 3109 3444 \n", "Q 3109 3797 2882 3972 \n", "Q 2656 4147 2194 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2241 4666 \n", "Q 2963 4666 3353 4366 \n", "Q 3744 4066 3744 3513 \n", "Q 3744 3084 3544 2831 \n", "Q 3344 2578 2956 2516 \n", "Q 3422 2416 3680 2098 \n", "Q 3938 1781 3938 1306 \n", "Q 3938 681 3513 340 \n", "Q 3088 0 2303 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-42\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"68.603516\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"96.386719\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"159.765625\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"223.144531\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"284.667969\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"348.144531\"/>\n", "     <use xlink:href=\"#DejaVuSans-4e\" x=\"379.931641\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"454.736328\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"510.449219\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 28.942188 465.838125 \n", "L 257.214915 465.838125 \n", "L 257.214915 264.238125 \n", "L 28.942188 264.238125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_61\">\n", "     <g id=\"line2d_79\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"39.318221\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(30.518221 480.436562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_62\">\n", "     <g id=\"line2d_80\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"108.491774\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(99.691774 480.436562) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_63\">\n", "     <g id=\"line2d_81\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"177.665328\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(168.865328 480.436562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_64\">\n", "     <g id=\"line2d_82\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"246.838882\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(238.038882 480.436562) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_65\">\n", "     <g id=\"line2d_83\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"32.614611\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_66\">\n", "     <g id=\"line2d_84\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"36.153012\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_67\">\n", "     <g id=\"line2d_85\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"60.141535\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_68\">\n", "     <g id=\"line2d_86\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"72.322393\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_69\">\n", "     <g id=\"line2d_87\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"80.96485\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_70\">\n", "     <g id=\"line2d_88\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"87.66846\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_71\">\n", "     <g id=\"line2d_89\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"93.145708\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_72\">\n", "     <g id=\"line2d_90\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"97.776655\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_73\">\n", "     <g id=\"line2d_91\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"101.788164\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_74\">\n", "     <g id=\"line2d_92\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"105.326566\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_75\">\n", "     <g id=\"line2d_93\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"129.315089\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_76\">\n", "     <g id=\"line2d_94\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"141.495947\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_77\">\n", "     <g id=\"line2d_95\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"150.138403\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_78\">\n", "     <g id=\"line2d_96\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"156.842013\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_79\">\n", "     <g id=\"line2d_97\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"162.319262\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_80\">\n", "     <g id=\"line2d_98\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"166.950209\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_81\">\n", "     <g id=\"line2d_99\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"170.961718\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_82\">\n", "     <g id=\"line2d_100\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"174.50012\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_83\">\n", "     <g id=\"line2d_101\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"198.488643\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_84\">\n", "     <g id=\"line2d_102\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"210.669501\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_85\">\n", "     <g id=\"line2d_103\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"219.311957\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_86\">\n", "     <g id=\"line2d_104\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"226.015567\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_87\">\n", "     <g id=\"line2d_105\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"231.492815\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_88\">\n", "     <g id=\"line2d_106\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"236.123763\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_89\">\n", "     <g id=\"line2d_107\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"240.135272\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_90\">\n", "     <g id=\"line2d_108\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"243.673673\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- $n_\\mathrm{pts}$ -->\n", "     <g transform=\"translate(134.328551 493.094375) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-Oblique-6e\" d=\"M 3566 2113 \n", "L 3156 0 \n", "L 2578 0 \n", "L 2988 2091 \n", "Q 3016 2238 3031 2350 \n", "Q 3047 2463 3047 2528 \n", "Q 3047 2791 2881 2937 \n", "Q 2716 3084 2419 3084 \n", "Q 1956 3084 1622 2776 \n", "Q 1288 2469 1184 1941 \n", "L 800 0 \n", "L 225 0 \n", "L 903 3500 \n", "L 1478 3500 \n", "L 1363 2950 \n", "Q 1603 3253 1940 3418 \n", "Q 2278 3584 2650 3584 \n", "Q 3113 3584 3367 3334 \n", "Q 3622 3084 3622 2631 \n", "Q 3622 2519 3608 2391 \n", "Q 3594 2263 3566 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" transform=\"translate(63.378906 -16.40625) scale(0.7)\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" transform=\"translate(107.8125 -16.40625) scale(0.7)\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" transform=\"translate(135.258789 -16.40625) scale(0.7)\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_109\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"445.678125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(7.2 449.477344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_110\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"405.358125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(7.2 409.157344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_111\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"365.038125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(15.579688 368.837344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_112\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"324.718125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(15.579688 328.517344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_113\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"28.942188\" y=\"284.398125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(15.579688 288.197344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_5\">\n", "    <path d=\"M 39.318221 404.453091 \n", "L 39.318221 333.507324 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 72.322393 383.477386 \n", "L 72.322393 347.657113 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 108.491774 377.644275 \n", "L 108.491774 343.837545 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 141.495947 378.38632 \n", "L 141.495947 345.038953 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 177.665328 384.952849 \n", "L 177.665328 351.617792 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.669501 382.026489 \n", "L 210.669501 348.902057 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 246.838882 381.185037 \n", "L 246.838882 348.073526 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_6\">\n", "    <path d=\"M 39.318221 504.394375 \n", "L 39.318221 -1 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 72.322393 384.33228 \n", "L 72.322393 347.249733 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 108.491774 382.063412 \n", "L 108.491774 348.682912 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 141.495947 382.725333 \n", "L 141.495947 350.192869 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 177.665328 379.809953 \n", "L 177.665328 347.26315 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.669501 383.1352 \n", "L 210.669501 350.93685 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 246.838882 384.079211 \n", "L 246.838882 351.900215 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_114\">\n", "    <path d=\"M 28.942188 385.198125 \n", "L 257.214915 385.198125 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_115\">\n", "    <path d=\"M 28.942188 344.878125 \n", "L 257.214915 344.878125 \n", "\" clip-path=\"url(#p03715bc5a1)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_116\">\n", "    <g clip-path=\"url(#p03715bc5a1)\">\n", "     <use xlink:href=\"#mad74139515\" x=\"39.318221\" y=\"368.980207\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"72.322393\" y=\"365.56725\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"108.491774\" y=\"360.74091\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"141.495947\" y=\"361.712637\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"177.665328\" y=\"368.285321\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"210.669501\" y=\"365.464273\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"246.838882\" y=\"364.629281\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_117\">\n", "    <g clip-path=\"url(#p03715bc5a1)\">\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"39.318221\" y=\"359.263645\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"72.322393\" y=\"365.791007\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"108.491774\" y=\"365.373162\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"141.495947\" y=\"366.459101\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"177.665328\" y=\"363.536551\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"210.669501\" y=\"367.036025\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"246.838882\" y=\"367.989713\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 28.942188 465.838125 \n", "L 28.942188 264.238125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 257.214915 465.838125 \n", "L 257.214915 264.238125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 28.942188 465.838125 \n", "L 257.214915 465.838125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 28.942188 264.238125 \n", "L 257.214915 264.238125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- Extended Unbinned NLL -->\n", "    <g transform=\"translate(70.815114 258.238125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-45\"/>\n", "     <use xlink:href=\"#DejaVuSans-78\" x=\"63.183594\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"122.363281\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"161.572266\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"223.095703\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"286.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"349.951172\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"411.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"474.951172\"/>\n", "     <use xlink:href=\"#DejaVuSans-55\" x=\"506.738281\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"579.931641\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"643.310547\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"706.787109\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"734.570312\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"797.949219\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"861.328125\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"922.851562\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"986.328125\"/>\n", "     <use xlink:href=\"#DejaVuSans-4e\" x=\"1018.115234\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"1092.919922\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"1148.632812\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 302.86946 465.838125 \n", "L 531.142187 465.838125 \n", "L 531.142187 264.238125 \n", "L 302.86946 264.238125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_91\">\n", "     <g id=\"line2d_118\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"313.245493\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(304.445493 480.436562) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_92\">\n", "     <g id=\"line2d_119\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"382.419047\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(373.619047 480.436562) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_93\">\n", "     <g id=\"line2d_120\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"451.592601\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(442.792601 480.436562) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_94\">\n", "     <g id=\"line2d_121\">\n", "      <g>\n", "       <use xlink:href=\"#mb928dd431c\" x=\"520.766154\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(511.966154 480.436562) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_95\">\n", "     <g id=\"line2d_122\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"306.541883\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_96\">\n", "     <g id=\"line2d_123\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"310.080285\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_97\">\n", "     <g id=\"line2d_124\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"334.068808\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_98\">\n", "     <g id=\"line2d_125\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"346.249666\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_99\">\n", "     <g id=\"line2d_126\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"354.892122\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_100\">\n", "     <g id=\"line2d_127\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"361.595732\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_101\">\n", "     <g id=\"line2d_128\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"367.072981\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_102\">\n", "     <g id=\"line2d_129\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"371.703928\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_103\">\n", "     <g id=\"line2d_130\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"375.715437\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_104\">\n", "     <g id=\"line2d_131\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"379.253839\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_105\">\n", "     <g id=\"line2d_132\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"403.242362\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_106\">\n", "     <g id=\"line2d_133\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"415.42322\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_107\">\n", "     <g id=\"line2d_134\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"424.065676\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_108\">\n", "     <g id=\"line2d_135\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"430.769286\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_109\">\n", "     <g id=\"line2d_136\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"436.246534\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_110\">\n", "     <g id=\"line2d_137\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"440.877482\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_111\">\n", "     <g id=\"line2d_138\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"444.888991\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_112\">\n", "     <g id=\"line2d_139\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"448.427392\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_113\">\n", "     <g id=\"line2d_140\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"472.415915\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_114\">\n", "     <g id=\"line2d_141\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"484.596773\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_115\">\n", "     <g id=\"line2d_142\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"493.23923\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_116\">\n", "     <g id=\"line2d_143\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"499.94284\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_117\">\n", "     <g id=\"line2d_144\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"505.420088\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_118\">\n", "     <g id=\"line2d_145\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"510.051035\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_119\">\n", "     <g id=\"line2d_146\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"514.062544\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_120\">\n", "     <g id=\"line2d_147\">\n", "      <g>\n", "       <use xlink:href=\"#ma57b084673\" x=\"517.600946\" y=\"465.838125\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_23\">\n", "     <!-- $n_\\mathrm{pts}$ -->\n", "     <g transform=\"translate(408.255824 493.094375) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" transform=\"translate(63.378906 -16.40625) scale(0.7)\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" transform=\"translate(107.8125 -16.40625) scale(0.7)\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" transform=\"translate(135.258789 -16.40625) scale(0.7)\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_148\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"445.678125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_149\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"405.358125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_150\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"365.038125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_151\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"324.718125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_152\">\n", "      <g>\n", "       <use xlink:href=\"#ma2d664a7f8\" x=\"302.86946\" y=\"284.398125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_7\">\n", "    <path d=\"M 313.245493 406.400522 \n", "L 313.245493 330.621912 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 346.249666 384.549028 \n", "L 346.249666 347.042697 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 382.419047 378.125439 \n", "L 382.419047 344.201928 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 415.42322 378.422644 \n", "L 415.42322 345.047381 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 451.592601 384.941282 \n", "L 451.592601 351.602756 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 484.596773 382.016122 \n", "L 484.596773 348.891191 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 520.766154 381.185388 \n", "L 520.766154 348.073802 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_8\">\n", "    <path d=\"M 313.245493 418.32016 \n", "L 313.245493 302.069315 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 346.249666 386.439005 \n", "L 346.249666 344.571416 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 382.419047 382.452522 \n", "L 382.419047 348.658837 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 415.42322 382.785032 \n", "L 415.42322 350.174353 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 451.592601 379.80716 \n", "L 451.592601 347.251914 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 484.596773 383.132133 \n", "L 484.596773 350.932692 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 520.766154 384.078534 \n", "L 520.766154 351.899628 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_153\">\n", "    <path d=\"M 302.86946 385.198125 \n", "L 531.142187 385.198125 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_154\">\n", "    <path d=\"M 302.86946 344.878125 \n", "L 531.142187 344.878125 \n", "\" clip-path=\"url(#pdefa37206b)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_155\">\n", "    <g clip-path=\"url(#pdefa37206b)\">\n", "     <use xlink:href=\"#mad74139515\" x=\"313.245493\" y=\"368.511217\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"346.249666\" y=\"365.795863\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"382.419047\" y=\"361.163684\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"415.42322\" y=\"361.735013\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"451.592601\" y=\"368.272019\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"484.596773\" y=\"365.453656\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#mad74139515\" x=\"520.766154\" y=\"364.629595\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_156\">\n", "    <g clip-path=\"url(#pdefa37206b)\">\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"313.245493\" y=\"360.194738\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"346.249666\" y=\"365.50521\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"382.419047\" y=\"365.555679\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"415.42322\" y=\"366.479693\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"451.592601\" y=\"363.529537\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"484.596773\" y=\"367.032412\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m87aa2491f5\" x=\"520.766154\" y=\"367.989081\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 302.86946 465.838125 \n", "L 302.86946 264.238125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 531.142187 465.838125 \n", "L 531.142187 264.238125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 302.86946 465.838125 \n", "L 531.142187 465.838125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 302.86946 264.238125 \n", "L 531.142187 264.238125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_24\">\n", "    <!-- Extended binned NLL -->\n", "    <g transform=\"translate(352.936136 258.238125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-45\"/>\n", "     <use xlink:href=\"#DejaVuSans-78\" x=\"63.183594\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"122.363281\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"161.572266\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"223.095703\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"286.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"349.951172\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"411.474609\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"474.951172\"/>\n", "     <use xlink:href=\"#DejaVuSans-62\" x=\"506.738281\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"570.214844\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"597.998047\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"661.376953\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"724.755859\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" x=\"786.279297\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"849.755859\"/>\n", "     <use xlink:href=\"#DejaVuSans-4e\" x=\"881.542969\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"956.347656\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"1012.060547\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_22\">\n", "     <path d=\"M 462.042187 302.81625 \n", "L 524.142187 302.81625 \n", "Q 526.142187 302.81625 526.142187 300.81625 \n", "L 526.142187 271.238125 \n", "Q 526.142187 269.238125 524.142187 269.238125 \n", "L 462.042187 269.238125 \n", "Q 460.042187 269.238125 460.042187 271.238125 \n", "L 460.042187 300.81625 \n", "Q 460.042187 302.81625 462.042187 302.81625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"LineCollection_9\">\n", "     <path d=\"M 474.042187 283.538125 \n", "L 474.042187 273.538125 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_157\"/>\n", "    <g id=\"line2d_158\">\n", "     <g>\n", "      <use xlink:href=\"#mad74139515\" x=\"474.042187\" y=\"278.538125\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- $\\sqrt{n}\\,\\Delta\\mu$ -->\n", "     <g transform=\"translate(492.042187 282.038125) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-221a\" d=\"M 3488 5191 \n", "L 4078 5191 \n", "L 4078 4891 \n", "L 3719 4891 \n", "L 1863 -128 \n", "L 1656 -128 \n", "L 659 2631 \n", "L 269 2491 \n", "L 191 2741 \n", "L 1075 3047 \n", "L 1875 831 \n", "L 3488 5191 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-394\" d=\"M 2188 4044 \n", "L 906 525 \n", "L 3472 525 \n", "L 2188 4044 \n", "z\n", "M 50 0 \n", "L 1831 4666 \n", "L 2547 4666 \n", "L 4325 0 \n", "L 50 0 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-Oblique-3bc\" d=\"M -84 -1331 \n", "L 856 3500 \n", "L 1434 3500 \n", "L 1009 1322 \n", "Q 997 1256 987 1175 \n", "Q 978 1094 978 1013 \n", "Q 978 722 1161 565 \n", "Q 1344 409 1684 409 \n", "Q 2147 409 2431 671 \n", "Q 2716 934 2816 1459 \n", "L 3213 3500 \n", "L 3788 3500 \n", "L 3266 809 \n", "Q 3253 750 3248 706 \n", "Q 3244 663 3244 628 \n", "Q 3244 531 3283 486 \n", "Q 3322 441 3406 441 \n", "Q 3438 441 3492 456 \n", "Q 3547 472 3647 513 \n", "L 3559 50 \n", "Q 3422 -19 3297 -55 \n", "Q 3172 -91 3053 -91 \n", "Q 2847 -91 2730 40 \n", "Q 2613 172 2613 403 \n", "Q 2438 153 2195 31 \n", "Q 1953 -91 1625 -91 \n", "Q 1334 -91 1117 43 \n", "Q 900 178 831 397 \n", "L 494 -1331 \n", "L -84 -1331 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-221a\" transform=\"translate(0 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(76.220703 0.75)\"/>\n", "      <use xlink:href=\"#DejaVuSans-394\" transform=\"translate(168.335286 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-3bc\" transform=\"translate(236.743489 0.890625)\"/>\n", "      <path d=\"M 63.720703 75.5 \n", "L 63.720703 81.75 \n", "L 152.099609 81.75 \n", "L 152.099609 75.5 \n", "L 63.720703 75.5 \n", "z\n", "\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"LineCollection_10\">\n", "     <path d=\"M 474.042187 298.236562 \n", "L 474.042187 288.236562 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_159\"/>\n", "    <g id=\"line2d_160\">\n", "     <g>\n", "      <use xlink:href=\"#m87aa2491f5\" x=\"474.042187\" y=\"293.236562\" style=\"fill: #ff7f0e; stroke: #ff7f0e; stroke-linejoin: miter\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_26\">\n", "     <!-- $\\sqrt{n}\\,\\Delta\\sigma$ -->\n", "     <g transform=\"translate(492.042187 296.736562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-Oblique-3c3\" d=\"M 2219 3044 \n", "Q 1744 3044 1422 2700 \n", "Q 1081 2341 969 1747 \n", "Q 844 1119 1044 756 \n", "Q 1241 397 1706 397 \n", "Q 2166 397 2503 759 \n", "Q 2844 1122 2966 1747 \n", "Q 3075 2319 2881 2700 \n", "Q 2700 3044 2219 3044 \n", "z\n", "M 2309 3503 \n", "L 4219 3500 \n", "L 4106 2925 \n", "L 3463 2925 \n", "Q 3706 2438 3575 1747 \n", "Q 3406 888 2884 400 \n", "Q 2359 -91 1609 -91 \n", "Q 856 -91 525 400 \n", "Q 194 888 363 1747 \n", "Q 528 2609 1050 3097 \n", "Q 1484 3503 2309 3503 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-221a\" transform=\"translate(0 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(76.220703 0.75)\"/>\n", "      <use xlink:href=\"#DejaVuSans-394\" transform=\"translate(168.335286 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-3c3\" transform=\"translate(236.743489 0.890625)\"/>\n", "      <path d=\"M 63.720703 75.5 \n", "L 63.720703 81.75 \n", "L 152.099609 81.75 \n", "L 152.099609 75.5 \n", "L 63.720703 75.5 \n", "z\n", "\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p7d93922c16\">\n", "   <rect x=\"28.942188\" y=\"22.318125\" width=\"228.272727\" height=\"201.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pa2abcce74e\">\n", "   <rect x=\"302.86946\" y=\"22.318125\" width=\"228.272727\" height=\"201.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p03715bc5a1\">\n", "   <rect x=\"28.942188\" y=\"264.238125\" width=\"228.272727\" height=\"201.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pdefa37206b\">\n", "   <rect x=\"302.86946\" y=\"264.238125\" width=\"228.272727\" height=\"201.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 900x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(2, 2, figsize=(9, 8), sharex=True, sharey=True)\n", "\n", "plt.sca(ax[0, 0])\n", "plt.title(\"Unbinned NLL\")\n", "plt.errorbar(\n", "    n_pts,\n", "    n_pts**0.5 * unbinned_nll[0],\n", "    np.sqrt(n_pts * unbinned_nll[1]),\n", "    fmt=\"o\",\n", "    label=r\"$\\sqrt{n}\\,\\Delta\\mu$\",\n", ")\n", "plt.errorbar(\n", "    n_pts,\n", "    n_pts**0.5 * unbinned_nll[2],\n", "    np.sqrt(n_pts * unbinned_nll[3]),\n", "    fmt=\"s\",\n", "    label=r\"$\\sqrt{n}\\,\\Delta\\sigma$\",\n", ")\n", "\n", "plt.sca(ax[0, 1])\n", "plt.title(\"Binned NLL\")\n", "plt.errorbar(\n", "    n_pts,\n", "    n_pts**0.5 * binned_nll[0],\n", "    np.sqrt(n_pts * binned_nll[1]),\n", "    fmt=\"o\",\n", "    label=r\"$\\sqrt{n}\\,\\Delta\\mu$\",\n", ")\n", "plt.errorbar(\n", "    n_pts,\n", "    n_pts**0.5 * binned_nll[2],\n", "    np.sqrt(n_pts * binned_nll[3]),\n", "    fmt=\"s\",\n", "    label=r\"$\\sqrt{n}\\,\\Delta\\sigma$\",\n", ")\n", "\n", "plt.sca(ax[1, 0])\n", "plt.title(\"Extended Unbinned NLL\")\n", "plt.errorbar(\n", "    n_pts,\n", "    n_pts**0.5 * extended_unbinned_nll[2],\n", "    np.sqrt(n_pts * extended_unbinned_nll[3]),\n", "    fmt=\"o\",\n", "    label=r\"$\\sqrt{n}\\,\\Delta\\mu$\",\n", ")\n", "plt.errorbar(\n", "    n_pts,\n", "    n_pts**0.5 * extended_unbinned_nll[4],\n", "    np.sqrt(n_pts * extended_unbinned_nll[5]),\n", "    fmt=\"s\",\n", "    label=r\"$\\sqrt{n}\\,\\Delta\\sigma$\",\n", ")\n", "\n", "plt.sca(ax[1, 1])\n", "plt.title(\"Extended binned NLL\")\n", "plt.errorbar(\n", "    n_pts,\n", "    n_pts**0.5 * extended_binned_nll[2],\n", "    np.sqrt(n_pts * extended_binned_nll[3]),\n", "    fmt=\"o\",\n", "    label=r\"$\\sqrt{n}\\,\\Delta\\mu$\",\n", ")\n", "plt.errorbar(\n", "    n_pts,\n", "    n_pts**0.5 * extended_binned_nll[4],\n", "    np.sqrt(n_pts * extended_binned_nll[5]),\n", "    fmt=\"s\",\n", "    label=r\"$\\sqrt{n}\\,\\Delta\\sigma$\",\n", ")\n", "\n", "plt.ylim(-5, 5)\n", "plt.legend()\n", "plt.semilogx()\n", "for i in (0, 1):\n", "    ax[1, i].set_xlabel(r\"$n_\\mathrm{pts}$\")\n", "for axi in ax.flat:\n", "    for y in (-1, 1):\n", "        axi.axhline(y, ls=\":\", color=\"0.5\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Least-squares fits\n", "\n", "We do the same as before, but this time we use a least-squares fit of $x,y$ scattered data and vary the residual function. Other functions than the identity can be used to reduce the pull of large outliers, turning the ordinary least-squares fit into a robust fit."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["n_tries = 100  # increase this to 500 to get less scattering\n", "\n", "truth = Namespace(a=1, b=2)\n", "\n", "n_pts = np.array((10, 30, 100, 300, 1000, 3000, 10000))\n", "\n", "\n", "@joblib.delayed\n", "def compute(n):\n", "    rng = np.random.default_rng(n)\n", "    x = np.linspace(0, 1, n)\n", "\n", "    linear = []\n", "    soft_l1 = []\n", "    arctan = []\n", "    for i_try in range(n_tries):\n", "\n", "        def model(x, a, b):\n", "            return a + b * x\n", "\n", "        while True:\n", "            y = model(x, 1, 2)\n", "            ye = 0.1\n", "            y += rng.normal(0, ye, len(y))\n", "\n", "            m = [\n", "                Minuit(LeastSquares(x, y, ye, model), a=0, b=0),\n", "                Minuit(LeastSquares(x, y, ye, model, loss=\"soft_l1\"), a=0, b=0),\n", "                Minuit(LeastSquares(x, y, ye, model, loss=np.arctan), a=0, b=0),\n", "            ]\n", "\n", "            all_good = True\n", "            for mi in m:\n", "                mi.migrad()\n", "                mi.hesse()\n", "                if not mi.valid or not mi.accurate:\n", "                    all_good = False\n", "                    break\n", "            if all_good:\n", "                break\n", "            print(f\"{n} {i_try} need to re-try {[(mi.valid, mi.accurate) for mi in m]}\")\n", "\n", "        linear.append(\n", "            (\n", "                m[0].values[\"a\"] - truth.a,\n", "                m[0].values[\"b\"] - truth.b,\n", "                m[0].errors[\"a\"] ** 2,\n", "                m[0].errors[\"b\"] ** 2,\n", "            )\n", "        )\n", "        soft_l1.append(\n", "            (\n", "                m[1].values[\"a\"] - truth.a,\n", "                m[1].values[\"b\"] - truth.b,\n", "                m[1].errors[\"a\"] ** 2,\n", "                m[1].errors[\"b\"] ** 2,\n", "            )\n", "        )\n", "        arctan.append(\n", "            (\n", "                m[2].values[\"a\"] - truth.a,\n", "                m[2].values[\"b\"] - truth.b,\n", "                m[2].errors[\"a\"] ** 2,\n", "                m[2].errors[\"b\"] ** 2,\n", "            )\n", "        )\n", "\n", "    return [\n", "        (*np.mean(t, axis=0), *np.var(np.array(t)[:, :2], axis=0))\n", "        for t in (linear, soft_l1, arctan)\n", "    ]\n", "\n", "\n", "linear = []\n", "soft_l1 = []\n", "arctan = []\n", "\n", "for li, so, ar in joblib.Parallel(-1)(compute(n) for n in n_pts):\n", "    linear.append(li)\n", "    soft_l1.append(so)\n", "    arctan.append(ar)\n", "\n", "linear = np.transpose(linear)\n", "soft_l1 = np.transpose(soft_l1)\n", "arctan = np.transpose(arctan)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"564.643905pt\" height=\"544.0755pt\" viewBox=\"0 0 564.**********.0755\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:07:00.399877</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 544.0755 \n", "L 564.**********.0755 \n", "L 564.643905 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 52.**********.3555 \n", "L 199.86682 237.3555 \n", "L 199.86682 35.7555 \n", "L 52.160938 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m79138910ba\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"59.871917\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"104.631207\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"149.390497\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"194.149787\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <defs>\n", "       <path id=\"m0104fef6e4\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"52.938615\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"55.534293\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"57.823844\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"73.345806\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"81.227526\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"86.819695\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"91.157318\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"94.701414\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"97.697905\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"100.293584\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"102.583134\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"118.105096\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"125.986816\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"131.578985\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"135.916608\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"139.460704\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"142.457195\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"145.052874\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"147.342424\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"162.864386\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"170.746106\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"176.338275\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"180.675898\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"184.219995\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"187.216485\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"189.812164\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"192.101714\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_32\">\n", "      <defs>\n", "       <path id=\"me6fd70110a\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −2.0 -->\n", "      <g transform=\"translate(20.878125 241.154719) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"212.1555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(20.878125 215.954719) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"186.9555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(20.878125 190.754719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"161.7555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(20.878125 165.554719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"136.5555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(29.257813 140.354719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"111.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(29.257813 115.154719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"86.1555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257813 89.954719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"60.9555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257813 64.754719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"35.7555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(29.257813 39.554719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- bias and variance -->\n", "     <g transform=\"translate(14.798438 180.89925) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"91.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"152.539062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"204.638672\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"236.425781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"297.705078\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"361.083984\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"424.560547\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"456.347656\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"515.527344\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"576.806641\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"617.919922\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"645.703125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"706.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"770.361328\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"825.341797\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 58.874841 147.492381 \n", "L 58.874841 127.839052 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 80.23045 145.988746 \n", "L 80.23045 124.877699 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 103.634131 144.599005 \n", "L 103.634131 124.815566 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 124.98974 146.548101 \n", "L 124.98974 126.828292 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 148.393421 144.575098 \n", "L 148.393421 124.663652 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 169.74903 147.199927 \n", "L 169.74903 124.263001 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 193.152711 147.721107 \n", "L 193.152711 128.764538 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_2\">\n", "    <path d=\"M 59.07839 152.214073 \n", "L 59.07839 120.847877 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 80.298537 155.779446 \n", "L 80.298537 120.605012 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 103.654582 154.705967 \n", "L 103.654582 120.641471 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 124.996559 150.785281 \n", "L 124.996559 118.366098 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 148.395467 157.779076 \n", "L 148.395467 123.943158 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 169.749712 156.112914 \n", "L 169.749712 119.386596 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 193.152916 150.293065 \n", "L 193.152916 116.519453 \n", "\" clip-path=\"url(#p39f048059d)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_41\">\n", "    <defs>\n", "     <path id=\"m509907cdf2\" d=\"M -3 3 \n", "L 3 3 \n", "L 3 -3 \n", "L -3 -3 \n", "z\n", "\" style=\"stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p39f048059d)\">\n", "     <use xlink:href=\"#m509907cdf2\" x=\"58.874841\" y=\"137.665717\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"80.23045\" y=\"135.433222\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"103.634131\" y=\"134.707285\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"124.98974\" y=\"136.688196\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"148.393421\" y=\"134.619375\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"169.74903\" y=\"135.731464\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"193.152711\" y=\"138.242822\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_42\">\n", "    <defs>\n", "     <path id=\"m0d97358074\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff7f0e\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p39f048059d)\">\n", "     <use xlink:href=\"#m0d97358074\" x=\"59.07839\" y=\"136.530975\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"80.298537\" y=\"138.192229\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"103.654582\" y=\"137.673719\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"124.996559\" y=\"134.57569\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"148.395467\" y=\"140.861117\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"169.749712\" y=\"137.749755\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"193.152916\" y=\"133.406259\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 52.**********.3555 \n", "L 52.160938 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 199.86682 237.3555 \n", "L 199.86682 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 52.**********.3555 \n", "L 199.86682 237.3555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 52.160938 35.7555 \n", "L 199.86682 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_11\">\n", "    <!-- Least-squares -->\n", "    <g transform=\"translate(84.201379 29.7555) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-4c\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 531 \n", "L 3531 531 \n", "L 3531 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-71\" d=\"M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "M 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 -1331 \n", "L 2906 -1331 \n", "L 2906 525 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-4c\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"53.962891\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"115.486328\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"176.765625\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"228.865234\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"268.074219\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"304.158203\"/>\n", "     <use xlink:href=\"#DejaVuSans-71\" x=\"356.257812\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"419.734375\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"483.113281\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"544.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"583.255859\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"644.779297\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 130.76682 73.11175 \n", "L 192.86682 73.11175 \n", "Q 194.86682 73.11175 194.86682 71.11175 \n", "L 194.86682 42.7555 \n", "Q 194.86682 40.7555 192.86682 40.7555 \n", "L 130.76682 40.7555 \n", "Q 128.76682 40.7555 128.76682 42.7555 \n", "L 128.76682 71.11175 \n", "Q 128.76682 73.11175 130.76682 73.11175 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"LineCollection_3\">\n", "     <path d=\"M 142.76682 53.853937 \n", "L 142.76682 43.853937 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_43\"/>\n", "    <g id=\"line2d_44\">\n", "     <g>\n", "      <use xlink:href=\"#m509907cdf2\" x=\"142.76682\" y=\"48.853937\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- $\\sqrt{n}\\,\\Delta a$ -->\n", "     <g transform=\"translate(160.76682 52.353937) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-221a\" d=\"M 3488 5191 \n", "L 4078 5191 \n", "L 4078 4891 \n", "L 3719 4891 \n", "L 1863 -128 \n", "L 1656 -128 \n", "L 659 2631 \n", "L 269 2491 \n", "L 191 2741 \n", "L 1075 3047 \n", "L 1875 831 \n", "L 3488 5191 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-Oblique-6e\" d=\"M 3566 2113 \n", "L 3156 0 \n", "L 2578 0 \n", "L 2988 2091 \n", "Q 3016 2238 3031 2350 \n", "Q 3047 2463 3047 2528 \n", "Q 3047 2791 2881 2937 \n", "Q 2716 3084 2419 3084 \n", "Q 1956 3084 1622 2776 \n", "Q 1288 2469 1184 1941 \n", "L 800 0 \n", "L 225 0 \n", "L 903 3500 \n", "L 1478 3500 \n", "L 1363 2950 \n", "Q 1603 3253 1940 3418 \n", "Q 2278 3584 2650 3584 \n", "Q 3113 3584 3367 3334 \n", "Q 3622 3084 3622 2631 \n", "Q 3622 2519 3608 2391 \n", "Q 3594 2263 3566 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-394\" d=\"M 2188 4044 \n", "L 906 525 \n", "L 3472 525 \n", "L 2188 4044 \n", "z\n", "M 50 0 \n", "L 1831 4666 \n", "L 2547 4666 \n", "L 4325 0 \n", "L 50 0 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-Oblique-61\" d=\"M 3438 1997 \n", "L 3047 0 \n", "L 2472 0 \n", "L 2578 531 \n", "Q 2325 219 2001 64 \n", "Q 1678 -91 1281 -91 \n", "Q 834 -91 548 182 \n", "Q 263 456 263 884 \n", "Q 263 1497 752 1853 \n", "Q 1241 2209 2100 2209 \n", "L 2900 2209 \n", "L 2931 2363 \n", "Q 2938 2388 2941 2417 \n", "Q 2944 2447 2944 2509 \n", "Q 2944 2788 2717 2942 \n", "Q 2491 3097 2081 3097 \n", "Q 1800 3097 1504 3025 \n", "Q 1209 2953 897 2809 \n", "L 997 3341 \n", "Q 1322 3463 1633 3523 \n", "Q 1944 3584 2234 3584 \n", "Q 2853 3584 3176 3315 \n", "Q 3500 3047 3500 2534 \n", "Q 3500 2431 3484 2292 \n", "Q 3469 2153 3438 1997 \n", "z\n", "M 2816 1759 \n", "L 2241 1759 \n", "Q 1534 1759 1195 1570 \n", "Q 856 1381 856 984 \n", "Q 856 709 1029 553 \n", "Q 1203 397 1509 397 \n", "Q 1978 397 2328 733 \n", "Q 2678 1069 2791 1631 \n", "L 2816 1759 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-221a\" transform=\"translate(0 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(76.220703 0.75)\"/>\n", "      <use xlink:href=\"#DejaVuSans-394\" transform=\"translate(168.335286 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-61\" transform=\"translate(236.743489 0.890625)\"/>\n", "      <path d=\"M 63.720703 75.5 \n", "L 63.720703 81.75 \n", "L 152.099609 81.75 \n", "L 152.099609 75.5 \n", "L 63.720703 75.5 \n", "z\n", "\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"LineCollection_4\">\n", "     <path d=\"M 142.76682 68.532062 \n", "L 142.76682 58.532062 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_45\"/>\n", "    <g id=\"line2d_46\">\n", "     <g>\n", "      <use xlink:href=\"#m0d97358074\" x=\"142.76682\" y=\"63.532062\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- $\\sqrt{n}\\,\\Delta b$ -->\n", "     <g transform=\"translate(160.76682 67.032062) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-Oblique-62\" d=\"M 3169 2138 \n", "Q 3169 2591 2961 2847 \n", "Q 2753 3103 2388 3103 \n", "Q 2122 3103 1889 2973 \n", "Q 1656 2844 1484 2597 \n", "Q 1303 2338 1198 1995 \n", "Q 1094 1653 1094 1313 \n", "Q 1094 881 1298 636 \n", "Q 1503 391 1863 391 \n", "Q 2134 391 2365 517 \n", "Q 2597 644 2772 891 \n", "Q 2950 1147 3059 1487 \n", "Q 3169 1828 3169 2138 \n", "z\n", "M 1381 2969 \n", "Q 1594 3256 1914 3420 \n", "Q 2234 3584 2584 3584 \n", "Q 3122 3584 3439 3221 \n", "Q 3756 2859 3756 2241 \n", "Q 3756 1734 3570 1259 \n", "Q 3384 784 3041 416 \n", "Q 2816 172 2522 40 \n", "Q 2228 -91 1906 -91 \n", "Q 1566 -91 1316 65 \n", "Q 1066 222 909 531 \n", "L 806 0 \n", "L 231 0 \n", "L 1178 4863 \n", "L 1753 4863 \n", "L 1381 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-221a\" transform=\"translate(0 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(76.220703 0.75)\"/>\n", "      <use xlink:href=\"#DejaVuSans-394\" transform=\"translate(168.335286 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-62\" transform=\"translate(236.743489 0.890625)\"/>\n", "      <path d=\"M 63.720703 75.5 \n", "L 63.720703 81.75 \n", "L 152.099609 81.75 \n", "L 152.099609 75.5 \n", "L 63.720703 75.5 \n", "z\n", "\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 229.407996 237.3555 \n", "L 377.113879 237.3555 \n", "L 377.113879 35.7555 \n", "L 229.407996 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"237.118976\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"281.878266\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"326.637556\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"371.396846\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"230.185674\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_37\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"232.781352\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_38\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"235.070903\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_39\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"250.592865\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_40\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"258.474584\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_41\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"264.066753\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_42\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"268.404377\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_43\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"271.948473\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_44\">\n", "     <g id=\"line2d_59\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"274.944964\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_45\">\n", "     <g id=\"line2d_60\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"277.540642\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_46\">\n", "     <g id=\"line2d_61\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"279.830193\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_47\">\n", "     <g id=\"line2d_62\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"295.352155\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_48\">\n", "     <g id=\"line2d_63\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"303.233874\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_49\">\n", "     <g id=\"line2d_64\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"308.826044\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_50\">\n", "     <g id=\"line2d_65\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"313.163667\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_51\">\n", "     <g id=\"line2d_66\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"316.707763\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_52\">\n", "     <g id=\"line2d_67\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"319.704254\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_53\">\n", "     <g id=\"line2d_68\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"322.299932\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_54\">\n", "     <g id=\"line2d_69\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"324.589483\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_55\">\n", "     <g id=\"line2d_70\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"340.111445\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_56\">\n", "     <g id=\"line2d_71\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"347.993164\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_57\">\n", "     <g id=\"line2d_72\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"353.585334\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_58\">\n", "     <g id=\"line2d_73\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"357.922957\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_59\">\n", "     <g id=\"line2d_74\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"361.467053\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_60\">\n", "     <g id=\"line2d_75\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"364.463544\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_61\">\n", "     <g id=\"line2d_76\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"367.059223\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_62\">\n", "     <g id=\"line2d_77\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"369.348773\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_78\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- −2.0 -->\n", "      <g transform=\"translate(198.125184 241.154719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_79\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"212.1555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(198.125184 215.954719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_80\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"186.9555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(198.125184 190.754719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_81\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"161.7555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(198.125184 165.554719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_82\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"136.5555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(206.504871 140.354719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_83\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"111.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(206.504871 115.154719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_84\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"86.1555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(206.504871 89.954719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_85\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"60.9555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(206.504871 64.754719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_18\">\n", "     <g id=\"line2d_86\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"35.7555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(206.504871 39.554719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_5\">\n", "    <path d=\"M 236.1219 147.562432 \n", "L 236.1219 127.779281 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 257.477509 146.584665 \n", "L 257.477509 124.32921 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 280.88119 145.260763 \n", "L 280.88119 123.955314 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 302.236799 147.025416 \n", "L 302.236799 126.744331 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 325.64048 145.058625 \n", "L 325.64048 124.920434 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 346.996089 147.668972 \n", "L 346.996089 124.547374 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 370.39977 148.363176 \n", "L 370.39977 128.544228 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_6\">\n", "    <path d=\"M 236.325449 152.52667 \n", "L 236.325449 120.633057 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 257.545595 156.419978 \n", "L 257.545595 119.406322 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 280.901641 155.904319 \n", "L 280.901641 119.611853 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 302.243618 150.907688 \n", "L 302.243618 117.260016 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 325.642526 157.910828 \n", "L 325.642526 123.55754 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 346.996771 156.324019 \n", "L 346.996771 118.017258 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 370.399975 150.737891 \n", "L 370.399975 115.399869 \n", "\" clip-path=\"url(#p8effb6a8b0)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_87\">\n", "    <g clip-path=\"url(#p8effb6a8b0)\">\n", "     <use xlink:href=\"#m509907cdf2\" x=\"236.1219\" y=\"137.670856\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"257.477509\" y=\"135.456938\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"280.88119\" y=\"134.608038\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"302.236799\" y=\"136.884873\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"325.64048\" y=\"134.98953\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"346.996089\" y=\"136.108173\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"370.39977\" y=\"138.453702\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_88\">\n", "    <g clip-path=\"url(#p8effb6a8b0)\">\n", "     <use xlink:href=\"#m0d97358074\" x=\"236.325449\" y=\"136.579863\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"257.545595\" y=\"137.91315\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"280.901641\" y=\"137.758086\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"302.243618\" y=\"134.083852\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"325.642526\" y=\"140.734184\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"346.996771\" y=\"137.170638\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"370.399975\" y=\"133.06888\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 229.407996 237.3555 \n", "L 229.407996 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 377.113879 237.3555 \n", "L 377.113879 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 229.407996 237.3555 \n", "L 377.113879 237.3555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 229.407996 35.7555 \n", "L 377.113879 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_23\">\n", "    <!-- Least-squares -->\n", "    <g transform=\"translate(261.448438 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-4c\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"53.962891\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"115.486328\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"176.765625\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"228.865234\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"268.074219\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"304.158203\"/>\n", "     <use xlink:href=\"#DejaVuSans-71\" x=\"356.257812\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"419.734375\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"483.113281\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"544.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"583.255859\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"644.779297\"/>\n", "    </g>\n", "    <!-- with soft L1 norm -->\n", "    <g transform=\"translate(250.815313 29.7555) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-77\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"81.787109\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"109.570312\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"148.779297\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"212.158203\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"243.945312\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"296.044922\"/>\n", "     <use xlink:href=\"#DejaVuSans-66\" x=\"357.226562\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"390.681641\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"429.890625\"/>\n", "     <use xlink:href=\"#DejaVuSans-4c\" x=\"461.677734\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" x=\"517.390625\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"581.013672\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"612.800781\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"676.179688\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"737.361328\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"776.724609\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_2\">\n", "    <g id=\"patch_13\">\n", "     <path d=\"M 308.013879 73.11175 \n", "L 370.113879 73.11175 \n", "Q 372.113879 73.11175 372.113879 71.11175 \n", "L 372.113879 42.7555 \n", "Q 372.113879 40.7555 370.113879 40.7555 \n", "L 308.013879 40.7555 \n", "Q 306.013879 40.7555 306.013879 42.7555 \n", "L 306.013879 71.11175 \n", "Q 306.013879 73.11175 308.013879 73.11175 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"LineCollection_7\">\n", "     <path d=\"M 320.013879 53.853937 \n", "L 320.013879 43.853937 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_89\"/>\n", "    <g id=\"line2d_90\">\n", "     <g>\n", "      <use xlink:href=\"#m509907cdf2\" x=\"320.013879\" y=\"48.853937\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_24\">\n", "     <!-- $\\sqrt{n}\\,\\Delta a$ -->\n", "     <g transform=\"translate(338.013879 52.353937) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-221a\" transform=\"translate(0 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(76.220703 0.75)\"/>\n", "      <use xlink:href=\"#DejaVuSans-394\" transform=\"translate(168.335286 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-61\" transform=\"translate(236.743489 0.890625)\"/>\n", "      <path d=\"M 63.720703 75.5 \n", "L 63.720703 81.75 \n", "L 152.099609 81.75 \n", "L 152.099609 75.5 \n", "L 63.720703 75.5 \n", "z\n", "\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"LineCollection_8\">\n", "     <path d=\"M 320.013879 68.532062 \n", "L 320.013879 58.532062 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_91\"/>\n", "    <g id=\"line2d_92\">\n", "     <g>\n", "      <use xlink:href=\"#m0d97358074\" x=\"320.013879\" y=\"63.532062\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- $\\sqrt{n}\\,\\Delta b$ -->\n", "     <g transform=\"translate(338.013879 67.032062) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-221a\" transform=\"translate(0 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(76.220703 0.75)\"/>\n", "      <use xlink:href=\"#DejaVuSans-394\" transform=\"translate(168.335286 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-62\" transform=\"translate(236.743489 0.890625)\"/>\n", "      <path d=\"M 63.720703 75.5 \n", "L 63.720703 81.75 \n", "L 152.099609 81.75 \n", "L 152.099609 75.5 \n", "L 63.720703 75.5 \n", "z\n", "\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 406.655055 237.3555 \n", "L 554.360938 237.3555 \n", "L 554.360938 35.7555 \n", "L 406.655055 35.7555 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_63\">\n", "     <g id=\"line2d_93\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"414.366035\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_64\">\n", "     <g id=\"line2d_94\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"459.125325\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_65\">\n", "     <g id=\"line2d_95\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"503.884615\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_66\">\n", "     <g id=\"line2d_96\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"548.643905\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_67\">\n", "     <g id=\"line2d_97\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"407.432733\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_68\">\n", "     <g id=\"line2d_98\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"410.028411\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_69\">\n", "     <g id=\"line2d_99\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"412.317962\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_70\">\n", "     <g id=\"line2d_100\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"427.839923\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_71\">\n", "     <g id=\"line2d_101\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"435.721643\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_72\">\n", "     <g id=\"line2d_102\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"441.313812\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_73\">\n", "     <g id=\"line2d_103\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"445.651436\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_74\">\n", "     <g id=\"line2d_104\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"449.195532\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_75\">\n", "     <g id=\"line2d_105\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"452.192023\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_76\">\n", "     <g id=\"line2d_106\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"454.787701\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_77\">\n", "     <g id=\"line2d_107\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"457.077252\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_78\">\n", "     <g id=\"line2d_108\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"472.599213\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_79\">\n", "     <g id=\"line2d_109\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"480.480933\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_80\">\n", "     <g id=\"line2d_110\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"486.073102\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_81\">\n", "     <g id=\"line2d_111\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"490.410726\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_82\">\n", "     <g id=\"line2d_112\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"493.954822\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_83\">\n", "     <g id=\"line2d_113\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"496.951313\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_84\">\n", "     <g id=\"line2d_114\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"499.546991\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_85\">\n", "     <g id=\"line2d_115\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"501.836542\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_86\">\n", "     <g id=\"line2d_116\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"517.358504\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_87\">\n", "     <g id=\"line2d_117\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"525.240223\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_88\">\n", "     <g id=\"line2d_118\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"530.832392\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_89\">\n", "     <g id=\"line2d_119\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"535.170016\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_90\">\n", "     <g id=\"line2d_120\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"538.714112\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_91\">\n", "     <g id=\"line2d_121\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"541.710603\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_92\">\n", "     <g id=\"line2d_122\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"544.306281\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_93\">\n", "     <g id=\"line2d_123\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"546.595832\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_19\">\n", "     <g id=\"line2d_124\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"237.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- −2.0 -->\n", "      <g transform=\"translate(375.372243 241.154719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_20\">\n", "     <g id=\"line2d_125\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"212.1555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- −1.5 -->\n", "      <g transform=\"translate(375.372243 215.954719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_21\">\n", "     <g id=\"line2d_126\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"186.9555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- −1.0 -->\n", "      <g transform=\"translate(375.372243 190.754719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_22\">\n", "     <g id=\"line2d_127\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"161.7555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- −0.5 -->\n", "      <g transform=\"translate(375.372243 165.554719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_23\">\n", "     <g id=\"line2d_128\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"136.5555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(383.75193 140.354719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_24\">\n", "     <g id=\"line2d_129\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"111.3555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_31\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(383.75193 115.154719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_25\">\n", "     <g id=\"line2d_130\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"86.1555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_32\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(383.75193 89.954719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_26\">\n", "     <g id=\"line2d_131\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"60.9555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_33\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(383.75193 64.754719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_27\">\n", "     <g id=\"line2d_132\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"35.7555\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_34\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(383.75193 39.554719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_9\">\n", "    <path d=\"M 413.368959 185.912449 \n", "L 413.368959 47.336147 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 434.724568 150.487904 \n", "L 434.724568 120.616527 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 458.128249 149.553634 \n", "L 458.128249 119.644839 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 479.483858 151.436334 \n", "L 479.483858 123.363595 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 502.887539 148.813544 \n", "L 502.887539 122.642146 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 524.243148 151.647904 \n", "L 524.243148 122.020977 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 547.646829 153.05965 \n", "L 547.646829 125.010989 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_10\">\n", "    <path d=\"M 413.572507 310.055692 \n", "L 413.572507 68.548532 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 434.792654 163.732983 \n", "L 434.792654 112.940191 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 458.1487 163.622065 \n", "L 458.1487 111.560764 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 479.490677 156.63279 \n", "L 479.490677 109.71007 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 502.889585 162.778194 \n", "L 502.889585 118.562243 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 524.24383 162.704879 \n", "L 524.24383 108.937287 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    <path d=\"M 547.647034 157.448243 \n", "L 547.647034 107.32733 \n", "\" clip-path=\"url(#p143815fb5e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_133\">\n", "    <g clip-path=\"url(#p143815fb5e)\">\n", "     <use xlink:href=\"#m509907cdf2\" x=\"413.368959\" y=\"116.624298\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"434.724568\" y=\"135.552215\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"458.128249\" y=\"134.599236\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"479.483858\" y=\"137.399965\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"502.887539\" y=\"135.727845\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"524.243148\" y=\"136.83444\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"547.646829\" y=\"139.03532\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_134\">\n", "    <g clip-path=\"url(#p143815fb5e)\">\n", "     <use xlink:href=\"#m0d97358074\" x=\"413.572507\" y=\"189.302112\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"434.792654\" y=\"138.336587\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"458.1487\" y=\"137.591414\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"479.490677\" y=\"133.17143\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"502.889585\" y=\"140.670219\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"524.24383\" y=\"135.821083\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"547.647034\" y=\"132.387787\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 406.655055 237.3555 \n", "L 406.655055 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 554.360938 237.3555 \n", "L 554.360938 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 406.655055 237.3555 \n", "L 554.360938 237.3555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 406.655055 35.7555 \n", "L 554.360938 35.7555 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_35\">\n", "    <!-- Least-squares -->\n", "    <g transform=\"translate(438.695496 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-4c\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"53.962891\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"115.486328\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"176.765625\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"228.865234\"/>\n", "     <use xlink:href=\"#DejaVuSans-2d\" x=\"268.074219\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"304.158203\"/>\n", "     <use xlink:href=\"#DejaVuSans-71\" x=\"356.257812\"/>\n", "     <use xlink:href=\"#DejaVuSans-75\" x=\"419.734375\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"483.113281\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"544.392578\"/>\n", "     <use xlink:href=\"#DejaVuSans-65\" x=\"583.255859\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"644.779297\"/>\n", "    </g>\n", "    <!-- with arctan norm -->\n", "    <g transform=\"translate(429.147059 29.7555) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-77\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"81.787109\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"109.570312\"/>\n", "     <use xlink:href=\"#DejaVuSans-68\" x=\"148.779297\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"212.158203\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"243.945312\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"305.224609\"/>\n", "     <use xlink:href=\"#DejaVuSans-63\" x=\"344.087891\"/>\n", "     <use xlink:href=\"#DejaVuSans-74\" x=\"399.068359\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"438.277344\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"499.556641\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"562.935547\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"594.722656\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" x=\"658.101562\"/>\n", "     <use xlink:href=\"#DejaVuSans-72\" x=\"719.283203\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"758.646484\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_3\">\n", "    <g id=\"patch_19\">\n", "     <path d=\"M 485.260938 73.11175 \n", "L 547.360938 73.11175 \n", "Q 549.360938 73.11175 549.360938 71.11175 \n", "L 549.360938 42.7555 \n", "Q 549.360938 40.7555 547.360938 40.7555 \n", "L 485.260938 40.7555 \n", "Q 483.260938 40.7555 483.260938 42.7555 \n", "L 483.260938 71.11175 \n", "Q 483.260938 73.11175 485.260938 73.11175 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"LineCollection_11\">\n", "     <path d=\"M 497.260938 53.853937 \n", "L 497.260938 43.853937 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_135\"/>\n", "    <g id=\"line2d_136\">\n", "     <g>\n", "      <use xlink:href=\"#m509907cdf2\" x=\"497.260938\" y=\"48.853937\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_36\">\n", "     <!-- $\\sqrt{n}\\,\\Delta a$ -->\n", "     <g transform=\"translate(515.260938 52.353937) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-221a\" transform=\"translate(0 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(76.220703 0.75)\"/>\n", "      <use xlink:href=\"#DejaVuSans-394\" transform=\"translate(168.335286 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-61\" transform=\"translate(236.743489 0.890625)\"/>\n", "      <path d=\"M 63.720703 75.5 \n", "L 63.720703 81.75 \n", "L 152.099609 81.75 \n", "L 152.099609 75.5 \n", "L 63.720703 75.5 \n", "z\n", "\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"LineCollection_12\">\n", "     <path d=\"M 497.260938 68.532062 \n", "L 497.260938 58.532062 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_137\"/>\n", "    <g id=\"line2d_138\">\n", "     <g>\n", "      <use xlink:href=\"#m0d97358074\" x=\"497.260938\" y=\"63.532062\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_37\">\n", "     <!-- $\\sqrt{n}\\,\\Delta b$ -->\n", "     <g transform=\"translate(515.260938 67.032062) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-221a\" transform=\"translate(0 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(76.220703 0.75)\"/>\n", "      <use xlink:href=\"#DejaVuSans-394\" transform=\"translate(168.335286 0.890625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-62\" transform=\"translate(236.743489 0.890625)\"/>\n", "      <path d=\"M 63.720703 75.5 \n", "L 63.720703 81.75 \n", "L 152.099609 81.75 \n", "L 152.099609 75.5 \n", "L 63.720703 75.5 \n", "z\n", "\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_4\">\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 52.160938 479.2755 \n", "L 199.86682 479.2755 \n", "L 199.86682 277.6755 \n", "L 52.160938 277.6755 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_7\">\n", "    <g id=\"xtick_94\">\n", "     <g id=\"line2d_139\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"59.871917\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_38\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(51.071917 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_95\">\n", "     <g id=\"line2d_140\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"104.631207\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_39\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(95.831207 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_96\">\n", "     <g id=\"line2d_141\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"149.390497\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_40\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(140.590497 493.873937) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_97\">\n", "     <g id=\"line2d_142\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"194.149787\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_41\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(185.349787 493.873937) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_98\">\n", "     <g id=\"line2d_143\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"52.938615\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_99\">\n", "     <g id=\"line2d_144\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"55.534293\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_100\">\n", "     <g id=\"line2d_145\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"57.823844\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_101\">\n", "     <g id=\"line2d_146\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"73.345806\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_102\">\n", "     <g id=\"line2d_147\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"81.227526\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_103\">\n", "     <g id=\"line2d_148\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"86.819695\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_104\">\n", "     <g id=\"line2d_149\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"91.157318\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_105\">\n", "     <g id=\"line2d_150\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"94.701414\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_106\">\n", "     <g id=\"line2d_151\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"97.697905\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_107\">\n", "     <g id=\"line2d_152\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"100.293584\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_108\">\n", "     <g id=\"line2d_153\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"102.583134\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_109\">\n", "     <g id=\"line2d_154\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"118.105096\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_110\">\n", "     <g id=\"line2d_155\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"125.986816\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_111\">\n", "     <g id=\"line2d_156\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"131.578985\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_112\">\n", "     <g id=\"line2d_157\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"135.916608\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_113\">\n", "     <g id=\"line2d_158\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"139.460704\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_114\">\n", "     <g id=\"line2d_159\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"142.457195\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_115\">\n", "     <g id=\"line2d_160\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"145.052874\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_116\">\n", "     <g id=\"line2d_161\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"147.342424\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_117\">\n", "     <g id=\"line2d_162\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"162.864386\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_118\">\n", "     <g id=\"line2d_163\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"170.746106\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_119\">\n", "     <g id=\"line2d_164\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"176.338275\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_120\">\n", "     <g id=\"line2d_165\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"180.675898\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_121\">\n", "     <g id=\"line2d_166\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"184.219995\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_122\">\n", "     <g id=\"line2d_167\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"187.216485\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_123\">\n", "     <g id=\"line2d_168\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"189.812164\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_124\">\n", "     <g id=\"line2d_169\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"192.101714\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_8\">\n", "    <g id=\"ytick_28\">\n", "     <g id=\"line2d_170\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"452.979848\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_42\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(29.257813 456.779067) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_29\">\n", "     <g id=\"line2d_171\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"409.153761\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_43\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(29.257813 412.95298) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_30\">\n", "     <g id=\"line2d_172\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"365.327674\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_44\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(29.257813 369.126893) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_31\">\n", "     <g id=\"line2d_173\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"321.501587\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_45\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(29.257813 325.300806) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_32\">\n", "     <g id=\"line2d_174\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"52.160938\" y=\"277.6755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_46\">\n", "      <!-- 3.0 -->\n", "      <g transform=\"translate(29.257813 281.474719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_47\">\n", "     <!-- estimated variance / true variance -->\n", "     <g transform=\"translate(23.178125 465.005969) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2f\" d=\"M 1625 4666 \n", "L 2156 4666 \n", "L 531 -594 \n", "L 0 -594 \n", "L 1625 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"113.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"152.832031\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"180.615234\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"278.027344\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"339.306641\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"378.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"440.039062\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"503.515625\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"535.302734\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"594.482422\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"655.761719\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"696.875\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"724.658203\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"785.9375\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"849.316406\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"904.296875\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"965.820312\"/>\n", "      <use xlink:href=\"#DejaVuSans-2f\" x=\"997.607422\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"1031.298828\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"1063.085938\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"1102.294922\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"1143.408203\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1206.787109\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"1268.310547\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"1300.097656\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1359.277344\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"1420.556641\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"1461.669922\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"1489.453125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"1550.732422\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"1614.111328\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"1669.091797\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_175\">\n", "    <g clip-path=\"url(#p368aa8f455)\">\n", "     <use xlink:href=\"#m509907cdf2\" x=\"58.874841\" y=\"460.978962\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"80.23045\" y=\"464.567083\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"103.634131\" y=\"450.96311\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"124.98974\" y=\"449.479508\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"148.393421\" y=\"450.912524\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"169.74903\" y=\"472.95267\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"193.152711\" y=\"441.511906\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_176\">\n", "    <g clip-path=\"url(#p368aa8f455)\">\n", "     <use xlink:href=\"#m0d97358074\" x=\"59.07839\" y=\"451.754658\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"80.298537\" y=\"459.82538\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"103.654582\" y=\"450.355478\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"124.996559\" y=\"439.62175\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"148.395467\" y=\"447.469669\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"169.749712\" y=\"461.451077\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"193.152916\" y=\"446.956519\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_177\">\n", "    <path d=\"M 52.160938 452.979848 \n", "L 199.86682 452.979848 \n", "\" clip-path=\"url(#p368aa8f455)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 52.160938 479.2755 \n", "L 52.160938 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 199.86682 479.2755 \n", "L 199.86682 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 52.160938 479.2755 \n", "L 199.86682 479.2755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 52.160938 277.6755 \n", "L 199.86682 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_5\">\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 229.407996 479.2755 \n", "L 377.113879 479.2755 \n", "L 377.113879 277.6755 \n", "L 229.407996 277.6755 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_9\">\n", "    <g id=\"xtick_125\">\n", "     <g id=\"line2d_178\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"237.118976\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_48\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(228.318976 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_126\">\n", "     <g id=\"line2d_179\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"281.878266\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_49\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(273.078266 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_127\">\n", "     <g id=\"line2d_180\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"326.637556\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_50\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(317.837556 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_128\">\n", "     <g id=\"line2d_181\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"371.396846\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_51\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(362.596846 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_129\">\n", "     <g id=\"line2d_182\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"230.185674\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_130\">\n", "     <g id=\"line2d_183\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"232.781352\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_131\">\n", "     <g id=\"line2d_184\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"235.070903\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_132\">\n", "     <g id=\"line2d_185\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"250.592865\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_133\">\n", "     <g id=\"line2d_186\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"258.474584\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_134\">\n", "     <g id=\"line2d_187\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"264.066753\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_135\">\n", "     <g id=\"line2d_188\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"268.404377\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_136\">\n", "     <g id=\"line2d_189\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"271.948473\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_137\">\n", "     <g id=\"line2d_190\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"274.944964\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_138\">\n", "     <g id=\"line2d_191\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"277.540642\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_139\">\n", "     <g id=\"line2d_192\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"279.830193\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_140\">\n", "     <g id=\"line2d_193\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"295.352155\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_141\">\n", "     <g id=\"line2d_194\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"303.233874\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_142\">\n", "     <g id=\"line2d_195\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"308.826044\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_143\">\n", "     <g id=\"line2d_196\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"313.163667\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_144\">\n", "     <g id=\"line2d_197\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"316.707763\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_145\">\n", "     <g id=\"line2d_198\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"319.704254\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_146\">\n", "     <g id=\"line2d_199\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"322.299932\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_147\">\n", "     <g id=\"line2d_200\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"324.589483\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_148\">\n", "     <g id=\"line2d_201\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"340.111445\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_149\">\n", "     <g id=\"line2d_202\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"347.993164\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_150\">\n", "     <g id=\"line2d_203\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"353.585334\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_151\">\n", "     <g id=\"line2d_204\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"357.922957\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_152\">\n", "     <g id=\"line2d_205\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"361.467053\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_153\">\n", "     <g id=\"line2d_206\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"364.463544\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_154\">\n", "     <g id=\"line2d_207\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"367.059223\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_155\">\n", "     <g id=\"line2d_208\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"369.348773\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_10\">\n", "    <g id=\"ytick_33\">\n", "     <g id=\"line2d_209\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"452.979848\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_52\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(206.504871 456.779067) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_34\">\n", "     <g id=\"line2d_210\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"409.153761\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_53\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(206.504871 412.95298) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_35\">\n", "     <g id=\"line2d_211\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"365.327674\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_54\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(206.504871 369.126893) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_36\">\n", "     <g id=\"line2d_212\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"321.501587\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_55\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(206.504871 325.300806) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_37\">\n", "     <g id=\"line2d_213\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"229.407996\" y=\"277.6755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_56\">\n", "      <!-- 3.0 -->\n", "      <g transform=\"translate(206.504871 281.474719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_214\">\n", "    <g clip-path=\"url(#pc259c8e592)\">\n", "     <use xlink:href=\"#m509907cdf2\" x=\"236.1219\" y=\"410.5487\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"257.477509\" y=\"422.10227\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"280.88119\" y=\"405.032316\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"302.236799\" y=\"388.518479\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"325.64048\" y=\"385.446998\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"346.996089\" y=\"422.894132\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"370.39977\" y=\"380.146234\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_215\">\n", "    <g clip-path=\"url(#pc259c8e592)\">\n", "     <use xlink:href=\"#m0d97358074\" x=\"236.325449\" y=\"399.099824\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"257.545595\" y=\"414.581542\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"280.901641\" y=\"400.58621\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"302.243618\" y=\"375.451168\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"325.642526\" y=\"380.939698\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"346.996771\" y=\"411.956057\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"370.399975\" y=\"389.162816\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_216\">\n", "    <path d=\"M 229.407996 452.979848 \n", "L 377.113879 452.979848 \n", "\" clip-path=\"url(#pc259c8e592)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 229.407996 479.2755 \n", "L 229.407996 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 377.113879 479.2755 \n", "L 377.113879 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 229.407996 479.2755 \n", "L 377.113879 479.2755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 229.407996 277.6755 \n", "L 377.113879 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_6\">\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 406.655055 479.2755 \n", "L 554.360938 479.2755 \n", "L 554.360938 277.6755 \n", "L 406.655055 277.6755 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_11\">\n", "    <g id=\"xtick_156\">\n", "     <g id=\"line2d_217\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"414.366035\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_57\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(405.566035 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_157\">\n", "     <g id=\"line2d_218\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"459.125325\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_58\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(450.325325 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_158\">\n", "     <g id=\"line2d_219\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"503.884615\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_59\">\n", "      <!-- $\\mathdefault{10^{3}}$ -->\n", "      <g transform=\"translate(495.084615 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_159\">\n", "     <g id=\"line2d_220\">\n", "      <g>\n", "       <use xlink:href=\"#m79138910ba\" x=\"548.643905\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_60\">\n", "      <!-- $\\mathdefault{10^{4}}$ -->\n", "      <g transform=\"translate(539.843905 493.873937) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_160\">\n", "     <g id=\"line2d_221\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"407.432733\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_161\">\n", "     <g id=\"line2d_222\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"410.028411\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_162\">\n", "     <g id=\"line2d_223\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"412.317962\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_163\">\n", "     <g id=\"line2d_224\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"427.839923\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_164\">\n", "     <g id=\"line2d_225\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"435.721643\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_165\">\n", "     <g id=\"line2d_226\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"441.313812\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_166\">\n", "     <g id=\"line2d_227\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"445.651436\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_167\">\n", "     <g id=\"line2d_228\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"449.195532\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_168\">\n", "     <g id=\"line2d_229\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"452.192023\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_169\">\n", "     <g id=\"line2d_230\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"454.787701\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_170\">\n", "     <g id=\"line2d_231\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"457.077252\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_171\">\n", "     <g id=\"line2d_232\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"472.599213\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_172\">\n", "     <g id=\"line2d_233\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"480.480933\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_173\">\n", "     <g id=\"line2d_234\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"486.073102\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_174\">\n", "     <g id=\"line2d_235\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"490.410726\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_175\">\n", "     <g id=\"line2d_236\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"493.954822\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_176\">\n", "     <g id=\"line2d_237\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"496.951313\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_177\">\n", "     <g id=\"line2d_238\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"499.546991\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_178\">\n", "     <g id=\"line2d_239\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"501.836542\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_179\">\n", "     <g id=\"line2d_240\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"517.358504\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_180\">\n", "     <g id=\"line2d_241\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"525.240223\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_181\">\n", "     <g id=\"line2d_242\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"530.832392\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_182\">\n", "     <g id=\"line2d_243\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"535.170016\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_183\">\n", "     <g id=\"line2d_244\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"538.714112\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_184\">\n", "     <g id=\"line2d_245\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"541.710603\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_185\">\n", "     <g id=\"line2d_246\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"544.306281\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_186\">\n", "     <g id=\"line2d_247\">\n", "      <g>\n", "       <use xlink:href=\"#m0104fef6e4\" x=\"546.595832\" y=\"479.2755\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_12\">\n", "    <g id=\"ytick_38\">\n", "     <g id=\"line2d_248\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"452.979848\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_61\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(383.75193 456.779067) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_39\">\n", "     <g id=\"line2d_249\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"409.153761\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_62\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(383.75193 412.95298) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_40\">\n", "     <g id=\"line2d_250\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"365.327674\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_63\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(383.75193 369.126893) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_41\">\n", "     <g id=\"line2d_251\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"321.501587\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_64\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(383.75193 325.300806) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_42\">\n", "     <g id=\"line2d_252\">\n", "      <g>\n", "       <use xlink:href=\"#me6fd70110a\" x=\"406.655055\" y=\"277.6755\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_65\">\n", "      <!-- 3.0 -->\n", "      <g transform=\"translate(383.75193 281.474719) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_253\">\n", "    <g clip-path=\"url(#pd62f9bcf1a)\">\n", "     <use xlink:href=\"#m509907cdf2\" x=\"413.368959\" y=\"485.71365\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"434.724568\" y=\"418.246269\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"458.128249\" y=\"395.565599\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"479.483858\" y=\"370.196891\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"502.887539\" y=\"345.097905\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"524.243148\" y=\"390.182213\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "     <use xlink:href=\"#m509907cdf2\" x=\"547.646829\" y=\"371.724892\" style=\"fill: #1f77b4; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_254\">\n", "    <g clip-path=\"url(#pd62f9bcf1a)\">\n", "     <use xlink:href=\"#m0d97358074\" x=\"413.572507\" y=\"489.350552\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"434.792654\" y=\"414.579729\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"458.1487\" y=\"396.521444\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"479.490677\" y=\"359.511615\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"502.889585\" y=\"336.225347\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"524.24383\" y=\"403.508945\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "     <use xlink:href=\"#m0d97358074\" x=\"547.647034\" y=\"381.823923\" style=\"fill: #ff7f0e; stroke: #ff7f0e\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_255\">\n", "    <path d=\"M 406.655055 452.979848 \n", "L 554.360938 452.979848 \n", "\" clip-path=\"url(#pd62f9bcf1a)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 406.655055 479.2755 \n", "L 406.655055 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 554.360938 479.2755 \n", "L 554.360938 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 406.655055 479.2755 \n", "L 554.360938 479.2755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 406.655055 277.6755 \n", "L 554.360938 277.6755 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"text_66\">\n", "   <!-- $n_\\mathrm{pts}$ -->\n", "   <g transform=\"translate(284.660938 533.1555) scale(0.12 -0.12)\">\n", "    <defs>\n", "     <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "    </defs>\n", "    <use xlink:href=\"#DejaVuSans-Oblique-6e\"/>\n", "    <use xlink:href=\"#DejaVuSans-70\" transform=\"translate(63.378906 -16.40625) scale(0.7)\"/>\n", "    <use xlink:href=\"#DejaVuSans-74\" transform=\"translate(107.8125 -16.40625) scale(0.7)\"/>\n", "    <use xlink:href=\"#DejaVuSans-73\" transform=\"translate(135.258789 -16.40625) scale(0.7)\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p39f048059d\">\n", "   <rect x=\"52.160938\" y=\"35.7555\" width=\"147.705882\" height=\"201.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p8effb6a8b0\">\n", "   <rect x=\"229.407996\" y=\"35.7555\" width=\"147.705882\" height=\"201.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p143815fb5e\">\n", "   <rect x=\"406.655055\" y=\"35.7555\" width=\"147.705882\" height=\"201.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p368aa8f455\">\n", "   <rect x=\"52.160938\" y=\"277.6755\" width=\"147.705882\" height=\"201.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pc259c8e592\">\n", "   <rect x=\"229.407996\" y=\"277.6755\" width=\"147.705882\" height=\"201.6\"/>\n", "  </clipPath>\n", "  <clipPath id=\"pd62f9bcf1a\">\n", "   <rect x=\"406.655055\" y=\"277.6755\" width=\"147.705882\" height=\"201.6\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 900x800 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(2, 3, figsize=(9, 8), sharex=True, sharey=False)\n", "\n", "for k, (title, func) in enumerate(\n", "    (\n", "        (\"Least-squares\", linear),\n", "        (\"Least-squares\\nwith soft L1 norm\", soft_l1),\n", "        (\"Least-squares\\nwith arctan norm\", arctan),\n", "    )\n", "):\n", "    ax[0, k].set_title(title)\n", "    for i, x in enumerate(\"ab\"):\n", "        ax[0, k].errorbar(\n", "            n_pts * 0.95 + 0.1 * i,\n", "            np.sqrt(n_pts) * func[0 + i],\n", "            np.sqrt(n_pts * func[4 + i]),\n", "            fmt=\"so\"[i],\n", "            label=rf\"$\\sqrt{{n}}\\,\\Delta {x}$\",\n", "        )\n", "        ax[1, k].plot(\n", "            n_pts * 0.95 + 0.1 * i,\n", "            func[2 + i] / func[4 + i],\n", "            \"so\"[i],\n", "            label=rf\"$\\sqrt{{n}}\\,\\Delta {x}$\",\n", "        )\n", "        ax[0, k].legend()\n", "plt.semilogx()\n", "for i in range(3):\n", "    ax[1, i].axhline(1, ls=\"--\", color=\"0.5\")\n", "    ax[0, i].set_ylim(-2, 2)\n", "    ax[1, i].set_ylim(0.7, 3)\n", "ax[0, 0].set_ylabel(\"bias and variance\")\n", "ax[1, 0].set_ylabel(\"estimated variance / true variance\")\n", "fig.supxlabel(r\"$n_\\mathrm{pts}$\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The normal least-squares fit has a smallest variance, which is equal to the minimum variance for this problem given by the <PERSON><PERSON><PERSON><PERSON> bound. The robust fits use less information to achieve robustness, hence the variance is larger. The loss from the soft L1 norm in this case is nearly negligible, but for the arctan norm it is noticable.\n", "\n", "**Beware**: The variance estimate obtained from the fit is wrong for robust least-squares, since the robust least-squares is not even asymptotically a maximum-likelihood estimator. The estimate is significantly larger than the actual variance for the soft_l1 and arctan norms in this case."]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "Python 3.8.14 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 4}