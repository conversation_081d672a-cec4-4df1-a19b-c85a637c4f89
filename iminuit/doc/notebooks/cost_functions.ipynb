{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# Cost functions\n", "\n", "We give an in-depth guide on how to use the built-in cost functions.\n", "\n", "The iminuit package comes with a couple of common cost functions that you can import from `iminuit.cost` for convenience. Of course, you can write your own cost functions to use with iminuit, but most of the cost function is always the same. What really varies is the statistical model which predicts the probability density as a function of the parameter values. This you still have to provide yourself and the iminuit package will not include machinery to build statistical models (that is out of scope).\n", "\n", "Using the built-in cost functions is not only convenient, they also have some extra features.\n", "\n", "* Support of fitted weighted histograms.\n", "* Technical tricks improve numerical stability.\n", "* Optional numba acceleration (if numba is installed).\n", "* Cost functions can be added to fit data sets with shared parameters.\n", "* Temporarily mask data.\n", "\n", "We demonstrate each cost function on a standard example from high-energy physics, the fit of a peak over some smooth background (here taken to be constant)."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "from iminuit import cost, Minuit\n", "\n", "# faster than scipy.stats functions\n", "from numba_stats import truncnorm, truncexpon, norm, expon\n", "import numpy as np\n", "from matplotlib import pyplot as plt\n", "\n", "# accurate numerical derivatives\n", "from jacobi import jacobi"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["We generate our data by sampling from a Gaussian peak and from exponential background in the range 0 to 2. The original data is then binned. One can fit the original or the binned data."]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["xr = (0, 2)  # xrange\n", "\n", "rng = np.random.default_rng(1)\n", "\n", "xdata = rng.normal(1, 0.1, size=1000)\n", "ydata = rng.exponential(size=len(xdata))\n", "xmix = np.append(xdata, ydata)\n", "xmix = xmix[(xr[0] < xmix) & (xmix < xr[1])]\n", "\n", "n, xe = np.histogram(xmix, bins=20, range=xr)\n", "cx = 0.5 * (xe[1:] + xe[:-1])\n", "dx = np.diff(xe)\n", "\n", "plt.errorbar(cx, n, n**0.5, fmt=\"ok\")\n", "plt.plot(xmix, np.zeros_like(xmix), \"|\", alpha=0.1);"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["We also generate some 2D data to demonstrate multivariate fits. In this case, a Gaussian along axis 1 and independently an exponential along axis 2. In this case, the distributions are not restricted to some range in x and y."]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["n2, _, ye = np.histogram2d(xdata, ydata, bins=(20, 5), range=(xr, (0, np.max(ydata))))\n", "\n", "plt.pcolormesh(xe, ye, n2.T)\n", "plt.scatter(xdata, ydata, marker=\".\", color=\"w\", s=1);"]}, {"cell_type": "markdown", "id": "6", "metadata": {}, "source": ["## Maximum-likelihood fits\n", "\n", "Maximum-likelihood fits are the state-of-the-art when it comes to fitting models to data. They can be applied to unbinned and binned data (histograms).\n", "\n", "* Unbinned fits are the easiest to use, because no data binning is needed. They become slow when the sample size is large.\n", "* Binned fits require you to appropriately bin the data. The binning has to be fine enough to retain all essential information. Binned fits are much faster when the sample size is large.\n", "\n", "### Unbinned fit\n", "\n", "Unbinned fits are ideal when the data samples are not too large or very high dimensional. There is no need to worry about the appropriate binning of the data. Unbinned fits are inefficient when the samples are very large and can become numerically unstable, too. Binned fits are a better choice then.\n", "\n", "The cost function for an unbinned maximum-likelihood fit is really simple, it is the sum of the logarithm of the pdf evaluated at each sample point (times -1 to turn maximization into minimization). You can easily write this yourself, but a naive implementation will suffer from instabilities when the pdf becomes locally zero. Our implementation mitigates the instabilities to some extent.\n", "\n", "To perform the unbinned fit you need to provide the pdf of the model, which must be vectorized (a Numpy ufunc). The pdf must be normalized, which means that the integral over the sample value range must be a constant for any combination of model parameters.\n", "\n", "The model pdf in this case is a linear combination of the normal and the exponential pdfs. The parameters are $z$ (the weight), $\\mu$ and $\\sigma$ of the normal distribution and $\\tau$ of the exponential. The cost function detects the parameter names.\n", "\n", "It is important to put appropriate limits on the parameters, so that the problem does not become mathematically undefined.\n", "* $0 < z < 1$,\n", "* $\\sigma > 0$,\n", "* $\\tau > 0$.\n", "\n", "In addition, it can be beneficial to use $0 < \\mu < 2$, but it is not required. We use `truncnorm` and `truncexpon`, which are normalized inside the data range (0, 2)."]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [], "source": ["def pdf(x, z, mu, sigma, tau):\n", "    return z * truncnorm.pdf(x, *xr, mu, sigma) + (1 - z) * truncexpon.pdf(\n", "        x, *xr, 0.0, tau\n", "    )\n", "\n", "\n", "c = cost.UnbinnedNLL(xmix, pdf)\n", "\n", "m = Minuit(c, z=0.4, mu=1, sigma=0.2, tau=1)\n", "m.limits[\"z\"] = (0, 1)\n", "m.limits[\"mu\"] = (0, 2)\n", "m.limits[\"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["If the gradient of the model is available, it can be passed to the cost function to enable the computation of its gradient, which <PERSON><PERSON> then uses to potentially improve the minimization. We use a numerically computed gradient here obtained from the `jaco<PERSON>` library. This is for demonstration purpose only and generally not recommended, since `jaco<PERSON>` computes the gradient much more accurately than what is required for <PERSON><PERSON>."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [], "source": ["def grad(x, *par):\n", "    return jacobi(lambda par: pdf(x, *par), par)[0].T\n", "\n", "\n", "c = cost.UnbinnedNLL(xmix, pdf, grad=grad)\n", "\n", "m = Minuit(c, z=0.4, mu=1, sigma=0.2, tau=1)\n", "m.limits[\"z\"] = (0, 1)\n", "m.limits[\"mu\"] = (0, 2)\n", "m.limits[\"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["We can also fit a multivariate model to multivariate data. We pass model as a logpdf this time, which works well because the pdfs factorize."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["def logpdf(xy, mu, sigma, tau):\n", "    x, y = xy\n", "    return norm.logpdf(x, mu, sigma) + expon.logpdf(y, 0, tau)\n", "\n", "\n", "c = cost.UnbinnedNLL((xdata, ydata), logpdf, log=True)\n", "m = Minuit(c, mu=1, sigma=2, tau=2)\n", "m.limits[\"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["And we can also use a gradient as before."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["def grad(xy, *par):\n", "    return jacobi(lambda p: logpdf(xy, *p), par)[0].T\n", "\n", "\n", "c = cost.UnbinnedNLL((xdata, ydata), logpdf, log=True, grad=grad)\n", "m = Minuit(c, mu=1, sigma=2, tau=2)\n", "m.limits[\"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["### Extended unbinned fit\n", "\n", "An important variant of the unbinned ML fit is described by [<PERSON>, Nucl.Instrum.Meth.A 297 (1990) 496-506](https://inspirehep.net/literature/297773). Use this if both the shape and the integral of the density are of interest. In practice, this is often the case, for example, if you want to estimate a cross-section or yield.\n", "\n", "The model in this case has to return the integral of the density and the density itself (which must be vectorized). The parameters in this case are those already discussed in the previous section and in addition $s$ (integral of the signal density), $b$ (integral of the uniform density). The additional limits are:\n", "\n", "* $s > 0$,\n", "* $b > 0$.\n", "\n", "Compared to the previous case, we have one more parameter to fit."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["def density(x, s, b, mu, sigma, tau):\n", "    return s + b, (\n", "        s * truncnorm.pdf(x, *xr, mu, sigma) + b * truncexpon.pdf(x, *xr, 0, tau)\n", "    )\n", "\n", "\n", "c = cost.ExtendedUnbinnedNLL(xmix, density)\n", "\n", "m = Minuit(c, s=300, b=1500, mu=0, sigma=0.2, tau=2)\n", "m.limits[\"s\", \"b\", \"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["The fitted values and the uncertainty estimates for the shape parameters are identical to the previous fit."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["m.visualize()"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["Once again, we fit 2D data, using the log-density mode."]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["def logdensity(xy, n, mu, sigma, tau):\n", "    x, y = xy\n", "    return n, np.log(n) + norm.logpdf(x, mu, sigma) + expon.logpdf(y, 0, tau)\n", "\n", "\n", "c = cost.ExtendedUnbinnedNLL((xdata, ydata), logdensity, log=True)\n", "m = Minuit(c, n=1, mu=1, sigma=2, tau=2)\n", "m.limits[\"n\", \"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "20", "metadata": {}, "source": ["### Binned Fit\n", "\n", "Binned fits are computationally more efficient and numerically more stable when samples are large. The caveat is that one has to choose an appropriate binning. The binning should be fine enough so that the essential information in the original is retained. Using large bins does not introduce a bias, but the parameters have a larger-than-minimal variance.\n", "\n", "In this case, 50 bins are fine enough to retain all information. Using many bins is safe, since the maximum-likelihood method correctly takes Poisson statistics into account, which works even if bins have zero entries. Using more bins than necessary just increases the computational cost.\n", "\n", "Instead of a pdf, you need to provide a cdf for a binned fit (which must be vectorized). "]}, {"cell_type": "code", "execution_count": null, "id": "21", "metadata": {}, "outputs": [], "source": ["def cdf(xe, z, mu, sigma, tau):\n", "    return z * truncnorm.cdf(xe, *xr, mu, sigma) + (1 - z) * truncexpon.cdf(\n", "        xe, *xr, 0, tau\n", "    )\n", "\n", "\n", "c = cost.BinnedNLL(n, xe, cdf)\n", "m = Minuit(c, z=0.4, mu=0, sigma=0.2, tau=2)\n", "m.limits[\"z\"] = (0, 1)\n", "m.limits[\"sigma\", \"tau\"] = (0.01, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "22", "metadata": {}, "source": ["iminuit also shows the chi-square goodness-of-fit test statistic when the data are binned. It is calculated for free in the binned case.\n", "\n", "Sometimes the cdf is expensive to calculate. In this case, you can approximate it via the cumulated sum of \"bin-width times pdf evaluated at center\". This approxmiation may lead to a bias. Using an accurate cdf avoids this bias.\n", "\n", "Here is the same example fitted with an approximate cdf."]}, {"cell_type": "code", "execution_count": null, "id": "23", "metadata": {}, "outputs": [], "source": ["def pdf(x, z, mu, sigma, tau):\n", "    return z * truncnorm.pdf(x, *xr, mu, sigma) + (1 - z) * truncexpon.pdf(\n", "        x, *xr, 0, tau\n", "    )\n", "\n", "\n", "def approximate_cdf(xe, z, mu, sigma, tau):\n", "    dx = np.diff(xe)\n", "    cx = xe[:-1] + 0.5 * dx\n", "    p = pdf(cx, z, mu, sigma, tau)\n", "    return np.append([0], np.cumsum(p * dx))\n", "\n", "\n", "c = cost.BinnedNLL(n, xe, approximate_cdf)\n", "m = Minuit(c, z=0.4, mu=0, sigma=0.2, tau=2)\n", "m.limits[\"z\"] = (0, 1)\n", "m.limits[\"sigma\", \"tau\"] = (0.01, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "24", "metadata": {}, "source": ["The fitted values and the uncertainty estimates for $\\mu$ and $\\sigma$ are not identical to the unbinned fit, but very close. For practical purposes, the results are equivalent. This shows that the binning is fine enough to retain the essential information in the original data.\n", "\n", "Since this approximation is useful in practice, the `BinnedNLL` computes it automatically if you pass the keyword `use_pdf=\"approximate\"`."]}, {"cell_type": "code", "execution_count": null, "id": "25", "metadata": {}, "outputs": [], "source": ["c = cost.BinnedNLL(n, xe, pdf, use_pdf=\"approximate\")\n", "m = Minuit(c, z=0.4, mu=0, sigma=0.2, tau=2)\n", "m.limits[\"z\"] = (0, 1)\n", "m.limits[\"sigma\", \"tau\"] = (0.01, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "26", "metadata": {}, "source": ["Another option is to compute the cdf numerically with `use_pdf=\"numerical\"`, but this tends to be expensive and is only supported for 1D histograms."]}, {"cell_type": "code", "execution_count": null, "id": "27", "metadata": {}, "outputs": [], "source": ["c = cost.BinnedNLL(n, xe, pdf, use_pdf=\"numerical\")\n", "m = Minuit(c, z=0.4, mu=0, sigma=0.2, tau=2)\n", "m.limits[\"z\"] = (0, 1)\n", "m.limits[\"sigma\", \"tau\"] = (0.01, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "28", "metadata": {}, "source": ["Fitting a multidimensional histogram is equally easy. Since the pdfs in this example factorise, the cdf of the 2D model is the product of the cdfs along each axis."]}, {"cell_type": "code", "execution_count": null, "id": "29", "metadata": {}, "outputs": [], "source": ["def cdf(xe_ye, mu, sigma, tau):\n", "    xe, ye = xe_ye\n", "    return norm.cdf(xe, mu, sigma) * expon.cdf(ye, 0, tau)\n", "\n", "\n", "c = cost.BinnedNLL(n2, (xe, ye), cdf)\n", "m = Minuit(c, mu=0.1, sigma=0.2, tau=2)\n", "m.limits[\"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "id": "30", "metadata": {}, "source": ["The automatically provided visualization for multidimensional data set is often not very pretty, but still helps to judge whether the fit is reasonable. There is no obvious way to draw higher dimensional data with error bars in comparison to a model, and so the automatic visualization shows all data bins as a single sequence. You can override the default visualization by calling `Minuit.visualize` with your own plotting function, or by assigning a plot function to the cost function `BinnedNLL` (monkey patching), or by deriving your own class from `BinnedNLL`."]}, {"cell_type": "markdown", "id": "31", "metadata": {}, "source": ["### Extended binned maximum-likelihood fit\n", "\n", "As in the unbinned case, the binned extended maximum-likelihood fit should be used when also the amplitudes of the pdfs are of interest.\n", "\n", "Instead of a density, you need to provide the integrated density in this case (which must be vectorized). There is no need to separately return the total integral of the density, like in the unbinned case. The parameters are the same as in the unbinned extended fit."]}, {"cell_type": "code", "execution_count": null, "id": "32", "metadata": {}, "outputs": [], "source": ["def integral(xe, s, b, mu, sigma, tau):\n", "    return s * truncnorm.cdf(xe, *xr, mu, sigma) + b * truncexpon.cdf(xe, *xr, 0, tau)\n", "\n", "\n", "c = cost.ExtendedBinnedNLL(n, xe, integral)\n", "m = Minuit(c, s=300, b=1500, mu=0, sigma=0.2, tau=2)\n", "m.limits[\"s\", \"b\", \"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "33", "metadata": {}, "source": ["Again, we can also fit multivariate data."]}, {"cell_type": "code", "execution_count": null, "id": "34", "metadata": {}, "outputs": [], "source": ["def integral(xe_ye, n, mu, sigma, tau):\n", "    xe, ye = xe_ye\n", "    return n * norm.cdf(xe, mu, sigma) * expon.cdf(ye, 0, tau)\n", "\n", "\n", "c = cost.ExtendedBinnedNLL(n2, (xe, ye), integral)\n", "m = Minuit(c, n=1500, mu=0.1, sigma=0.2, tau=2)\n", "m.limits[\"n\", \"sigma\", \"tau\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "35", "metadata": {}, "source": ["### Temporary masking\n", "\n", "In complicated binned fits with peak and background, it is sometimes useful to fit in several stages. One typically starts by masking the signal region, to fit only the background region.\n", "\n", "The cost functions have a mask attribute to that end. We demonstrate the use of the mask with an extended binned fit."]}, {"cell_type": "code", "execution_count": null, "id": "36", "metadata": {}, "outputs": [], "source": ["def integral(xe, s, b, mu, sigma, tau):\n", "    return s * truncnorm.cdf(xe, *xr, mu, sigma) + b * truncexpon.cdf(xe, *xr, 0, tau)\n", "\n", "\n", "c = cost.ExtendedBinnedNLL(n, xe, integral)\n", "\n", "# we set the signal amplitude to zero and fix all signal parameters\n", "m = Minuit(c, s=0, b=1500, mu=1, sigma=0.2, tau=2)\n", "\n", "m.limits[\"s\", \"b\", \"sigma\", \"tau\"] = (0, None)\n", "m.fixed[\"s\", \"mu\", \"sigma\"] = True\n", "\n", "# we temporarily mask out the signal\n", "c.mask = (cx < 0.5) | (1.5 < cx)\n", "\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "37", "metadata": {}, "source": ["We plot the intermediate result. Points which have been masked out are shown with open markers."]}, {"cell_type": "code", "execution_count": null, "id": "38", "metadata": {}, "outputs": [], "source": ["for ma, co in ((c.mask, \"k\"), (~c.mask, \"w\")):\n", "    plt.errorbar(cx[ma], n[ma], n[ma] ** 0.5, fmt=\"o\", color=co, mec=\"k\", ecolor=\"k\")\n", "plt.stairs(\n", "    np.diff(integral(xe, *[p.value for p in m.init_params])), xe, ls=\":\", label=\"init\"\n", ")\n", "plt.stairs(np.diff(integral(xe, *m.values)), xe, label=\"fit\")\n", "plt.legend();"]}, {"cell_type": "markdown", "id": "39", "metadata": {}, "source": ["Now we fix the background and fit only the signal parameters."]}, {"cell_type": "code", "execution_count": null, "id": "40", "metadata": {}, "outputs": [], "source": ["c.mask = None  # remove mask\n", "m.fixed = False  # release all parameters\n", "m.fixed[\"b\"] = True  # fix background amplitude\n", "m.values[\"s\"] = 100  # do not start at the limit\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "41", "metadata": {}, "source": ["Finally, we release all parameters and fit again to get the correct uncertainty estimates."]}, {"cell_type": "code", "execution_count": null, "id": "42", "metadata": {}, "outputs": [], "source": ["m.fixed = None\n", "m.migrad()"]}, {"cell_type": "markdown", "id": "43", "metadata": {}, "source": ["We get the same result as before. Since this was an easy problem, we did not need these extra steps, but doing this can be helpful to fit lots of histograms without adjusting each fit manually."]}, {"cell_type": "markdown", "id": "44", "metadata": {}, "source": ["### Weighted histograms\n", "\n", "The cost functions for binned data also support weighted histograms. Just pass an array with the shape `(n, 2)` instead of `(n,)` as the first argument, where the first number of each pair is the sum of weights and the second is the sum of weights squared (an estimate of the variance of that bin value)."]}, {"cell_type": "markdown", "id": "45", "metadata": {}, "source": ["## Least-squares fits\n", "\n", "A cost function for a general weighted least-squares fit (aka chi-square fit) is also included. In statistics this is called non-linear regression.\n", "\n", "In this case you need to provide a model that predicts the y-values as a function of the x-values and the parameters. The fit needs estimates of the y-errors. If those are wrong, the fit may be biased. If your data has errors on the x-values as well, checkout the tutorial about automatic differentiation, which includes an application of that to such fits."]}, {"cell_type": "code", "execution_count": null, "id": "46", "metadata": {}, "outputs": [], "source": ["def model(x, a, b):\n", "    return a + b * x**2\n", "\n", "\n", "rng = np.random.default_rng(4)\n", "\n", "truth = 1, 2\n", "x = np.linspace(0, 1, 20)\n", "yt = model(x, *truth)\n", "ye = 0.4 * x**5 + 0.1\n", "y = rng.normal(yt, ye)\n", "\n", "plt.plot(x, yt, ls=\"--\", label=\"truth\")\n", "plt.errorbar(x, y, ye, fmt=\"ok\", label=\"data\")\n", "plt.legend(loc=\"upper left\");"]}, {"cell_type": "code", "execution_count": null, "id": "47", "metadata": {}, "outputs": [], "source": ["c = cost.LeastSquares(x, y, ye, model)\n", "m1 = Minuit(c, a=0, b=0)\n", "m1.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "id": "48", "metadata": {}, "source": ["We can also plot the standard visualization manually and add further graphs to the figure."]}, {"cell_type": "code", "execution_count": null, "id": "49", "metadata": {}, "outputs": [], "source": ["m1.visualize()\n", "plt.plot(c.x, model(c.x, *truth), ls=\"--\", label=\"truth\");"]}, {"cell_type": "markdown", "id": "50", "metadata": {}, "source": ["We can also fit a multivariate model, in this case we fit a plane in 2D."]}, {"cell_type": "code", "execution_count": null, "id": "51", "metadata": {}, "outputs": [], "source": ["def model2(x_y, a, bx, by):\n", "    x, y = x_y\n", "    return a + bx * x + by * y\n", "\n", "\n", "# generate a regular grid in x and y\n", "x = np.linspace(-1, 1, 10)\n", "y = np.linspace(-1, 1, 10)\n", "X, Y = np.meshgrid(x, y)\n", "x = X.flatten()\n", "y = Y.flatten()\n", "\n", "# model truth\n", "Z = model2((x, y), 1, 2, 3)\n", "\n", "# add some noise\n", "rng = np.random.default_rng(1)\n", "Zerr = 1\n", "Z = rng.normal(<PERSON>, <PERSON><PERSON><PERSON>)\n", "\n", "plt.scatter(x, y, c=Z)\n", "plt.colorbar();"]}, {"cell_type": "code", "execution_count": null, "id": "52", "metadata": {}, "outputs": [], "source": ["c2 = cost.LeastSquares((x, y), <PERSON>, <PERSON><PERSON><PERSON>, model2)\n", "m2 = Minuit(c2, 0, 0, 0)\n", "m2.migrad()"]}, {"cell_type": "markdown", "id": "53", "metadata": {}, "source": ["Multivariate fits are difficult to check by eye. Here we use color to indicate the function value.\n", "\n", "To guarantee that plot of the function and the plot of the data use the same color scale, we use the same normalising function for pyplot.pcolormesh and pyplot.scatter."]}, {"cell_type": "code", "execution_count": null, "id": "54", "metadata": {}, "outputs": [], "source": ["xm = np.linspace(-1, 1, 100)\n", "ym = np.linspace(-1, 1, 100)\n", "Xm, Ym = np.meshgrid(xm, ym)\n", "xm = Xm.flatten()\n", "ym = Ym.flatten()\n", "\n", "qm = plt.pcolormesh(Xm, Ym, model2((xm, ym), *m2.values).reshape(Xm.shape))\n", "plt.scatter(c2.x[0], c2.x[1], c=c2.y, edgecolors=\"w\", norm=qm.norm)\n", "plt.colorbar();"]}, {"cell_type": "markdown", "id": "55", "metadata": {}, "source": ["### Robust least-squares\n", "\n", "The built-in least-squares function also supports robust fitting with an alternative loss functions. See the documentation of `iminuit.cost.LeastSquares` for details. Users can pass their own loss functions. Builtin loss functions are:\n", "\n", "* `linear` (default): gives ordinary weighted least-squares\n", "* `soft_l1`: quadratic ordinary loss for small deviations ($\\ll 1\\sigma$), linear loss for large deviations ($\\gg 1\\sigma$), and smooth interpolation in between\n", "\n", "Let's create one outlier and see what happens with ordinary loss."]}, {"cell_type": "code", "execution_count": null, "id": "56", "metadata": {}, "outputs": [], "source": ["c.y[3] = 3  # generate an outlier\n", "\n", "m3 = Minuit(c, a=0, b=0)\n", "m3.migrad()"]}, {"cell_type": "code", "execution_count": null, "id": "57", "metadata": {}, "outputs": [], "source": ["m3.visualize()\n", "plt.plot(c.x, model(c.x, 1, 2), ls=\"--\", label=\"truth\");"]}, {"cell_type": "markdown", "id": "58", "metadata": {}, "source": ["The result is distorted by the outlier. Note that the error did not increase! The size of the error computed by Minuit does **not** include mismodelling.\n", "\n", "We can repair this with by switching to \"soft_l1\" loss."]}, {"cell_type": "code", "execution_count": null, "id": "59", "metadata": {}, "outputs": [], "source": ["c.loss = \"soft_l1\"\n", "m3.migrad()"]}, {"cell_type": "code", "execution_count": null, "id": "60", "metadata": {}, "outputs": [], "source": ["m3.visualize()\n", "plt.plot(c.x, model(c.x, *truth), ls=\"--\", label=\"truth\");"]}, {"cell_type": "markdown", "id": "61", "metadata": {}, "source": ["The result is almost identical as in the previous case without an outlier.\n", "\n", "Robust fitting is very useful if the data are contaminated with small amounts of outliers. It comes with a price, however, the uncertainties are in general larger and the errors computed by Minuit are not correct anymore.\n", "\n", "Calculating the parameter uncertainty properly for this case requires a so-called sandwich estimator, which is currently not implemented. As an alternative, one can use the bootstrap to compute parameter uncertainties. We use the `resample` library to do this."]}, {"cell_type": "code", "execution_count": null, "id": "62", "metadata": {}, "outputs": [], "source": ["from resample.bootstrap import variance as bvar\n", "\n", "\n", "def fit(x, y, ye):\n", "    c = cost.LeastSquares(x, y, ye, model, loss=\"soft_l1\")\n", "    m = Minuit(c, a=0, b=0)\n", "    m.migrad()\n", "    return m.values\n", "\n", "\n", "berr = bvar(fit, c.x, c.y, c.yerror, size=1000, random_state=1) ** 0.5\n", "\n", "fig, ax = plt.subplots(1, 2, figsize=(9, 4))\n", "for i, axi in enumerate(ax):\n", "    axi.errorbar(0, m1.values[i], m1.errors[i], fmt=\"o\")\n", "    axi.errorbar(1, m3.values[i], m3.errors[i], fmt=\"o\")\n", "    axi.errorbar(2, m3.values[i], berr[i], fmt=\"o\")\n", "    axi.set_xticks(np.arange(3), (\"no outlier\", \"Minuit, soft_l1\", \"bootstrap\"))"]}, {"cell_type": "markdown", "id": "63", "metadata": {}, "source": ["In this case, <PERSON><PERSON>'s estimate is similar to the bootstrap estimate, but that is not generally true when the \"soft_l1\" loss is used.\n", "\n", "Robust fits are very powerful when the outliers cannot be removed by other means. If one can identify outliers by other means, it is better to remove them. We manually remove the point (using the mask attribute) and switch back to ordinary loss."]}, {"cell_type": "code", "execution_count": null, "id": "64", "metadata": {}, "outputs": [], "source": ["c.mask = np.arange(len(c.x)) != 3\n", "c.loss = \"linear\"\n", "m4 = Minuit(c, a=0, b=0)\n", "m4.migrad()"]}, {"cell_type": "markdown", "id": "65", "metadata": {}, "source": ["Now the uncertainties are essentially the same as before adding the outlier."]}, {"cell_type": "code", "execution_count": null, "id": "66", "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(1, 2, figsize=(9, 4))\n", "for i, axi in enumerate(ax):\n", "    axi.errorbar(0, m1.values[i], m1.errors[i], fmt=\"o\")\n", "    axi.errorbar(1, m3.values[i], m3.errors[i], fmt=\"o\")\n", "    axi.errorbar(2, m3.values[i], berr[i], fmt=\"o\")\n", "    axi.errorbar(3, m4.values[i], m4.errors[i], fmt=\"o\")\n", "    axi.set_xticks(\n", "        np.arange(4), (\"no outlier\", \"Minuit, soft_l1\", \"bootstrap\", \"outlier removed\")\n", "    )"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 5}