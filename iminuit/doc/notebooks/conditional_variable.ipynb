{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# Fit PDF with conditional variable\n", "\n", "In this example, we show an unusual  fit where the total sample is not drawn form a single probability distribution, but each individual sample $x$ is drawn from a different distribution, whose parameters are determined by a conditional variable $y$.\n", "\n", "In our example, we are drawing samples $x$ from varying Gaussian distributions. The location of each Gaussian is a function of the conditional variable $y$, but all share the same width parameter $\\sigma$. We fit the shared parameter $\\sigma$, but also the parameters $a$ and $b$ which determine how the location of each gaussian depends on $y$, assuming a line function $\\mu = a + b y$.\n", "\n", "This tutorial reproduces a [corresponding one from RooFit](https://root.cern.ch/doc/master/rf303__conditional_8C.html)."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["iminuit version 2.28.0\n"]}], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "import iminuit\n", "from iminuit.cost import UnbinnedNLL\n", "from iminuit import Minuit\n", "import numpy as np\n", "import numba as nb\n", "import boost_histogram as bh\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import norm\n", "from numba_stats import norm as norm_nb\n", "\n", "print(\"iminuit version\", iminuit.__version__)"]}, {"cell_type": "code", "execution_count": null, "id": "2", "metadata": {}, "outputs": [], "source": ["rng = np.random.default_rng(1)\n", "\n", "# conditional variable: each sample is paired with a random y parameter\n", "y = rng.normal(0, 10, size=10000)\n", "y = y[np.abs(y) < 10]  # truncate at 10\n", "\n", "\n", "# location of each gaussian is a function of y\n", "def mu(y, a, b):\n", "    return a + b * y\n", "\n", "\n", "# draw samples from Gaussians whose locations depend on y\n", "truth = {\"a\": 0, \"b\": 0.5, \"sigma\": 1.0}\n", "x = rng.normal(mu(y, truth[\"a\"], truth[\"b\"]), truth[\"sigma\"])"]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["The distribution in $x$ is more broad than the usual Gaussian because it is a convolution of many Gaussian distributions with varying means. We can visualise this by binning the data in $x$ and $y$."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"411.285625pt\" height=\"310.86825pt\" viewBox=\"0 0 411.**********.86825\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:01:08.429955</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 310.86825 \n", "L 411.**********.86825 \n", "L 411.285625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 46.**********.312 \n", "L 404.**********.312 \n", "L 404.085625 7.2 \n", "L 46.965625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 63.198352 273.312 \n", "L 63.198352 273.312 \n", "L 66.444898 273.312 \n", "L 66.444898 273.312 \n", "L 69.691443 273.312 \n", "L 69.691443 273.312 \n", "L 72.937989 273.312 \n", "L 72.937989 273.312 \n", "L 76.184534 273.312 \n", "L 76.184534 273.312 \n", "L 79.43108 273.312 \n", "L 79.43108 273.312 \n", "L 82.677625 273.312 \n", "L 82.677625 273.312 \n", "L 85.92417 273.312 \n", "L 85.92417 273.312 \n", "L 89.170716 273.312 \n", "L 89.170716 273.312 \n", "L 92.417261 273.312 \n", "L 92.417261 273.312 \n", "L 95.663807 273.312 \n", "L 95.663807 273.312 \n", "L 98.910352 273.312 \n", "L 98.910352 273.312 \n", "L 102.156898 273.312 \n", "L 102.156898 271.88818 \n", "L 105.403443 271.88818 \n", "L 105.403443 273.312 \n", "L 108.649989 273.312 \n", "L 108.649989 273.312 \n", "L 111.896534 273.312 \n", "L 111.896534 269.040539 \n", "L 115.14308 269.040539 \n", "L 115.14308 273.312 \n", "L 118.389625 273.312 \n", "L 118.389625 259.073798 \n", "L 121.63617 259.073798 \n", "L 121.63617 257.649978 \n", "L 124.882716 257.649978 \n", "L 124.882716 250.530876 \n", "L 128.129261 250.530876 \n", "L 128.129261 243.411775 \n", "L 131.375807 243.411775 \n", "L 131.375807 237.716494 \n", "L 134.622352 237.716494 \n", "L 134.622352 234.868854 \n", "L 137.868898 234.868854 \n", "L 137.868898 213.511551 \n", "L 141.115443 213.511551 \n", "L 141.115443 196.425708 \n", "L 144.361989 196.425708 \n", "L 144.361989 180.763685 \n", "L 147.608534 180.763685 \n", "L 147.608534 179.339865 \n", "L 150.85508 179.339865 \n", "L 150.85508 129.506157 \n", "L 154.101625 129.506157 \n", "L 154.101625 165.101663 \n", "L 157.34817 165.101663 \n", "L 157.34817 148.01582 \n", "L 160.594716 148.01582 \n", "L 160.594716 162.254022 \n", "L 163.841261 162.254022 \n", "L 163.841261 162.254022 \n", "L 167.087807 162.254022 \n", "L 167.087807 180.763685 \n", "L 170.334352 180.763685 \n", "L 170.334352 175.068404 \n", "L 173.580898 175.068404 \n", "L 173.580898 186.458966 \n", "L 176.827443 186.458966 \n", "L 176.827443 189.306607 \n", "L 180.073989 189.306607 \n", "L 180.073989 199.273348 \n", "L 183.320534 199.273348 \n", "L 183.320534 230.597393 \n", "L 186.56708 230.597393 \n", "L 186.56708 220.630652 \n", "L 189.813625 220.630652 \n", "L 189.813625 246.259416 \n", "L 193.06017 246.259416 \n", "L 193.06017 256.226157 \n", "L 196.306716 256.226157 \n", "L 196.306716 254.802337 \n", "L 199.553261 254.802337 \n", "L 199.553261 260.497618 \n", "L 202.799807 260.497618 \n", "L 202.799807 270.46436 \n", "L 206.046352 270.46436 \n", "L 206.046352 263.345258 \n", "L 209.292898 263.345258 \n", "L 209.292898 266.192899 \n", "L 212.539443 266.192899 \n", "L 212.539443 271.88818 \n", "L 215.785989 271.88818 \n", "L 215.785989 273.312 \n", "L 219.032534 273.312 \n", "L 219.032534 273.312 \n", "L 222.27908 273.312 \n", "L 222.27908 273.312 \n", "L 225.525625 273.312 \n", "L 225.525625 271.88818 \n", "L 228.77217 271.88818 \n", "L 228.77217 273.312 \n", "L 232.018716 273.312 \n", "L 232.018716 273.312 \n", "L 235.265261 273.312 \n", "L 235.265261 273.312 \n", "L 238.511807 273.312 \n", "L 238.511807 273.312 \n", "L 241.758352 273.312 \n", "L 241.758352 273.312 \n", "L 245.004898 273.312 \n", "L 245.004898 273.312 \n", "L 248.251443 273.312 \n", "L 248.251443 273.312 \n", "L 251.497989 273.312 \n", "L 251.497989 273.312 \n", "L 254.744534 273.312 \n", "L 254.744534 273.312 \n", "L 257.99108 273.312 \n", "L 257.99108 273.312 \n", "L 261.237625 273.312 \n", "L 261.237625 273.312 \n", "L 264.48417 273.312 \n", "L 264.48417 273.312 \n", "L 267.730716 273.312 \n", "L 267.730716 273.312 \n", "L 270.977261 273.312 \n", "L 270.977261 273.312 \n", "L 274.223807 273.312 \n", "L 274.223807 273.312 \n", "L 277.470352 273.312 \n", "L 277.470352 273.312 \n", "L 280.716898 273.312 \n", "L 280.716898 273.312 \n", "L 283.963443 273.312 \n", "L 283.963443 273.312 \n", "L 287.209989 273.312 \n", "L 287.209989 273.312 \n", "L 290.456534 273.312 \n", "L 290.456534 273.312 \n", "L 293.70308 273.312 \n", "L 293.70308 273.312 \n", "L 296.949625 273.312 \n", "L 296.949625 273.312 \n", "L 300.19617 273.312 \n", "L 300.19617 273.312 \n", "L 303.442716 273.312 \n", "L 303.442716 273.312 \n", "L 306.689261 273.312 \n", "L 306.689261 273.312 \n", "L 309.935807 273.312 \n", "L 309.935807 273.312 \n", "L 313.182352 273.312 \n", "L 313.182352 273.312 \n", "L 316.428898 273.312 \n", "L 316.428898 273.312 \n", "L 319.675443 273.312 \n", "L 319.675443 273.312 \n", "L 322.921989 273.312 \n", "L 322.921989 273.312 \n", "L 326.168534 273.312 \n", "L 326.168534 273.312 \n", "L 329.41508 273.312 \n", "L 329.41508 273.312 \n", "L 332.661625 273.312 \n", "L 332.661625 273.312 \n", "L 335.90817 273.312 \n", "L 335.90817 273.312 \n", "L 339.154716 273.312 \n", "L 339.154716 273.312 \n", "L 342.401261 273.312 \n", "L 342.401261 273.312 \n", "L 345.647807 273.312 \n", "L 345.647807 273.312 \n", "L 348.894352 273.312 \n", "L 348.894352 273.312 \n", "L 352.140898 273.312 \n", "L 352.140898 273.312 \n", "L 355.387443 273.312 \n", "L 355.387443 273.312 \n", "L 358.633989 273.312 \n", "L 358.633989 273.312 \n", "L 361.880534 273.312 \n", "L 361.880534 273.312 \n", "L 365.12708 273.312 \n", "L 365.12708 273.312 \n", "L 368.373625 273.312 \n", "L 368.373625 273.312 \n", "L 371.62017 273.312 \n", "L 371.62017 273.312 \n", "L 374.866716 273.312 \n", "L 374.866716 273.312 \n", "L 378.113261 273.312 \n", "L 378.113261 273.312 \n", "L 381.359807 273.312 \n", "L 381.359807 273.312 \n", "L 384.606352 273.312 \n", "L 384.606352 273.312 \n", "L 387.852898 273.312 \n", "L 387.852898 273.312 \n", "\" clip-path=\"url(#p907ffaa003)\" style=\"fill: #1f77b4; opacity: 0.2\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 63.198352 273.312 \n", "L 63.198352 273.312 \n", "L 66.444898 273.312 \n", "L 66.444898 273.312 \n", "L 69.691443 273.312 \n", "L 69.691443 273.312 \n", "L 72.937989 273.312 \n", "L 72.937989 273.312 \n", "L 76.184534 273.312 \n", "L 76.184534 273.312 \n", "L 79.43108 273.312 \n", "L 79.43108 273.312 \n", "L 82.677625 273.312 \n", "L 82.677625 273.312 \n", "L 85.92417 273.312 \n", "L 85.92417 273.312 \n", "L 89.170716 273.312 \n", "L 89.170716 273.312 \n", "L 92.417261 273.312 \n", "L 92.417261 273.312 \n", "L 95.663807 273.312 \n", "L 95.663807 273.312 \n", "L 98.910352 273.312 \n", "L 98.910352 273.312 \n", "L 102.156898 273.312 \n", "L 102.156898 273.312 \n", "L 105.403443 273.312 \n", "L 105.403443 273.312 \n", "L 108.649989 273.312 \n", "L 108.649989 273.312 \n", "L 111.896534 273.312 \n", "L 111.896534 273.312 \n", "L 115.14308 273.312 \n", "L 115.14308 273.312 \n", "L 118.389625 273.312 \n", "L 118.389625 273.312 \n", "L 121.63617 273.312 \n", "L 121.63617 273.312 \n", "L 124.882716 273.312 \n", "L 124.882716 273.312 \n", "L 128.129261 273.312 \n", "L 128.129261 273.312 \n", "L 131.375807 273.312 \n", "L 131.375807 273.312 \n", "L 134.622352 273.312 \n", "L 134.622352 273.312 \n", "L 137.868898 273.312 \n", "L 137.868898 273.312 \n", "L 141.115443 273.312 \n", "L 141.115443 271.88818 \n", "L 144.361989 271.88818 \n", "L 144.361989 270.46436 \n", "L 147.608534 270.46436 \n", "L 147.608534 264.769079 \n", "L 150.85508 264.769079 \n", "L 150.85508 261.921438 \n", "L 154.101625 261.921438 \n", "L 154.101625 251.954697 \n", "L 157.34817 251.954697 \n", "L 157.34817 253.378517 \n", "L 160.594716 253.378517 \n", "L 160.594716 233.445034 \n", "L 163.841261 233.445034 \n", "L 163.841261 230.597393 \n", "L 167.087807 230.597393 \n", "L 167.087807 210.66391 \n", "L 170.334352 210.66391 \n", "L 170.334352 209.24009 \n", "L 173.580898 209.24009 \n", "L 173.580898 179.339865 \n", "L 176.827443 179.339865 \n", "L 176.827443 187.882787 \n", "L 180.073989 187.882787 \n", "L 180.073989 138.049079 \n", "L 183.320534 138.049079 \n", "L 183.320534 120.963236 \n", "L 186.56708 120.963236 \n", "L 186.56708 129.506157 \n", "L 189.813625 129.506157 \n", "L 189.813625 140.896719 \n", "L 193.06017 140.896719 \n", "L 193.06017 129.506157 \n", "L 196.306716 129.506157 \n", "L 196.306716 109.572674 \n", "L 199.553261 109.572674 \n", "L 199.553261 146.592 \n", "L 202.799807 146.592 \n", "L 202.799807 136.625258 \n", "L 206.046352 136.625258 \n", "L 206.046352 165.101663 \n", "L 209.292898 165.101663 \n", "L 209.292898 186.458966 \n", "L 212.539443 186.458966 \n", "L 212.539443 190.730427 \n", "L 215.785989 190.730427 \n", "L 215.785989 204.968629 \n", "L 219.032534 204.968629 \n", "L 219.032534 229.173573 \n", "L 222.27908 229.173573 \n", "L 222.27908 233.445034 \n", "L 225.525625 233.445034 \n", "L 225.525625 250.530876 \n", "L 228.77217 250.530876 \n", "L 228.77217 264.769079 \n", "L 232.018716 264.769079 \n", "L 232.018716 259.073798 \n", "L 235.265261 259.073798 \n", "L 235.265261 264.769079 \n", "L 238.511807 264.769079 \n", "L 238.511807 269.040539 \n", "L 241.758352 269.040539 \n", "L 241.758352 271.88818 \n", "L 245.004898 271.88818 \n", "L 245.004898 270.46436 \n", "L 248.251443 270.46436 \n", "L 248.251443 270.46436 \n", "L 251.497989 270.46436 \n", "L 251.497989 270.46436 \n", "L 254.744534 270.46436 \n", "L 254.744534 273.312 \n", "L 257.99108 273.312 \n", "L 257.99108 270.46436 \n", "L 261.237625 270.46436 \n", "L 261.237625 273.312 \n", "L 264.48417 273.312 \n", "L 264.48417 273.312 \n", "L 267.730716 273.312 \n", "L 267.730716 273.312 \n", "L 270.977261 273.312 \n", "L 270.977261 273.312 \n", "L 274.223807 273.312 \n", "L 274.223807 273.312 \n", "L 277.470352 273.312 \n", "L 277.470352 273.312 \n", "L 280.716898 273.312 \n", "L 280.716898 273.312 \n", "L 283.963443 273.312 \n", "L 283.963443 273.312 \n", "L 287.209989 273.312 \n", "L 287.209989 273.312 \n", "L 290.456534 273.312 \n", "L 290.456534 273.312 \n", "L 293.70308 273.312 \n", "L 293.70308 273.312 \n", "L 296.949625 273.312 \n", "L 296.949625 273.312 \n", "L 300.19617 273.312 \n", "L 300.19617 273.312 \n", "L 303.442716 273.312 \n", "L 303.442716 273.312 \n", "L 306.689261 273.312 \n", "L 306.689261 273.312 \n", "L 309.935807 273.312 \n", "L 309.935807 273.312 \n", "L 313.182352 273.312 \n", "L 313.182352 273.312 \n", "L 316.428898 273.312 \n", "L 316.428898 273.312 \n", "L 319.675443 273.312 \n", "L 319.675443 273.312 \n", "L 322.921989 273.312 \n", "L 322.921989 273.312 \n", "L 326.168534 273.312 \n", "L 326.168534 273.312 \n", "L 329.41508 273.312 \n", "L 329.41508 273.312 \n", "L 332.661625 273.312 \n", "L 332.661625 273.312 \n", "L 335.90817 273.312 \n", "L 335.90817 273.312 \n", "L 339.154716 273.312 \n", "L 339.154716 273.312 \n", "L 342.401261 273.312 \n", "L 342.401261 273.312 \n", "L 345.647807 273.312 \n", "L 345.647807 273.312 \n", "L 348.894352 273.312 \n", "L 348.894352 273.312 \n", "L 352.140898 273.312 \n", "L 352.140898 273.312 \n", "L 355.387443 273.312 \n", "L 355.387443 273.312 \n", "L 358.633989 273.312 \n", "L 358.633989 273.312 \n", "L 361.880534 273.312 \n", "L 361.880534 273.312 \n", "L 365.12708 273.312 \n", "L 365.12708 273.312 \n", "L 368.373625 273.312 \n", "L 368.373625 273.312 \n", "L 371.62017 273.312 \n", "L 371.62017 273.312 \n", "L 374.866716 273.312 \n", "L 374.866716 273.312 \n", "L 378.113261 273.312 \n", "L 378.113261 273.312 \n", "L 381.359807 273.312 \n", "L 381.359807 273.312 \n", "L 384.606352 273.312 \n", "L 384.606352 273.312 \n", "L 387.852898 273.312 \n", "L 387.852898 273.312 \n", "\" clip-path=\"url(#p907ffaa003)\" style=\"fill: #ff7f0e; opacity: 0.2\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 63.198352 273.312 \n", "L 63.198352 273.312 \n", "L 66.444898 273.312 \n", "L 66.444898 273.312 \n", "L 69.691443 273.312 \n", "L 69.691443 273.312 \n", "L 72.937989 273.312 \n", "L 72.937989 273.312 \n", "L 76.184534 273.312 \n", "L 76.184534 273.312 \n", "L 79.43108 273.312 \n", "L 79.43108 273.312 \n", "L 82.677625 273.312 \n", "L 82.677625 273.312 \n", "L 85.92417 273.312 \n", "L 85.92417 273.312 \n", "L 89.170716 273.312 \n", "L 89.170716 273.312 \n", "L 92.417261 273.312 \n", "L 92.417261 273.312 \n", "L 95.663807 273.312 \n", "L 95.663807 273.312 \n", "L 98.910352 273.312 \n", "L 98.910352 273.312 \n", "L 102.156898 273.312 \n", "L 102.156898 273.312 \n", "L 105.403443 273.312 \n", "L 105.403443 273.312 \n", "L 108.649989 273.312 \n", "L 108.649989 273.312 \n", "L 111.896534 273.312 \n", "L 111.896534 273.312 \n", "L 115.14308 273.312 \n", "L 115.14308 273.312 \n", "L 118.389625 273.312 \n", "L 118.389625 273.312 \n", "L 121.63617 273.312 \n", "L 121.63617 273.312 \n", "L 124.882716 273.312 \n", "L 124.882716 273.312 \n", "L 128.129261 273.312 \n", "L 128.129261 273.312 \n", "L 131.375807 273.312 \n", "L 131.375807 273.312 \n", "L 134.622352 273.312 \n", "L 134.622352 273.312 \n", "L 137.868898 273.312 \n", "L 137.868898 273.312 \n", "L 141.115443 273.312 \n", "L 141.115443 273.312 \n", "L 144.361989 273.312 \n", "L 144.361989 273.312 \n", "L 147.608534 273.312 \n", "L 147.608534 273.312 \n", "L 150.85508 273.312 \n", "L 150.85508 273.312 \n", "L 154.101625 273.312 \n", "L 154.101625 273.312 \n", "L 157.34817 273.312 \n", "L 157.34817 273.312 \n", "L 160.594716 273.312 \n", "L 160.594716 273.312 \n", "L 163.841261 273.312 \n", "L 163.841261 273.312 \n", "L 167.087807 273.312 \n", "L 167.087807 271.88818 \n", "L 170.334352 271.88818 \n", "L 170.334352 271.88818 \n", "L 173.580898 271.88818 \n", "L 173.580898 271.88818 \n", "L 176.827443 271.88818 \n", "L 176.827443 271.88818 \n", "L 180.073989 271.88818 \n", "L 180.073989 263.345258 \n", "L 183.320534 263.345258 \n", "L 183.320534 256.226157 \n", "L 186.56708 256.226157 \n", "L 186.56708 257.649978 \n", "L 189.813625 257.649978 \n", "L 189.813625 247.683236 \n", "L 193.06017 247.683236 \n", "L 193.06017 236.292674 \n", "L 196.306716 236.292674 \n", "L 196.306716 216.359191 \n", "L 199.553261 216.359191 \n", "L 199.553261 200.697169 \n", "L 202.799807 200.697169 \n", "L 202.799807 199.273348 \n", "L 206.046352 199.273348 \n", "L 206.046352 173.644584 \n", "L 209.292898 173.644584 \n", "L 209.292898 145.16818 \n", "L 212.539443 145.16818 \n", "L 212.539443 162.254022 \n", "L 215.785989 162.254022 \n", "L 215.785989 130.929978 \n", "L 219.032534 130.929978 \n", "L 219.032534 126.658517 \n", "L 222.27908 126.658517 \n", "L 222.27908 92.486831 \n", "L 225.525625 92.486831 \n", "L 225.525625 119.539416 \n", "L 228.77217 119.539416 \n", "L 228.77217 139.472899 \n", "L 232.018716 139.472899 \n", "L 232.018716 125.234697 \n", "L 235.265261 125.234697 \n", "L 235.265261 163.677843 \n", "L 238.511807 163.677843 \n", "L 238.511807 152.287281 \n", "L 241.758352 152.287281 \n", "L 241.758352 163.677843 \n", "L 245.004898 163.677843 \n", "L 245.004898 182.187506 \n", "L 248.251443 182.187506 \n", "L 248.251443 204.968629 \n", "L 251.497989 204.968629 \n", "L 251.497989 240.564135 \n", "L 254.744534 240.564135 \n", "L 254.744534 229.173573 \n", "L 257.99108 229.173573 \n", "L 257.99108 250.530876 \n", "L 261.237625 250.530876 \n", "L 261.237625 250.530876 \n", "L 264.48417 250.530876 \n", "L 264.48417 256.226157 \n", "L 267.730716 256.226157 \n", "L 267.730716 269.040539 \n", "L 270.977261 269.040539 \n", "L 270.977261 266.192899 \n", "L 274.223807 266.192899 \n", "L 274.223807 270.46436 \n", "L 277.470352 270.46436 \n", "L 277.470352 269.040539 \n", "L 280.716898 269.040539 \n", "L 280.716898 271.88818 \n", "L 283.963443 271.88818 \n", "L 283.963443 273.312 \n", "L 287.209989 273.312 \n", "L 287.209989 273.312 \n", "L 290.456534 273.312 \n", "L 290.456534 273.312 \n", "L 293.70308 273.312 \n", "L 293.70308 273.312 \n", "L 296.949625 273.312 \n", "L 296.949625 273.312 \n", "L 300.19617 273.312 \n", "L 300.19617 273.312 \n", "L 303.442716 273.312 \n", "L 303.442716 273.312 \n", "L 306.689261 273.312 \n", "L 306.689261 273.312 \n", "L 309.935807 273.312 \n", "L 309.935807 273.312 \n", "L 313.182352 273.312 \n", "L 313.182352 273.312 \n", "L 316.428898 273.312 \n", "L 316.428898 273.312 \n", "L 319.675443 273.312 \n", "L 319.675443 273.312 \n", "L 322.921989 273.312 \n", "L 322.921989 273.312 \n", "L 326.168534 273.312 \n", "L 326.168534 273.312 \n", "L 329.41508 273.312 \n", "L 329.41508 273.312 \n", "L 332.661625 273.312 \n", "L 332.661625 273.312 \n", "L 335.90817 273.312 \n", "L 335.90817 273.312 \n", "L 339.154716 273.312 \n", "L 339.154716 273.312 \n", "L 342.401261 273.312 \n", "L 342.401261 273.312 \n", "L 345.647807 273.312 \n", "L 345.647807 273.312 \n", "L 348.894352 273.312 \n", "L 348.894352 273.312 \n", "L 352.140898 273.312 \n", "L 352.140898 273.312 \n", "L 355.387443 273.312 \n", "L 355.387443 273.312 \n", "L 358.633989 273.312 \n", "L 358.633989 273.312 \n", "L 361.880534 273.312 \n", "L 361.880534 273.312 \n", "L 365.12708 273.312 \n", "L 365.12708 273.312 \n", "L 368.373625 273.312 \n", "L 368.373625 273.312 \n", "L 371.62017 273.312 \n", "L 371.62017 273.312 \n", "L 374.866716 273.312 \n", "L 374.866716 273.312 \n", "L 378.113261 273.312 \n", "L 378.113261 273.312 \n", "L 381.359807 273.312 \n", "L 381.359807 273.312 \n", "L 384.606352 273.312 \n", "L 384.606352 273.312 \n", "L 387.852898 273.312 \n", "L 387.852898 273.312 \n", "\" clip-path=\"url(#p907ffaa003)\" style=\"fill: #2ca02c; opacity: 0.2\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 63.198352 273.312 \n", "L 63.198352 273.312 \n", "L 66.444898 273.312 \n", "L 66.444898 273.312 \n", "L 69.691443 273.312 \n", "L 69.691443 273.312 \n", "L 72.937989 273.312 \n", "L 72.937989 273.312 \n", "L 76.184534 273.312 \n", "L 76.184534 273.312 \n", "L 79.43108 273.312 \n", "L 79.43108 273.312 \n", "L 82.677625 273.312 \n", "L 82.677625 273.312 \n", "L 85.92417 273.312 \n", "L 85.92417 273.312 \n", "L 89.170716 273.312 \n", "L 89.170716 273.312 \n", "L 92.417261 273.312 \n", "L 92.417261 273.312 \n", "L 95.663807 273.312 \n", "L 95.663807 273.312 \n", "L 98.910352 273.312 \n", "L 98.910352 273.312 \n", "L 102.156898 273.312 \n", "L 102.156898 273.312 \n", "L 105.403443 273.312 \n", "L 105.403443 273.312 \n", "L 108.649989 273.312 \n", "L 108.649989 273.312 \n", "L 111.896534 273.312 \n", "L 111.896534 273.312 \n", "L 115.14308 273.312 \n", "L 115.14308 273.312 \n", "L 118.389625 273.312 \n", "L 118.389625 273.312 \n", "L 121.63617 273.312 \n", "L 121.63617 273.312 \n", "L 124.882716 273.312 \n", "L 124.882716 273.312 \n", "L 128.129261 273.312 \n", "L 128.129261 273.312 \n", "L 131.375807 273.312 \n", "L 131.375807 273.312 \n", "L 134.622352 273.312 \n", "L 134.622352 273.312 \n", "L 137.868898 273.312 \n", "L 137.868898 273.312 \n", "L 141.115443 273.312 \n", "L 141.115443 273.312 \n", "L 144.361989 273.312 \n", "L 144.361989 273.312 \n", "L 147.608534 273.312 \n", "L 147.608534 273.312 \n", "L 150.85508 273.312 \n", "L 150.85508 273.312 \n", "L 154.101625 273.312 \n", "L 154.101625 273.312 \n", "L 157.34817 273.312 \n", "L 157.34817 273.312 \n", "L 160.594716 273.312 \n", "L 160.594716 273.312 \n", "L 163.841261 273.312 \n", "L 163.841261 273.312 \n", "L 167.087807 273.312 \n", "L 167.087807 273.312 \n", "L 170.334352 273.312 \n", "L 170.334352 273.312 \n", "L 173.580898 273.312 \n", "L 173.580898 273.312 \n", "L 176.827443 273.312 \n", "L 176.827443 273.312 \n", "L 180.073989 273.312 \n", "L 180.073989 273.312 \n", "L 183.320534 273.312 \n", "L 183.320534 273.312 \n", "L 186.56708 273.312 \n", "L 186.56708 273.312 \n", "L 189.813625 273.312 \n", "L 189.813625 273.312 \n", "L 193.06017 273.312 \n", "L 193.06017 273.312 \n", "L 196.306716 273.312 \n", "L 196.306716 273.312 \n", "L 199.553261 273.312 \n", "L 199.553261 271.88818 \n", "L 202.799807 271.88818 \n", "L 202.799807 267.616719 \n", "L 206.046352 267.616719 \n", "L 206.046352 269.040539 \n", "L 209.292898 269.040539 \n", "L 209.292898 259.073798 \n", "L 212.539443 259.073798 \n", "L 212.539443 266.192899 \n", "L 215.785989 266.192899 \n", "L 215.785989 261.921438 \n", "L 219.032534 261.921438 \n", "L 219.032534 247.683236 \n", "L 222.27908 247.683236 \n", "L 222.27908 240.564135 \n", "L 225.525625 240.564135 \n", "L 225.525625 232.021213 \n", "L 228.77217 232.021213 \n", "L 228.77217 236.292674 \n", "L 232.018716 236.292674 \n", "L 232.018716 196.425708 \n", "L 235.265261 196.425708 \n", "L 235.265261 190.730427 \n", "L 238.511807 190.730427 \n", "L 238.511807 180.763685 \n", "L 241.758352 180.763685 \n", "L 241.758352 170.796944 \n", "L 245.004898 170.796944 \n", "L 245.004898 166.525483 \n", "L 248.251443 166.525483 \n", "L 248.251443 126.658517 \n", "L 251.497989 126.658517 \n", "L 251.497989 148.01582 \n", "L 254.744534 148.01582 \n", "L 254.744534 153.711101 \n", "L 257.99108 153.711101 \n", "L 257.99108 139.472899 \n", "L 261.237625 139.472899 \n", "L 261.237625 138.049079 \n", "L 264.48417 138.049079 \n", "L 264.48417 135.201438 \n", "L 267.730716 135.201438 \n", "L 267.730716 156.558742 \n", "L 270.977261 156.558742 \n", "L 270.977261 186.458966 \n", "L 274.223807 186.458966 \n", "L 274.223807 179.339865 \n", "L 277.470352 179.339865 \n", "L 277.470352 192.154247 \n", "L 280.716898 192.154247 \n", "L 280.716898 216.359191 \n", "L 283.963443 216.359191 \n", "L 283.963443 224.902112 \n", "L 287.209989 224.902112 \n", "L 287.209989 237.716494 \n", "L 290.456534 237.716494 \n", "L 290.456534 230.597393 \n", "L 293.70308 230.597393 \n", "L 293.70308 256.226157 \n", "L 296.949625 256.226157 \n", "L 296.949625 259.073798 \n", "L 300.19617 259.073798 \n", "L 300.19617 270.46436 \n", "L 303.442716 270.46436 \n", "L 303.442716 270.46436 \n", "L 306.689261 270.46436 \n", "L 306.689261 270.46436 \n", "L 309.935807 270.46436 \n", "L 309.935807 271.88818 \n", "L 313.182352 271.88818 \n", "L 313.182352 270.46436 \n", "L 316.428898 270.46436 \n", "L 316.428898 273.312 \n", "L 319.675443 273.312 \n", "L 319.675443 273.312 \n", "L 322.921989 273.312 \n", "L 322.921989 273.312 \n", "L 326.168534 273.312 \n", "L 326.168534 273.312 \n", "L 329.41508 273.312 \n", "L 329.41508 271.88818 \n", "L 332.661625 271.88818 \n", "L 332.661625 273.312 \n", "L 335.90817 273.312 \n", "L 335.90817 273.312 \n", "L 339.154716 273.312 \n", "L 339.154716 273.312 \n", "L 342.401261 273.312 \n", "L 342.401261 273.312 \n", "L 345.647807 273.312 \n", "L 345.647807 273.312 \n", "L 348.894352 273.312 \n", "L 348.894352 273.312 \n", "L 352.140898 273.312 \n", "L 352.140898 273.312 \n", "L 355.387443 273.312 \n", "L 355.387443 273.312 \n", "L 358.633989 273.312 \n", "L 358.633989 273.312 \n", "L 361.880534 273.312 \n", "L 361.880534 273.312 \n", "L 365.12708 273.312 \n", "L 365.12708 273.312 \n", "L 368.373625 273.312 \n", "L 368.373625 273.312 \n", "L 371.62017 273.312 \n", "L 371.62017 273.312 \n", "L 374.866716 273.312 \n", "L 374.866716 273.312 \n", "L 378.113261 273.312 \n", "L 378.113261 273.312 \n", "L 381.359807 273.312 \n", "L 381.359807 273.312 \n", "L 384.606352 273.312 \n", "L 384.606352 273.312 \n", "L 387.852898 273.312 \n", "L 387.852898 273.312 \n", "\" clip-path=\"url(#p907ffaa003)\" style=\"fill: #d62728; opacity: 0.2\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 63.198352 273.312 \n", "L 63.198352 273.312 \n", "L 66.444898 273.312 \n", "L 66.444898 273.312 \n", "L 69.691443 273.312 \n", "L 69.691443 273.312 \n", "L 72.937989 273.312 \n", "L 72.937989 273.312 \n", "L 76.184534 273.312 \n", "L 76.184534 273.312 \n", "L 79.43108 273.312 \n", "L 79.43108 273.312 \n", "L 82.677625 273.312 \n", "L 82.677625 273.312 \n", "L 85.92417 273.312 \n", "L 85.92417 273.312 \n", "L 89.170716 273.312 \n", "L 89.170716 273.312 \n", "L 92.417261 273.312 \n", "L 92.417261 273.312 \n", "L 95.663807 273.312 \n", "L 95.663807 273.312 \n", "L 98.910352 273.312 \n", "L 98.910352 273.312 \n", "L 102.156898 273.312 \n", "L 102.156898 273.312 \n", "L 105.403443 273.312 \n", "L 105.403443 273.312 \n", "L 108.649989 273.312 \n", "L 108.649989 273.312 \n", "L 111.896534 273.312 \n", "L 111.896534 273.312 \n", "L 115.14308 273.312 \n", "L 115.14308 273.312 \n", "L 118.389625 273.312 \n", "L 118.389625 273.312 \n", "L 121.63617 273.312 \n", "L 121.63617 273.312 \n", "L 124.882716 273.312 \n", "L 124.882716 273.312 \n", "L 128.129261 273.312 \n", "L 128.129261 273.312 \n", "L 131.375807 273.312 \n", "L 131.375807 273.312 \n", "L 134.622352 273.312 \n", "L 134.622352 273.312 \n", "L 137.868898 273.312 \n", "L 137.868898 273.312 \n", "L 141.115443 273.312 \n", "L 141.115443 273.312 \n", "L 144.361989 273.312 \n", "L 144.361989 273.312 \n", "L 147.608534 273.312 \n", "L 147.608534 273.312 \n", "L 150.85508 273.312 \n", "L 150.85508 273.312 \n", "L 154.101625 273.312 \n", "L 154.101625 273.312 \n", "L 157.34817 273.312 \n", "L 157.34817 273.312 \n", "L 160.594716 273.312 \n", "L 160.594716 273.312 \n", "L 163.841261 273.312 \n", "L 163.841261 273.312 \n", "L 167.087807 273.312 \n", "L 167.087807 273.312 \n", "L 170.334352 273.312 \n", "L 170.334352 273.312 \n", "L 173.580898 273.312 \n", "L 173.580898 273.312 \n", "L 176.827443 273.312 \n", "L 176.827443 273.312 \n", "L 180.073989 273.312 \n", "L 180.073989 273.312 \n", "L 183.320534 273.312 \n", "L 183.320534 273.312 \n", "L 186.56708 273.312 \n", "L 186.56708 273.312 \n", "L 189.813625 273.312 \n", "L 189.813625 273.312 \n", "L 193.06017 273.312 \n", "L 193.06017 273.312 \n", "L 196.306716 273.312 \n", "L 196.306716 273.312 \n", "L 199.553261 273.312 \n", "L 199.553261 273.312 \n", "L 202.799807 273.312 \n", "L 202.799807 273.312 \n", "L 206.046352 273.312 \n", "L 206.046352 273.312 \n", "L 209.292898 273.312 \n", "L 209.292898 273.312 \n", "L 212.539443 273.312 \n", "L 212.539443 273.312 \n", "L 215.785989 273.312 \n", "L 215.785989 273.312 \n", "L 219.032534 273.312 \n", "L 219.032534 273.312 \n", "L 222.27908 273.312 \n", "L 222.27908 273.312 \n", "L 225.525625 273.312 \n", "L 225.525625 273.312 \n", "L 228.77217 273.312 \n", "L 228.77217 273.312 \n", "L 232.018716 273.312 \n", "L 232.018716 273.312 \n", "L 235.265261 273.312 \n", "L 235.265261 270.46436 \n", "L 238.511807 270.46436 \n", "L 238.511807 271.88818 \n", "L 241.758352 271.88818 \n", "L 241.758352 270.46436 \n", "L 245.004898 270.46436 \n", "L 245.004898 264.769079 \n", "L 248.251443 264.769079 \n", "L 248.251443 263.345258 \n", "L 251.497989 263.345258 \n", "L 251.497989 241.987955 \n", "L 254.744534 241.987955 \n", "L 254.744534 246.259416 \n", "L 257.99108 246.259416 \n", "L 257.99108 232.021213 \n", "L 261.237625 232.021213 \n", "L 261.237625 233.445034 \n", "L 264.48417 233.445034 \n", "L 264.48417 212.08773 \n", "L 267.730716 212.08773 \n", "L 267.730716 214.935371 \n", "L 270.977261 214.935371 \n", "L 270.977261 199.273348 \n", "L 274.223807 199.273348 \n", "L 274.223807 193.578067 \n", "L 277.470352 193.578067 \n", "L 277.470352 160.830202 \n", "L 280.716898 160.830202 \n", "L 280.716898 156.558742 \n", "L 283.963443 156.558742 \n", "L 283.963443 183.611326 \n", "L 287.209989 183.611326 \n", "L 287.209989 113.844135 \n", "L 290.456534 113.844135 \n", "L 290.456534 182.187506 \n", "L 293.70308 182.187506 \n", "L 293.70308 152.287281 \n", "L 296.949625 152.287281 \n", "L 296.949625 189.306607 \n", "L 300.19617 189.306607 \n", "L 300.19617 180.763685 \n", "L 303.442716 180.763685 \n", "L 303.442716 190.730427 \n", "L 306.689261 190.730427 \n", "L 306.689261 209.24009 \n", "L 309.935807 209.24009 \n", "L 309.935807 210.66391 \n", "L 313.182352 210.66391 \n", "L 313.182352 223.478292 \n", "L 316.428898 223.478292 \n", "L 316.428898 227.749753 \n", "L 319.675443 227.749753 \n", "L 319.675443 243.411775 \n", "L 322.921989 243.411775 \n", "L 322.921989 254.802337 \n", "L 326.168534 254.802337 \n", "L 326.168534 257.649978 \n", "L 329.41508 257.649978 \n", "L 329.41508 259.073798 \n", "L 332.661625 259.073798 \n", "L 332.661625 269.040539 \n", "L 335.90817 269.040539 \n", "L 335.90817 267.616719 \n", "L 339.154716 267.616719 \n", "L 339.154716 267.616719 \n", "L 342.401261 267.616719 \n", "L 342.401261 271.88818 \n", "L 345.647807 271.88818 \n", "L 345.647807 273.312 \n", "L 348.894352 273.312 \n", "L 348.894352 271.88818 \n", "L 352.140898 271.88818 \n", "L 352.140898 273.312 \n", "L 355.387443 273.312 \n", "L 355.387443 273.312 \n", "L 358.633989 273.312 \n", "L 358.633989 273.312 \n", "L 361.880534 273.312 \n", "L 361.880534 273.312 \n", "L 365.12708 273.312 \n", "L 365.12708 273.312 \n", "L 368.373625 273.312 \n", "L 368.373625 273.312 \n", "L 371.62017 273.312 \n", "L 371.62017 273.312 \n", "L 374.866716 273.312 \n", "L 374.866716 273.312 \n", "L 378.113261 273.312 \n", "L 378.113261 273.312 \n", "L 381.359807 273.312 \n", "L 381.359807 273.312 \n", "L 384.606352 273.312 \n", "L 384.606352 273.312 \n", "L 387.852898 273.312 \n", "L 387.852898 273.312 \n", "\" clip-path=\"url(#p907ffaa003)\" style=\"fill: #9467bd; opacity: 0.2\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 63.198352 273.312 \n", "L 102.156898 273.312 \n", "L 102.156898 271.88818 \n", "L 105.403443 271.88818 \n", "L 105.403443 273.312 \n", "L 111.896534 273.312 \n", "L 111.896534 269.040539 \n", "L 115.14308 269.040539 \n", "L 115.14308 273.312 \n", "L 118.389625 273.312 \n", "L 118.389625 259.073798 \n", "L 121.63617 259.073798 \n", "L 121.63617 257.649978 \n", "L 124.882716 257.649978 \n", "L 124.882716 250.530876 \n", "L 128.129261 250.530876 \n", "L 128.129261 243.411775 \n", "L 131.375807 243.411775 \n", "L 131.375807 237.716494 \n", "L 134.622352 237.716494 \n", "L 134.622352 234.868854 \n", "L 137.868898 234.868854 \n", "L 137.868898 213.511551 \n", "L 141.115443 213.511551 \n", "L 141.115443 195.001888 \n", "L 144.361989 195.001888 \n", "L 144.361989 177.916045 \n", "L 147.608534 177.916045 \n", "L 147.608534 170.796944 \n", "L 150.85508 170.796944 \n", "L 150.85508 118.115596 \n", "L 154.101625 118.115596 \n", "L 154.101625 143.74436 \n", "L 157.34817 143.74436 \n", "L 157.34817 128.082337 \n", "L 160.594716 128.082337 \n", "L 160.594716 122.387056 \n", "L 163.841261 122.387056 \n", "L 163.841261 119.539416 \n", "L 167.087807 119.539416 \n", "L 167.087807 116.691775 \n", "L 170.334352 116.691775 \n", "L 170.334352 109.572674 \n", "L 173.580898 109.572674 \n", "L 173.580898 91.063011 \n", "L 176.827443 91.063011 \n", "L 176.827443 102.453573 \n", "L 180.073989 102.453573 \n", "L 180.073989 54.043685 \n", "L 183.320534 54.043685 \n", "L 183.320534 61.162787 \n", "L 189.813625 61.162787 \n", "L 189.813625 88.215371 \n", "L 193.06017 88.215371 \n", "L 193.06017 75.400989 \n", "L 196.306716 75.400989 \n", "L 196.306716 34.110202 \n", "L 199.553261 34.110202 \n", "L 199.553261 59.738966 \n", "L 202.799807 59.738966 \n", "L 202.799807 54.043685 \n", "L 206.046352 54.043685 \n", "L 206.046352 51.196045 \n", "L 209.292898 51.196045 \n", "L 209.292898 36.957843 \n", "L 212.539443 36.957843 \n", "L 212.539443 71.129528 \n", "L 215.785989 71.129528 \n", "L 215.785989 51.196045 \n", "L 219.032534 51.196045 \n", "L 219.032534 56.891326 \n", "L 222.27908 56.891326 \n", "L 222.27908 19.872 \n", "L 225.525625 19.872 \n", "L 225.525625 54.043685 \n", "L 228.77217 54.043685 \n", "L 228.77217 93.910652 \n", "L 232.018716 93.910652 \n", "L 232.018716 34.110202 \n", "L 235.265261 34.110202 \n", "L 235.265261 69.705708 \n", "L 238.511807 69.705708 \n", "L 238.511807 54.043685 \n", "L 241.758352 54.043685 \n", "L 241.758352 56.891326 \n", "L 245.004898 56.891326 \n", "L 245.004898 64.010427 \n", "L 248.251443 64.010427 \n", "L 248.251443 45.500764 \n", "L 251.497989 45.500764 \n", "L 251.497989 81.09627 \n", "L 254.744534 81.09627 \n", "L 254.744534 82.52009 \n", "L 257.99108 82.52009 \n", "L 257.99108 72.553348 \n", "L 261.237625 72.553348 \n", "L 261.237625 75.400989 \n", "L 264.48417 75.400989 \n", "L 264.48417 56.891326 \n", "L 267.730716 56.891326 \n", "L 267.730716 93.910652 \n", "L 270.977261 93.910652 \n", "L 270.977261 105.301213 \n", "L 274.223807 105.301213 \n", "L 274.223807 96.758292 \n", "L 277.470352 96.758292 \n", "L 277.470352 75.400989 \n", "L 280.716898 75.400989 \n", "L 280.716898 98.182112 \n", "L 283.963443 98.182112 \n", "L 283.963443 135.201438 \n", "L 287.209989 135.201438 \n", "L 287.209989 78.248629 \n", "L 290.456534 78.248629 \n", "L 290.456534 139.472899 \n", "L 293.70308 139.472899 \n", "L 293.70308 135.201438 \n", "L 296.949625 135.201438 \n", "L 296.949625 175.068404 \n", "L 300.19617 175.068404 \n", "L 300.19617 177.916045 \n", "L 303.442716 177.916045 \n", "L 303.442716 187.882787 \n", "L 306.689261 187.882787 \n", "L 306.689261 206.392449 \n", "L 309.935807 206.392449 \n", "L 309.935807 209.24009 \n", "L 313.182352 209.24009 \n", "L 313.182352 220.630652 \n", "L 316.428898 220.630652 \n", "L 316.428898 227.749753 \n", "L 319.675443 227.749753 \n", "L 319.675443 243.411775 \n", "L 322.921989 243.411775 \n", "L 322.921989 254.802337 \n", "L 326.168534 254.802337 \n", "L 326.168534 257.649978 \n", "L 332.661625 257.649978 \n", "L 332.661625 269.040539 \n", "L 335.90817 269.040539 \n", "L 335.90817 267.616719 \n", "L 342.401261 267.616719 \n", "L 342.401261 271.88818 \n", "L 345.647807 271.88818 \n", "L 345.647807 273.312 \n", "L 348.894352 273.312 \n", "L 348.894352 271.88818 \n", "L 352.140898 271.88818 \n", "L 352.140898 273.312 \n", "L 387.852898 273.312 \n", "L 387.852898 273.312 \n", "\" clip-path=\"url(#p907ffaa003)\" style=\"fill: none; stroke: #000000; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m5bf99bd6e3\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"63.198352\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −10.0 -->\n", "      <g transform=\"translate(47.875696 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"103.78017\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −7.5 -->\n", "      <g transform=\"translate(91.638764 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"144.361989\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- −5.0 -->\n", "      <g transform=\"translate(132.220582 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"184.943807\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- −2.5 -->\n", "      <g transform=\"translate(172.802401 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"225.525625\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(217.574063 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"266.107443\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(258.155881 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"306.689261\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 5.0 -->\n", "      <g transform=\"translate(298.737699 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"347.27108\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 7.5 -->\n", "      <g transform=\"translate(339.319517 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m5bf99bd6e3\" x=\"387.852898\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 10.0 -->\n", "      <g transform=\"translate(376.720085 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- x -->\n", "     <g transform=\"translate(222.56625 301.588562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m1d623c8c98\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1d623c8c98\" x=\"46.965625\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(33.603125 277.111219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m1d623c8c98\" x=\"46.965625\" y=\"237.716494\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 25 -->\n", "      <g transform=\"translate(27.240625 241.515713) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m1d623c8c98\" x=\"46.965625\" y=\"202.120989\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(27.240625 205.920208) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m1d623c8c98\" x=\"46.965625\" y=\"166.525483\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 75 -->\n", "      <g transform=\"translate(27.240625 170.324702) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m1d623c8c98\" x=\"46.965625\" y=\"130.929978\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(20.878125 134.729196) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m1d623c8c98\" x=\"46.965625\" y=\"95.334472\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 125 -->\n", "      <g transform=\"translate(20.878125 99.133691) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m1d623c8c98\" x=\"46.965625\" y=\"59.738966\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 150 -->\n", "      <g transform=\"translate(20.878125 63.538185) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m1d623c8c98\" x=\"46.965625\" y=\"24.143461\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 175 -->\n", "      <g transform=\"translate(20.878125 27.942679) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- events -->\n", "     <g transform=\"translate(14.798438 157.102094) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"120.703125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"182.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"245.605469\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"284.814453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 46.**********.312 \n", "L 46.965625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 404.**********.312 \n", "L 404.085625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 46.**********.312 \n", "L 404.**********.312 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 46.965625 7.2 \n", "L 404.085625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"text_20\">\n", "     <!-- y interval -->\n", "     <g transform=\"translate(331.702813 23.798437) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-79\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"59.179688\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"90.966797\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"118.75\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"182.128906\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"221.337891\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"282.861328\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"323.974609\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"383.154297\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"444.433594\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_13\">\n", "     <path d=\"M 315.541875 38.476562 \n", "L 327.541875 38.476562 \n", "L 327.541875 31.476562 \n", "L 315.541875 31.476562 \n", "z\n", "\" style=\"fill: #1f77b4; opacity: 0.2\"/>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- [-10.0, -6.0) -->\n", "     <g transform=\"translate(335.541875 38.476562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-5b\" d=\"M 550 4863 \n", "L 1875 4863 \n", "L 1875 4416 \n", "L 1125 4416 \n", "L 1125 -397 \n", "L 1875 -397 \n", "L 1875 -844 \n", "L 550 -844 \n", "L 550 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2d\" d=\"M 313 2009 \n", "L 1997 2009 \n", "L 1997 1497 \n", "L 313 1497 \n", "L 313 2009 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2c\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 256 \n", "L 897 -744 \n", "L 494 -744 \n", "L 750 256 \n", "L 750 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-5b\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"39.013672\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"75.097656\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"138.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"202.34375\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"234.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"297.753906\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"329.541016\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"361.328125\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"397.412109\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"461.035156\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"492.822266\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"556.445312\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_14\">\n", "     <path d=\"M 315.541875 53.154688 \n", "L 327.541875 53.154688 \n", "L 327.541875 46.154688 \n", "L 315.541875 46.154688 \n", "z\n", "\" style=\"fill: #ff7f0e; opacity: 0.2\"/>\n", "    </g>\n", "    <g id=\"text_22\">\n", "     <!-- [-6.0, -2.0) -->\n", "     <g transform=\"translate(335.541875 53.154688) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-5b\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"39.013672\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"75.097656\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"138.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"170.507812\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"234.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"265.917969\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"297.705078\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"333.789062\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"397.412109\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"429.199219\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"492.822266\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_15\">\n", "     <path d=\"M 315.541875 67.832812 \n", "L 327.541875 67.832812 \n", "L 327.541875 60.832812 \n", "L 315.541875 60.832812 \n", "z\n", "\" style=\"fill: #2ca02c; opacity: 0.2\"/>\n", "    </g>\n", "    <g id=\"text_23\">\n", "     <!-- [-2.0, 2.0) -->\n", "     <g transform=\"translate(335.541875 67.832812) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-5b\"/>\n", "      <use xlink:href=\"#DejaVuSans-2d\" x=\"39.013672\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"75.097656\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"138.720703\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"170.507812\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"234.130859\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"265.917969\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"297.705078\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"361.328125\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"393.115234\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"456.738281\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_16\">\n", "     <path d=\"M 315.541875 82.510938 \n", "L 327.541875 82.510938 \n", "L 327.541875 75.510938 \n", "L 315.541875 75.510938 \n", "z\n", "\" style=\"fill: #d62728; opacity: 0.2\"/>\n", "    </g>\n", "    <g id=\"text_24\">\n", "     <!-- [2.0, 6.0) -->\n", "     <g transform=\"translate(335.541875 82.510938) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-5b\"/>\n", "      <use xlink:href=\"#DejaVuSans-32\" x=\"39.013672\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"102.636719\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"134.423828\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"198.046875\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"229.833984\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"261.621094\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"325.244141\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"357.03125\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"420.654297\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_17\">\n", "     <path d=\"M 315.541875 97.189063 \n", "L 327.541875 97.189063 \n", "L 327.541875 90.189063 \n", "L 315.541875 90.189063 \n", "z\n", "\" style=\"fill: #9467bd; opacity: 0.2\"/>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- [6.0, 10.0) -->\n", "     <g transform=\"translate(335.541875 97.189063) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-5b\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"39.013672\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"102.636719\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"134.423828\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" x=\"198.046875\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"229.833984\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"261.621094\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"325.244141\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"388.867188\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"420.654297\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"484.277344\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 315.541875 108.367188 \n", "L 327.541875 108.367188 \n", "\" style=\"fill: none; stroke: #000000; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_26\">\n", "     <!-- total -->\n", "     <g transform=\"translate(335.541875 111.867188) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-74\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"39.208984\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"100.390625\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"139.599609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"200.878906\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p907ffaa003\">\n", "   <rect x=\"46.965625\" y=\"7.2\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["ax_x = bh.axis.Regular(100, -10, 10)\n", "ax_y = bh.axis.Regular(5, -10, 10)\n", "h = bh.Histogram(ax_x, ax_y)\n", "h.fill(x, y)\n", "for i, (a, b) in enumerate(ax_y):\n", "    plt.stairs(h.values()[:, i], ax_x.edges, label=f\"[{a}, {b})\", fill=True, alpha=0.2)\n", "h1 = h[:, sum]\n", "plt.stairs(h1.values(), ax_x.edges, color=\"k\", label=\"total\")\n", "plt.xlabel(\"x\")\n", "plt.ylabel(\"events\")\n", "plt.legend(title=\"y interval\", frameon=False, handlelength=1.2);"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["## Fit with conditional variable\n", "\n", "The random distribution of $x$ depends on the value of $y$. We can exploit that information in the likelihood function to obtain a more accurate estimate of the parameters."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 1.93e+04 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 130 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 2.25e-06 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\"> time = 0.1 sec </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> a </td>\n", "        <td> -0.010 </td>\n", "        <td> 0.012 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> b </td>\n", "        <td> 0.4993 </td>\n", "        <td> 0.0022 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 2 </th>\n", "        <td> sigma </td>\n", "        <td> 0.986 </td>\n", "        <td> 0.008 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> a </th>\n", "        <th> b </th>\n", "        <th> sigma </th>\n", "    </tr>\n", "    <tr>\n", "        <th> a </th>\n", "        <td> 0.000142 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0e-6 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> b </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0e-6 </td>\n", "        <td> 4.76e-06 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0e-6 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> sigma </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0e-6 </td>\n", "        <td> 7.08e-05 </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 1.93e+04                   │              Nfcn = 130              │\n", "│ EDM = 2.25e-06 (Goal: 0.0002)    │            time = 0.1 sec            │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬───────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name  │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼───────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ a     │  -0.010   │   0.012   │            │            │         │         │       │\n", "│ 1 │ b     │  0.4993   │  0.0022   │            │            │         │         │       │\n", "│ 2 │ sigma │   0.986   │   0.008   │            │            │    0    │         │       │\n", "└───┴───────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌───────┬────────────────────────────┐\n", "│       │        a        b    sigma │\n", "├───────┼────────────────────────────┤\n", "│     a │ 0.000142     0e-6        0 │\n", "│     b │     0e-6 4.76e-06     0e-6 │\n", "│ sigma │        0     0e-6 7.08e-05 │\n", "└───────┴────────────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["def model(xy, a, b, sigma):\n", "    x, y = xy\n", "    mu = a + b * y\n", "    # cannot use norm.pdf from numba_stats here, because it is not vectorized in mu\n", "    return norm.pdf(x, mu, sigma)\n", "\n", "\n", "nll = UnbinnedNLL((x, y), model)\n", "\n", "m = Minuit(nll, 0.0, 0.0, 2.0)\n", "m.limits[\"sigma\"] = (0, None)\n", "m.migrad()"]}, {"cell_type": "code", "execution_count": null, "id": "7", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"411.285625pt\" height=\"313.463795pt\" viewBox=\"0 0 411.**********.463795\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:01:08.931729</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 313.463795 \n", "L 411.**********.463795 \n", "L 411.285625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 46.**********.907545 \n", "L 404.**********.907545 \n", "L 404.085625 9.795545 \n", "L 46.965625 9.795545 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m5d054785ef\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"63.198352\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −10.0 -->\n", "      <g transform=\"translate(47.875696 290.505983) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"103.78017\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −7.5 -->\n", "      <g transform=\"translate(91.638764 290.505983) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"144.361989\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- −5.0 -->\n", "      <g transform=\"translate(132.220582 290.505983) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"184.943807\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- −2.5 -->\n", "      <g transform=\"translate(172.802401 290.505983) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"225.525625\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(217.574063 290.505983) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"266.107443\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(258.155881 290.505983) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"306.689261\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 5.0 -->\n", "      <g transform=\"translate(298.737699 290.505983) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"347.27108\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 7.5 -->\n", "      <g transform=\"translate(339.319517 290.505983) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m5d054785ef\" x=\"387.852898\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 10.0 -->\n", "      <g transform=\"translate(376.720085 290.505983) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- x -->\n", "     <g transform=\"translate(222.56625 304.184108) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m3350491998\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"275.907545\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(33.603125 279.706764) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"242.794004\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 25 -->\n", "      <g transform=\"translate(27.240625 246.593223) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"209.680464\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(27.240625 213.479682) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"176.566923\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 75 -->\n", "      <g transform=\"translate(27.240625 180.366142) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"143.453382\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(20.878125 147.252601) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"110.339841\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 125 -->\n", "      <g transform=\"translate(20.878125 114.13906) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"77.2263\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 150 -->\n", "      <g transform=\"translate(20.878125 81.025519) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"44.11276\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 175 -->\n", "      <g transform=\"translate(20.878125 47.911978) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m3350491998\" x=\"46.965625\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(20.878125 14.798438) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_20\">\n", "     <!-- events -->\n", "     <g transform=\"translate(14.798438 159.697639) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"61.523438\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"120.703125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"182.226562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"245.605469\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"284.814453\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 64.821625 275.907545 \n", "L 64.821625 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 68.06817 275.907545 \n", "L 68.06817 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 71.314716 275.907545 \n", "L 71.314716 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 74.561261 275.907545 \n", "L 74.561261 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 77.807807 275.907545 \n", "L 77.807807 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 81.054352 275.907545 \n", "L 81.054352 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 84.300898 275.907545 \n", "L 84.300898 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 87.547443 275.907545 \n", "L 87.547443 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 90.793989 275.907545 \n", "L 90.793989 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 94.040534 275.907545 \n", "L 94.040534 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 97.28708 275.907545 \n", "L 97.28708 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 100.533625 275.907545 \n", "L 100.533625 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 103.78017 275.907545 \n", "L 103.78017 273.258462 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 107.026716 275.907545 \n", "L 107.026716 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 110.273261 275.907545 \n", "L 110.273261 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 113.519807 274.228094 \n", "L 113.519807 269.639747 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 116.766352 275.907545 \n", "L 116.766352 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 120.012898 266.850697 \n", "L 120.012898 258.47356 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 123.259443 265.730595 \n", "L 123.259443 256.94458 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 126.505989 260.013046 \n", "L 126.505989 249.416713 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 129.752534 254.161983 \n", "L 129.752534 242.022359 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 132.99908 249.416713 \n", "L 132.99908 236.171296 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 136.245625 247.027441 \n", "L 136.245625 233.262401 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 139.49217 228.860807 \n", "L 139.49217 211.692786 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 142.738716 212.880819 \n", "L 142.738716 193.234692 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 145.985261 198.005096 \n", "L 145.985261 176.321415 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 149.231807 191.779656 \n", "L 149.231807 169.301439 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 152.478352 145.361128 \n", "L 152.478352 117.703887 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 155.724898 168.009579 \n", "L 155.724898 142.738935 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 158.971443 154.181513 \n", "L 158.971443 127.427084 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 162.217989 149.143123 \n", "L 162.217989 121.869141 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 165.464534 146.622089 \n", "L 165.464534 119.092008 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 168.71108 144.099875 \n", "L 168.71108 116.316056 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 171.957625 137.789384 \n", "L 171.957625 109.381131 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 175.20417 121.351694 \n", "L 175.20417 91.380738 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 178.450716 131.472176 \n", "L 178.450716 102.452923 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 181.697261 88.365263 \n", "L 181.697261 55.491004 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 184.943807 94.718933 \n", "L 184.943807 62.382751 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 188.190352 94.718933 \n", "L 188.190352 62.382751 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 191.436898 118.819231 \n", "L 191.436898 88.615035 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 194.683443 107.412374 \n", "L 194.683443 76.180143 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 197.929989 70.552573 \n", "L 197.929989 36.216529 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 201.176534 93.448556 \n", "L 201.176534 61.004045 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 204.42308 88.365263 \n", "L 204.42308 55.491004 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 207.669625 85.82257 \n", "L 207.669625 52.735531 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.91617 73.099159 \n", "L 210.91617 38.968109 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 214.162716 103.606369 \n", "L 214.162716 72.038898 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 217.409261 85.82257 \n", "L 217.409261 52.735531 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 220.655807 90.907263 \n", "L 220.655807 58.247171 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 223.902352 57.810724 \n", "L 223.902352 22.467545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 227.148898 88.365263 \n", "L 227.148898 55.491004 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 230.395443 123.883242 \n", "L 230.395443 94.147357 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 233.641989 70.552573 \n", "L 233.641989 36.216529 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 236.888534 102.337306 \n", "L 236.888534 70.658878 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 240.13508 88.365263 \n", "L 240.13508 55.491004 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 243.381625 90.907263 \n", "L 243.381625 58.247171 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 246.62817 97.259139 \n", "L 246.62817 65.140711 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 249.874716 80.735158 \n", "L 249.874716 47.22661 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 253.121261 112.484208 \n", "L 253.121261 81.704642 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 256.367807 113.751644 \n", "L 256.367807 83.086289 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 259.614352 104.875235 \n", "L 259.614352 73.419115 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 262.860898 107.412374 \n", "L 262.860898 76.180143 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 266.107443 90.907263 \n", "L 266.107443 58.247171 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 269.353989 123.883242 \n", "L 269.353989 94.147357 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 272.600534 133.999838 \n", "L 272.600534 105.223428 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 275.84708 126.413854 \n", "L 275.84708 96.914911 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 279.093625 107.412374 \n", "L 279.093625 76.180143 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 282.34017 127.678802 \n", "L 282.34017 98.299047 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 285.586716 160.472229 \n", "L 285.586716 134.381785 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 288.833261 109.948704 \n", "L 288.833261 78.941979 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 292.079807 164.242539 \n", "L 292.079807 138.558724 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 295.326352 160.472229 \n", "L 295.326352 134.381785 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 298.572898 195.516642 \n", "L 298.572898 173.511703 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 301.819443 198.005096 \n", "L 301.819443 176.321415 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 305.065989 206.694903 \n", "L 305.065989 186.175192 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 308.312534 222.734688 \n", "L 308.312534 204.573489 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 311.55908 225.188467 \n", "L 311.55908 207.417876 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 314.805625 234.956377 \n", "L 314.805625 218.842633 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 318.05217 241.014952 \n", "L 318.05217 226.029474 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 321.298716 254.161983 \n", "L 321.298716 242.022359 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 324.545261 263.464207 \n", "L 324.545261 253.912801 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 327.791807 265.730595 \n", "L 327.791807 256.94458 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 331.038352 265.730595 \n", "L 331.038352 256.94458 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 334.284898 274.228094 \n", "L 334.284898 269.639747 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 337.531443 273.258462 \n", "L 337.531443 267.960295 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 340.777989 273.258462 \n", "L 340.777989 267.960295 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 344.024534 275.907545 \n", "L 344.024534 273.258462 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 347.27108 275.907545 \n", "L 347.27108 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 350.517625 275.907545 \n", "L 350.517625 273.258462 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 353.76417 275.907545 \n", "L 353.76417 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 357.010716 275.907545 \n", "L 357.010716 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 360.257261 275.907545 \n", "L 360.257261 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 363.503807 275.907545 \n", "L 363.503807 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 366.750352 275.907545 \n", "L 366.750352 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 369.996898 275.907545 \n", "L 369.996898 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 373.243443 275.907545 \n", "L 373.243443 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 376.489989 275.907545 \n", "L 376.489989 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 379.736534 275.907545 \n", "L 379.736534 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 382.98308 275.907545 \n", "L 382.98308 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 386.229625 275.907545 \n", "L 386.229625 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <defs>\n", "     <path id=\"md5f0a7a939\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p568e3b97e5)\">\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"64.821625\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"68.06817\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"71.314716\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"74.561261\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"77.807807\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"81.054352\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"84.300898\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"87.547443\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"90.793989\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"94.040534\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"97.28708\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"100.533625\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"103.78017\" y=\"274.583004\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"107.026716\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"110.273261\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"113.519807\" y=\"271.93392\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"116.766352\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"120.012898\" y=\"262.662129\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"123.259443\" y=\"261.337587\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"126.505989\" y=\"254.714879\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"129.752534\" y=\"248.092171\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"132.99908\" y=\"242.794004\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"136.245625\" y=\"240.144921\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"139.49217\" y=\"220.276797\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"142.738716\" y=\"203.057755\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"145.985261\" y=\"187.163256\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"149.231807\" y=\"180.540548\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"152.478352\" y=\"131.532507\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"155.724898\" y=\"155.374257\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"158.971443\" y=\"140.804299\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"162.217989\" y=\"135.506132\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"165.464534\" y=\"132.857049\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"168.71108\" y=\"130.207966\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"171.957625\" y=\"123.585257\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"175.20417\" y=\"106.366216\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"178.450716\" y=\"116.962549\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"181.697261\" y=\"71.928134\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"184.943807\" y=\"78.550842\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"188.190352\" y=\"78.550842\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"191.436898\" y=\"103.717133\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"194.683443\" y=\"91.796258\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"197.929989\" y=\"53.384551\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"201.176534\" y=\"77.2263\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"204.42308\" y=\"71.928134\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"207.669625\" y=\"69.279051\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"210.91617\" y=\"56.033634\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"214.162716\" y=\"87.822633\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"217.409261\" y=\"69.279051\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"220.655807\" y=\"74.577217\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"223.902352\" y=\"40.139135\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"227.148898\" y=\"71.928134\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"230.395443\" y=\"109.0153\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"233.641989\" y=\"53.384551\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"236.888534\" y=\"86.498092\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"240.13508\" y=\"71.928134\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"243.381625\" y=\"74.577217\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"246.62817\" y=\"81.199925\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"249.874716\" y=\"63.980884\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"253.121261\" y=\"97.094425\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"256.367807\" y=\"98.418966\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"259.614352\" y=\"89.147175\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"262.860898\" y=\"91.796258\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"266.107443\" y=\"74.577217\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"269.353989\" y=\"109.0153\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"272.600534\" y=\"119.611633\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"275.84708\" y=\"111.664383\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"279.093625\" y=\"91.796258\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"282.34017\" y=\"112.988924\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"285.586716\" y=\"147.427007\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"288.833261\" y=\"94.445342\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"292.079807\" y=\"151.400632\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"295.326352\" y=\"147.427007\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"298.572898\" y=\"184.514173\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"301.819443\" y=\"187.163256\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"305.065989\" y=\"196.435047\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"308.312534\" y=\"213.654088\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"311.55908\" y=\"216.303172\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"314.805625\" y=\"226.899505\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"318.05217\" y=\"233.522213\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"321.298716\" y=\"248.092171\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"324.545261\" y=\"258.688504\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"327.791807\" y=\"261.337587\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"331.038352\" y=\"261.337587\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"334.284898\" y=\"271.93392\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"337.531443\" y=\"270.609379\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"340.777989\" y=\"270.609379\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"344.024534\" y=\"274.583004\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"347.27108\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"350.517625\" y=\"274.583004\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"353.76417\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"357.010716\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"360.257261\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"363.503807\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"366.750352\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"369.996898\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"373.243443\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"376.489989\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"379.736534\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"382.98308\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#md5f0a7a939\" x=\"386.229625\" y=\"275.907545\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 46.**********.907545 \n", "L 46.965625 9.795545 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 404.**********.907545 \n", "L 404.085625 9.795545 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 46.**********.907545 \n", "L 404.**********.907545 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 46.965625 9.795545 \n", "L 404.085625 9.795545 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 63.198352 275.907545 \n", "L 63.198352 275.907498 \n", "L 92.417261 275.850468 \n", "L 92.417261 275.791023 \n", "L 95.663807 275.791023 \n", "L 95.663807 275.678539 \n", "L 98.910352 275.678539 \n", "L 98.910352 275.47413 \n", "L 102.156898 275.47413 \n", "L 102.156898 275.117395 \n", "L 105.403443 275.117395 \n", "L 105.403443 274.519462 \n", "L 108.649989 274.519462 \n", "L 108.649989 273.556866 \n", "L 111.896534 273.556866 \n", "L 111.896534 272.068357 \n", "L 115.14308 272.068357 \n", "L 115.14308 269.85724 \n", "L 118.389625 269.85724 \n", "L 118.389625 266.701692 \n", "L 121.63617 266.701692 \n", "L 121.63617 262.374522 \n", "L 124.882716 262.374522 \n", "L 124.882716 256.671805 \n", "L 128.129261 256.671805 \n", "L 128.129261 249.447143 \n", "L 131.375807 249.447143 \n", "L 131.375807 240.645687 \n", "L 134.622352 240.645687 \n", "L 134.622352 230.330373 \n", "L 137.868898 230.330373 \n", "L 137.868898 218.69308 \n", "L 141.115443 218.69308 \n", "L 141.115443 206.045901 \n", "L 144.361989 206.045901 \n", "L 144.361989 192.79205 \n", "L 147.608534 192.79205 \n", "L 147.608534 179.380916 \n", "L 150.85508 179.380916 \n", "L 150.85508 166.255844 \n", "L 154.101625 166.255844 \n", "L 154.101625 153.804971 \n", "L 157.34817 153.804971 \n", "L 157.34817 142.324358 \n", "L 160.594716 142.324358 \n", "L 160.594716 131.999082 \n", "L 163.841261 131.999082 \n", "L 163.841261 122.90327 \n", "L 167.087807 122.90327 \n", "L 167.087807 115.015822 \n", "L 170.334352 115.015822 \n", "L 170.334352 108.245888 \n", "L 173.580898 108.245888 \n", "L 173.580898 102.4616 \n", "L 176.827443 102.4616 \n", "L 176.827443 97.516604 \n", "L 180.073989 97.516604 \n", "L 180.073989 93.270852 \n", "L 183.320534 93.270852 \n", "L 183.320534 89.604072 \n", "L 186.56708 89.604072 \n", "L 186.56708 86.421876 \n", "L 189.813625 86.421876 \n", "L 189.813625 83.655524 \n", "L 193.06017 83.655524 \n", "L 193.06017 81.257088 \n", "L 196.306716 81.257088 \n", "L 196.306716 79.192253 \n", "L 199.553261 79.192253 \n", "L 199.553261 77.433146 \n", "L 202.799807 77.433146 \n", "L 202.799807 75.953303 \n", "L 206.046352 75.953303 \n", "L 206.046352 74.72605 \n", "L 209.292898 74.72605 \n", "L 209.292898 73.726344 \n", "L 212.539443 73.726344 \n", "L 212.539443 72.934885 \n", "L 215.785989 72.934885 \n", "L 215.785989 72.342519 \n", "L 219.032534 72.342519 \n", "L 219.032534 71.9529 \n", "L 222.27908 71.9529 \n", "L 222.27908 71.782042 \n", "L 228.77217 71.854534 \n", "L 228.77217 72.197284 \n", "L 232.018716 72.197284 \n", "L 232.018716 72.832439 \n", "L 235.265261 72.832439 \n", "L 235.265261 73.771334 \n", "L 238.511807 73.771334 \n", "L 238.511807 75.011025 \n", "L 241.758352 75.011025 \n", "L 241.758352 76.534327 \n", "L 245.004898 76.534327 \n", "L 245.004898 78.313503 \n", "L 248.251443 78.313503 \n", "L 248.251443 80.317014 \n", "L 251.497989 80.317014 \n", "L 251.497989 82.518158 \n", "L 254.744534 82.518158 \n", "L 254.744534 84.904109 \n", "L 257.99108 84.904109 \n", "L 257.99108 87.483858 \n", "L 261.237625 87.483858 \n", "L 261.237625 90.293849 \n", "L 264.48417 90.293849 \n", "L 264.48417 93.400633 \n", "L 267.730716 93.400633 \n", "L 267.730716 96.900404 \n", "L 270.977261 96.900404 \n", "L 270.977261 100.915617 \n", "L 274.223807 100.915617 \n", "L 274.223807 105.588823 \n", "L 277.470352 105.588823 \n", "L 277.470352 111.073478 \n", "L 280.716898 111.073478 \n", "L 280.716898 117.521022 \n", "L 283.963443 117.521022 \n", "L 283.963443 125.063513 \n", "L 287.209989 125.063513 \n", "L 287.209989 133.791882 \n", "L 290.456534 133.791882 \n", "L 290.456534 143.731582 \n", "L 293.70308 143.731582 \n", "L 293.70308 154.819609 \n", "L 296.949625 154.819609 \n", "L 296.949625 166.888598 \n", "L 300.19617 166.888598 \n", "L 300.19617 179.664026 \n", "L 303.442716 179.664026 \n", "L 303.442716 192.77874 \n", "L 306.689261 192.77874 \n", "L 306.689261 205.805259 \n", "L 309.935807 205.805259 \n", "L 309.935807 218.301599 \n", "L 313.182352 218.301599 \n", "L 313.182352 229.86233 \n", "L 316.428898 229.86233 \n", "L 316.428898 240.164676 \n", "L 319.675443 240.164676 \n", "L 319.675443 249.000525 \n", "L 322.921989 249.000525 \n", "L 322.921989 256.288799 \n", "L 326.168534 256.288799 \n", "L 326.168534 262.067622 \n", "L 329.41508 262.067622 \n", "L 329.41508 266.470354 \n", "L 332.661625 266.470354 \n", "L 332.661625 269.692506 \n", "L 335.90817 269.692506 \n", "L 335.90817 271.957236 \n", "L 339.154716 271.957236 \n", "L 339.154716 273.485732 \n", "L 342.401261 273.485732 \n", "L 342.401261 274.476197 \n", "L 345.647807 274.476197 \n", "L 345.647807 275.092375 \n", "L 348.894352 275.092375 \n", "L 348.894352 275.460368 \n", "L 352.140898 275.460368 \n", "L 352.140898 275.671338 \n", "L 355.387443 275.671338 \n", "L 355.387443 275.787441 \n", "L 368.373625 275.895013 \n", "L 368.373625 275.902086 \n", "L 387.852898 275.907498 \n", "L 387.852898 275.907545 \n", "\" clip-path=\"url(#p568e3b97e5)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 2; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 335.946563 22.893983 \n", "L 355.946563 22.893983 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 2; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- model -->\n", "     <g transform=\"translate(363.946563 26.393983) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6d\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"97.412109\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"158.59375\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"222.070312\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"283.59375\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"LineCollection_2\">\n", "     <path d=\"M 345.946563 42.572108 \n", "L 345.946563 32.572108 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_21\"/>\n", "    <g id=\"line2d_22\">\n", "     <g>\n", "      <use xlink:href=\"#md5f0a7a939\" x=\"345.946563\" y=\"37.572108\" style=\"stroke: #000000\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_22\">\n", "     <!-- data -->\n", "     <g transform=\"translate(363.946563 41.072108) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-64\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"163.964844\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p568e3b97e5\">\n", "   <rect x=\"46.965625\" y=\"9.795545\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# construct model representation for comparison with data histogram\n", "a, b, sigma = m.values\n", "\n", "# get expected content per bin from cdf, sum over the individual cdfs\n", "v = np.diff(np.sum(norm.cdf(ax_x.edges[:, np.newaxis], mu(y, a, b), sigma), axis=1))\n", "\n", "plt.stairs(v, ax_x.edges, label=\"model\", zorder=5, lw=2)\n", "plt.errorbar(ax_x.centers, h1.values(), h1.variances() ** 0.5, fmt=\"ok\", label=\"data\")\n", "plt.xlabel(\"x\")\n", "plt.ylabel(\"events\")\n", "plt.legend(frameon=False);"]}, {"cell_type": "markdown", "id": "8", "metadata": {}, "source": ["## Fit without conditional variable\n", "\n", "We can also ignore the dependence of $x$ and $y$ and just fit the total $x$ distribution with a model built from the distribution of $y$ values. This also works in this case, but information is lost and therefore the parameter uncertainties become larger than in the previous case.\n", "\n", "On top of that, the calculation is much slower, because building the pdf is more expensive. We parallelise the computation with numba."]}, {"cell_type": "code", "execution_count": null, "id": "9", "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = -8.774e+04 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 95 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 1.33e-06 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\"> time = 12.5 sec </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> a </td>\n", "        <td> 0.002 </td>\n", "        <td> 0.029 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> b </td>\n", "        <td> 0.500 </td>\n", "        <td> 0.005 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 2 </th>\n", "        <td> sigma </td>\n", "        <td> 0.98 </td>\n", "        <td> 0.04 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> a </th>\n", "        <th> b </th>\n", "        <th> sigma </th>\n", "    </tr>\n", "    <tr>\n", "        <th> a </th>\n", "        <td> 0.000839 </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 0.004e-3 <strong>(0.030)</strong> </td>\n", "        <td style=\"background-color:rgb(246,246,250);color:black\"> -0 <strong>(-0.027)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> b </th>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 0.004e-3 <strong>(0.030)</strong> </td>\n", "        <td> 2.43e-05 </td>\n", "        <td style=\"background-color:rgb(157,157,250);color:black\"> -0.141e-3 <strong>(-0.718)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> sigma </th>\n", "        <td style=\"background-color:rgb(246,246,250);color:black\"> -0 <strong>(-0.027)</strong> </td>\n", "        <td style=\"background-color:rgb(157,157,250);color:black\"> -0.141e-3 <strong>(-0.718)</strong> </td>\n", "        <td> 0.0016 </td>\n", "    </tr>\n", "</table><?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"360pt\" height=\"288pt\" viewBox=\"0 0 360 288\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:01:21.558091</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 288 \n", "L 360 288 \n", "L 360 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 25.**********.321635 \n", "L 356.99976 268.321635 \n", "L 356.99976 13.598677 \n", "L 25.903365 13.598677 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"PolyCollection_1\">\n", "    <path d=\"M 40.953201 255.984312 \n", "L 40.953201 256.743319 \n", "L 45.656275 256.743319 \n", "L 50.359349 256.743319 \n", "L 55.062423 256.743319 \n", "L 59.765496 256.743319 \n", "L 64.46857 256.743319 \n", "L 69.171644 256.743319 \n", "L 71.523181 256.743319 \n", "L 73.874718 256.743319 \n", "L 76.226255 256.743319 \n", "L 78.577791 256.743319 \n", "L 83.280865 256.743319 \n", "L 87.983939 256.743319 \n", "L 92.687013 256.743319 \n", "L 97.390087 256.743319 \n", "L 102.09316 256.743319 \n", "L 106.796234 256.743319 \n", "L 111.499308 256.743319 \n", "L 116.202382 256.743319 \n", "L 120.905456 256.743319 \n", "L 125.608529 256.743319 \n", "L 130.311603 256.743319 \n", "L 135.014677 256.743319 \n", "L 139.717751 256.743319 \n", "L 144.420825 256.743319 \n", "L 149.123898 256.743319 \n", "L 153.826972 256.743319 \n", "L 163.23312 256.743319 \n", "L 172.639267 256.743319 \n", "L 182.045415 256.743319 \n", "L 191.451562 256.743319 \n", "L 200.85771 256.743319 \n", "L 210.263858 256.743319 \n", "L 219.670005 256.743319 \n", "L 229.076153 256.743319 \n", "L 233.779227 256.743319 \n", "L 238.4823 256.743319 \n", "L 243.185374 256.743319 \n", "L 247.888448 256.743319 \n", "L 252.591522 256.743319 \n", "L 257.294596 256.743319 \n", "L 261.997669 256.743319 \n", "L 266.700743 256.743319 \n", "L 285.513038 256.743319 \n", "L 304.325334 256.743319 \n", "L 306.67687 256.743319 \n", "L 309.028407 256.743319 \n", "L 311.379944 256.743319 \n", "L 313.731481 256.743319 \n", "L 318.434555 256.743319 \n", "L 323.137629 256.743319 \n", "L 327.840702 256.743319 \n", "L 332.543776 256.743319 \n", "L 337.24685 256.743319 \n", "L 341.949924 256.743319 \n", "L 341.949924 256.30934 \n", "L 341.949924 256.30934 \n", "L 337.24685 255.843476 \n", "L 332.543776 254.976083 \n", "L 327.840702 253.453664 \n", "L 323.137629 250.934489 \n", "L 318.434555 247.003902 \n", "L 313.731481 241.219815 \n", "L 311.379944 237.505415 \n", "L 309.028407 233.189321 \n", "L 306.67687 228.245976 \n", "L 304.325334 222.664906 \n", "L 285.513038 159.699894 \n", "L 266.700743 95.718705 \n", "L 261.997669 84.003191 \n", "L 257.294596 74.20287 \n", "L 252.591522 66.111697 \n", "L 247.888448 59.448479 \n", "L 243.185374 53.911659 \n", "L 238.4823 49.218719 \n", "L 233.779227 45.132655 \n", "L 229.076153 41.478659 \n", "L 219.670005 35.115665 \n", "L 210.263858 30.025364 \n", "L 200.85771 26.627908 \n", "L 191.451562 25.176994 \n", "L 182.045415 25.459847 \n", "L 172.639267 27.113914 \n", "L 163.23312 30.103272 \n", "L 153.826972 34.751147 \n", "L 149.123898 37.823977 \n", "L 144.420825 41.470584 \n", "L 139.717751 45.775338 \n", "L 135.014677 50.870196 \n", "L 130.311603 56.948403 \n", "L 125.608529 64.262196 \n", "L 120.905456 73.098173 \n", "L 116.202382 83.727743 \n", "L 111.499308 96.336016 \n", "L 106.796234 110.941248 \n", "L 102.09316 127.326278 \n", "L 97.390087 145.008111 \n", "L 92.687013 163.266458 \n", "L 87.983939 181.23549 \n", "L 83.280865 198.040618 \n", "L 78.577791 212.943956 \n", "L 76.226255 219.518294 \n", "L 73.874718 225.457479 \n", "L 71.523181 230.747964 \n", "L 69.171644 235.394349 \n", "L 64.46857 242.851119 \n", "L 59.765496 248.136036 \n", "L 55.062423 251.672236 \n", "L 50.359349 253.905398 \n", "L 45.656275 255.236148 \n", "L 40.953201 255.984312 \n", "z\n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mbcbf0dd4b5\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"31.807315\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −8 -->\n", "      <g transform=\"translate(24.436222 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"71.184633\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −6 -->\n", "      <g transform=\"translate(63.813539 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"110.561951\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(103.190857 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"149.939268\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(142.568175 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"189.316586\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(186.135336 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"228.693904\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(225.512654 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"268.071221\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(264.889971 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"307.448539\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(304.267289 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mbcbf0dd4b5\" x=\"346.825857\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(343.644607 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m863f68aee2\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"256.743319\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(3.00024 260.542537) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"228.060985\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(3.00024 231.860204) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"199.378652\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(3.00024 203.177871) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"170.696318\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(3.00024 174.495537) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"142.013985\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(3.00024 145.813204) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"113.331651\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(3.00024 117.13087) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"84.649318\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 1.2 -->\n", "      <g transform=\"translate(3.00024 88.448537) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"55.966984\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 1.4 -->\n", "      <g transform=\"translate(3.00024 59.766203) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m863f68aee2\" x=\"25.903365\" y=\"27.284651\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 1.6 -->\n", "      <g transform=\"translate(3.00024 31.08387) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- 1e6 -->\n", "     <g transform=\"translate(25.903365 10.598677) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-31\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"125.146484\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 43.963168 256.743319 \n", "L 43.963168 256.743032 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 49.983103 256.743319 \n", "L 49.983103 256.743032 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 56.003037 256.743235 \n", "L 56.003037 256.742829 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 62.022972 256.741848 \n", "L 62.022972 256.740774 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 68.042906 256.740708 \n", "L 68.042906 256.739333 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 74.062841 256.739541 \n", "L 74.062841 256.737918 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 80.082775 256.738357 \n", "L 80.082775 256.736521 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 86.10271 256.734345 \n", "L 86.10271 256.731928 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 92.122644 256.731228 \n", "L 92.122644 256.728448 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 98.142578 256.724663 \n", "L 98.142578 256.721245 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 104.162513 256.724251 \n", "L 104.162513 256.720797 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 110.182447 256.722049 \n", "L 110.182447 256.71841 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 116.202382 256.721498 \n", "L 116.202382 256.717814 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 122.222316 256.721774 \n", "L 122.222316 256.718112 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 128.242251 256.716526 \n", "L 128.242251 256.71246 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 134.262185 256.716388 \n", "L 134.262185 256.712311 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 140.28212 256.710705 \n", "L 140.28212 256.706234 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 146.302054 256.715696 \n", "L 146.302054 256.711569 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 152.321989 256.714311 \n", "L 152.321989 256.710086 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 158.341923 256.710983 \n", "L 158.341923 256.70653 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 164.361857 256.713202 \n", "L 164.361857 256.7089 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 170.381792 256.7089 \n", "L 170.381792 256.704311 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 176.401726 256.712786 \n", "L 176.401726 256.708455 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 182.421661 256.712509 \n", "L 182.421661 256.708159 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 188.441595 256.709039 \n", "L 188.441595 256.704459 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 194.46153 256.714727 \n", "L 194.46153 256.710531 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 200.481464 256.710705 \n", "L 200.481464 256.706234 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 206.501399 256.712648 \n", "L 206.501399 256.708307 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 212.521333 256.713341 \n", "L 212.521333 256.709048 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 218.541268 256.710844 \n", "L 218.541268 256.706382 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 224.561202 256.715142 \n", "L 224.561202 256.710976 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 230.581136 256.716111 \n", "L 230.581136 256.712014 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 236.601071 256.712648 \n", "L 236.601071 256.708307 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 242.621005 256.717494 \n", "L 242.621005 256.713499 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 248.64094 256.719014 \n", "L 248.64094 256.715134 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 254.660874 256.715142 \n", "L 254.660874 256.710976 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 260.680809 256.722325 \n", "L 260.680809 256.718708 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 266.700743 256.7186 \n", "L 266.700743 256.714688 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 272.720678 256.724251 \n", "L 272.720678 256.720797 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 278.740612 256.729457 \n", "L 278.740612 256.72649 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 284.760547 256.730684 \n", "L 284.760547 256.727845 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 290.780481 256.73421 \n", "L 290.780481 256.731776 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 296.800415 256.736227 \n", "L 296.800415 256.734062 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 302.82035 256.738621 \n", "L 302.82035 256.73683 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 308.840284 256.739932 \n", "L 308.840284 256.738388 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 314.860219 256.742095 \n", "L 314.860219 256.741101 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 320.880153 256.742217 \n", "L 320.880153 256.741266 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 326.900088 256.742922 \n", "L 326.900088 256.742281 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 332.920022 256.743137 \n", "L 332.920022 256.74264 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 338.939957 256.743319 \n", "L 338.939957 256.743032 \n", "\" clip-path=\"url(#p2f76972811)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <defs>\n", "     <path id=\"mac0f4b8a90\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p2f76972811)\">\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"43.963168\" y=\"256.743175\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"49.983103\" y=\"256.743175\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"56.003037\" y=\"256.743032\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"62.022972\" y=\"256.741311\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"68.042906\" y=\"256.74002\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"74.062841\" y=\"256.73873\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"80.082775\" y=\"256.737439\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"86.10271\" y=\"256.733137\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"92.122644\" y=\"256.729838\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"98.142578\" y=\"256.722954\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"104.162513\" y=\"256.722524\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"110.182447\" y=\"256.720229\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"116.202382\" y=\"256.719656\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"122.222316\" y=\"256.719943\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"128.242251\" y=\"256.714493\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"134.262185\" y=\"256.71435\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"140.28212\" y=\"256.70847\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"146.302054\" y=\"256.713633\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"152.321989\" y=\"256.712198\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"158.341923\" y=\"256.708757\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"164.361857\" y=\"256.711051\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"170.381792\" y=\"256.706605\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"176.401726\" y=\"256.710621\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"182.421661\" y=\"256.710334\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"188.441595\" y=\"256.706749\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"194.46153\" y=\"256.712629\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"200.481464\" y=\"256.70847\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"206.501399\" y=\"256.710477\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"212.521333\" y=\"256.711195\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"218.541268\" y=\"256.708613\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"224.561202\" y=\"256.713059\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"230.581136\" y=\"256.714063\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"236.601071\" y=\"256.710477\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"242.621005\" y=\"256.715497\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"248.64094\" y=\"256.717074\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"254.660874\" y=\"256.713059\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"260.680809\" y=\"256.720516\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"266.700743\" y=\"256.716644\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"272.720678\" y=\"256.722524\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"278.740612\" y=\"256.727974\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"284.760547\" y=\"256.729264\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"290.780481\" y=\"256.732993\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"296.800415\" y=\"256.735144\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"302.82035\" y=\"256.737726\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"308.840284\" y=\"256.73916\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"314.860219\" y=\"256.741598\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"320.880153\" y=\"256.741741\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"326.900088\" y=\"256.742602\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"332.920022\" y=\"256.742889\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mac0f4b8a90\" x=\"338.939957\" y=\"256.743175\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 25.**********.321635 \n", "L 25.903365 13.598677 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 356.99976 268.321635 \n", "L 356.99976 13.598677 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 25.**********.321635 \n", "L 356.99976 268.321635 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 25.903365 13.598677 \n", "L 356.99976 13.598677 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p2f76972811\">\n", "   <rect x=\"25.903365\" y=\"13.598677\" width=\"331.096395\" height=\"254.722958\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = -8.774e+04                 │              Nfcn = 95               │\n", "│ EDM = 1.33e-06 (Goal: 0.0002)    │           time = 12.5 sec            │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬───────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name  │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼───────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ a     │   0.002   │   0.029   │            │            │         │         │       │\n", "│ 1 │ b     │   0.500   │   0.005   │            │            │         │         │       │\n", "│ 2 │ sigma │   0.98    │   0.04    │            │            │    0    │         │       │\n", "└───┴───────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌───────┬───────────────────────────────┐\n", "│       │         a         b     sigma │\n", "├───────┼───────────────────────────────┤\n", "│     a │  0.000839  0.004e-3        -0 │\n", "│     b │  0.004e-3  2.43e-05 -0.141e-3 │\n", "│ sigma │        -0 -0.141e-3    0.0016 │\n", "└───────┴───────────────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["nb.config.THREADING_LAYER = \"workqueue\"\n", "\n", "\n", "@nb.njit(parallel=True, fastmath=True)\n", "def model(x, a, b, sigma):\n", "    mu = a + b * y\n", "    total = np.zeros_like(x)\n", "    for i in nb.prange(len(mu)):\n", "        total += norm_nb.pdf(x, mu[i], sigma)\n", "    return total\n", "\n", "\n", "nll = UnbinnedNLL(x, model)\n", "m2 = Minuit(nll, 0.0, 0.0, 2.0)\n", "m2.limits[\"sigma\"] = (0, None)\n", "m2.migrad()"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"584.39952pt\" height=\"152.39952pt\" viewBox=\"0 0 584.39952 152.39952\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:01:22.050500</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 152.39952 \n", "L 584.39952 152.39952 \n", "L 584.39952 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 44.**********.323582 \n", "L 196.**********.323582 \n", "L 196.665145 22.318125 \n", "L 44.845313 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma73e932919\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma73e932919\" x=\"82.800271\" y=\"117.323582\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- with -->\n", "      <g transform=\"translate(72.193239 131.92202) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-77\" d=\"M 269 3500 \n", "L 844 3500 \n", "L 1563 769 \n", "L 2278 3500 \n", "L 2956 3500 \n", "L 3675 769 \n", "L 4391 3500 \n", "L 4966 3500 \n", "L 4050 0 \n", "L 3372 0 \n", "L 2619 2869 \n", "L 1863 0 \n", "L 1184 0 \n", "L 269 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-68\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-77\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"81.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"109.570312\"/>\n", "       <use xlink:href=\"#DejaVuSans-68\" x=\"148.779297\"/>\n", "      </g>\n", "      <!--  conditional -->\n", "      <g transform=\"translate(53.640114 143.119833) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-20\"/>\n", "       <use xlink:href=\"#DejaVuSans-63\" x=\"31.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"86.767578\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"147.949219\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"211.328125\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"274.804688\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"302.587891\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"341.796875\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"369.580078\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"430.761719\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"494.140625\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"555.419922\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma73e932919\" x=\"158.710187\" y=\"117.323582\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- without -->\n", "      <g transform=\"translate(139.914874 131.92202) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-77\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"81.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"109.570312\"/>\n", "       <use xlink:href=\"#DejaVuSans-68\" x=\"148.779297\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"212.158203\"/>\n", "       <use xlink:href=\"#DejaVuSans-75\" x=\"273.339844\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"336.71875\"/>\n", "      </g>\n", "      <!--  conditional -->\n", "      <g transform=\"translate(129.550031 143.119833) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-20\"/>\n", "       <use xlink:href=\"#DejaVuSans-63\" x=\"31.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"86.767578\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"147.949219\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"211.328125\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"274.804688\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"302.587891\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"341.796875\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"369.580078\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"430.761719\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"494.140625\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"555.419922\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"mc4b7e205ae\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"44.845313\" y=\"110.808243\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- −0.05 -->\n", "      <g transform=\"translate(7.2 114.607462) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"44.845313\" y=\"69.820854\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(15.579688 73.620072) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"44.845313\" y=\"28.833464\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(15.579688 32.632683) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_6\">\n", "    <path d=\"M 44.845313 69.820854 \n", "L 196.665145 69.820854 \n", "\" clip-path=\"url(#pddeb5bebed)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 82.800271 88.097597 \n", "L 82.800271 68.58219 \n", "\" clip-path=\"url(#pddeb5bebed)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_2\">\n", "    <path d=\"M 158.710187 91.904735 \n", "L 158.710187 44.402007 \n", "\" clip-path=\"url(#pddeb5bebed)\" style=\"fill: none; stroke: #ff0000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_7\">\n", "    <defs>\n", "     <path id=\"m805ada5130\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pddeb5bebed)\">\n", "     <use xlink:href=\"#m805ada5130\" x=\"82.800271\" y=\"78.339894\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_8\">\n", "    <defs>\n", "     <path id=\"m048dce8754\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #ff0000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pddeb5bebed)\">\n", "     <use xlink:href=\"#m048dce8754\" x=\"158.710187\" y=\"68.153371\" style=\"fill: #ff0000; stroke: #ff0000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 44.**********.323582 \n", "L 44.845313 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 196.**********.323582 \n", "L 196.665145 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 44.**********.323582 \n", "L 196.**********.323582 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 44.845313 22.318125 \n", "L 196.665145 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_6\">\n", "    <!-- a -->\n", "    <g transform=\"translate(117.078354 16.318125) scale(0.12 -0.12)\">\n", "     <use xlink:href=\"#DejaVuSans-61\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 238.29375 117.323582 \n", "L 390.113583 117.323582 \n", "L 390.113583 22.318125 \n", "L 238.29375 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma73e932919\" x=\"276.248708\" y=\"117.323582\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- with -->\n", "      <g transform=\"translate(265.641677 131.92202) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-77\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"81.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"109.570312\"/>\n", "       <use xlink:href=\"#DejaVuSans-68\" x=\"148.779297\"/>\n", "      </g>\n", "      <!--  conditional -->\n", "      <g transform=\"translate(247.088552 143.119833) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-20\"/>\n", "       <use xlink:href=\"#DejaVuSans-63\" x=\"31.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"86.767578\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"147.949219\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"211.328125\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"274.804688\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"302.587891\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"341.796875\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"369.580078\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"430.761719\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"494.140625\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"555.419922\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#ma73e932919\" x=\"352.158624\" y=\"117.323582\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- without -->\n", "      <g transform=\"translate(333.363312 131.92202) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-77\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"81.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"109.570312\"/>\n", "       <use xlink:href=\"#DejaVuSans-68\" x=\"148.779297\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"212.158203\"/>\n", "       <use xlink:href=\"#DejaVuSans-75\" x=\"273.339844\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"336.71875\"/>\n", "      </g>\n", "      <!--  conditional -->\n", "      <g transform=\"translate(322.998468 143.119833) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-20\"/>\n", "       <use xlink:href=\"#DejaVuSans-63\" x=\"31.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"86.767578\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"147.949219\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"211.328125\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"274.804688\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"302.587891\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"341.796875\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"369.580078\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"430.761719\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"494.140625\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"555.419922\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"238.29375\" y=\"93.921101\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.495 -->\n", "      <g transform=\"translate(202.665625 97.72032) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"238.29375\" y=\"69.820854\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.500 -->\n", "      <g transform=\"translate(202.665625 73.620073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"238.29375\" y=\"45.720607\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.505 -->\n", "      <g transform=\"translate(202.665625 49.519825) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"222.65625\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 238.29375 69.820854 \n", "L 390.113583 69.820854 \n", "\" clip-path=\"url(#p0d5fac6ec2)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_3\">\n", "    <path d=\"M 276.248708 83.504163 \n", "L 276.248708 62.476418 \n", "\" clip-path=\"url(#p0d5fac6ec2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_4\">\n", "    <path d=\"M 352.158624 91.728471 \n", "L 352.158624 44.225742 \n", "\" clip-path=\"url(#p0d5fac6ec2)\" style=\"fill: none; stroke: #ff0000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <g clip-path=\"url(#p0d5fac6ec2)\">\n", "     <use xlink:href=\"#m805ada5130\" x=\"276.248708\" y=\"72.990291\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <g clip-path=\"url(#p0d5fac6ec2)\">\n", "     <use xlink:href=\"#m048dce8754\" x=\"352.158624\" y=\"67.977107\" style=\"fill: #ff0000; stroke: #ff0000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 238.29375 117.323582 \n", "L 238.29375 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 390.113583 117.323582 \n", "L 390.113583 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 238.29375 117.323582 \n", "L 390.113583 117.323582 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 238.29375 22.318125 \n", "L 390.113583 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- b -->\n", "    <g transform=\"translate(310.394604 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-62\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_3\">\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 425.379688 117.323582 \n", "L 577.19952 117.323582 \n", "L 577.19952 22.318125 \n", "L 425.379688 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_5\">\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#ma73e932919\" x=\"463.334646\" y=\"117.323582\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- with -->\n", "      <g transform=\"translate(452.727614 131.92202) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-77\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"81.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"109.570312\"/>\n", "       <use xlink:href=\"#DejaVuSans-68\" x=\"148.779297\"/>\n", "      </g>\n", "      <!--  conditional -->\n", "      <g transform=\"translate(434.174489 143.119833) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-20\"/>\n", "       <use xlink:href=\"#DejaVuSans-63\" x=\"31.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"86.767578\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"147.949219\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"211.328125\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"274.804688\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"302.587891\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"341.796875\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"369.580078\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"430.761719\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"494.140625\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"555.419922\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#ma73e932919\" x=\"539.244562\" y=\"117.323582\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- without -->\n", "      <g transform=\"translate(520.449249 131.92202) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-77\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"81.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"109.570312\"/>\n", "       <use xlink:href=\"#DejaVuSans-68\" x=\"148.779297\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"212.158203\"/>\n", "       <use xlink:href=\"#DejaVuSans-75\" x=\"273.339844\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"336.71875\"/>\n", "      </g>\n", "      <!--  conditional -->\n", "      <g transform=\"translate(510.084406 143.119833) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-20\"/>\n", "       <use xlink:href=\"#DejaVuSans-63\" x=\"31.787109\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"86.767578\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"147.949219\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"211.328125\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"274.804688\"/>\n", "       <use xlink:href=\"#DejaVuSans-74\" x=\"302.587891\"/>\n", "       <use xlink:href=\"#DejaVuSans-69\" x=\"341.796875\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"369.580078\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"430.761719\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"494.140625\"/>\n", "       <use xlink:href=\"#DejaVuSans-6c\" x=\"555.419922\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_6\">\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"425.379688\" y=\"99.545834\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0.95 -->\n", "      <g transform=\"translate(396.114063 103.345053) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"425.379688\" y=\"69.820854\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(396.114063 73.620073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#mc4b7e205ae\" x=\"425.379688\" y=\"40.095874\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 1.05 -->\n", "      <g transform=\"translate(396.114063 43.895092) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_22\">\n", "    <path d=\"M 425.379688 69.820854 \n", "L 577.19952 69.820854 \n", "\" clip-path=\"url(#p745fac5b0c)\" style=\"fill: none; stroke-dasharray: 5.55,2.4; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_5\">\n", "    <path d=\"M 463.334646 82.87028 \n", "L 463.334646 72.862651 \n", "\" clip-path=\"url(#p745fac5b0c)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_6\">\n", "    <path d=\"M 539.244562 104.954376 \n", "L 539.244562 57.451648 \n", "\" clip-path=\"url(#p745fac5b0c)\" style=\"fill: none; stroke: #ff0000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_23\">\n", "    <g clip-path=\"url(#p745fac5b0c)\">\n", "     <use xlink:href=\"#m805ada5130\" x=\"463.334646\" y=\"77.866466\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <g clip-path=\"url(#p745fac5b0c)\">\n", "     <use xlink:href=\"#m048dce8754\" x=\"539.244562\" y=\"81.203012\" style=\"fill: #ff0000; stroke: #ff0000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 425.379688 117.323583 \n", "L 425.379688 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 577.19952 117.323583 \n", "L 577.19952 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 425.379688 117.323582 \n", "L 577.19952 117.323582 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 425.379688 22.318125 \n", "L 577.19952 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- sigma -->\n", "    <g transform=\"translate(483.166791 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-73\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"52.099609\"/>\n", "     <use xlink:href=\"#DejaVuSans-67\" x=\"79.882812\"/>\n", "     <use xlink:href=\"#DejaVuSans-6d\" x=\"143.359375\"/>\n", "     <use xlink:href=\"#DejaVuSans-61\" x=\"240.771484\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pddeb5bebed\">\n", "   <rect x=\"44.845313\" y=\"22.318125\" width=\"151.819833\" height=\"95.005457\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p0d5fac6ec2\">\n", "   <rect x=\"238.29375\" y=\"22.318125\" width=\"151.819833\" height=\"95.005457\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p745fac5b0c\">\n", "   <rect x=\"425.379688\" y=\"22.318125\" width=\"151.819832\" height=\"95.005457\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 800x200 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 3, figsize=(8, 2), constrained_layout=True)\n", "for par, axi in zip(m.parameters, ax):\n", "    axi.set_title(par)\n", "    t = truth[par]\n", "    axi.axhline(t, ls=\"--\", color=\"0.5\")\n", "    axi.errorbar([\"with\\n conditional\"], m.values[par], m.errors[par], fmt=\"ok\")\n", "    axi.errorbar([\"without\\n conditional\"], m2.values[par], m2.errors[par], fmt=\"or\")\n", "    axi.set_xlim(-0.5, 1.5)\n", "    dt = 2 * m2.errors[par]\n", "    axi.set_ylim(t - dt, t + dt)"]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "Python 3.8.14 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 5}