{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Template fits: comparing two chi-square distributed test statistics\n", "\n", "The builtin binned cost functions in `iminuit.cost` provide a method `pulls` which returns the pull distribution for a given data set. The sum of pulls squared is asymptotically chi-square distributed, but as is explained in the documentation, it is better to use the minimum value of the cost function instead of the pulls to compute this test statistic. The reason is that the cost function has been designed so that the minimum is chi-square distributed and it approaches the asymptotic limit faster than a simple sum of pulls squared.\n", "\n", "We demonstrate this in this example for a Template fit. We generate random samples from a mixed model (gaussian peak over exponential background), which is fitted many times. The distribution of the cost function minimum and alternatively the sum of pulls squared is compared with a chi-square distribution by computing p-values based on the expected distribution. If the test statistic follows the chi-square distribution, the distribution of p-values must be uniform."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "from iminuit import cost, Minuit\n", "import numpy as np\n", "from matplotlib import pyplot as plt\n", "from scipy.stats import chi2\n", "from IPython.display import display"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 13.87 (χ²/ndof = 0.8) </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 101 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 2.91e-08 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\"> time = 0.2 sec </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 1.01e3 </td>\n", "        <td> 0.05e3 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 870 </td>\n", "        <td> 50 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 2.49e+03 </td>\n", "        <td style=\"background-color:rgb(224,224,250);color:black\"> -0.5e3 <strong>(-0.196)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(224,224,250);color:black\"> -0.5e3 <strong>(-0.196)</strong> </td>\n", "        <td> 2.21e+03 </td>\n", "    </tr>\n", "</table><?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"360pt\" height=\"288pt\" viewBox=\"0 0 360 288\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:44:15.234319</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 288 \n", "L 360 288 \n", "L 360 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "L 29.08774 3.00024 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.992832 208.678953 \n", "L 43.992832 197.071097 \n", "L 58.897924 197.071097 \n", "L 58.897924 215.334192 \n", "L 73.803015 215.334192 \n", "L 73.803015 209.227662 \n", "L 88.708107 209.227662 \n", "L 88.708107 215.334192 \n", "L 103.613199 215.334192 \n", "L 103.613199 223.309857 \n", "L 118.518291 223.309857 \n", "L 118.518291 220.850673 \n", "L 133.423383 220.850673 \n", "L 133.423383 225.766509 \n", "L 148.328475 225.766509 \n", "L 148.328475 220.735093 \n", "L 163.233566 220.735093 \n", "L 163.233566 151.543708 \n", "L 178.138658 151.543708 \n", "L 178.138658 33.407623 \n", "L 193.04375 33.407623 \n", "L 193.04375 19.609307 \n", "L 207.948842 19.609307 \n", "L 207.948842 151.470223 \n", "L 222.853934 151.470223 \n", "L 222.853934 233.709316 \n", "L 237.759025 233.709316 \n", "L 237.759025 236.307024 \n", "L 252.664117 236.307024 \n", "L 252.664117 241.345922 \n", "L 267.569209 241.345922 \n", "L 267.569209 243.878409 \n", "L 282.474301 243.878409 \n", "L 282.474301 247.07082 \n", "L 297.379393 247.07082 \n", "L 297.379393 245.789736 \n", "L 312.284485 245.789736 \n", "L 312.284485 250.3044 \n", "L 327.189576 250.3044 \n", "L 327.189576 251.61387 \n", "L 342.094668 251.61387 \n", "L 342.094668 255.778372 \n", "L 342.094668 255.778372 \n", "L 342.094668 255.778372 \n", "L 327.189576 255.778372 \n", "L 327.189576 254.777792 \n", "L 312.284485 254.777792 \n", "L 312.284485 251.207283 \n", "L 297.379393 251.207283 \n", "L 297.379393 252.236248 \n", "L 282.474301 252.236248 \n", "L 282.474301 249.653534 \n", "L 267.569209 249.653534 \n", "L 267.569209 247.565922 \n", "L 252.664117 247.565922 \n", "L 252.664117 243.337286 \n", "L 237.759025 243.337286 \n", "L 237.759025 241.137221 \n", "L 222.853934 241.137221 \n", "L 222.853934 166.894576 \n", "L 207.948842 166.894576 \n", "L 207.948842 42.784583 \n", "L 193.04375 42.784583 \n", "L 193.04375 55.89352 \n", "L 178.138658 55.89352 \n", "L 178.138658 166.957762 \n", "L 163.233566 166.957762 \n", "L 163.233566 229.855921 \n", "L 148.328475 229.855921 \n", "L 148.328475 234.256045 \n", "L 133.423383 234.256045 \n", "L 133.423383 229.945348 \n", "L 118.518291 229.945348 \n", "L 118.518291 232.106265 \n", "L 103.613199 232.106265 \n", "L 103.613199 225.066605 \n", "L 88.708107 225.066605 \n", "L 88.708107 219.622886 \n", "L 73.803015 219.622886 \n", "L 73.803015 225.066605 \n", "L 58.897924 225.066605 \n", "L 58.897924 208.678953 \n", "L 43.992832 208.678953 \n", "L 43.992832 208.678953 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 43.992832 208.678953 \n", "L 43.992832 197.071097 \n", "L 58.897924 197.071097 \n", "L 58.897924 215.334192 \n", "L 73.803015 215.334192 \n", "L 73.803015 209.227662 \n", "L 88.708107 209.227662 \n", "L 88.708107 215.334192 \n", "L 103.613199 215.334192 \n", "L 103.613199 223.309857 \n", "L 118.518291 223.309857 \n", "L 118.518291 220.850673 \n", "L 133.423383 220.850673 \n", "L 133.423383 225.766509 \n", "L 148.328475 225.766509 \n", "L 148.328475 220.735093 \n", "L 163.233566 220.735093 \n", "L 163.233566 151.543708 \n", "L 178.138658 151.543708 \n", "L 178.138658 33.407623 \n", "L 193.04375 33.407623 \n", "L 193.04375 19.609307 \n", "L 207.948842 19.609307 \n", "L 207.948842 151.470223 \n", "L 222.853934 151.470223 \n", "L 222.853934 233.709316 \n", "L 237.759025 233.709316 \n", "L 237.759025 236.307024 \n", "L 252.664117 236.307024 \n", "L 252.664117 241.345922 \n", "L 267.569209 241.345922 \n", "L 267.569209 243.878409 \n", "L 282.474301 243.878409 \n", "L 282.474301 247.07082 \n", "L 297.379393 247.07082 \n", "L 297.379393 245.789736 \n", "L 312.284485 245.789736 \n", "L 312.284485 250.3044 \n", "L 327.189576 250.3044 \n", "L 327.189576 251.61387 \n", "L 342.094668 251.61387 \n", "L 342.094668 255.778372 \n", "L 342.094668 255.778372 \n", "L 342.094668 255.778372 \n", "L 327.189576 255.778372 \n", "L 327.189576 254.777792 \n", "L 312.284485 254.777792 \n", "L 312.284485 251.207283 \n", "L 297.379393 251.207283 \n", "L 297.379393 252.236248 \n", "L 282.474301 252.236248 \n", "L 282.474301 249.653534 \n", "L 267.569209 249.653534 \n", "L 267.569209 247.565922 \n", "L 252.664117 247.565922 \n", "L 252.664117 243.337286 \n", "L 237.759025 243.337286 \n", "L 237.759025 241.137221 \n", "L 222.853934 241.137221 \n", "L 222.853934 166.894576 \n", "L 207.948842 166.894576 \n", "L 207.948842 42.784583 \n", "L 193.04375 42.784583 \n", "L 193.04375 55.89352 \n", "L 178.138658 55.89352 \n", "L 178.138658 166.957762 \n", "L 163.233566 166.957762 \n", "L 163.233566 229.855921 \n", "L 148.328475 229.855921 \n", "L 148.328475 234.256045 \n", "L 133.423383 234.256045 \n", "L 133.423383 229.945348 \n", "L 118.518291 229.945348 \n", "L 118.518291 232.106265 \n", "L 103.613199 232.106265 \n", "L 103.613199 225.066605 \n", "L 88.708107 225.066605 \n", "L 88.708107 219.622886 \n", "L 73.803015 219.622886 \n", "L 73.803015 225.066605 \n", "L 58.897924 225.066605 \n", "L 58.897924 208.678953 \n", "L 43.992832 208.678953 \n", "L 43.992832 208.678953 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"ma273c33618\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"43.992832\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(32.860019 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"81.255561\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(70.122749 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"118.518291\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(107.385478 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"155.78102\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(144.648208 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"193.04375\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(181.910937 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"230.30648\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.25 -->\n", "      <g transform=\"translate(219.173667 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"267.569209\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.50 -->\n", "      <g transform=\"translate(256.436397 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"304.831939\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.75 -->\n", "      <g transform=\"translate(293.699126 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#ma273c33618\" x=\"342.094668\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.00 -->\n", "      <g transform=\"translate(330.961856 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"mf88a082e23\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mf88a082e23\" x=\"29.08774\" y=\"261.203783\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(15.72524 265.003002) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mf88a082e23\" x=\"29.08774\" y=\"203.304646\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(3.00024 207.103865) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mf88a082e23\" x=\"29.08774\" y=\"145.40551\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(3.00024 149.204729) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#mf88a082e23\" x=\"29.08774\" y=\"87.506373\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(3.00024 91.305592) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mf88a082e23\" x=\"29.08774\" y=\"29.607237\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(3.00024 33.406455) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 51.445378 205.791779 \n", "L 51.445378 193.869617 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 66.35047 219.516405 \n", "L 66.35047 209.09456 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 81.255561 227.694101 \n", "L 81.255561 218.286605 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 96.160653 225.518577 \n", "L 96.160653 215.830198 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 111.065745 227.694101 \n", "L 111.065745 218.286605 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 125.970837 236.886146 \n", "L 125.970837 228.780267 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 140.875929 234.732361 \n", "L 140.875929 226.30212 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 155.78102 219.516405 \n", "L 155.78102 209.09456 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 170.686112 158.060181 \n", "L 170.686112 142.014701 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 185.591204 58.67803 \n", "L 185.591204 36.433908 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 200.496296 38.364256 \n", "L 200.496296 15.060303 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 215.401388 182.00156 \n", "L 215.401388 167.866579 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 230.30648 236.348302 \n", "L 230.30648 228.160128 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 245.211571 248.579544 \n", "L 245.211571 242.562489 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 260.116663 247.530997 \n", "L 260.116663 241.29507 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 275.021755 252.726712 \n", "L 275.021755 247.679182 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 289.926847 251.698236 \n", "L 289.926847 246.391693 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 304.831939 250.663727 \n", "L 304.831939 245.110237 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 319.73703 256.261572 \n", "L 319.73703 252.250202 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 334.642122 254.255887 \n", "L 334.642122 249.623956 \n", "\" clip-path=\"url(#p500d5a0283)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <defs>\n", "     <path id=\"m3d396bc9d8\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p500d5a0283)\">\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"51.445378\" y=\"199.830698\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"66.35047\" y=\"214.305482\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"81.255561\" y=\"222.990353\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"96.160653\" y=\"220.674387\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"111.065745\" y=\"222.990353\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"125.970837\" y=\"232.833206\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"140.875929\" y=\"230.517241\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"155.78102\" y=\"214.305482\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"170.686112\" y=\"150.037441\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"185.591204\" y=\"47.555969\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"200.496296\" y=\"26.71228\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"215.401388\" y=\"174.93407\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"230.30648\" y=\"232.254215\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"245.211571\" y=\"245.571016\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"260.116663\" y=\"244.413033\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"275.021755\" y=\"250.202947\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"289.926847\" y=\"249.044964\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"304.831939\" y=\"247.886982\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"319.73703\" y=\"254.255887\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m3d396bc9d8\" x=\"334.642122\" y=\"251.939921\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 29.08774 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 29.08774 3.00024 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p500d5a0283\">\n", "   <rect x=\"29.08774\" y=\"3.00024\" width=\"327.91202\" height=\"265.321395\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 13.87 (χ²/ndof = 0.8)      │              Nfcn = 101              │\n", "│ EDM = 2.91e-08 (Goal: 0.0002)    │            time = 0.2 sec            │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │  1.01e3   │  0.05e3   │            │            │    0    │         │       │\n", "│ 1 │ x1   │    870    │    50     │            │            │    0    │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌────┬───────────────────┐\n", "│    │       x0       x1 │\n", "├────┼───────────────────┤\n", "│ x0 │ 2.49e+03   -0.5e3 │\n", "│ x1 │   -0.5e3 2.21e+03 │\n", "└────┴───────────────────┘"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 201.8 (χ²/ndof = 1.0) </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 100 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 2.37e-05 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 1.02e3 </td>\n", "        <td> 0.05e3 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 810 </td>\n", "        <td> 40 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 2.41e+03 </td>\n", "        <td style=\"background-color:rgb(229,229,250);color:black\"> -0.3e3 <strong>(-0.161)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(229,229,250);color:black\"> -0.3e3 <strong>(-0.161)</strong> </td>\n", "        <td> 1.92e+03 </td>\n", "    </tr>\n", "</table><?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"360pt\" height=\"288pt\" viewBox=\"0 0 360 288\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:44:15.435767</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 288 \n", "L 360 288 \n", "L 360 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 22.72524 268.321635 \n", "L 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "L 22.72524 3.00024 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 37.919536 230.839788 \n", "L 37.919536 202.069026 \n", "L 39.438966 202.069026 \n", "L 39.438966 197.536205 \n", "L 40.958396 197.536205 \n", "L 40.958396 225.11852 \n", "L 42.477825 225.11852 \n", "L 42.477825 211.201355 \n", "L 43.997255 211.201355 \n", "L 43.997255 202.069026 \n", "L 45.516685 202.069026 \n", "L 45.516685 220.444194 \n", "L 47.036114 220.444194 \n", "L 47.036114 229.837144 \n", "L 48.555544 229.837144 \n", "L 48.555544 225.11852 \n", "L 50.074973 225.11852 \n", "L 50.074973 229.837144 \n", "L 51.594403 229.837144 \n", "L 51.594403 215.806954 \n", "L 53.113833 215.806954 \n", "L 53.113833 239.451267 \n", "L 56.152692 239.451267 \n", "L 56.152692 244.382914 \n", "L 57.672122 244.382914 \n", "L 57.672122 220.444194 \n", "L 59.191551 220.444194 \n", "L 59.191551 229.837144 \n", "L 60.710981 229.837144 \n", "L 60.710981 220.444194 \n", "L 62.230411 220.444194 \n", "L 62.230411 229.837144 \n", "L 63.74984 229.837144 \n", "L 63.74984 225.11852 \n", "L 65.26927 225.11852 \n", "L 65.26927 215.806954 \n", "L 66.788699 215.806954 \n", "L 66.788699 234.609973 \n", "L 68.308129 234.609973 \n", "L 68.308129 229.837144 \n", "L 69.827559 229.837144 \n", "L 69.827559 234.609973 \n", "L 72.866418 234.609973 \n", "L 72.866418 229.837144 \n", "L 74.385848 229.837144 \n", "L 74.385848 225.11852 \n", "L 75.905277 225.11852 \n", "L 75.905277 215.806954 \n", "L 77.424707 215.806954 \n", "L 77.424707 254.699651 \n", "L 78.944137 254.699651 \n", "L 78.944137 220.444194 \n", "L 80.463566 220.444194 \n", "L 80.463566 215.806954 \n", "L 81.982996 215.806954 \n", "L 81.982996 229.837144 \n", "L 85.021855 229.837144 \n", "L 85.021855 244.382914 \n", "L 86.541285 244.382914 \n", "L 86.541285 220.444194 \n", "L 88.060714 220.444194 \n", "L 88.060714 244.382914 \n", "L 89.580144 244.382914 \n", "L 89.580144 229.837144 \n", "L 91.099574 229.837144 \n", "L 91.099574 234.609973 \n", "L 92.619003 234.609973 \n", "L 92.619003 249.441761 \n", "L 94.138433 249.441761 \n", "L 94.138433 225.11852 \n", "L 95.657863 225.11852 \n", "L 95.657863 215.806954 \n", "L 98.696722 215.806954 \n", "L 98.696722 220.444194 \n", "L 100.216151 220.444194 \n", "L 100.216151 206.623167 \n", "L 101.735581 206.623167 \n", "L 101.735581 211.201355 \n", "L 103.255011 211.201355 \n", "L 103.255011 244.382914 \n", "L 104.77444 244.382914 \n", "L 104.77444 249.441761 \n", "L 106.29387 249.441761 \n", "L 106.29387 229.837144 \n", "L 107.8133 229.837144 \n", "L 107.8133 249.441761 \n", "L 110.852159 249.441761 \n", "L 110.852159 220.444194 \n", "L 113.891018 220.444194 \n", "L 113.891018 249.441761 \n", "L 115.410448 249.441761 \n", "L 115.410448 244.382914 \n", "L 116.929877 244.382914 \n", "L 116.929877 220.444194 \n", "L 118.449307 220.444194 \n", "L 118.449307 229.837144 \n", "L 119.968737 229.837144 \n", "L 119.968737 254.699651 \n", "L 121.488166 254.699651 \n", "L 121.488166 225.11852 \n", "L 124.527026 225.11852 \n", "L 124.527026 215.806954 \n", "L 126.046455 215.806954 \n", "L 126.046455 260.342061 \n", "L 127.565885 260.342061 \n", "L 127.565885 244.382914 \n", "L 129.085315 244.382914 \n", "L 129.085315 239.451267 \n", "L 132.124174 239.451267 \n", "L 132.124174 249.441761 \n", "L 133.643603 249.441761 \n", "L 133.643603 244.382914 \n", "L 135.163033 244.382914 \n", "L 135.163033 243.927506 \n", "L 136.682463 243.927506 \n", "L 136.682463 234.609973 \n", "L 138.201892 234.609973 \n", "L 138.201892 220.444194 \n", "L 139.721322 220.444194 \n", "L 139.721322 225.11852 \n", "L 141.240752 225.11852 \n", "L 141.240752 220.444194 \n", "L 142.760181 220.444194 \n", "L 142.760181 239.451267 \n", "L 144.279611 239.451267 \n", "L 144.279611 234.182529 \n", "L 145.799041 234.182529 \n", "L 145.799041 239.451267 \n", "L 147.31847 239.451267 \n", "L 147.31847 233.756684 \n", "L 148.8379 233.756684 \n", "L 148.8379 210.412785 \n", "L 150.357329 210.412785 \n", "L 150.357329 215.009397 \n", "L 153.396189 215.009397 \n", "L 153.396189 254.18225 \n", "L 154.915618 254.18225 \n", "L 154.915618 238.574648 \n", "L 156.435048 238.574648 \n", "L 156.435048 229.002103 \n", "L 157.954478 229.002103 \n", "L 157.954478 214.611734 \n", "L 159.473907 214.611734 \n", "L 159.473907 238.574648 \n", "L 160.993337 238.574648 \n", "L 160.993337 210.412785 \n", "L 162.512767 210.412785 \n", "L 162.512767 188.473877 \n", "L 164.032196 188.473877 \n", "L 164.032196 226.935474 \n", "L 165.551626 226.935474 \n", "L 165.551626 193.336017 \n", "L 167.071055 193.336017 \n", "L 167.071055 142.1194 \n", "L 168.590485 142.1194 \n", "L 168.590485 197.455226 \n", "L 170.109915 197.455226 \n", "L 170.109915 151.231917 \n", "L 171.629344 151.231917 \n", "L 171.629344 178.805572 \n", "L 173.148774 178.805572 \n", "L 173.148774 122.918945 \n", "L 174.668204 122.918945 \n", "L 174.668204 113.189836 \n", "L 176.187633 113.189836 \n", "L 176.187633 121.853038 \n", "L 177.707063 121.853038 \n", "L 177.707063 116.10268 \n", "L 179.226493 116.10268 \n", "L 179.226493 84.516313 \n", "L 180.745922 84.516313 \n", "L 180.745922 19.88407 \n", "L 182.265352 19.88407 \n", "L 182.265352 43.228216 \n", "L 183.784781 43.228216 \n", "L 183.784781 43.916098 \n", "L 185.304211 43.916098 \n", "L 185.304211 52.452965 \n", "L 186.823641 52.452965 \n", "L 186.823641 29.75963 \n", "L 188.34307 29.75963 \n", "L 188.34307 107.807648 \n", "L 189.8625 107.807648 \n", "L 189.8625 84.167549 \n", "L 191.38193 84.167549 \n", "L 191.38193 61.346832 \n", "L 192.901359 61.346832 \n", "L 192.901359 15.634592 \n", "L 194.420789 15.634592 \n", "L 194.420789 38.277771 \n", "L 195.940219 38.277771 \n", "L 195.940219 70.948521 \n", "L 197.459648 70.948521 \n", "L 197.459648 57.070894 \n", "L 198.979078 57.070894 \n", "L 198.979078 125.480766 \n", "L 200.498507 125.480766 \n", "L 200.498507 93.466645 \n", "L 202.017937 93.466645 \n", "L 202.017937 117.873296 \n", "L 203.537367 117.873296 \n", "L 203.537367 98.123947 \n", "L 205.056796 98.123947 \n", "L 205.056796 126.904561 \n", "L 206.576226 126.904561 \n", "L 206.576226 140.321396 \n", "L 208.095656 140.321396 \n", "L 208.095656 183.631004 \n", "L 209.615085 183.631004 \n", "L 209.615085 208.06208 \n", "L 211.134515 208.06208 \n", "L 211.134515 182.882973 \n", "L 212.653945 182.882973 \n", "L 212.653945 192.956659 \n", "L 214.173374 192.956659 \n", "L 214.173374 212.240359 \n", "L 215.692804 212.240359 \n", "L 215.692804 189.228205 \n", "L 217.212233 189.228205 \n", "L 217.212233 227.346547 \n", "L 218.731663 227.346547 \n", "L 218.731663 213.423007 \n", "L 220.251093 213.423007 \n", "L 220.251093 213.818554 \n", "L 221.770522 213.818554 \n", "L 221.770522 243.025184 \n", "L 223.289952 243.025184 \n", "L 223.289952 233.332379 \n", "L 224.809382 233.332379 \n", "L 224.809382 234.182529 \n", "L 226.328811 234.182529 \n", "L 226.328811 243.927506 \n", "L 227.848241 243.927506 \n", "L 227.848241 243.474981 \n", "L 229.367671 243.474981 \n", "L 229.367671 259.740414 \n", "L 230.8871 259.740414 \n", "L 230.8871 254.18225 \n", "L 232.40653 254.18225 \n", "L 232.40653 248.488598 \n", "L 233.925959 248.488598 \n", "L 233.925959 229.837144 \n", "L 236.964819 229.837144 \n", "L 236.964819 254.699651 \n", "L 238.484248 254.699651 \n", "L 238.484248 254.18225 \n", "L 240.003678 254.18225 \n", "L 240.003678 249.441761 \n", "L 241.523108 249.441761 \n", "L 241.523108 234.609973 \n", "L 243.042537 234.609973 \n", "L 243.042537 249.441761 \n", "L 246.081397 249.441761 \n", "L 246.081397 225.11852 \n", "L 247.600826 225.11852 \n", "L 247.600826 254.699651 \n", "L 250.639685 254.699651 \n", "L 250.639685 249.441761 \n", "L 252.159115 249.441761 \n", "L 252.159115 254.699651 \n", "L 255.197974 254.699651 \n", "L 255.197974 260.342061 \n", "L 258.236834 260.342061 \n", "L 258.236834 239.451267 \n", "L 259.756263 239.451267 \n", "L 259.756263 249.441761 \n", "L 261.275693 249.441761 \n", "L 261.275693 268.321635 \n", "L 262.795123 268.321635 \n", "L 262.795123 260.342061 \n", "L 264.314552 260.342061 \n", "L 264.314552 234.609973 \n", "L 265.833982 234.609973 \n", "L 265.833982 260.342061 \n", "L 267.353411 260.342061 \n", "L 267.353411 268.321635 \n", "L 268.872841 268.321635 \n", "L 268.872841 254.699651 \n", "L 270.392271 254.699651 \n", "L 270.392271 260.342061 \n", "L 271.9117 260.342061 \n", "L 271.9117 244.382914 \n", "L 273.43113 244.382914 \n", "L 273.43113 254.699651 \n", "L 274.95056 254.699651 \n", "L 274.95056 239.451267 \n", "L 276.469989 239.451267 \n", "L 276.469989 268.321635 \n", "L 277.989419 268.321635 \n", "L 277.989419 244.382914 \n", "L 281.028278 244.382914 \n", "L 281.028278 268.321635 \n", "L 284.067137 268.321635 \n", "L 284.067137 249.441761 \n", "L 285.586567 249.441761 \n", "L 285.586567 254.699651 \n", "L 287.105997 254.699651 \n", "L 287.105997 260.342061 \n", "L 288.625426 260.342061 \n", "L 288.625426 268.321635 \n", "L 290.144856 268.321635 \n", "L 290.144856 254.699651 \n", "L 291.664286 254.699651 \n", "L 291.664286 244.382914 \n", "L 293.183715 244.382914 \n", "L 293.183715 254.699651 \n", "L 294.703145 254.699651 \n", "L 294.703145 260.342061 \n", "L 296.222575 260.342061 \n", "L 296.222575 239.451267 \n", "L 297.742004 239.451267 \n", "L 297.742004 268.321635 \n", "L 299.261434 268.321635 \n", "L 299.261434 260.342061 \n", "L 303.819723 260.342061 \n", "L 303.819723 268.321635 \n", "L 305.339152 268.321635 \n", "L 305.339152 239.451267 \n", "L 306.858582 239.451267 \n", "L 306.858582 268.321635 \n", "L 308.378012 268.321635 \n", "L 308.378012 254.699651 \n", "L 309.897441 254.699651 \n", "L 309.897441 249.441761 \n", "L 311.416871 249.441761 \n", "L 311.416871 254.699651 \n", "L 312.936301 254.699651 \n", "L 312.936301 268.321635 \n", "L 314.45573 268.321635 \n", "L 314.45573 260.342061 \n", "L 315.97516 260.342061 \n", "L 315.97516 244.382914 \n", "L 317.494589 244.382914 \n", "L 317.494589 254.699651 \n", "L 320.533449 254.699651 \n", "L 320.533449 249.441761 \n", "L 322.052878 249.441761 \n", "L 322.052878 260.342061 \n", "L 323.572308 260.342061 \n", "L 323.572308 249.441761 \n", "L 325.091738 249.441761 \n", "L 325.091738 260.342061 \n", "L 326.611167 260.342061 \n", "L 326.611167 268.321635 \n", "L 328.130597 268.321635 \n", "L 328.130597 260.342061 \n", "L 329.650027 260.342061 \n", "L 329.650027 268.321635 \n", "L 331.169456 268.321635 \n", "L 331.169456 260.342061 \n", "L 332.688886 260.342061 \n", "L 332.688886 268.321635 \n", "L 334.208315 268.321635 \n", "L 334.208315 260.342061 \n", "L 335.727745 260.342061 \n", "L 335.727745 254.699651 \n", "L 337.247175 254.699651 \n", "L 337.247175 268.321635 \n", "L 338.766604 268.321635 \n", "L 338.766604 254.699651 \n", "L 340.286034 254.699651 \n", "L 340.286034 260.342061 \n", "L 341.805464 260.342061 \n", "L 341.805464 268.321635 \n", "L 341.805464 268.321635 \n", "L 340.286034 268.321635 \n", "L 340.286034 265.984472 \n", "L 338.766604 265.984472 \n", "L 338.766604 268.321635 \n", "L 337.247175 268.321635 \n", "L 337.247175 265.984472 \n", "L 335.727745 265.984472 \n", "L 335.727745 268.321635 \n", "L 325.091738 268.321635 \n", "L 325.091738 263.262788 \n", "L 323.572308 263.262788 \n", "L 323.572308 268.321635 \n", "L 322.052878 268.321635 \n", "L 322.052878 263.262788 \n", "L 320.533449 263.262788 \n", "L 320.533449 265.984472 \n", "L 317.494589 265.984472 \n", "L 317.494589 260.342061 \n", "L 315.97516 260.342061 \n", "L 315.97516 268.321635 \n", "L 312.936301 268.321635 \n", "L 312.936301 265.984472 \n", "L 311.416871 265.984472 \n", "L 311.416871 263.262788 \n", "L 309.897441 263.262788 \n", "L 309.897441 265.984472 \n", "L 308.378012 265.984472 \n", "L 308.378012 268.321635 \n", "L 306.858582 268.321635 \n", "L 306.858582 257.294136 \n", "L 305.339152 257.294136 \n", "L 305.339152 268.321635 \n", "L 297.742004 268.321635 \n", "L 297.742004 257.294136 \n", "L 296.222575 257.294136 \n", "L 296.222575 268.321635 \n", "L 294.703145 268.321635 \n", "L 294.703145 265.984472 \n", "L 293.183715 265.984472 \n", "L 293.183715 260.342061 \n", "L 291.664286 260.342061 \n", "L 291.664286 265.984472 \n", "L 290.144856 265.984472 \n", "L 290.144856 268.321635 \n", "L 287.105997 268.321635 \n", "L 287.105997 265.984472 \n", "L 285.586567 265.984472 \n", "L 285.586567 263.262788 \n", "L 284.067137 263.262788 \n", "L 284.067137 268.321635 \n", "L 281.028278 268.321635 \n", "L 281.028278 260.342061 \n", "L 277.989419 260.342061 \n", "L 277.989419 268.321635 \n", "L 276.469989 268.321635 \n", "L 276.469989 257.294136 \n", "L 274.95056 257.294136 \n", "L 274.95056 265.984472 \n", "L 273.43113 265.984472 \n", "L 273.43113 260.342061 \n", "L 271.9117 260.342061 \n", "L 271.9117 268.321635 \n", "L 270.392271 268.321635 \n", "L 270.392271 265.984472 \n", "L 268.872841 265.984472 \n", "L 268.872841 268.321635 \n", "L 265.833982 268.321635 \n", "L 265.833982 254.155856 \n", "L 264.314552 254.155856 \n", "L 264.314552 268.321635 \n", "L 261.275693 268.321635 \n", "L 261.275693 263.262788 \n", "L 259.756263 263.262788 \n", "L 259.756263 257.294136 \n", "L 258.236834 257.294136 \n", "L 258.236834 268.321635 \n", "L 255.197974 268.321635 \n", "L 255.197974 265.984472 \n", "L 252.159115 265.984472 \n", "L 252.159115 263.262788 \n", "L 250.639685 263.262788 \n", "L 250.639685 265.984472 \n", "L 247.600826 265.984472 \n", "L 247.600826 247.688162 \n", "L 246.081397 247.688162 \n", "L 246.081397 263.262788 \n", "L 243.042537 263.262788 \n", "L 243.042537 254.155856 \n", "L 241.523108 254.155856 \n", "L 241.523108 263.262788 \n", "L 240.003678 263.262788 \n", "L 240.003678 265.900225 \n", "L 236.964819 265.984472 \n", "L 236.964819 250.949111 \n", "L 233.925959 250.949111 \n", "L 233.925959 263.012657 \n", "L 232.40653 263.012657 \n", "L 232.40653 265.900225 \n", "L 230.8871 265.900225 \n", "L 230.8871 268.321635 \n", "L 229.367671 268.321635 \n", "L 229.367671 260.0467 \n", "L 227.848241 260.0467 \n", "L 227.848241 260.195823 \n", "L 226.328811 260.195823 \n", "L 226.328811 253.981653 \n", "L 224.809382 253.981653 \n", "L 224.809382 253.628508 \n", "L 223.289952 253.628508 \n", "L 223.289952 259.89485 \n", "L 221.770522 259.89485 \n", "L 221.770522 240.020744 \n", "L 220.251093 240.020744 \n", "L 220.251093 239.814644 \n", "L 218.731663 239.814644 \n", "L 218.731663 249.829824 \n", "L 217.212233 249.829824 \n", "L 217.212233 221.704988 \n", "L 215.692804 221.704988 \n", "L 215.692804 239.192349 \n", "L 214.173374 239.192349 \n", "L 214.173374 224.752813 \n", "L 212.653945 224.752813 \n", "L 212.653945 217.06241 \n", "L 211.134515 217.06241 \n", "L 211.134515 235.992702 \n", "L 209.615085 235.992702 \n", "L 209.615085 217.517673 \n", "L 208.095656 217.517673 \n", "L 208.095656 182.994646 \n", "L 206.576226 182.994646 \n", "L 206.576226 171.871113 \n", "L 205.056796 171.871113 \n", "L 205.056796 147.961106 \n", "L 203.537367 147.961106 \n", "L 203.537367 164.341584 \n", "L 202.017937 164.341584 \n", "L 202.017937 144.037188 \n", "L 200.498507 144.037188 \n", "L 200.498507 170.888319 \n", "L 198.979078 170.888319 \n", "L 198.979078 112.986466 \n", "L 197.459648 112.986466 \n", "L 197.459648 124.852501 \n", "L 195.940219 124.852501 \n", "L 195.940219 96.853058 \n", "L 194.420789 96.853058 \n", "L 194.420789 77.191779 \n", "L 192.901359 77.191779 \n", "L 192.901359 116.690102 \n", "L 191.38193 116.690102 \n", "L 191.38193 136.173842 \n", "L 189.8625 136.173842 \n", "L 189.8625 156.041495 \n", "L 188.34307 156.041495 \n", "L 188.34307 89.412052 \n", "L 186.823641 89.412052 \n", "L 186.823641 109.023174 \n", "L 185.304211 109.023174 \n", "L 185.304211 101.600894 \n", "L 183.784781 101.600894 \n", "L 183.784781 101.085481 \n", "L 182.265352 101.085481 \n", "L 182.265352 80.921875 \n", "L 180.745922 80.921875 \n", "L 180.745922 136.426724 \n", "L 179.226493 136.426724 \n", "L 179.226493 163.103962 \n", "L 177.707063 163.103962 \n", "L 177.707063 167.739767 \n", "L 176.187633 167.739767 \n", "L 176.187633 160.443822 \n", "L 174.668204 160.443822 \n", "L 174.668204 168.478803 \n", "L 173.148774 168.478803 \n", "L 173.148774 213.761884 \n", "L 171.629344 213.761884 \n", "L 171.629344 191.653156 \n", "L 170.109915 191.653156 \n", "L 170.109915 228.233819 \n", "L 168.590485 228.233819 \n", "L 168.590485 184.204879 \n", "L 167.071055 184.204879 \n", "L 167.071055 224.975102 \n", "L 165.551626 224.975102 \n", "L 165.551626 249.63925 \n", "L 164.032196 249.63925 \n", "L 164.032196 221.256022 \n", "L 162.512767 221.256022 \n", "L 162.512767 237.251881 \n", "L 160.993337 237.251881 \n", "L 160.993337 256.967459 \n", "L 159.473907 256.967459 \n", "L 159.473907 240.430859 \n", "L 157.954478 240.430859 \n", "L 157.954478 250.580858 \n", "L 156.435048 250.580858 \n", "L 156.435048 256.967459 \n", "L 154.915618 256.967459 \n", "L 154.915618 265.900225 \n", "L 153.396189 265.900225 \n", "L 153.396189 240.634843 \n", "L 150.357329 240.634843 \n", "L 150.357329 237.251881 \n", "L 148.8379 237.251881 \n", "L 148.8379 253.80585 \n", "L 147.31847 253.80585 \n", "L 147.31847 257.294136 \n", "L 145.799041 257.294136 \n", "L 145.799041 253.981653 \n", "L 144.279611 253.981653 \n", "L 144.279611 257.294136 \n", "L 142.760181 257.294136 \n", "L 142.760181 244.382914 \n", "L 141.240752 244.382914 \n", "L 141.240752 247.688162 \n", "L 139.721322 247.688162 \n", "L 139.721322 244.382914 \n", "L 138.201892 244.382914 \n", "L 138.201892 254.155856 \n", "L 136.682463 254.155856 \n", "L 136.682463 260.195823 \n", "L 135.163033 260.195823 \n", "L 135.163033 260.342061 \n", "L 133.643603 260.342061 \n", "L 133.643603 263.262788 \n", "L 132.124174 263.262788 \n", "L 132.124174 257.294136 \n", "L 129.085315 257.294136 \n", "L 129.085315 260.342061 \n", "L 127.565885 260.342061 \n", "L 127.565885 268.321635 \n", "L 126.046455 268.321635 \n", "L 126.046455 241.040581 \n", "L 124.527026 241.040581 \n", "L 124.527026 247.688162 \n", "L 121.488166 247.688162 \n", "L 121.488166 265.984472 \n", "L 119.968737 265.984472 \n", "L 119.968737 250.949111 \n", "L 118.449307 250.949111 \n", "L 118.449307 244.382914 \n", "L 116.929877 244.382914 \n", "L 116.929877 260.342061 \n", "L 115.410448 260.342061 \n", "L 115.410448 263.262788 \n", "L 113.891018 263.262788 \n", "L 113.891018 244.382914 \n", "L 110.852159 244.382914 \n", "L 110.852159 263.262788 \n", "L 107.8133 263.262788 \n", "L 107.8133 250.949111 \n", "L 106.29387 250.949111 \n", "L 106.29387 263.262788 \n", "L 104.77444 263.262788 \n", "L 104.77444 260.342061 \n", "L 103.255011 260.342061 \n", "L 103.255011 237.666606 \n", "L 101.735581 237.666606 \n", "L 101.735581 234.265221 \n", "L 100.216151 234.265221 \n", "L 100.216151 244.382914 \n", "L 98.696722 244.382914 \n", "L 98.696722 241.040581 \n", "L 95.657863 241.040581 \n", "L 95.657863 247.688162 \n", "L 94.138433 247.688162 \n", "L 94.138433 263.262788 \n", "L 92.619003 263.262788 \n", "L 92.619003 254.155856 \n", "L 91.099574 254.155856 \n", "L 91.099574 250.949111 \n", "L 89.580144 250.949111 \n", "L 89.580144 260.342061 \n", "L 88.060714 260.342061 \n", "L 88.060714 244.382914 \n", "L 86.541285 244.382914 \n", "L 86.541285 260.342061 \n", "L 85.021855 260.342061 \n", "L 85.021855 250.949111 \n", "L 81.982996 250.949111 \n", "L 81.982996 241.040581 \n", "L 80.463566 241.040581 \n", "L 80.463566 244.382914 \n", "L 78.944137 244.382914 \n", "L 78.944137 265.984472 \n", "L 77.424707 265.984472 \n", "L 77.424707 241.040581 \n", "L 75.905277 241.040581 \n", "L 75.905277 247.688162 \n", "L 74.385848 247.688162 \n", "L 74.385848 250.949111 \n", "L 72.866418 250.949111 \n", "L 72.866418 254.155856 \n", "L 69.827559 254.155856 \n", "L 69.827559 250.949111 \n", "L 68.308129 250.949111 \n", "L 68.308129 254.155856 \n", "L 66.788699 254.155856 \n", "L 66.788699 241.040581 \n", "L 65.26927 241.040581 \n", "L 65.26927 247.688162 \n", "L 63.74984 247.688162 \n", "L 63.74984 250.949111 \n", "L 62.230411 250.949111 \n", "L 62.230411 244.382914 \n", "L 60.710981 244.382914 \n", "L 60.710981 250.949111 \n", "L 59.191551 250.949111 \n", "L 59.191551 244.382914 \n", "L 57.672122 244.382914 \n", "L 57.672122 260.342061 \n", "L 56.152692 260.342061 \n", "L 56.152692 257.294136 \n", "L 53.113833 257.294136 \n", "L 53.113833 241.040581 \n", "L 51.594403 241.040581 \n", "L 51.594403 250.949111 \n", "L 50.074973 250.949111 \n", "L 50.074973 247.688162 \n", "L 48.555544 247.688162 \n", "L 48.555544 250.949111 \n", "L 47.036114 250.949111 \n", "L 47.036114 244.382914 \n", "L 45.516685 244.382914 \n", "L 45.516685 230.839788 \n", "L 43.997255 230.839788 \n", "L 43.997255 237.666606 \n", "L 42.477825 237.666606 \n", "L 42.477825 247.688162 \n", "L 40.958396 247.688162 \n", "L 40.958396 227.393035 \n", "L 39.438966 227.393035 \n", "L 39.438966 230.839788 \n", "L 37.919536 230.839788 \n", "L 37.919536 230.839788 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 37.919536 230.839788 \n", "L 37.919536 202.069026 \n", "L 39.438966 202.069026 \n", "L 39.438966 197.536205 \n", "L 40.958396 197.536205 \n", "L 40.958396 225.11852 \n", "L 42.477825 225.11852 \n", "L 42.477825 211.201355 \n", "L 43.997255 211.201355 \n", "L 43.997255 202.069026 \n", "L 45.516685 202.069026 \n", "L 45.516685 220.444194 \n", "L 47.036114 220.444194 \n", "L 47.036114 229.837144 \n", "L 48.555544 229.837144 \n", "L 48.555544 225.11852 \n", "L 50.074973 225.11852 \n", "L 50.074973 229.837144 \n", "L 51.594403 229.837144 \n", "L 51.594403 215.806954 \n", "L 53.113833 215.806954 \n", "L 53.113833 239.451267 \n", "L 54.633262 239.451267 \n", "L 54.633262 239.451267 \n", "L 56.152692 239.451267 \n", "L 56.152692 244.382914 \n", "L 57.672122 244.382914 \n", "L 57.672122 220.444194 \n", "L 59.191551 220.444194 \n", "L 59.191551 229.837144 \n", "L 60.710981 229.837144 \n", "L 60.710981 220.444194 \n", "L 62.230411 220.444194 \n", "L 62.230411 229.837144 \n", "L 63.74984 229.837144 \n", "L 63.74984 225.11852 \n", "L 65.26927 225.11852 \n", "L 65.26927 215.806954 \n", "L 66.788699 215.806954 \n", "L 66.788699 234.609973 \n", "L 68.308129 234.609973 \n", "L 68.308129 229.837144 \n", "L 69.827559 229.837144 \n", "L 69.827559 234.609973 \n", "L 71.346988 234.609973 \n", "L 71.346988 234.609973 \n", "L 72.866418 234.609973 \n", "L 72.866418 229.837144 \n", "L 74.385848 229.837144 \n", "L 74.385848 225.11852 \n", "L 75.905277 225.11852 \n", "L 75.905277 215.806954 \n", "L 77.424707 215.806954 \n", "L 77.424707 254.699651 \n", "L 78.944137 254.699651 \n", "L 78.944137 220.444194 \n", "L 80.463566 220.444194 \n", "L 80.463566 215.806954 \n", "L 81.982996 215.806954 \n", "L 81.982996 229.837144 \n", "L 83.502425 229.837144 \n", "L 83.502425 229.837144 \n", "L 85.021855 229.837144 \n", "L 85.021855 244.382914 \n", "L 86.541285 244.382914 \n", "L 86.541285 220.444194 \n", "L 88.060714 220.444194 \n", "L 88.060714 244.382914 \n", "L 89.580144 244.382914 \n", "L 89.580144 229.837144 \n", "L 91.099574 229.837144 \n", "L 91.099574 234.609973 \n", "L 92.619003 234.609973 \n", "L 92.619003 249.441761 \n", "L 94.138433 249.441761 \n", "L 94.138433 225.11852 \n", "L 95.657863 225.11852 \n", "L 95.657863 215.806954 \n", "L 97.177292 215.806954 \n", "L 97.177292 215.806954 \n", "L 98.696722 215.806954 \n", "L 98.696722 220.444194 \n", "L 100.216151 220.444194 \n", "L 100.216151 206.623167 \n", "L 101.735581 206.623167 \n", "L 101.735581 211.201355 \n", "L 103.255011 211.201355 \n", "L 103.255011 244.382914 \n", "L 104.77444 244.382914 \n", "L 104.77444 249.441761 \n", "L 106.29387 249.441761 \n", "L 106.29387 229.837144 \n", "L 107.8133 229.837144 \n", "L 107.8133 249.441761 \n", "L 109.332729 249.441761 \n", "L 109.332729 249.441761 \n", "L 110.852159 249.441761 \n", "L 110.852159 220.444194 \n", "L 112.371589 220.444194 \n", "L 112.371589 220.444194 \n", "L 113.891018 220.444194 \n", "L 113.891018 249.441761 \n", "L 115.410448 249.441761 \n", "L 115.410448 244.382914 \n", "L 116.929877 244.382914 \n", "L 116.929877 220.444194 \n", "L 118.449307 220.444194 \n", "L 118.449307 229.837144 \n", "L 119.968737 229.837144 \n", "L 119.968737 254.699651 \n", "L 121.488166 254.699651 \n", "L 121.488166 225.11852 \n", "L 123.007596 225.11852 \n", "L 123.007596 225.11852 \n", "L 124.527026 225.11852 \n", "L 124.527026 215.806954 \n", "L 126.046455 215.806954 \n", "L 126.046455 260.342061 \n", "L 127.565885 260.342061 \n", "L 127.565885 244.382914 \n", "L 129.085315 244.382914 \n", "L 129.085315 239.451267 \n", "L 130.604744 239.451267 \n", "L 130.604744 239.451267 \n", "L 132.124174 239.451267 \n", "L 132.124174 249.441761 \n", "L 133.643603 249.441761 \n", "L 133.643603 244.382914 \n", "L 135.163033 244.382914 \n", "L 135.163033 243.927506 \n", "L 136.682463 243.927506 \n", "L 136.682463 234.609973 \n", "L 138.201892 234.609973 \n", "L 138.201892 220.444194 \n", "L 139.721322 220.444194 \n", "L 139.721322 225.11852 \n", "L 141.240752 225.11852 \n", "L 141.240752 220.444194 \n", "L 142.760181 220.444194 \n", "L 142.760181 239.451267 \n", "L 144.279611 239.451267 \n", "L 144.279611 234.182529 \n", "L 145.799041 234.182529 \n", "L 145.799041 239.451267 \n", "L 147.31847 239.451267 \n", "L 147.31847 233.756684 \n", "L 148.8379 233.756684 \n", "L 148.8379 210.412785 \n", "L 150.357329 210.412785 \n", "L 150.357329 215.009397 \n", "L 151.876759 215.009397 \n", "L 151.876759 215.009397 \n", "L 153.396189 215.009397 \n", "L 153.396189 254.18225 \n", "L 154.915618 254.18225 \n", "L 154.915618 238.574648 \n", "L 156.435048 238.574648 \n", "L 156.435048 229.002103 \n", "L 157.954478 229.002103 \n", "L 157.954478 214.611734 \n", "L 159.473907 214.611734 \n", "L 159.473907 238.574648 \n", "L 160.993337 238.574648 \n", "L 160.993337 210.412785 \n", "L 162.512767 210.412785 \n", "L 162.512767 188.473877 \n", "L 164.032196 188.473877 \n", "L 164.032196 226.935474 \n", "L 165.551626 226.935474 \n", "L 165.551626 193.336017 \n", "L 167.071055 193.336017 \n", "L 167.071055 142.1194 \n", "L 168.590485 142.1194 \n", "L 168.590485 197.455226 \n", "L 170.109915 197.455226 \n", "L 170.109915 151.231917 \n", "L 171.629344 151.231917 \n", "L 171.629344 178.805572 \n", "L 173.148774 178.805572 \n", "L 173.148774 122.918945 \n", "L 174.668204 122.918945 \n", "L 174.668204 113.189836 \n", "L 176.187633 113.189836 \n", "L 176.187633 121.853038 \n", "L 177.707063 121.853038 \n", "L 177.707063 116.10268 \n", "L 179.226493 116.10268 \n", "L 179.226493 84.516313 \n", "L 180.745922 84.516313 \n", "L 180.745922 19.88407 \n", "L 182.265352 19.88407 \n", "L 182.265352 43.228216 \n", "L 183.784781 43.228216 \n", "L 183.784781 43.916098 \n", "L 185.304211 43.916098 \n", "L 185.304211 52.452965 \n", "L 186.823641 52.452965 \n", "L 186.823641 29.75963 \n", "L 188.34307 29.75963 \n", "L 188.34307 107.807648 \n", "L 189.8625 107.807648 \n", "L 189.8625 84.167549 \n", "L 191.38193 84.167549 \n", "L 191.38193 61.346832 \n", "L 192.901359 61.346832 \n", "L 192.901359 15.634592 \n", "L 194.420789 15.634592 \n", "L 194.420789 38.277771 \n", "L 195.940219 38.277771 \n", "L 195.940219 70.948521 \n", "L 197.459648 70.948521 \n", "L 197.459648 57.070894 \n", "L 198.979078 57.070894 \n", "L 198.979078 125.480766 \n", "L 200.498507 125.480766 \n", "L 200.498507 93.466645 \n", "L 202.017937 93.466645 \n", "L 202.017937 117.873296 \n", "L 203.537367 117.873296 \n", "L 203.537367 98.123947 \n", "L 205.056796 98.123947 \n", "L 205.056796 126.904561 \n", "L 206.576226 126.904561 \n", "L 206.576226 140.321396 \n", "L 208.095656 140.321396 \n", "L 208.095656 183.631004 \n", "L 209.615085 183.631004 \n", "L 209.615085 208.06208 \n", "L 211.134515 208.06208 \n", "L 211.134515 182.882973 \n", "L 212.653945 182.882973 \n", "L 212.653945 192.956659 \n", "L 214.173374 192.956659 \n", "L 214.173374 212.240359 \n", "L 215.692804 212.240359 \n", "L 215.692804 189.228205 \n", "L 217.212233 189.228205 \n", "L 217.212233 227.346547 \n", "L 218.731663 227.346547 \n", "L 218.731663 213.423007 \n", "L 220.251093 213.423007 \n", "L 220.251093 213.818554 \n", "L 221.770522 213.818554 \n", "L 221.770522 243.025184 \n", "L 223.289952 243.025184 \n", "L 223.289952 233.332379 \n", "L 224.809382 233.332379 \n", "L 224.809382 234.182529 \n", "L 226.328811 234.182529 \n", "L 226.328811 243.927506 \n", "L 227.848241 243.927506 \n", "L 227.848241 243.474981 \n", "L 229.367671 243.474981 \n", "L 229.367671 259.740414 \n", "L 230.8871 259.740414 \n", "L 230.8871 254.18225 \n", "L 232.40653 254.18225 \n", "L 232.40653 248.488598 \n", "L 233.925959 248.488598 \n", "L 233.925959 229.837144 \n", "L 235.445389 229.837144 \n", "L 235.445389 229.837144 \n", "L 236.964819 229.837144 \n", "L 236.964819 254.699651 \n", "L 238.484248 254.699651 \n", "L 238.484248 254.18225 \n", "L 240.003678 254.18225 \n", "L 240.003678 249.441761 \n", "L 241.523108 249.441761 \n", "L 241.523108 234.609973 \n", "L 243.042537 234.609973 \n", "L 243.042537 249.441761 \n", "L 244.561967 249.441761 \n", "L 244.561967 249.441761 \n", "L 246.081397 249.441761 \n", "L 246.081397 225.11852 \n", "L 247.600826 225.11852 \n", "L 247.600826 254.699651 \n", "L 249.120256 254.699651 \n", "L 249.120256 254.699651 \n", "L 250.639685 254.699651 \n", "L 250.639685 249.441761 \n", "L 252.159115 249.441761 \n", "L 252.159115 254.699651 \n", "L 253.678545 254.699651 \n", "L 253.678545 254.699651 \n", "L 255.197974 254.699651 \n", "L 255.197974 260.342061 \n", "L 256.717404 260.342061 \n", "L 256.717404 260.342061 \n", "L 258.236834 260.342061 \n", "L 258.236834 239.451267 \n", "L 259.756263 239.451267 \n", "L 259.756263 249.441761 \n", "L 261.275693 249.441761 \n", "L 261.275693 268.321635 \n", "L 262.795123 268.321635 \n", "L 262.795123 260.342061 \n", "L 264.314552 260.342061 \n", "L 264.314552 234.609973 \n", "L 265.833982 234.609973 \n", "L 265.833982 260.342061 \n", "L 267.353411 260.342061 \n", "L 267.353411 268.321635 \n", "L 268.872841 268.321635 \n", "L 268.872841 254.699651 \n", "L 270.392271 254.699651 \n", "L 270.392271 260.342061 \n", "L 271.9117 260.342061 \n", "L 271.9117 244.382914 \n", "L 273.43113 244.382914 \n", "L 273.43113 254.699651 \n", "L 274.95056 254.699651 \n", "L 274.95056 239.451267 \n", "L 276.469989 239.451267 \n", "L 276.469989 268.321635 \n", "L 277.989419 268.321635 \n", "L 277.989419 244.382914 \n", "L 279.508849 244.382914 \n", "L 279.508849 244.382914 \n", "L 281.028278 244.382914 \n", "L 281.028278 268.321635 \n", "L 282.547708 268.321635 \n", "L 282.547708 268.321635 \n", "L 284.067137 268.321635 \n", "L 284.067137 249.441761 \n", "L 285.586567 249.441761 \n", "L 285.586567 254.699651 \n", "L 287.105997 254.699651 \n", "L 287.105997 260.342061 \n", "L 288.625426 260.342061 \n", "L 288.625426 268.321635 \n", "L 290.144856 268.321635 \n", "L 290.144856 254.699651 \n", "L 291.664286 254.699651 \n", "L 291.664286 244.382914 \n", "L 293.183715 244.382914 \n", "L 293.183715 254.699651 \n", "L 294.703145 254.699651 \n", "L 294.703145 260.342061 \n", "L 296.222575 260.342061 \n", "L 296.222575 239.451267 \n", "L 297.742004 239.451267 \n", "L 297.742004 268.321635 \n", "L 299.261434 268.321635 \n", "L 299.261434 260.342061 \n", "L 300.780863 260.342061 \n", "L 300.780863 260.342061 \n", "L 302.300293 260.342061 \n", "L 302.300293 260.342061 \n", "L 303.819723 260.342061 \n", "L 303.819723 268.321635 \n", "L 305.339152 268.321635 \n", "L 305.339152 239.451267 \n", "L 306.858582 239.451267 \n", "L 306.858582 268.321635 \n", "L 308.378012 268.321635 \n", "L 308.378012 254.699651 \n", "L 309.897441 254.699651 \n", "L 309.897441 249.441761 \n", "L 311.416871 249.441761 \n", "L 311.416871 254.699651 \n", "L 312.936301 254.699651 \n", "L 312.936301 268.321635 \n", "L 314.45573 268.321635 \n", "L 314.45573 260.342061 \n", "L 315.97516 260.342061 \n", "L 315.97516 244.382914 \n", "L 317.494589 244.382914 \n", "L 317.494589 254.699651 \n", "L 319.014019 254.699651 \n", "L 319.014019 254.699651 \n", "L 320.533449 254.699651 \n", "L 320.533449 249.441761 \n", "L 322.052878 249.441761 \n", "L 322.052878 260.342061 \n", "L 323.572308 260.342061 \n", "L 323.572308 249.441761 \n", "L 325.091738 249.441761 \n", "L 325.091738 260.342061 \n", "L 326.611167 260.342061 \n", "L 326.611167 268.321635 \n", "L 328.130597 268.321635 \n", "L 328.130597 260.342061 \n", "L 329.650027 260.342061 \n", "L 329.650027 268.321635 \n", "L 331.169456 268.321635 \n", "L 331.169456 260.342061 \n", "L 332.688886 260.342061 \n", "L 332.688886 268.321635 \n", "L 334.208315 268.321635 \n", "L 334.208315 260.342061 \n", "L 335.727745 260.342061 \n", "L 335.727745 254.699651 \n", "L 337.247175 254.699651 \n", "L 337.247175 268.321635 \n", "L 338.766604 268.321635 \n", "L 338.766604 254.699651 \n", "L 340.286034 254.699651 \n", "L 340.286034 260.342061 \n", "L 341.805464 260.342061 \n", "L 341.805464 268.321635 \n", "L 341.805464 268.321635 \n", "L 341.805464 268.321635 \n", "L 340.286034 268.321635 \n", "L 340.286034 265.984472 \n", "L 338.766604 265.984472 \n", "L 338.766604 268.321635 \n", "L 337.247175 268.321635 \n", "L 337.247175 265.984472 \n", "L 335.727745 265.984472 \n", "L 335.727745 268.321635 \n", "L 334.208315 268.321635 \n", "L 334.208315 268.321635 \n", "L 332.688886 268.321635 \n", "L 332.688886 268.321635 \n", "L 331.169456 268.321635 \n", "L 331.169456 268.321635 \n", "L 329.650027 268.321635 \n", "L 329.650027 268.321635 \n", "L 328.130597 268.321635 \n", "L 328.130597 268.321635 \n", "L 326.611167 268.321635 \n", "L 326.611167 268.321635 \n", "L 325.091738 268.321635 \n", "L 325.091738 263.262788 \n", "L 323.572308 263.262788 \n", "L 323.572308 268.321635 \n", "L 322.052878 268.321635 \n", "L 322.052878 263.262788 \n", "L 320.533449 263.262788 \n", "L 320.533449 265.984472 \n", "L 319.014019 265.984472 \n", "L 319.014019 265.984472 \n", "L 317.494589 265.984472 \n", "L 317.494589 260.342061 \n", "L 315.97516 260.342061 \n", "L 315.97516 268.321635 \n", "L 314.45573 268.321635 \n", "L 314.45573 268.321635 \n", "L 312.936301 268.321635 \n", "L 312.936301 265.984472 \n", "L 311.416871 265.984472 \n", "L 311.416871 263.262788 \n", "L 309.897441 263.262788 \n", "L 309.897441 265.984472 \n", "L 308.378012 265.984472 \n", "L 308.378012 268.321635 \n", "L 306.858582 268.321635 \n", "L 306.858582 257.294136 \n", "L 305.339152 257.294136 \n", "L 305.339152 268.321635 \n", "L 303.819723 268.321635 \n", "L 303.819723 268.321635 \n", "L 302.300293 268.321635 \n", "L 302.300293 268.321635 \n", "L 300.780863 268.321635 \n", "L 300.780863 268.321635 \n", "L 299.261434 268.321635 \n", "L 299.261434 268.321635 \n", "L 297.742004 268.321635 \n", "L 297.742004 257.294136 \n", "L 296.222575 257.294136 \n", "L 296.222575 268.321635 \n", "L 294.703145 268.321635 \n", "L 294.703145 265.984472 \n", "L 293.183715 265.984472 \n", "L 293.183715 260.342061 \n", "L 291.664286 260.342061 \n", "L 291.664286 265.984472 \n", "L 290.144856 265.984472 \n", "L 290.144856 268.321635 \n", "L 288.625426 268.321635 \n", "L 288.625426 268.321635 \n", "L 287.105997 268.321635 \n", "L 287.105997 265.984472 \n", "L 285.586567 265.984472 \n", "L 285.586567 263.262788 \n", "L 284.067137 263.262788 \n", "L 284.067137 268.321635 \n", "L 282.547708 268.321635 \n", "L 282.547708 268.321635 \n", "L 281.028278 268.321635 \n", "L 281.028278 260.342061 \n", "L 279.508849 260.342061 \n", "L 279.508849 260.342061 \n", "L 277.989419 260.342061 \n", "L 277.989419 268.321635 \n", "L 276.469989 268.321635 \n", "L 276.469989 257.294136 \n", "L 274.95056 257.294136 \n", "L 274.95056 265.984472 \n", "L 273.43113 265.984472 \n", "L 273.43113 260.342061 \n", "L 271.9117 260.342061 \n", "L 271.9117 268.321635 \n", "L 270.392271 268.321635 \n", "L 270.392271 265.984472 \n", "L 268.872841 265.984472 \n", "L 268.872841 268.321635 \n", "L 267.353411 268.321635 \n", "L 267.353411 268.321635 \n", "L 265.833982 268.321635 \n", "L 265.833982 254.155856 \n", "L 264.314552 254.155856 \n", "L 264.314552 268.321635 \n", "L 262.795123 268.321635 \n", "L 262.795123 268.321635 \n", "L 261.275693 268.321635 \n", "L 261.275693 263.262788 \n", "L 259.756263 263.262788 \n", "L 259.756263 257.294136 \n", "L 258.236834 257.294136 \n", "L 258.236834 268.321635 \n", "L 256.717404 268.321635 \n", "L 256.717404 268.321635 \n", "L 255.197974 268.321635 \n", "L 255.197974 265.984472 \n", "L 253.678545 265.984472 \n", "L 253.678545 265.984472 \n", "L 252.159115 265.984472 \n", "L 252.159115 263.262788 \n", "L 250.639685 263.262788 \n", "L 250.639685 265.984472 \n", "L 249.120256 265.984472 \n", "L 249.120256 265.984472 \n", "L 247.600826 265.984472 \n", "L 247.600826 247.688162 \n", "L 246.081397 247.688162 \n", "L 246.081397 263.262788 \n", "L 244.561967 263.262788 \n", "L 244.561967 263.262788 \n", "L 243.042537 263.262788 \n", "L 243.042537 254.155856 \n", "L 241.523108 254.155856 \n", "L 241.523108 263.262788 \n", "L 240.003678 263.262788 \n", "L 240.003678 265.900225 \n", "L 238.484248 265.900225 \n", "L 238.484248 265.984472 \n", "L 236.964819 265.984472 \n", "L 236.964819 250.949111 \n", "L 235.445389 250.949111 \n", "L 235.445389 250.949111 \n", "L 233.925959 250.949111 \n", "L 233.925959 263.012657 \n", "L 232.40653 263.012657 \n", "L 232.40653 265.900225 \n", "L 230.8871 265.900225 \n", "L 230.8871 268.321635 \n", "L 229.367671 268.321635 \n", "L 229.367671 260.0467 \n", "L 227.848241 260.0467 \n", "L 227.848241 260.195823 \n", "L 226.328811 260.195823 \n", "L 226.328811 253.981653 \n", "L 224.809382 253.981653 \n", "L 224.809382 253.628508 \n", "L 223.289952 253.628508 \n", "L 223.289952 259.89485 \n", "L 221.770522 259.89485 \n", "L 221.770522 240.020744 \n", "L 220.251093 240.020744 \n", "L 220.251093 239.814644 \n", "L 218.731663 239.814644 \n", "L 218.731663 249.829824 \n", "L 217.212233 249.829824 \n", "L 217.212233 221.704988 \n", "L 215.692804 221.704988 \n", "L 215.692804 239.192349 \n", "L 214.173374 239.192349 \n", "L 214.173374 224.752813 \n", "L 212.653945 224.752813 \n", "L 212.653945 217.06241 \n", "L 211.134515 217.06241 \n", "L 211.134515 235.992702 \n", "L 209.615085 235.992702 \n", "L 209.615085 217.517673 \n", "L 208.095656 217.517673 \n", "L 208.095656 182.994646 \n", "L 206.576226 182.994646 \n", "L 206.576226 171.871113 \n", "L 205.056796 171.871113 \n", "L 205.056796 147.961106 \n", "L 203.537367 147.961106 \n", "L 203.537367 164.341584 \n", "L 202.017937 164.341584 \n", "L 202.017937 144.037188 \n", "L 200.498507 144.037188 \n", "L 200.498507 170.888319 \n", "L 198.979078 170.888319 \n", "L 198.979078 112.986466 \n", "L 197.459648 112.986466 \n", "L 197.459648 124.852501 \n", "L 195.940219 124.852501 \n", "L 195.940219 96.853058 \n", "L 194.420789 96.853058 \n", "L 194.420789 77.191779 \n", "L 192.901359 77.191779 \n", "L 192.901359 116.690102 \n", "L 191.38193 116.690102 \n", "L 191.38193 136.173842 \n", "L 189.8625 136.173842 \n", "L 189.8625 156.041495 \n", "L 188.34307 156.041495 \n", "L 188.34307 89.412052 \n", "L 186.823641 89.412052 \n", "L 186.823641 109.023174 \n", "L 185.304211 109.023174 \n", "L 185.304211 101.600894 \n", "L 183.784781 101.600894 \n", "L 183.784781 101.085481 \n", "L 182.265352 101.085481 \n", "L 182.265352 80.921875 \n", "L 180.745922 80.921875 \n", "L 180.745922 136.426724 \n", "L 179.226493 136.426724 \n", "L 179.226493 163.103962 \n", "L 177.707063 163.103962 \n", "L 177.707063 167.739767 \n", "L 176.187633 167.739767 \n", "L 176.187633 160.443822 \n", "L 174.668204 160.443822 \n", "L 174.668204 168.478803 \n", "L 173.148774 168.478803 \n", "L 173.148774 213.761884 \n", "L 171.629344 213.761884 \n", "L 171.629344 191.653156 \n", "L 170.109915 191.653156 \n", "L 170.109915 228.233819 \n", "L 168.590485 228.233819 \n", "L 168.590485 184.204879 \n", "L 167.071055 184.204879 \n", "L 167.071055 224.975102 \n", "L 165.551626 224.975102 \n", "L 165.551626 249.63925 \n", "L 164.032196 249.63925 \n", "L 164.032196 221.256022 \n", "L 162.512767 221.256022 \n", "L 162.512767 237.251881 \n", "L 160.993337 237.251881 \n", "L 160.993337 256.967459 \n", "L 159.473907 256.967459 \n", "L 159.473907 240.430859 \n", "L 157.954478 240.430859 \n", "L 157.954478 250.580858 \n", "L 156.435048 250.580858 \n", "L 156.435048 256.967459 \n", "L 154.915618 256.967459 \n", "L 154.915618 265.900225 \n", "L 153.396189 265.900225 \n", "L 153.396189 240.634843 \n", "L 151.876759 240.634843 \n", "L 151.876759 240.634843 \n", "L 150.357329 240.634843 \n", "L 150.357329 237.251881 \n", "L 148.8379 237.251881 \n", "L 148.8379 253.80585 \n", "L 147.31847 253.80585 \n", "L 147.31847 257.294136 \n", "L 145.799041 257.294136 \n", "L 145.799041 253.981653 \n", "L 144.279611 253.981653 \n", "L 144.279611 257.294136 \n", "L 142.760181 257.294136 \n", "L 142.760181 244.382914 \n", "L 141.240752 244.382914 \n", "L 141.240752 247.688162 \n", "L 139.721322 247.688162 \n", "L 139.721322 244.382914 \n", "L 138.201892 244.382914 \n", "L 138.201892 254.155856 \n", "L 136.682463 254.155856 \n", "L 136.682463 260.195823 \n", "L 135.163033 260.195823 \n", "L 135.163033 260.342061 \n", "L 133.643603 260.342061 \n", "L 133.643603 263.262788 \n", "L 132.124174 263.262788 \n", "L 132.124174 257.294136 \n", "L 130.604744 257.294136 \n", "L 130.604744 257.294136 \n", "L 129.085315 257.294136 \n", "L 129.085315 260.342061 \n", "L 127.565885 260.342061 \n", "L 127.565885 268.321635 \n", "L 126.046455 268.321635 \n", "L 126.046455 241.040581 \n", "L 124.527026 241.040581 \n", "L 124.527026 247.688162 \n", "L 123.007596 247.688162 \n", "L 123.007596 247.688162 \n", "L 121.488166 247.688162 \n", "L 121.488166 265.984472 \n", "L 119.968737 265.984472 \n", "L 119.968737 250.949111 \n", "L 118.449307 250.949111 \n", "L 118.449307 244.382914 \n", "L 116.929877 244.382914 \n", "L 116.929877 260.342061 \n", "L 115.410448 260.342061 \n", "L 115.410448 263.262788 \n", "L 113.891018 263.262788 \n", "L 113.891018 244.382914 \n", "L 112.371589 244.382914 \n", "L 112.371589 244.382914 \n", "L 110.852159 244.382914 \n", "L 110.852159 263.262788 \n", "L 109.332729 263.262788 \n", "L 109.332729 263.262788 \n", "L 107.8133 263.262788 \n", "L 107.8133 250.949111 \n", "L 106.29387 250.949111 \n", "L 106.29387 263.262788 \n", "L 104.77444 263.262788 \n", "L 104.77444 260.342061 \n", "L 103.255011 260.342061 \n", "L 103.255011 237.666606 \n", "L 101.735581 237.666606 \n", "L 101.735581 234.265221 \n", "L 100.216151 234.265221 \n", "L 100.216151 244.382914 \n", "L 98.696722 244.382914 \n", "L 98.696722 241.040581 \n", "L 97.177292 241.040581 \n", "L 97.177292 241.040581 \n", "L 95.657863 241.040581 \n", "L 95.657863 247.688162 \n", "L 94.138433 247.688162 \n", "L 94.138433 263.262788 \n", "L 92.619003 263.262788 \n", "L 92.619003 254.155856 \n", "L 91.099574 254.155856 \n", "L 91.099574 250.949111 \n", "L 89.580144 250.949111 \n", "L 89.580144 260.342061 \n", "L 88.060714 260.342061 \n", "L 88.060714 244.382914 \n", "L 86.541285 244.382914 \n", "L 86.541285 260.342061 \n", "L 85.021855 260.342061 \n", "L 85.021855 250.949111 \n", "L 83.502425 250.949111 \n", "L 83.502425 250.949111 \n", "L 81.982996 250.949111 \n", "L 81.982996 241.040581 \n", "L 80.463566 241.040581 \n", "L 80.463566 244.382914 \n", "L 78.944137 244.382914 \n", "L 78.944137 265.984472 \n", "L 77.424707 265.984472 \n", "L 77.424707 241.040581 \n", "L 75.905277 241.040581 \n", "L 75.905277 247.688162 \n", "L 74.385848 247.688162 \n", "L 74.385848 250.949111 \n", "L 72.866418 250.949111 \n", "L 72.866418 254.155856 \n", "L 71.346988 254.155856 \n", "L 71.346988 254.155856 \n", "L 69.827559 254.155856 \n", "L 69.827559 250.949111 \n", "L 68.308129 250.949111 \n", "L 68.308129 254.155856 \n", "L 66.788699 254.155856 \n", "L 66.788699 241.040581 \n", "L 65.26927 241.040581 \n", "L 65.26927 247.688162 \n", "L 63.74984 247.688162 \n", "L 63.74984 250.949111 \n", "L 62.230411 250.949111 \n", "L 62.230411 244.382914 \n", "L 60.710981 244.382914 \n", "L 60.710981 250.949111 \n", "L 59.191551 250.949111 \n", "L 59.191551 244.382914 \n", "L 57.672122 244.382914 \n", "L 57.672122 260.342061 \n", "L 56.152692 260.342061 \n", "L 56.152692 257.294136 \n", "L 54.633262 257.294136 \n", "L 54.633262 257.294136 \n", "L 53.113833 257.294136 \n", "L 53.113833 241.040581 \n", "L 51.594403 241.040581 \n", "L 51.594403 250.949111 \n", "L 50.074973 250.949111 \n", "L 50.074973 247.688162 \n", "L 48.555544 247.688162 \n", "L 48.555544 250.949111 \n", "L 47.036114 250.949111 \n", "L 47.036114 244.382914 \n", "L 45.516685 244.382914 \n", "L 45.516685 230.839788 \n", "L 43.997255 230.839788 \n", "L 43.997255 237.666606 \n", "L 42.477825 237.666606 \n", "L 42.477825 247.688162 \n", "L 40.958396 247.688162 \n", "L 40.958396 227.393035 \n", "L 39.438966 227.393035 \n", "L 39.438966 230.839788 \n", "L 37.919536 230.839788 \n", "L 37.919536 230.839788 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m7b07e9537f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"37.919536\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(26.786724 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"75.905277\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(64.772465 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"113.891018\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(102.758206 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"151.876759\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(140.743947 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"189.8625\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(178.729687 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"227.848241\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.25 -->\n", "      <g transform=\"translate(216.715428 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"265.833982\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.50 -->\n", "      <g transform=\"translate(254.701169 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"303.819723\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.75 -->\n", "      <g transform=\"translate(292.68691 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m7b07e9537f\" x=\"341.805464\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.00 -->\n", "      <g transform=\"translate(330.672651 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"me32b4f1d00\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me32b4f1d00\" x=\"22.72524\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(9.36274 272.120854) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#me32b4f1d00\" x=\"22.72524\" y=\"226.32316\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(3.00024 230.122379) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#me32b4f1d00\" x=\"22.72524\" y=\"184.324686\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(3.00024 188.123905) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#me32b4f1d00\" x=\"22.72524\" y=\"142.326211\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(3.00024 146.12543) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#me32b4f1d00\" x=\"22.72524\" y=\"100.327737\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(3.00024 104.126955) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#me32b4f1d00\" x=\"22.72524\" y=\"58.329262\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(3.00024 62.128481) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#me32b4f1d00\" x=\"22.72524\" y=\"16.330787\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(3.00024 20.130006) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 38.679251 243.12255 \n", "L 38.679251 217.923465 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 40.198681 236.052631 \n", "L 40.198681 208.193995 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 41.71811 228.866383 \n", "L 41.71811 198.580853 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 43.23754 256.713542 \n", "L 43.23754 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 44.75697 243.12255 \n", "L 44.75697 217.923465 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 46.276399 236.052631 \n", "L 46.276399 208.193995 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 47.795829 236.052631 \n", "L 47.795829 208.193995 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 49.315259 232.472164 \n", "L 49.315259 203.374767 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 50.834688 236.052631 \n", "L 50.834688 208.193995 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 52.354118 246.601818 \n", "L 52.354118 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 53.873548 246.601818 \n", "L 53.873548 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 55.392977 239.604244 \n", "L 55.392977 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 56.912407 243.12255 \n", "L 56.912407 217.923465 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 58.431836 253.410034 \n", "L 58.431836 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 59.951266 228.866383 \n", "L 59.951266 198.580853 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 61.470696 239.604244 \n", "L 61.470696 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 62.990125 239.604244 \n", "L 62.990125 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 64.509555 253.410034 \n", "L 64.509555 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 66.028985 250.034455 \n", "L 66.028985 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 67.548414 250.034455 \n", "L 67.548414 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 69.067844 250.034455 \n", "L 69.067844 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 70.587274 239.604244 \n", "L 70.587274 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 72.106703 239.604244 \n", "L 72.106703 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 73.626133 236.052631 \n", "L 73.626133 208.193995 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 75.145562 243.12255 \n", "L 75.145562 217.923465 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 76.664992 256.713542 \n", "L 76.664992 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 78.184422 246.601818 \n", "L 78.184422 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 79.703851 239.604244 \n", "L 79.703851 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 81.223281 250.034455 \n", "L 81.223281 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 82.742711 243.12255 \n", "L 82.742711 217.923465 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 84.26214 243.12255 \n", "L 84.26214 217.923465 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 85.78157 256.713542 \n", "L 85.78157 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 87.301 228.866383 \n", "L 87.301 198.580853 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 88.820429 246.601818 \n", "L 88.820429 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 90.339859 246.601818 \n", "L 90.339859 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 91.859288 243.12255 \n", "L 91.859288 217.923465 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 93.378718 250.034455 \n", "L 93.378718 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 94.898148 246.601818 \n", "L 94.898148 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 96.417577 239.604244 \n", "L 96.417577 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 97.937007 246.601818 \n", "L 97.937007 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 99.456437 259.92194 \n", "L 99.456437 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 100.975866 239.604244 \n", "L 100.975866 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 102.495296 259.92194 \n", "L 102.495296 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 104.014726 246.601818 \n", "L 104.014726 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 105.534155 262.996442 \n", "L 105.534155 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 107.053585 259.92194 \n", "L 107.053585 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 108.573014 250.034455 \n", "L 108.573014 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 110.092444 268.321635 \n", "L 110.092444 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 111.611874 262.996442 \n", "L 111.611874 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 113.131303 265.861421 \n", "L 113.131303 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 114.650733 253.410034 \n", "L 114.650733 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 116.170163 256.713542 \n", "L 116.170163 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 117.689592 256.713542 \n", "L 117.689592 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 119.209022 253.410034 \n", "L 119.209022 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 120.728452 239.604244 \n", "L 120.728452 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 122.247881 259.92194 \n", "L 122.247881 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 123.767311 246.601818 \n", "L 123.767311 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 125.28674 253.410034 \n", "L 125.28674 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 126.80617 259.92194 \n", "L 126.80617 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 128.3256 259.92194 \n", "L 128.3256 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 129.845029 262.996442 \n", "L 129.845029 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 131.364459 250.034455 \n", "L 131.364459 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 132.883889 256.713542 \n", "L 132.883889 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 134.403318 256.713542 \n", "L 134.403318 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 135.922748 253.410034 \n", "L 135.922748 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 137.442178 243.12255 \n", "L 137.442178 217.923465 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 138.961607 259.92194 \n", "L 138.961607 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 140.481037 268.321635 \n", "L 140.481037 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 143.519896 253.410034 \n", "L 143.519896 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 145.039326 262.996442 \n", "L 145.039326 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 146.558755 259.92194 \n", "L 146.558755 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 148.078185 253.410034 \n", "L 148.078185 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 149.597615 253.410034 \n", "L 149.597615 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 151.117044 256.713542 \n", "L 151.117044 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 152.636474 256.713542 \n", "L 152.636474 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 154.155904 246.601818 \n", "L 154.155904 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 155.675333 253.410034 \n", "L 155.675333 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 157.194763 253.410034 \n", "L 157.194763 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 158.714192 250.034455 \n", "L 158.714192 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 160.233622 225.238161 \n", "L 160.233622 193.80938 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 161.753052 250.034455 \n", "L 161.753052 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 163.272481 214.240643 \n", "L 163.272481 179.607814 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 164.791911 214.240643 \n", "L 164.791911 179.607814 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 166.311341 191.866904 \n", "L 166.311341 151.583383 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 167.83077 210.542824 \n", "L 167.83077 174.905937 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 169.3502 199.370957 \n", "L 169.3502 160.878719 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 170.86963 206.831244 \n", "L 170.86963 170.217823 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 172.389059 195.624022 \n", "L 172.389059 156.22596 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 173.908489 180.540705 \n", "L 173.908489 137.710497 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 175.427918 184.324686 \n", "L 175.427918 142.326211 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 176.947348 134.61703 \n", "L 176.947348 82.837833 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 178.466778 130.755623 \n", "L 178.466778 78.299545 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 179.986207 95.826354 \n", "L 179.986207 37.63156 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 181.505637 138.473954 \n", "L 181.505637 87.380604 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 183.025067 111.386983 \n", "L 183.025067 55.66971 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 184.544496 134.61703 \n", "L 184.544496 82.837833 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 186.063926 95.826354 \n", "L 186.063926 37.63156 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 187.583356 134.61703 \n", "L 187.583356 82.837833 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 189.102785 84.122325 \n", "L 189.102785 24.136505 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 190.622215 134.61703 \n", "L 190.622215 82.837833 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 192.141644 103.613403 \n", "L 192.141644 46.643901 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 193.661074 99.721508 \n", "L 193.661074 42.136101 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 195.180504 184.324686 \n", "L 195.180504 142.326211 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 196.699933 126.889904 \n", "L 196.699933 73.765569 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 198.219363 123.020034 \n", "L 198.219363 69.235744 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 199.738793 150.01593 \n", "L 199.738793 101.037713 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 201.258222 157.684441 \n", "L 201.258222 110.168591 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 202.777652 176.748801 \n", "L 202.777652 133.102706 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 204.297082 153.852956 \n", "L 204.297082 105.600382 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 205.816511 180.540705 \n", "L 205.816511 137.710497 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 207.335941 195.624022 \n", "L 207.335941 156.22596 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 208.85537 191.866904 \n", "L 208.85537 151.583383 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 210.3748 232.472164 \n", "L 210.3748 203.374767 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 211.89423 228.866383 \n", "L 211.89423 198.580853 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 213.413659 239.604244 \n", "L 213.413659 213.042077 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 214.933089 217.923465 \n", "L 214.933089 184.324686 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 216.452519 232.472164 \n", "L 216.452519 203.374767 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 217.971948 246.601818 \n", "L 217.971948 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 219.491378 250.034455 \n", "L 219.491378 227.810951 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 221.010808 256.713542 \n", "L 221.010808 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 222.530237 246.601818 \n", "L 222.530237 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 224.049667 262.996442 \n", "L 224.049667 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 225.569096 246.601818 \n", "L 225.569096 222.843893 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 227.088526 259.92194 \n", "L 227.088526 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 228.607956 256.713542 \n", "L 228.607956 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 230.127385 265.861421 \n", "L 230.127385 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 231.646815 253.410034 \n", "L 231.646815 232.835067 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 233.166245 265.861421 \n", "L 233.166245 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 234.685674 265.861421 \n", "L 234.685674 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 237.724534 262.996442 \n", "L 237.724534 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 239.243963 256.713542 \n", "L 239.243963 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 240.763393 256.713542 \n", "L 240.763393 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 242.282822 268.321635 \n", "L 242.282822 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 243.802252 265.861421 \n", "L 243.802252 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 245.321682 262.996442 \n", "L 245.321682 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 246.841111 265.861421 \n", "L 246.841111 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 248.360541 259.92194 \n", "L 248.360541 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 249.879971 256.713542 \n", "L 249.879971 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 251.3994 262.996442 \n", "L 251.3994 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 252.91883 268.321635 \n", "L 252.91883 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 254.43826 268.321635 \n", "L 254.43826 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 255.957689 256.713542 \n", "L 255.957689 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 257.477119 268.321635 \n", "L 257.477119 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 258.996548 268.321635 \n", "L 258.996548 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 260.515978 262.996442 \n", "L 260.515978 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 262.035408 268.321635 \n", "L 262.035408 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 265.074267 265.861421 \n", "L 265.074267 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 266.593697 259.92194 \n", "L 266.593697 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 269.632556 262.996442 \n", "L 269.632556 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 271.151986 262.996442 \n", "L 271.151986 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 272.671415 259.92194 \n", "L 272.671415 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 274.190845 268.321635 \n", "L 274.190845 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 275.710274 262.996442 \n", "L 275.710274 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 277.229704 256.713542 \n", "L 277.229704 237.931253 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 278.749134 259.92194 \n", "L 278.749134 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 280.268563 265.861421 \n", "L 280.268563 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 281.787993 262.996442 \n", "L 281.787993 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 284.826852 262.996442 \n", "L 284.826852 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 286.346282 265.861421 \n", "L 286.346282 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 287.865712 265.861421 \n", "L 287.865712 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 289.385141 268.321635 \n", "L 289.385141 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 292.424 262.996442 \n", "L 292.424 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 293.94343 268.321635 \n", "L 293.94343 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 295.46286 268.321635 \n", "L 295.46286 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 296.982289 265.861421 \n", "L 296.982289 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 298.501719 265.861421 \n", "L 298.501719 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 300.021149 268.321635 \n", "L 300.021149 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 301.540578 265.861421 \n", "L 301.540578 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 304.579438 265.861421 \n", "L 304.579438 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 306.098867 268.321635 \n", "L 306.098867 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 307.618297 268.321635 \n", "L 307.618297 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 309.137726 259.92194 \n", "L 309.137726 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 310.657156 268.321635 \n", "L 310.657156 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 312.176586 265.861421 \n", "L 312.176586 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 313.696015 265.861421 \n", "L 313.696015 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 316.734875 268.321635 \n", "L 316.734875 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 318.254304 262.996442 \n", "L 318.254304 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 319.773734 268.321635 \n", "L 319.773734 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 321.293164 265.861421 \n", "L 321.293164 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 322.812593 268.321635 \n", "L 322.812593 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 324.332023 259.92194 \n", "L 324.332023 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 325.851452 262.996442 \n", "L 325.851452 248.447743 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 327.370882 268.321635 \n", "L 327.370882 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 328.890312 265.861421 \n", "L 328.890312 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 330.409741 259.92194 \n", "L 330.409741 243.12255 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 331.929171 265.861421 \n", "L 331.929171 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 333.448601 268.321635 \n", "L 333.448601 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 334.96803 268.321635 \n", "L 334.96803 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 336.48746 265.861421 \n", "L 336.48746 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 339.526319 268.321635 \n", "L 339.526319 259.92194 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 341.045749 265.861421 \n", "L 341.045749 253.982459 \n", "\" clip-path=\"url(#p235df86df2)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <defs>\n", "     <path id=\"mdf41b40d50\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p235df86df2)\">\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"38.679251\" y=\"230.523008\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"40.198681\" y=\"222.123313\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"41.71811\" y=\"213.723618\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"43.23754\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"44.75697\" y=\"230.523008\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"46.276399\" y=\"222.123313\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"47.795829\" y=\"222.123313\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"49.315259\" y=\"217.923465\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"50.834688\" y=\"222.123313\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"52.354118\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"53.873548\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"55.392977\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"56.912407\" y=\"230.523008\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"58.431836\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"59.951266\" y=\"213.723618\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"61.470696\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"62.990125\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"64.509555\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"66.028985\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"67.548414\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"69.067844\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"70.587274\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"72.106703\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"73.626133\" y=\"222.123313\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"75.145562\" y=\"230.523008\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"76.664992\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"78.184422\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"79.703851\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"81.223281\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"82.742711\" y=\"230.523008\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"84.26214\" y=\"230.523008\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"85.78157\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"87.301\" y=\"213.723618\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"88.820429\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"90.339859\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"91.859288\" y=\"230.523008\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"93.378718\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"94.898148\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"96.417577\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"97.937007\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"99.456437\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"100.975866\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"102.495296\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"104.014726\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"105.534155\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"107.053585\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"108.573014\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"110.092444\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"111.611874\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"113.131303\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"114.650733\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"116.170163\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"117.689592\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"119.209022\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"120.728452\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"122.247881\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"123.767311\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"125.28674\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"126.80617\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"128.3256\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"129.845029\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"131.364459\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"132.883889\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"134.403318\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"135.922748\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"137.442178\" y=\"230.523008\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"138.961607\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"140.481037\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"143.519896\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"145.039326\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"146.558755\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"148.078185\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"149.597615\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"151.117044\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"152.636474\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"154.155904\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"155.675333\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"157.194763\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"158.714192\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"160.233622\" y=\"209.523771\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"161.753052\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"163.272481\" y=\"196.924228\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"164.791911\" y=\"196.924228\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"166.311341\" y=\"171.725143\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"167.83077\" y=\"192.724381\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"169.3502\" y=\"180.124838\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"170.86963\" y=\"188.524533\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"172.389059\" y=\"175.924991\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"173.908489\" y=\"159.125601\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"175.427918\" y=\"163.325449\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"176.947348\" y=\"108.727432\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"178.466778\" y=\"104.527584\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"179.986207\" y=\"66.728957\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"181.505637\" y=\"112.927279\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"183.025067\" y=\"83.528347\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"184.544496\" y=\"108.727432\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"186.063926\" y=\"66.728957\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"187.583356\" y=\"108.727432\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"189.102785\" y=\"54.129415\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"190.622215\" y=\"108.727432\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"192.141644\" y=\"75.128652\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"193.661074\" y=\"70.928804\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"195.180504\" y=\"163.325449\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"196.699933\" y=\"100.327737\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"198.219363\" y=\"96.127889\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"199.738793\" y=\"125.526821\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"201.258222\" y=\"133.926516\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"202.777652\" y=\"154.925754\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"204.297082\" y=\"129.726669\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"205.816511\" y=\"159.125601\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"207.335941\" y=\"175.924991\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"208.85537\" y=\"171.725143\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"210.3748\" y=\"217.923465\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"211.89423\" y=\"213.723618\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"213.413659\" y=\"226.32316\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"214.933089\" y=\"201.124076\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"216.452519\" y=\"217.923465\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"217.971948\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"219.491378\" y=\"238.922703\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"221.010808\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"222.530237\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"224.049667\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"225.569096\" y=\"234.722855\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"227.088526\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"228.607956\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"230.127385\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"231.646815\" y=\"243.12255\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"233.166245\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"234.685674\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"237.724534\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"239.243963\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"240.763393\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"242.282822\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"243.802252\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"245.321682\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"246.841111\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"248.360541\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"249.879971\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"251.3994\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"252.91883\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"254.43826\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"255.957689\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"257.477119\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"258.996548\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"260.515978\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"262.035408\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"265.074267\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"266.593697\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"269.632556\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"271.151986\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"272.671415\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"274.190845\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"275.710274\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"277.229704\" y=\"247.322398\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"278.749134\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"280.268563\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"281.787993\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"284.826852\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"286.346282\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"287.865712\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"289.385141\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"292.424\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"293.94343\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"295.46286\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"296.982289\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"298.501719\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"300.021149\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"301.540578\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"304.579438\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"306.098867\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"307.618297\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"309.137726\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"310.657156\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"312.176586\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"313.696015\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"316.734875\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"318.254304\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"319.773734\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"321.293164\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"322.812593\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"324.332023\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"325.851452\" y=\"255.722093\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"327.370882\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"328.890312\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"330.409741\" y=\"251.522245\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"331.929171\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"333.448601\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"334.96803\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"336.48746\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"339.526319\" y=\"264.121788\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mdf41b40d50\" x=\"341.045749\" y=\"259.92194\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 22.72524 268.321635 \n", "L 22.72524 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 22.72524 268.321635 \n", "L 356.99976 268.321635 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 22.72524 3.00024 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p235df86df2\">\n", "   <rect x=\"22.72524\" y=\"3.00024\" width=\"334.27452\" height=\"265.321395\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 201.8 (χ²/ndof = 1.0)      │              Nfcn = 100              │\n", "│ EDM = 2.37e-05 (Goal: 0.0002)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │  1.02e3   │  0.05e3   │            │            │    0    │         │       │\n", "│ 1 │ x1   │    810    │    40     │            │            │    0    │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌────┬───────────────────┐\n", "│    │       x0       x1 │\n", "├────┼───────────────────┤\n", "│ x0 │ 2.41e+03   -0.3e3 │\n", "│ x1 │   -0.3e3 1.92e+03 │\n", "└────┴───────────────────┘"]}, "metadata": {}, "output_type": "display_data"}], "source": ["xr = (0, 2)  # xrange\n", "rng = np.random.default_rng(1)\n", "\n", "nmc = 1000\n", "trials = 1000\n", "\n", "data = {}\n", "data2 = {}\n", "\n", "first = True\n", "for trial in range(trials):\n", "    for bins in (\n", "        20,\n", "        200,\n", "    ):\n", "        xdata = rng.normal(1, 0.1, size=1000)\n", "        ydata = rng.exponential(size=len(xdata))\n", "        xmix = np.append(xdata, ydata)\n", "        xmix = xmix[(xr[0] < xmix) & (xmix < xr[1])]\n", "\n", "        n, xe = np.histogram(xmix, bins=bins, range=xr)\n", "\n", "        x = rng.normal(1, 0.1, size=nmc)\n", "        y = rng.exponential(size=nmc)\n", "        t = [\n", "            np.histogram(x, bins=bins, range=xr)[0],\n", "            np.histogram(y, bins=bins, range=xr)[0],\n", "        ]\n", "        c = cost.Template(n, xe, t)\n", "        m = Minuit(c, 1, 1)\n", "        m.migrad()\n", "        assert m.valid\n", "        assert m.accurate\n", "        data.setdefault(bins, []).append(m.fmin.fval)\n", "        data2.setdefault(bins, []).append(np.nansum(c.pulls(m.values) ** 2))\n", "        # display one example fit\n", "        if first:\n", "            display(m)\n", "    first = False\n", "\n", "for key in tuple(data):\n", "    val = data[key]\n", "    data[key] = np.array(val)\n", "    val = data2[key]\n", "    data2[key] = np.array(val)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"397.6075pt\" height=\"312.30825pt\" viewBox=\"0 0 397.6075 312.30825\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:44:26.393355</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 312.30825 \n", "L 397.6075 312.30825 \n", "L 397.6075 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 288.430125 \n", "L 390.4075 288.430125 \n", "L 390.4075 22.318125 \n", "L 33.2875 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 49.520227 288.430125 \n", "L 81.985682 288.430125 \n", "L 81.985682 91.079305 \n", "L 49.520227 91.079305 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 81.985682 288.430125 \n", "L 114.451136 288.430125 \n", "L 114.451136 82.769797 \n", "L 81.985682 82.769797 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 114.451136 288.430125 \n", "L 146.916591 288.430125 \n", "L 146.916591 86.924551 \n", "L 114.451136 86.924551 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 146.916591 288.430125 \n", "L 179.382045 288.430125 \n", "L 179.382045 41.222256 \n", "L 146.916591 41.222256 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 179.382045 288.430125 \n", "L 211.8475 288.430125 \n", "L 211.8475 84.847174 \n", "L 179.382045 84.847174 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 211.8475 288.430125 \n", "L 244.312955 288.430125 \n", "L 244.312955 89.001928 \n", "L 211.8475 89.001928 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 244.312955 288.430125 \n", "L 276.778409 288.430125 \n", "L 276.778409 78.615043 \n", "L 244.312955 78.615043 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 276.778409 288.430125 \n", "L 309.243864 288.430125 \n", "L 309.243864 61.996027 \n", "L 276.778409 61.996027 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 309.243864 288.430125 \n", "L 341.709318 288.430125 \n", "L 341.709318 107.698322 \n", "L 309.243864 107.698322 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 341.709318 288.430125 \n", "L 374.174773 288.430125 \n", "L 374.174773 82.769797 \n", "L 341.709318 82.769797 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 49.520227 288.430125 \n", "L 81.985682 288.430125 \n", "L 81.985682 91.079305 \n", "L 49.520227 91.079305 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 81.985682 288.430125 \n", "L 114.451136 288.430125 \n", "L 114.451136 80.69242 \n", "L 81.985682 80.69242 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 114.451136 288.430125 \n", "L 146.916591 288.430125 \n", "L 146.916591 80.69242 \n", "L 114.451136 80.69242 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 146.916591 288.430125 \n", "L 179.382045 288.430125 \n", "L 179.382045 34.990125 \n", "L 146.916591 34.990125 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 179.382045 288.430125 \n", "L 211.8475 288.430125 \n", "L 211.8475 86.924551 \n", "L 179.382045 86.924551 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 211.8475 288.430125 \n", "L 244.312955 288.430125 \n", "L 244.312955 93.156682 \n", "L 211.8475 93.156682 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 244.312955 288.430125 \n", "L 276.778409 288.430125 \n", "L 276.778409 72.382912 \n", "L 244.312955 72.382912 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 276.778409 288.430125 \n", "L 309.243864 288.430125 \n", "L 309.243864 57.841273 \n", "L 276.778409 57.841273 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 309.243864 288.430125 \n", "L 341.709318 288.430125 \n", "L 341.709318 113.930453 \n", "L 309.243864 113.930453 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 341.709318 288.430125 \n", "L 374.174773 288.430125 \n", "L 374.174773 95.234059 \n", "L 341.709318 95.234059 \n", "z\n", "\" clip-path=\"url(#p34ad7ace3b)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mc81345dae9\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc81345dae9\" x=\"49.520227\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(41.568665 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mc81345dae9\" x=\"114.451136\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(106.499574 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mc81345dae9\" x=\"179.382045\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(171.430483 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc81345dae9\" x=\"244.312955\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(236.361392 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mc81345dae9\" x=\"309.243864\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(301.292301 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mc81345dae9\" x=\"374.174773\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(366.22321 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m32c661e029\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m32c661e029\" x=\"33.2875\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 292.229344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m32c661e029\" x=\"33.2875\" y=\"246.882584\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(13.5625 250.681803) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m32c661e029\" x=\"33.2875\" y=\"205.335043\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(13.5625 209.134262) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m32c661e029\" x=\"33.2875\" y=\"163.787502\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(13.5625 167.586721) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m32c661e029\" x=\"33.2875\" y=\"122.239961\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(13.5625 126.03918) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m32c661e029\" x=\"33.2875\" y=\"80.69242\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 84.491639) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m32c661e029\" x=\"33.2875\" y=\"39.144879\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 120 -->\n", "      <g transform=\"translate(7.2 42.944098) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 33.2875 288.430125 \n", "L 33.2875 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 390.4075 288.430125 \n", "L 390.4075 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 33.2875 288.430125 \n", "L 390.4075 288.430125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 33.2875 22.318125 \n", "L 390.4075 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_14\">\n", "    <!-- bins = 20 -->\n", "    <g transform=\"translate(182.966875 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-62\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"91.259766\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"206.738281\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"238.525391\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"322.314453\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"354.101562\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"417.724609\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_27\">\n", "     <path d=\"M 247.084063 59.674375 \n", "L 383.4075 59.674375 \n", "Q 385.4075 59.674375 385.4075 57.674375 \n", "L 385.4075 29.318125 \n", "Q 385.4075 27.318125 383.4075 27.318125 \n", "L 247.084063 27.318125 \n", "Q 245.084063 27.318125 245.084063 29.318125 \n", "L 245.084063 57.674375 \n", "Q 245.084063 59.674375 247.084063 59.674375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"patch_28\">\n", "     <path d=\"M 249.084063 38.916562 \n", "L 269.084063 38.916562 \n", "L 269.084063 31.916562 \n", "L 249.084063 31.916562 \n", "z\n", "\" style=\"fill: #1f77b4\"/>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- cost function -->\n", "     <g transform=\"translate(277.084063 38.916562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"116.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"168.261719\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"207.470703\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"239.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"274.462891\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"337.841797\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"401.220703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"456.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"495.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"523.193359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"584.375\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_29\">\n", "     <path d=\"M 249.084063 53.594687 \n", "L 269.084063 53.594687 \n", "L 269.084063 46.594687 \n", "L 249.084063 46.594687 \n", "z\n", "\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- sum of pulls squared -->\n", "     <g transform=\"translate(277.084063 53.594687) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-71\" d=\"M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "M 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 -1331 \n", "L 2906 -1331 \n", "L 2906 525 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"115.478516\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"212.890625\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"244.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"305.859375\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"341.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"372.851562\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"436.328125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"499.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"527.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"555.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"607.373047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"639.160156\"/>\n", "      <use xlink:href=\"#DejaVuSans-71\" x=\"691.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"754.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"818.115234\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"879.394531\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"918.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"979.78125\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p34ad7ace3b\">\n", "   <rect x=\"33.2875\" y=\"22.318125\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"397.6075pt\" height=\"312.30825pt\" viewBox=\"0 0 397.6075 312.30825\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:44:26.481424</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 312.30825 \n", "L 397.6075 312.30825 \n", "L 397.6075 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 288.430125 \n", "L 390.4075 288.430125 \n", "L 390.4075 22.318125 \n", "L 33.2875 22.318125 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 49.520227 288.430125 \n", "L 81.985682 288.430125 \n", "L 81.985682 171.24819 \n", "L 49.520227 171.24819 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 81.985682 288.430125 \n", "L 114.451136 288.430125 \n", "L 114.451136 212.125609 \n", "L 81.985682 212.125609 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 114.451136 288.430125 \n", "L 146.916591 288.430125 \n", "L 146.916591 223.026254 \n", "L 114.451136 223.026254 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 146.916591 288.430125 \n", "L 179.382045 288.430125 \n", "L 179.382045 225.206383 \n", "L 146.916591 225.206383 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 179.382045 288.430125 \n", "L 211.8475 288.430125 \n", "L 211.8475 237.742125 \n", "L 179.382045 237.742125 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 211.8475 288.430125 \n", "L 244.312955 288.430125 \n", "L 244.312955 241.012319 \n", "L 211.8475 241.012319 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 244.312955 288.430125 \n", "L 276.778409 288.430125 \n", "L 276.778409 243.73748 \n", "L 244.312955 243.73748 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 276.778409 288.430125 \n", "L 309.243864 288.430125 \n", "L 309.243864 256.273222 \n", "L 276.778409 256.273222 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 309.243864 288.430125 \n", "L 341.709318 288.430125 \n", "L 341.709318 261.723544 \n", "L 309.243864 261.723544 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 341.709318 288.430125 \n", "L 374.174773 288.430125 \n", "L 374.174773 267.173867 \n", "L 341.709318 267.173867 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 49.520227 288.430125 \n", "L 81.985682 288.430125 \n", "L 81.985682 34.990125 \n", "L 49.520227 34.990125 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 81.985682 288.430125 \n", "L 114.451136 288.430125 \n", "L 114.451136 187.599157 \n", "L 81.985682 187.599157 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 114.451136 288.430125 \n", "L 146.916591 288.430125 \n", "L 146.916591 233.926899 \n", "L 114.451136 233.926899 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 146.916591 288.430125 \n", "L 179.382045 288.430125 \n", "L 179.382045 250.277867 \n", "L 146.916591 250.277867 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 179.382045 288.430125 \n", "L 211.8475 288.430125 \n", "L 211.8475 251.367931 \n", "L 179.382045 251.367931 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 211.8475 288.430125 \n", "L 244.312955 288.430125 \n", "L 244.312955 262.268577 \n", "L 211.8475 262.268577 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 244.312955 288.430125 \n", "L 276.778409 288.430125 \n", "L 276.778409 276.439415 \n", "L 244.312955 276.439415 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 276.778409 288.430125 \n", "L 309.243864 288.430125 \n", "L 309.243864 278.619544 \n", "L 276.778409 278.619544 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 309.243864 288.430125 \n", "L 341.709318 288.430125 \n", "L 341.709318 280.799673 \n", "L 309.243864 280.799673 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 341.709318 288.430125 \n", "L 374.174773 288.430125 \n", "L 374.174773 282.979802 \n", "L 341.709318 282.979802 \n", "z\n", "\" clip-path=\"url(#p7b204d80b4)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m5d1e74eac3\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5d1e74eac3\" x=\"49.520227\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(41.568665 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m5d1e74eac3\" x=\"114.451136\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(106.499574 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m5d1e74eac3\" x=\"179.382045\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(171.430483 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m5d1e74eac3\" x=\"244.312955\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(236.361392 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m5d1e74eac3\" x=\"309.243864\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(301.292301 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m5d1e74eac3\" x=\"374.174773\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(366.22321 303.028562) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"m757c51f736\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m757c51f736\" x=\"33.2875\" y=\"288.430125\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 292.229344) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m757c51f736\" x=\"33.2875\" y=\"233.926899\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 237.726118) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m757c51f736\" x=\"33.2875\" y=\"179.423673\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 183.222892) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m757c51f736\" x=\"33.2875\" y=\"124.920448\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 128.719666) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m757c51f736\" x=\"33.2875\" y=\"70.417222\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 74.216441) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 33.2875 288.430125 \n", "L 33.2875 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 390.4075 288.430125 \n", "L 390.4075 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 33.2875 288.430125 \n", "L 390.4075 288.430125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 33.2875 22.318125 \n", "L 390.4075 22.318125 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- bins = 200 -->\n", "    <g transform=\"translate(179.149375 16.318125) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-62\"/>\n", "     <use xlink:href=\"#DejaVuSans-69\" x=\"63.476562\"/>\n", "     <use xlink:href=\"#DejaVuSans-6e\" x=\"91.259766\"/>\n", "     <use xlink:href=\"#DejaVuSans-73\" x=\"154.638672\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"206.738281\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" x=\"238.525391\"/>\n", "     <use xlink:href=\"#DejaVuSans-20\" x=\"322.314453\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" x=\"354.101562\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"417.724609\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"481.347656\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_27\">\n", "     <path d=\"M 247.084063 59.674375 \n", "L 383.4075 59.674375 \n", "Q 385.4075 59.674375 385.4075 57.674375 \n", "L 385.4075 29.318125 \n", "Q 385.4075 27.318125 383.4075 27.318125 \n", "L 247.084063 27.318125 \n", "Q 245.084063 27.318125 245.084063 29.318125 \n", "L 245.084063 57.674375 \n", "Q 245.084063 59.674375 247.084063 59.674375 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"patch_28\">\n", "     <path d=\"M 249.084063 38.916562 \n", "L 269.084063 38.916562 \n", "L 269.084063 31.916562 \n", "L 249.084063 31.916562 \n", "z\n", "\" style=\"fill: #1f77b4\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- cost function -->\n", "     <g transform=\"translate(277.084063 38.916562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"116.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"168.261719\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"207.470703\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"239.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"274.462891\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"337.841797\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"401.220703\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"456.201172\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"495.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"523.193359\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"584.375\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_29\">\n", "     <path d=\"M 249.084063 53.594687 \n", "L 269.084063 53.594687 \n", "L 269.084063 46.594687 \n", "L 249.084063 46.594687 \n", "z\n", "\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- sum of pulls squared -->\n", "     <g transform=\"translate(277.084063 53.594687) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-71\" d=\"M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "M 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 -1331 \n", "L 2906 -1331 \n", "L 2906 525 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"115.478516\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"212.890625\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"244.677734\"/>\n", "      <use xlink:href=\"#DejaVuSans-66\" x=\"305.859375\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"341.064453\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"372.851562\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"436.328125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"499.707031\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"527.490234\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"555.273438\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"607.373047\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"639.160156\"/>\n", "      <use xlink:href=\"#DejaVuSans-71\" x=\"691.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"754.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"818.115234\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"879.394531\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"918.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"979.78125\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p7b204d80b4\">\n", "   <rect x=\"33.2875\" y=\"22.318125\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for bins in data:\n", "    plt.figure()\n", "    plt.title(f\"bins = {bins}\")\n", "    plt.hist(\n", "        chi2(bins - 2).cdf(data[bins]), bins=10, range=(0, 1), label=\"cost function\"\n", "    )\n", "    plt.hist(\n", "        chi2(bins - 2).cdf(data2[bins]),\n", "        bins=10,\n", "        range=(0, 1),\n", "        alpha=0.5,\n", "        label=\"sum of pulls squared\",\n", "    )\n", "    plt.legend()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["When 20 bins are used, the number of counts per bin is large enough so that both test statistics are chi-square distributed. When 200 bins are used with samples of the same size, the density in some bins drops low enough so that we are not in the asymptotic limit and see deviations from the theoretical chi-square distribution. These deviations are larger for the sum of pulls squared."]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "py310", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}