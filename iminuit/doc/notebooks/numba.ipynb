{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Acceleration with <PERSON><PERSON>\n", "\n", "We explore how the computation of cost functions can be dramatically accelerated with numba's JIT compiler.\n", "\n", "The run-time of iminuit is usually dominated by the execution time of the cost function. To get good performance, it recommended to use array arthimetic and scipy and numpy functions in the body of the cost function. Python loops should be avoided, but if they are unavoidable, [<PERSON><PERSON>](https://numba.pydata.org/) can help. <PERSON><PERSON> can also parallelize numerical calculations to make full use of multi-core CPUs and even do computations on the GPU.\n", "\n", "Note: This tutorial shows how one can generate faster pdfs with Numba. Before you start to write your own pdf, please check whether one is already implemented in the [numba_stats library](https://github.com/HDembinski/numba-stats). If you have a pdf that is not included there, please consider contributing it to numba_stats."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install matplotlib numpy numba scipy iminuit\n", "%config InlineBackend.figure_formats = ['svg']\n", "from iminuit import Minuit\n", "import numpy as np\n", "import numba as nb\n", "import math\n", "from scipy.stats import expon, norm\n", "from matplotlib import pyplot as plt\n", "from argparse import Namespace"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The standard fit in particle physics is the fit of a peak over some smooth background. We generate a Gaussian peak over exponential background, using scipy."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"397.6075pt\" height=\"310.86825pt\" viewBox=\"0 0 397.6075 310.86825\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:31:13.398908</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 310.86825 \n", "L 397.6075 310.86825 \n", "L 397.6075 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 273.312 \n", "L 390.4075 273.312 \n", "L 390.4075 7.2 \n", "L 33.2875 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 49.520227 273.312 \n", "L 56.013318 273.312 \n", "L 56.013318 19.872 \n", "L 49.520227 19.872 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 56.013318 273.312 \n", "L 62.506409 273.312 \n", "L 62.506409 49.941153 \n", "L 56.013318 49.941153 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 62.506409 273.312 \n", "L 68.9995 273.312 \n", "L 68.9995 60.094373 \n", "L 62.506409 60.094373 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 68.9995 273.312 \n", "L 75.492591 273.312 \n", "L 75.492591 74.152678 \n", "L 68.9995 74.152678 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 75.492591 273.312 \n", "L 81.985682 273.312 \n", "L 81.985682 68.295051 \n", "L 75.492591 68.295051 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 81.985682 273.312 \n", "L 88.478773 273.312 \n", "L 88.478773 80.010305 \n", "L 81.985682 80.010305 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 88.478773 273.312 \n", "L 94.971864 273.312 \n", "L 94.971864 95.240136 \n", "L 88.478773 95.240136 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 94.971864 273.312 \n", "L 101.464955 273.312 \n", "L 101.464955 104.221831 \n", "L 94.971864 104.221831 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 101.464955 273.312 \n", "L 107.958045 273.312 \n", "L 107.958045 95.240136 \n", "L 101.464955 95.240136 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 107.958045 273.312 \n", "L 114.451136 273.312 \n", "L 114.451136 114.375051 \n", "L 107.958045 114.375051 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 114.451136 273.312 \n", "L 120.944227 273.312 \n", "L 120.944227 110.860475 \n", "L 114.451136 110.860475 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 120.944227 273.312 \n", "L 127.437318 273.312 \n", "L 127.437318 112.813017 \n", "L 120.944227 112.813017 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_15\">\n", "    <path d=\"M 127.437318 273.312 \n", "L 133.930409 273.312 \n", "L 133.930409 129.99539 \n", "L 127.437318 129.99539 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_16\">\n", "    <path d=\"M 133.930409 273.312 \n", "L 140.4235 273.312 \n", "L 140.4235 122.575729 \n", "L 133.930409 122.575729 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_17\">\n", "    <path d=\"M 140.4235 273.312 \n", "L 146.916591 273.312 \n", "L 146.916591 129.604881 \n", "L 140.4235 129.604881 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_18\">\n", "    <path d=\"M 146.916591 273.312 \n", "L 153.409682 273.312 \n", "L 153.409682 126.480814 \n", "L 146.916591 126.480814 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_19\">\n", "    <path d=\"M 153.409682 273.312 \n", "L 159.902773 273.312 \n", "L 159.902773 145.22522 \n", "L 153.409682 145.22522 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_20\">\n", "    <path d=\"M 159.902773 273.312 \n", "L 166.395864 273.312 \n", "L 166.395864 160.845559 \n", "L 159.902773 160.845559 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_21\">\n", "    <path d=\"M 166.395864 273.312 \n", "L 172.888955 273.312 \n", "L 172.888955 166.312678 \n", "L 166.395864 166.312678 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_22\">\n", "    <path d=\"M 172.888955 273.312 \n", "L 179.382045 273.312 \n", "L 179.382045 173.341831 \n", "L 172.888955 173.341831 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_23\">\n", "    <path d=\"M 179.382045 273.312 \n", "L 185.875136 273.312 \n", "L 185.875136 162.017085 \n", "L 179.382045 162.017085 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_24\">\n", "    <path d=\"M 185.875136 273.312 \n", "L 192.368227 273.312 \n", "L 192.368227 172.170305 \n", "L 185.875136 172.170305 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_25\">\n", "    <path d=\"M 192.368227 273.312 \n", "L 198.861318 273.312 \n", "L 198.861318 161.236068 \n", "L 192.368227 161.236068 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_26\">\n", "    <path d=\"M 198.861318 273.312 \n", "L 205.354409 273.312 \n", "L 205.354409 178.027932 \n", "L 198.861318 178.027932 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_27\">\n", "    <path d=\"M 205.354409 273.312 \n", "L 211.8475 273.312 \n", "L 211.8475 179.980475 \n", "L 205.354409 179.980475 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_28\">\n", "    <path d=\"M 211.8475 273.312 \n", "L 218.340591 273.312 \n", "L 218.340591 187.400136 \n", "L 211.8475 187.400136 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_29\">\n", "    <path d=\"M 218.340591 273.312 \n", "L 224.833682 273.312 \n", "L 224.833682 176.856407 \n", "L 218.340591 176.856407 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_30\">\n", "    <path d=\"M 224.833682 273.312 \n", "L 231.326773 273.312 \n", "L 231.326773 192.086237 \n", "L 224.833682 192.086237 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_31\">\n", "    <path d=\"M 231.326773 273.312 \n", "L 237.819864 273.312 \n", "L 237.819864 203.801492 \n", "L 231.326773 203.801492 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_32\">\n", "    <path d=\"M 237.819864 273.312 \n", "L 244.312955 273.312 \n", "L 244.312955 195.210305 \n", "L 237.819864 195.210305 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_33\">\n", "    <path d=\"M 244.312955 273.312 \n", "L 250.806045 273.312 \n", "L 250.806045 199.11539 \n", "L 244.312955 199.11539 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_34\">\n", "    <path d=\"M 250.806045 273.312 \n", "L 257.299136 273.312 \n", "L 257.299136 206.144542 \n", "L 250.806045 206.144542 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_35\">\n", "    <path d=\"M 257.299136 273.312 \n", "L 263.792227 273.312 \n", "L 263.792227 199.505898 \n", "L 257.299136 199.505898 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_36\">\n", "    <path d=\"M 263.792227 273.312 \n", "L 270.285318 273.312 \n", "L 270.285318 207.316068 \n", "L 263.792227 207.316068 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_37\">\n", "    <path d=\"M 270.285318 273.312 \n", "L 276.778409 273.312 \n", "L 276.778409 208.487593 \n", "L 270.285318 208.487593 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_38\">\n", "    <path d=\"M 276.778409 273.312 \n", "L 283.2715 273.312 \n", "L 283.2715 210.440136 \n", "L 276.778409 210.440136 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_39\">\n", "    <path d=\"M 283.2715 273.312 \n", "L 289.764591 273.312 \n", "L 289.764591 222.936407 \n", "L 283.2715 222.936407 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_40\">\n", "    <path d=\"M 289.764591 273.312 \n", "L 296.257682 273.312 \n", "L 296.257682 219.421831 \n", "L 289.764591 219.421831 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_41\">\n", "    <path d=\"M 296.257682 273.312 \n", "L 302.750773 273.312 \n", "L 302.750773 219.421831 \n", "L 296.257682 219.421831 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_42\">\n", "    <path d=\"M 302.750773 273.312 \n", "L 309.243864 273.312 \n", "L 309.243864 227.622508 \n", "L 302.750773 227.622508 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_43\">\n", "    <path d=\"M 309.243864 273.312 \n", "L 315.736955 273.312 \n", "L 315.736955 223.717424 \n", "L 309.243864 223.717424 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_44\">\n", "    <path d=\"M 315.736955 273.312 \n", "L 322.230045 273.312 \n", "L 322.230045 225.669966 \n", "L 315.736955 225.669966 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_45\">\n", "    <path d=\"M 322.230045 273.312 \n", "L 328.723136 273.312 \n", "L 328.723136 228.403525 \n", "L 322.230045 228.403525 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_46\">\n", "    <path d=\"M 328.723136 273.312 \n", "L 335.216227 273.312 \n", "L 335.216227 229.575051 \n", "L 328.723136 229.575051 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_47\">\n", "    <path d=\"M 335.216227 273.312 \n", "L 341.709318 273.312 \n", "L 341.709318 233.480136 \n", "L 335.216227 233.480136 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_48\">\n", "    <path d=\"M 341.709318 273.312 \n", "L 348.202409 273.312 \n", "L 348.202409 227.232 \n", "L 341.709318 227.232 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_49\">\n", "    <path d=\"M 348.202409 273.312 \n", "L 354.6955 273.312 \n", "L 354.6955 232.699119 \n", "L 348.202409 232.699119 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_50\">\n", "    <path d=\"M 354.6955 273.312 \n", "L 361.188591 273.312 \n", "L 361.188591 233.089627 \n", "L 354.6955 233.089627 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_51\">\n", "    <path d=\"M 361.188591 273.312 \n", "L 367.681682 273.312 \n", "L 367.681682 236.213695 \n", "L 361.188591 236.213695 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_52\">\n", "    <path d=\"M 367.681682 273.312 \n", "L 374.174773 273.312 \n", "L 374.174773 238.556746 \n", "L 367.681682 238.556746 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_53\">\n", "    <path d=\"M 49.520227 19.872 \n", "L 56.013318 19.872 \n", "L 56.013318 19.872 \n", "L 49.520227 19.872 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_54\">\n", "    <path d=\"M 56.013318 49.941153 \n", "L 62.506409 49.941153 \n", "L 62.506409 49.941153 \n", "L 56.013318 49.941153 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_55\">\n", "    <path d=\"M 62.506409 60.094373 \n", "L 68.9995 60.094373 \n", "L 68.9995 60.094373 \n", "L 62.506409 60.094373 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_56\">\n", "    <path d=\"M 68.9995 74.152678 \n", "L 75.492591 74.152678 \n", "L 75.492591 74.152678 \n", "L 68.9995 74.152678 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_57\">\n", "    <path d=\"M 75.492591 68.295051 \n", "L 81.985682 68.295051 \n", "L 81.985682 68.295051 \n", "L 75.492591 68.295051 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_58\">\n", "    <path d=\"M 81.985682 80.010305 \n", "L 88.478773 80.010305 \n", "L 88.478773 80.010305 \n", "L 81.985682 80.010305 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_59\">\n", "    <path d=\"M 88.478773 95.240136 \n", "L 94.971864 95.240136 \n", "L 94.971864 95.240136 \n", "L 88.478773 95.240136 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_60\">\n", "    <path d=\"M 94.971864 104.221831 \n", "L 101.464955 104.221831 \n", "L 101.464955 104.221831 \n", "L 94.971864 104.221831 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_61\">\n", "    <path d=\"M 101.464955 95.240136 \n", "L 107.958045 95.240136 \n", "L 107.958045 95.240136 \n", "L 101.464955 95.240136 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_62\">\n", "    <path d=\"M 107.958045 114.375051 \n", "L 114.451136 114.375051 \n", "L 114.451136 114.375051 \n", "L 107.958045 114.375051 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_63\">\n", "    <path d=\"M 114.451136 110.860475 \n", "L 120.944227 110.860475 \n", "L 120.944227 110.860475 \n", "L 114.451136 110.860475 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_64\">\n", "    <path d=\"M 120.944227 112.813017 \n", "L 127.437318 112.813017 \n", "L 127.437318 112.813017 \n", "L 120.944227 112.813017 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_65\">\n", "    <path d=\"M 127.437318 129.99539 \n", "L 133.930409 129.99539 \n", "L 133.930409 129.99539 \n", "L 127.437318 129.99539 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_66\">\n", "    <path d=\"M 133.930409 122.575729 \n", "L 140.4235 122.575729 \n", "L 140.4235 122.575729 \n", "L 133.930409 122.575729 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_67\">\n", "    <path d=\"M 140.4235 129.604881 \n", "L 146.916591 129.604881 \n", "L 146.916591 129.604881 \n", "L 140.4235 129.604881 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_68\">\n", "    <path d=\"M 146.916591 126.480814 \n", "L 153.409682 126.480814 \n", "L 153.409682 125.309288 \n", "L 146.916591 125.309288 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_69\">\n", "    <path d=\"M 153.409682 145.22522 \n", "L 159.902773 145.22522 \n", "L 159.902773 141.710644 \n", "L 153.409682 141.710644 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_70\">\n", "    <path d=\"M 159.902773 160.845559 \n", "L 166.395864 160.845559 \n", "L 166.395864 154.206915 \n", "L 159.902773 154.206915 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_71\">\n", "    <path d=\"M 166.395864 166.312678 \n", "L 172.888955 166.312678 \n", "L 172.888955 153.816407 \n", "L 166.395864 153.816407 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_72\">\n", "    <path d=\"M 172.888955 173.341831 \n", "L 179.382045 173.341831 \n", "L 179.382045 157.330983 \n", "L 172.888955 157.330983 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_73\">\n", "    <path d=\"M 179.382045 162.017085 \n", "L 185.875136 162.017085 \n", "L 185.875136 126.090305 \n", "L 179.382045 126.090305 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_74\">\n", "    <path d=\"M 185.875136 172.170305 \n", "L 192.368227 172.170305 \n", "L 192.368227 124.91878 \n", "L 185.875136 124.91878 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_75\">\n", "    <path d=\"M 192.368227 161.236068 \n", "L 198.861318 161.236068 \n", "L 198.861318 94.849627 \n", "L 192.368227 94.849627 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_76\">\n", "    <path d=\"M 198.861318 178.027932 \n", "L 205.354409 178.027932 \n", "L 205.354409 85.867932 \n", "L 198.861318 85.867932 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_77\">\n", "    <path d=\"M 205.354409 179.980475 \n", "L 211.8475 179.980475 \n", "L 211.8475 81.962847 \n", "L 205.354409 81.962847 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_78\">\n", "    <path d=\"M 211.8475 187.400136 \n", "L 218.340591 187.400136 \n", "L 218.340591 90.554034 \n", "L 211.8475 90.554034 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_79\">\n", "    <path d=\"M 218.340591 176.856407 \n", "L 224.833682 176.856407 \n", "L 224.833682 86.258441 \n", "L 218.340591 86.258441 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_80\">\n", "    <path d=\"M 224.833682 192.086237 \n", "L 231.326773 192.086237 \n", "L 231.326773 115.937085 \n", "L 224.833682 115.937085 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_81\">\n", "    <path d=\"M 231.326773 203.801492 \n", "L 237.819864 203.801492 \n", "L 237.819864 152.254373 \n", "L 231.326773 152.254373 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_82\">\n", "    <path d=\"M 237.819864 195.210305 \n", "L 244.312955 195.210305 \n", "L 244.312955 158.893017 \n", "L 237.819864 158.893017 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_83\">\n", "    <path d=\"M 244.312955 199.11539 \n", "L 250.806045 199.11539 \n", "L 250.806045 171.779797 \n", "L 244.312955 171.779797 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_84\">\n", "    <path d=\"M 250.806045 206.144542 \n", "L 257.299136 206.144542 \n", "L 257.299136 190.133695 \n", "L 250.806045 190.133695 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_85\">\n", "    <path d=\"M 257.299136 199.505898 \n", "L 263.792227 199.505898 \n", "L 263.792227 195.991322 \n", "L 257.299136 195.991322 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_86\">\n", "    <path d=\"M 263.792227 207.316068 \n", "L 270.285318 207.316068 \n", "L 270.285318 206.144542 \n", "L 263.792227 206.144542 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_87\">\n", "    <path d=\"M 270.285318 208.487593 \n", "L 276.778409 208.487593 \n", "L 276.778409 207.706576 \n", "L 270.285318 207.706576 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_88\">\n", "    <path d=\"M 276.778409 210.440136 \n", "L 283.2715 210.440136 \n", "L 283.2715 209.659119 \n", "L 276.778409 209.659119 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_89\">\n", "    <path d=\"M 283.2715 222.936407 \n", "L 289.764591 222.936407 \n", "L 289.764591 222.936407 \n", "L 283.2715 222.936407 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_90\">\n", "    <path d=\"M 289.764591 219.421831 \n", "L 296.257682 219.421831 \n", "L 296.257682 219.031322 \n", "L 289.764591 219.031322 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_91\">\n", "    <path d=\"M 296.257682 219.421831 \n", "L 302.750773 219.421831 \n", "L 302.750773 219.421831 \n", "L 296.257682 219.421831 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_92\">\n", "    <path d=\"M 302.750773 227.622508 \n", "L 309.243864 227.622508 \n", "L 309.243864 227.622508 \n", "L 302.750773 227.622508 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_93\">\n", "    <path d=\"M 309.243864 223.717424 \n", "L 315.736955 223.717424 \n", "L 315.736955 223.717424 \n", "L 309.243864 223.717424 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_94\">\n", "    <path d=\"M 315.736955 225.669966 \n", "L 322.230045 225.669966 \n", "L 322.230045 225.669966 \n", "L 315.736955 225.669966 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_95\">\n", "    <path d=\"M 322.230045 228.403525 \n", "L 328.723136 228.403525 \n", "L 328.723136 228.403525 \n", "L 322.230045 228.403525 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_96\">\n", "    <path d=\"M 328.723136 229.575051 \n", "L 335.216227 229.575051 \n", "L 335.216227 229.575051 \n", "L 328.723136 229.575051 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_97\">\n", "    <path d=\"M 335.216227 233.480136 \n", "L 341.709318 233.480136 \n", "L 341.709318 233.480136 \n", "L 335.216227 233.480136 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_98\">\n", "    <path d=\"M 341.709318 227.232 \n", "L 348.202409 227.232 \n", "L 348.202409 227.232 \n", "L 341.709318 227.232 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_99\">\n", "    <path d=\"M 348.202409 232.699119 \n", "L 354.6955 232.699119 \n", "L 354.6955 232.699119 \n", "L 348.202409 232.699119 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_100\">\n", "    <path d=\"M 354.6955 233.089627 \n", "L 361.188591 233.089627 \n", "L 361.188591 233.089627 \n", "L 354.6955 233.089627 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_101\">\n", "    <path d=\"M 361.188591 236.213695 \n", "L 367.681682 236.213695 \n", "L 367.681682 236.213695 \n", "L 361.188591 236.213695 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"patch_102\">\n", "    <path d=\"M 367.681682 238.556746 \n", "L 374.174773 238.556746 \n", "L 374.174773 238.556746 \n", "L 367.681682 238.556746 \n", "z\n", "\" clip-path=\"url(#p4e048ac185)\" style=\"fill: #ff7f0e\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m967850cb8f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"49.514169\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 1 -->\n", "      <g transform=\"translate(46.332919 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"90.113915\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(86.932665 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"130.71366\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 3 -->\n", "      <g transform=\"translate(127.53241 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"171.313406\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(168.132156 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"211.913152\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 5 -->\n", "      <g transform=\"translate(208.731902 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"252.512898\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(249.331648 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"293.112643\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 7 -->\n", "      <g transform=\"translate(289.931393 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"333.712389\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(330.531139 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m967850cb8f\" x=\"374.312135\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 9 -->\n", "      <g transform=\"translate(371.130885 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-39\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- x -->\n", "     <g transform=\"translate(208.888125 301.588562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"ma822fc44d4\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma822fc44d4\" x=\"33.2875\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 277.111219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#ma822fc44d4\" x=\"33.2875\" y=\"234.261153\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 238.060371) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#ma822fc44d4\" x=\"33.2875\" y=\"195.210305\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(7.2 199.009524) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#ma822fc44d4\" x=\"33.2875\" y=\"156.159458\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 300 -->\n", "      <g transform=\"translate(7.2 159.958676) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#ma822fc44d4\" x=\"33.2875\" y=\"117.10861\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 400 -->\n", "      <g transform=\"translate(7.2 120.907829) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#ma822fc44d4\" x=\"33.2875\" y=\"78.057763\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 500 -->\n", "      <g transform=\"translate(7.2 81.856981) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#ma822fc44d4\" x=\"33.2875\" y=\"39.006915\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 600 -->\n", "      <g transform=\"translate(7.2 42.806134) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_103\">\n", "    <path d=\"M 33.2875 273.312 \n", "L 33.2875 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_104\">\n", "    <path d=\"M 390.4075 273.312 \n", "L 390.4075 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_105\">\n", "    <path d=\"M 33.2875 273.312 \n", "L 390.4075 273.312 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_106\">\n", "    <path d=\"M 33.2875 7.2 \n", "L 390.4075 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_107\">\n", "     <path d=\"M 292.265312 44.55625 \n", "L 383.4075 44.55625 \n", "Q 385.4075 44.55625 385.4075 42.55625 \n", "L 385.4075 14.2 \n", "Q 385.4075 12.2 383.4075 12.2 \n", "L 292.265312 12.2 \n", "Q 290.265312 12.2 290.265312 14.2 \n", "L 290.265312 42.55625 \n", "Q 290.265312 44.55625 292.265312 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"patch_108\">\n", "     <path d=\"M 294.265312 23.798437 \n", "L 314.265312 23.798437 \n", "L 314.265312 16.798437 \n", "L 294.265312 16.798437 \n", "z\n", "\" style=\"fill: #1f77b4\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- background -->\n", "     <g transform=\"translate(322.265312 23.798437) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"179.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"237.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"301.123047\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"339.986328\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"401.167969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"464.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"527.925781\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_109\">\n", "     <path d=\"M 294.265312 38.476562 \n", "L 314.265312 38.476562 \n", "L 314.265312 31.476562 \n", "L 294.265312 31.476562 \n", "z\n", "\" style=\"fill: #ff7f0e\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- signal -->\n", "     <g transform=\"translate(322.265312 38.476562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"79.882812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"143.359375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"206.738281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"268.017578\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p4e048ac185\">\n", "   <rect x=\"33.2875\" y=\"7.2\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["np.random.seed(1)  # fix seed\n", "\n", "# true parameters for signal and background\n", "truth = Namespace(n_sig=2000, f_bkg=10, sig=(5.0, 0.5), bkg=(0.0, 4.0))\n", "n_bkg = truth.n_sig * truth.f_bkg\n", "\n", "# make a data set\n", "x = np.empty(truth.n_sig + n_bkg)\n", "\n", "# fill m variables\n", "x[: truth.n_sig] = norm(*truth.sig).rvs(truth.n_sig)\n", "x[truth.n_sig :] = expon(*truth.bkg).rvs(n_bkg)\n", "\n", "# cut a range in x\n", "xrange = np.array((1.0, 9.0))\n", "ma = (xrange[0] < x) & (x < xrange[1])\n", "x = x[ma]\n", "\n", "plt.hist(\n", "    (x[truth.n_sig :], x[: truth.n_sig]),\n", "    bins=50,\n", "    stacked=True,\n", "    label=(\"background\", \"signal\"),\n", ")\n", "plt.xlabel(\"x\")\n", "plt.legend();"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ideal starting values for iminuit\n", "start = np.array((truth.n_sig, n_bkg, truth.sig[0], truth.sig[1], truth.bkg[1]))\n", "\n", "\n", "# iminuit instance factory, will be called a lot in the benchmarks blow\n", "def m_init(fcn):\n", "    m = Minuit(fcn, start, name=(\"ns\", \"nb\", \"mu\", \"sigma\", \"lambd\"))\n", "    m.limits = ((0, None), (0, None), None, (0, None), (0, None))\n", "    m.errordef = Minuit.LIKELIHOOD\n", "    return m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-103168.78482586428)"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["# extended likelihood (https://doi.org/10.1016/0168-9002(90)91334-8)\n", "# this version uses numpy and scipy and array arithmetic\n", "def nll(par):\n", "    n_sig, n_bkg, mu, sigma, lambd = par\n", "    s = norm(mu, sigma)\n", "    b = expon(0, lambd)\n", "    # normalisation factors are needed for pdfs, since x range is restricted\n", "    sn = s.cdf(xrange)\n", "    bn = b.cdf(xrange)\n", "    sn = sn[1] - sn[0]\n", "    bn = bn[1] - bn[0]\n", "    return (n_sig + n_bkg) - np.sum(\n", "        np.log(s.pdf(x) / sn * n_sig + b.pdf(x) / bn * n_bkg)\n", "    )\n", "\n", "\n", "nll(start)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["327 ms ± 66.2 ms per loop (mean ± std. dev. of 3 runs, 1 loop each)\n"]}], "source": ["%%timeit -r 3 -n 1\n", "m = m_init(nll)  # setup time is negligible\n", "m.migrad();"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see whether we can beat that. The code above is already pretty fast, because numpy and scipy routines are fast, and we spend most of the time in those. But these implementations do not parallelize the execution and are not optimised for this particular CPU, unlike numba-jitted functions.\n", "\n", "To use numba, in theory we just need to put the `njit` decorator on top of the function, but often that doesn't work out of the box. numba understands many numpy functions, but no scipy. We must evaluate the code that uses scipy in 'object mode', which is numba-speak for calling into the Python interpreter."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["OMP: Info #276: omp_set_nested routine deprecated, please use omp_set_max_active_levels instead.\n"]}, {"data": {"text/plain": ["-103168.78482586429"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["# first attempt to use numba\n", "@nb.njit(parallel=True)\n", "def nll(par):\n", "    n_sig, n_bkg, mu, sigma, lambd = par\n", "    with nb.objmode(spdf=\"float64[:]\", bpdf=\"float64[:]\", sn=\"float64\", bn=\"float64\"):\n", "        s = norm(mu, sigma)\n", "        b = expon(0, lambd)\n", "        # normalisation factors are needed for pdfs, since x range is restricted\n", "        sn = np.diff(s.cdf(xrange))[0]\n", "        bn = np.diff(b.cdf(xrange))[0]\n", "        spdf = s.pdf(x)\n", "        bpdf = b.pdf(x)\n", "    no = n_sig + n_bkg\n", "    return no - np.sum(np.log(spdf / sn * n_sig + bpdf / bn * n_bkg))\n", "\n", "\n", "nll(start)  # test and warm-up JIT"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["432 ms ± 31.5 ms per loop (mean ± std. dev. of 3 runs, 1 loop each)\n"]}], "source": ["%%timeit -r 3 -n 1 m = m_init(nll)\n", "m.migrad()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It is even a bit slower. :( Let's break the original function down by parts to see why."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1.75 ms ± 24 μs per loop (mean ± std. dev. of 3 runs, 100 loops each)\n", "1.32 ms ± 92.1 μs per loop (mean ± std. dev. of 3 runs, 500 loops each)\n", "154 μs ± 9.82 μs per loop (mean ± std. dev. of 3 runs, 1,000 loops each)\n"]}], "source": ["# let's time the body of the function\n", "n_sig, n_bkg, mu, sigma, lambd = start\n", "s = norm(mu, sigma)\n", "b = expon(0, lambd)\n", "# normalisation factors are needed for pdfs, since x range is restricted\n", "sn = np.diff(s.cdf(xrange))[0]\n", "bn = np.diff(b.cdf(xrange))[0]\n", "spdf = s.pdf(x)\n", "bpdf = b.pdf(x)\n", "\n", "%timeit -r 3 -n 100 norm(*start[2:4]).pdf(x)\n", "%timeit -r 3 -n 500 expon(0, start[4]).pdf(x)\n", "%timeit -r 3 -n 1000 n_sig + n_bkg - np.sum(np.log(spdf / sn * n_sig + bpdf / bn * n_bkg))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Most of the time is spend in `norm` and `expon` which numba could not accelerate and the total time is dominated by the slowest part.\n", "\n", "This, unfortunately, means we have to do much more manual work to make the function faster, since we have to replace the scipy routines with Python code that <PERSON><PERSON> can accelerate and run in parallel."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["np.float64(-103168.78482586428)"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["# when parallel is enabled, also enable associative math\n", "kwd = {\"parallel\": True, \"fastmath\": {\"reassoc\", \"contract\", \"arcp\"}}\n", "\n", "\n", "@nb.njit(**kwd)\n", "def sum_log(fs, spdf, fb, bpdf):\n", "    return np.sum(np.log(fs * spdf + fb * bpdf))\n", "\n", "\n", "@nb.njit(**kwd)\n", "def norm_pdf(x, mu, sigma):\n", "    invs = 1.0 / sigma\n", "    z = (x - mu) * invs\n", "    invnorm = 1 / np.sqrt(2 * np.pi) * invs\n", "    return np.exp(-0.5 * z**2) * invnorm\n", "\n", "\n", "@nb.njit(**kwd)\n", "def nb_erf(x):\n", "    y = np.empty_like(x)\n", "    for i in nb.prange(len(x)):\n", "        y[i] = math.erf(x[i])\n", "    return y\n", "\n", "\n", "@nb.njit(**kwd)\n", "def norm_cdf(x, mu, sigma):\n", "    invs = 1.0 / (sigma * np.sqrt(2))\n", "    z = (x - mu) * invs\n", "    return 0.5 * (1 + nb_erf(z))\n", "\n", "\n", "@nb.njit(**kwd)\n", "def expon_pdf(x, lambd):\n", "    inv_lambd = 1.0 / lambd\n", "    return inv_lambd * np.exp(-inv_lambd * x)\n", "\n", "\n", "@nb.njit(**kwd)\n", "def expon_cdf(x, lambd):\n", "    inv_lambd = 1.0 / lambd\n", "    return 1.0 - np.exp(-inv_lambd * x)\n", "\n", "\n", "def nll(par):\n", "    n_sig, n_bkg, mu, sigma, lambd = par\n", "    # normalisation factors are needed for pdfs, since x range is restricted\n", "    sn = norm_cdf(xrange, mu, sigma)\n", "    bn = expon_cdf(xrange, lambd)\n", "    sn = sn[1] - sn[0]\n", "    bn = bn[1] - bn[0]\n", "    spdf = norm_pdf(x, mu, sigma)\n", "    bpdf = expon_pdf(x, lambd)\n", "    no = n_sig + n_bkg\n", "    return no - sum_log(n_sig / sn, spdf, n_bkg / bn, bpdf)\n", "\n", "\n", "nll(start)  # test and warm-up JIT"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's see how well these versions do:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The slowest run took 10.86 times longer than the fastest. This could mean that an intermediate result is being cached.\n", "191 μs ± 152 μs per loop (mean ± std. dev. of 5 runs, 100 loops each)\n", "45.6 μs ± 11.4 μs per loop (mean ± std. dev. of 5 runs, 500 loops each)\n", "64.2 μs ± 23.9 μs per loop (mean ± std. dev. of 5 runs, 1,000 loops each)\n"]}], "source": ["%timeit -r 5 -n 100 norm_pdf(x, *start[2:4])\n", "%timeit -r 5 -n 500 expon_pdf(x, start[4])\n", "%timeit -r 5 -n 1000 sum_log(n_sig / sn, spdf, n_bkg / bn, bpdf)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Only a minor improvement for `sum_log`, but the pdf calculation was drastically accelerated. Since this was the bottleneck before, we expect also <PERSON><PERSON> to finish faster now."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["55.5 ms ± 24.6 ms per loop (mean ± std. dev. of 3 runs, 1 loop each)\n"]}], "source": ["%%timeit -r 3 -n 1\n", "m = m_init(nll)  # setup time is negligible\n", "m.migrad();"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Success! We managed to get a big speed improvement over the initial code. This is impressive, but it cost us a lot of developer time. This is not always a good trade-off, especially if you consider that library routines are heavily tested, while you always need to test your own code in addition to writing it.\n", "\n", "By putting these faster functions into a library, however, we would only have to pay the developer cost once. You can find those in the [numba-stats](https://github.com/HDembinski/numba-stats) library."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["151 μs ± 19.2 μs per loop (mean ± std. dev. of 5 runs, 100 loops each)\n", "185 μs ± 31.9 μs per loop (mean ± std. dev. of 5 runs, 500 loops each)\n"]}], "source": ["from numba_stats import norm, expon\n", "\n", "%timeit -r 5 -n 100 norm.pdf(x, *start[2:4])\n", "%timeit -r 5 -n 500 expon.pdf(x, 0, start[4])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The implementation of the normal pdf in numba-stats is even faster than our simple implementation here.\n", "\n", "Try to compile the functions again with `parallel=False` to see how much of the speed increase came from the parallelization and how much from the generally optimized code that `numba` generated for our specific CPU. On my machine, the gain was entirely due to numba.\n", "\n", "In general, it is good advice to not automatically add `parallel=True`, because this comes with an overhead of breaking data into chunks, copy chunks to the individual CPUs and finally merging everything back together. For large arrays, this overhead is negligible, but for small arrays, it can be a net loss.\n", "\n", "So why is `numba` so fast even without parallelization? We can look at the assembly code generated."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["signature: (Array(float64, 1, 'C', False, aligned=True), float64, float64)\n", "--------------------------------------------------------------------------\n", "\t.section\t__TEXT,__text,regular,pure_instructions\n", "\t.build_version macos, 14, 0\n", "\t.section\t__TEXT,__literal8,8byte_literals\n", "\t.p2align\t3\n", "LCPI0_0:\n", "\t.quad\t0x3ff0000000000000\n", "LCPI0_1:\n", "\t.quad\t0x3fd9884533d43651\n", "\t.section\t__TEXT,__const\n", "\t.p2align\t5\n", "LCPI0_2:\n", "\t.quad\t0\n", "\t.quad\t8\n", "\t.quad\t8\n", "\t.quad\t8\n", "\t.section\t__TEXT,__text,regular,pure_instructions\n", "\t.globl\t__ZN8__main__8norm_pdfB3v22B150c8tJTC_2fWQAliW1xhDEoY6EEMEUOEMISPGsAQMVj4QniQ4IXKQEMXwoMGLoQDDVsQR1NHAS2hQ9XgStYw86ABbYse0tXqiUXJBeo6CurJ_2bXklRYnJJSB2ETCRF_2bcnq9cC7QNGJsRqAA_3d_3dE5ArrayIdLi1E1C7mutable7alignedEdd\n", "\t.p2align\t4, 0x90\n", "__ZN8__main__8norm_pdfB3v22B150c8tJTC_2fWQAliW1xhDEoY6EEMEUOEMISPGsAQMVj4QniQ4IXKQEMXwoMGLoQDDVsQR1NHAS2hQ9XgStYw86ABbYse0tXqiUXJBeo6CurJ_2bXklRYnJJSB2ETCRF_2bcnq9cC7QNGJsRqAA_3d_3dE5ArrayIdLi1E1C7mutable7alignedEdd:\n", "\t.cfi_startproc\n", "\tpushq\t%rbp\n", "\t.cfi_def_cfa_offset 16\n", "\tpushq\t%r15\n", "\t.cfi_def_cfa_offset 24\n", "\tpushq\t%r14\n", "\t.cfi_def_cfa_offset 32\n", "\tpushq\t%r13\n", "\t.cfi_def_cfa_offset 40\n", "\tpushq\t%r12\n", "\t.cfi_def_cfa_offset 48\n", "\tpushq\t%r\n", "[...]\n"]}], "source": ["for signature, code in norm_pdf.inspect_asm().items():\n", "    print(\n", "        f\"signature: {signature}\\n{'-' * (len(str(signature)) + 11)}\\n{code[:1000]}\\n[...]\"\n", "    )"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This code section is very long, but the assembly grammar is very simple. Constants start with `.` and `SOMETHING:` is a jump label for the assembly equivalent of `goto`. Everything else is an instruction with its name on the left and the arguments are on the right.\n", "\n", "The SIMD instructions are the interesting commands that operate on multiple values at once. This is where the speed comes from. \n", "- If you are on the **x86** platform, those instructions end with `pd` and `ps`.\n", "- On **arch64**, they contain a dot `.` and some letters/numbers afterwards."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["signature: (Array(float64, 1, 'C', False, aligned=True), float64, float64)\n", "--------------------------------------------------------------------------\n", "Instructions\n", "addq      :    26\n", "imulq     :    10\n", "movabsq   :   105\n", "movl      :    23\n", "movq      :   305\n", "movslq    :     2\n", "subq      :     8\n", "vmovapd   :    17\n", "vmovaps   :     7\n", "vmovsd    :    28\n", "vmovupd   :    34\n", "vmovups   :     8\n", "vmulpd    :    19\n", "vmulsd    :     5\n", "vsubpd    :    10\n", "vsubsd    :     1\n"]}], "source": ["from collections import Counter\n", "\n", "for signature, code in norm_pdf.inspect_asm().items():\n", "    print(f\"signature: {signature}\\n{'-' * (len(str(signature)) + 11)}\")\n", "    instructions = []\n", "    for line in code.split(\"\\n\"):\n", "        instr = line.strip().split(\"\\t\")[0]\n", "        if instr.startswith(\".\"):\n", "            continue\n", "        for match in (\"add\", \"sub\", \"mul\", \"mov\"):\n", "            if match in instr:\n", "                instructions.append(instr)\n", "    c = Counter(instructions)\n", "    print(\"Instructions\")\n", "    for k in sorted(c):\n", "        print(f\"{k:10}: {c[k]:5}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["- `add`: subtract numbers\n", "- `sub`: subtract numbers\n", "- `mul`: multiply numbers\n", "- `mov`: copy values from memory to CPU registers and back\n", "\n", "You can google all the other commands.\n", "\n", "There is a lot of repetition in the assembly code, because the optimizer unrolls loops over subsequences to make them faster. Using an unrolled loop only works if the remaining chunk of data is large enough. Since the compiler does not know the length of the incoming array, it generates sections which handle shorter chunks and all the code to select which section to use. Finally, there is some code which does the translation from and to Python objects with corresponding error handling.\n", "\n", "We don't need to write SIMD instructions by hand, the optimizer does it for us and in a very sophisticated way."]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "Python 3.8.13 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 4}