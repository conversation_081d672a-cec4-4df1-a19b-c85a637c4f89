{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Fitting a mixture of templates and parametric models\n", "\n", "The class `iminuit.cost.Template` supports fitting a mixture of templates and parametric models. This is useful if some components have a simple shape like a Gaussian peak, while other components are complicated and need to be estimated from simulation or a control measurement.\n", "\n", "In this notebook, we demonstrate this usage. Our data consists of a Gaussian peak and exponential background. We fit the Gaussian peak with a parametric model and use a template to describe the exponential background."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "from iminuit import Minuit\n", "from iminuit.cost import Template, ExtendedBinnedNLL\n", "from numba_stats import norm, truncexpon\n", "import numpy as np\n", "from matplotlib import pyplot as plt"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We generate the toy data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["rng = np.random.default_rng(1)\n", "\n", "s = rng.normal(0.5, 0.05, size=1000)\n", "b = rng.exponential(1, size=1000)\n", "b = b[b < 1]\n", "\n", "ns, xe = np.histogram(s, bins=100, range=(0, 1))\n", "nb, _ = np.histogram(b, bins=xe)\n", "n = ns + nb\n", "\n", "plt.stairs(nb, xe, color=\"C1\", fill=True, label=\"background\")\n", "plt.stairs(n, xe, baseline=nb, color=\"C0\", fill=True, label=\"signal\")\n", "plt.legend();"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Fitting a parametric component and a template\n", "\n", "We now model the peaking component parametrically with a Gaussian. A template fit is an extended binned fit, so we need to provide a scaled cumulative density function like for `iminuit.cost.ExtendedBinnedNLL`. To obtain a background template, we generate more samples from the exponential distribution and make a histogram."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# signal model: scaled cdf of a normal distribution\n", "def signal(xe, n, mu, sigma):\n", "    return n * norm.cdf(xe, mu, sigma)\n", "\n", "\n", "# background template: histogram of MC simulation\n", "rng = np.random.default_rng(2)\n", "b2 = rng.exponential(1, size=1000)\n", "b2 = b2[b2 < 1]\n", "template = np.histogram(b2, bins=xe)[0]\n", "\n", "# fit\n", "c = Template(n, xe, (signal, template))\n", "m = Minuit(c, x0_n=500, x0_mu=0.5, x0_sigma=0.05, x1=100)\n", "m.limits[\"x0_n\", \"x1\", \"x0_sigma\"] = (0, None)\n", "m.limits[\"x0_mu\"] = (0, 1)\n", "m.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The fit succeeded and the statistical uncertainty in the template is propagated into the result. You can play with this demo and see what happens if you increase the statistic of the template.\n", "\n", "Note: the parameters of a parametric components are prefixed with `xn_` where `n` is the index of the component. This is to avoid name clashes between the parameter names of individual components and for clarity which parameter belongs to which component."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Extreme case: Fitting two parametric components\n", "\n", "Although this is not recommended, you can also use the `Template` class with two parametric components and no template. If you are in that situation, however, it is simpler and more efficient to use `iminuit.cost.ExtendedBinnedNLL`. The following snipped is therefore just a proof that `iminuit.cost.Template` handles this limiting case as well."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# signal model: scaled cdf of a normal distribution\n", "def signal(xe, n, mu, sigma):\n", "    return n * norm.cdf(xe, mu, sigma)\n", "\n", "\n", "# background model: scaled cdf a an exponential distribution\n", "def background(xe, n, mu):\n", "    return n * truncexpon.cdf(xe, xe[0], xe[-1], 0, mu)\n", "\n", "\n", "# fit\n", "c = Template(n, xe, (signal, background))\n", "m = Minuit(c, x0_n=500, x0_mu=0.5, x0_sigma=0.05, x1_n=100, x1_mu=1)\n", "m.limits[\"x0_n\", \"x1_n\", \"x0_sigma\", \"x1_mu\"] = (0, None)\n", "m.limits[\"x0_mu\"] = (0, 1)\n", "m.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The result is identical when we fit with `iminuit.cost.ExtendedBinnedNLL`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def total(xe, x0_n, x0_mu, x0_sigma, x1_n, x1_mu):\n", "    return signal(xe, x0_n, x0_mu, x0_sigma) + background(xe, x1_n, x1_mu)\n", "\n", "\n", "c = ExtendedBinnedNLL(n, xe, total)\n", "m = Minuit(c, x0_n=500, x0_mu=0.5, x0_sigma=0.05, x1_n=100, x1_mu=1)\n", "m.limits[\"x0_n\", \"x1_n\", \"x0_sigma\", \"x1_mu\"] = (0, None)\n", "m.limits[\"x0_mu\"] = (0, 1)\n", "m.migrad()"]}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 2}