{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fitting data with correlated uncertainties\n", "\n", "We sometimes want to combine results from different studies. If these results have independent uncertainties and can be expected to have the same mean, then the optimal combination (minimum variance) is given by a weighted mean, where the weight is inversely proportional to the variance (uncertainty squared) of each individual input value. [This is a well-known result](https://en.wikipedia.org/wiki/Inverse-variance_weighting).\n", "\n", "If the uncertainties of the results are correlated, it is more complicated to compute an optimally weighted mean. Instead of deriving analytical formulas, we use a fit here to obtain the mixing weight, which is equivalent. It serves to demonstrate how fits to correlated data values can be carried out. A whole project built on this approach is [GammaCombo](https://gammacombo.github.io/).\n", "\n", "We consider a toy example where two measurements should be combined which have strongly correlated systematic uncertainties."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "from iminuit import Minuit\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here are the results we want to comine. The statistical uncertainties are assumed to be uncorrelated, the systematic uncertainties are assumed to be perfectly correlated (represented by thick bars in the plot)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["value = [1.2, 1.5]\n", "error_sta = [0.3, 0.3]\n", "error_sys = [0.1, 0.2]\n", "correlation_sys = 1.0\n", "\n", "plt.errorbar((\"result 1\", \"result 2\"), value, error_sta, fmt=\"o\")\n", "plt.errorbar((\"result 1\", \"result 2\"), value, error_sys, lw=3, fmt=\"none\")\n", "plt.xlim(-0.5, 1.5);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We formulate the combination as a fitting problem. We assume that both results originate from a common true value, which we can estimate in the usual way. Our objective function is derived from the log-probability of a multivariate normal distribution (in the derivation we dropped constants and scaled the result). We predict the constant mean of this distribution, which is matched to the two observed values while taking their covariance into account. The covariance matrix of the multivariate normal distribution captures the correlated uncertainties of the individual results.\n", "\n", "The combined result obtained in this way is unbiased (assuming all inputs are unbiased) and also happens to minimize the variance, so this approach is optimal (unbiased and has minimum variance) even if the input results are not distributed like a multivariate normal.\n", "\n", "The simpler special case for uncorrelated observations is handled in `iminuit.cost.LeastSquares`, but for the general case there is no ready-made cost function yet, so we write it here."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# construct covariance matrices\n", "cov_sta = np.diag(np.square(error_sta))\n", "cov_sys = np.diag(np.square(error_sys))\n", "cov_sys[0, 1] = error_sys[0] * error_sys[1] * correlation_sys\n", "cov_sys[1, 0] = cov_sys[0, 1]\n", "\n", "# total covariance is sum of individual contributions\n", "cov = cov_sta + cov_sys\n", "inv_cov = np.linalg.inv(cov)\n", "\n", "\n", "def model(x, z):\n", "    \"\"\"Return combined value based on input x and mixing parameter z.\"\"\"\n", "    return z * x[1] + (1 - z) * x[0]\n", "\n", "\n", "def cost(z):\n", "    \"\"\"Chi-square distributed cost function.\"\"\"\n", "    xp = model(value, z)\n", "    delta = value - xp\n", "    return np.einsum(\"i,j,ij\", delta, delta, inv_cov)\n", "\n", "\n", "# with this extra information, imin<PERSON> will also print the chi2/ndof gof statistic\n", "cost.errordef = Minuit.LEAST_SQUARES\n", "cost.ndata = len(value)\n", "\n", "m = Minuit(cost, 0.5)\n", "m.limits[\"z\"] = (0, 1)\n", "m.migrad()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Our fit gives more weight to the more accurate measurement, as expected. In order to find the statistical and systematic uncertainty of the combined result, we do error propagation. We compute the trivial Jacobian for our model analytically."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["z = m.values[0]\n", "jac = np.array([z, (1 - z)])\n", "total = model(value, z)\n", "total_err_sta = np.einsum(\"i,j,ij\", jac, jac, cov_sta) ** 0.5\n", "total_err_sys = np.einsum(\"i,j,ij\", jac, jac, cov_sys) ** 0.5\n", "\n", "print(f\"total = {total:.2f} +/- {total_err_sta:.2f}(sta) + {total_err_sys:.2f}(sys)\")\n", "\n", "plt.errorbar(\n", "    (\"result 1\", \"result 2\", \"combined\"),\n", "    value + [total],\n", "    error_sta + [total_err_sta],\n", "    fmt=\"o\",\n", ")\n", "plt.errorbar(\n", "    (\"result 1\", \"result 2\", \"combined\"),\n", "    value + [total],\n", "    error_sys + [total_err_sys],\n", "    lw=3,\n", "    fmt=\"none\",\n", ")\n", "plt.xlim(-1, 3);"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Note how the systematic uncertainty gets barely reduced by the combination, a consequence of the strong correlation. Try running this example with zero correlation to see how the uncertainty becomes smaller.\n", "\n", "## Further reading\n", "\n", "[<PERSON><PERSON><PERSON> (2000)](https://arxiv.org/abs/hep-ex/0006004) argues that a combination may ignore data correlations if they are not well-known, because a result of a combination that ignores correlations is still unbiased, it merely does not have minimum possible variance. Depending on the situation, this may be preferable over the result shown here which takes the correlation into account, since one then has to propagate the uncertainty of correlation into the combined result.\n", "\n", "[<PERSON><PERSON> (2018)](https://inspirehep.net/literature/1694207) demonstrates that it is techniqually feasible and beneficial to include uncertainties on uncertainties explicitly in the likelihood when combining measurements.\n", "\n", "[<PERSON> (2022)](https://inspirehep.net/literature/1509024) discusses two commonly used techniques to combine measurements with correlated systematic uncertainties, and shows under which conditions they are equivalent."]}], "metadata": {"kernelspec": {"display_name": "py311", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}