{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# 101: Basics\n", "\n", "This example corresponds to [RF101](https://root.cern.ch/doc/master/rf101__basics_8py.html)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from iminuit import Minuit\n", "from iminuit.cost import UnbinnedNLL\n", "from numba_stats import truncnorm\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "\n", "xrange = (-10, 10)\n", "\n", "\n", "def model(x, mu, sigma):\n", "    return truncnorm.pdf(x, *xrange, mu, sigma)\n", "\n", "\n", "rng = np.random.default_rng(1)\n", "x = rng.normal(1, 3, size=10000)\n", "x = x[(x > xrange[0]) & (x < xrange[1])]\n", "\n", "c = UnbinnedNLL(x, model)\n", "m = Minuit(c, 1, 3)\n", "m.limits[\"mu\"] = (-10, 10)\n", "m.limits[\"sigma\"] = (0.1, 10)\n", "m.migrad()\n", "\n", "fig, ax = plt.subplots(1, 2, figsize=(8, 3.5), sharex=True, constrained_layout=True)\n", "\n", "plt.sca(ax[0])\n", "plt.title(\"Gaussian pdf\")\n", "xm = np.linspace(*xrange, 1000)\n", "plt.plot(xm, model(xm, 1, 1), label=\"sigma=1\")\n", "plt.plot(xm, model(xm, 1, 3), label=\"sigma=3\")\n", "plt.legend()\n", "plt.xlim(*xrange)\n", "\n", "plt.sca(ax[1])\n", "plt.title(\"Gaussian pdf with data\")\n", "m.visualize(bins=100)\n", "plt.xlim(*xrange);"]}], "metadata": {"kernelspec": {"display_name": "py39", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 2}