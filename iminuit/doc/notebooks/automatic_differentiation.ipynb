{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Automatic differentiation with JAX\n", "\n", "Here we look into automatic differentiation, which can speed up fits with very many parameters.\n", "\n", "iminuit's minimization algorithm MIGRAD uses a mix of gradient descent and <PERSON>'s method to find the minimum. Both require a first derivative, which MIGRAD usually computes numerically from finite differences. This requires many function evaluations and the gradient may not be accurate. As an alternative, iminuit also allows the user to compute the gradient and pass it to MIGRAD.\n", "\n", "Although computing derivatives is often straight-forward, it is usually too much hassle to do manually. Automatic differentiation (AD) is an interesting alternative, it allows one to compute exact derivatives efficiently for pure Python/numpy functions. We demonstrate automatic differentiation with the JAX module, which can not only compute derivatives, but also accelerates the computation of Python code (including the gradient code) with a just-in-time compiler.\n", "\n", "[Recommended read: Gentle introduction to AD](https://www.kaggle.com/borisettinger/gentle-introduction-to-automatic-differentiation)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Fit of a gaussian model to a histogram\n", "\n", "We fit a gaussian to a histogram using a maximum-likelihood approach based on Poisson statistics. This example is used to investigate how automatic differentiation can accelerate a typical fit in a counting experiment.\n", "\n", "To compare fits with and without passing an analytic gradient fairly, we use `Minuit.strategy = 0`, which prevents <PERSON><PERSON> from automatically computing the Hesse matrix after the fit."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["JAX version 0.4.31\n", "numba version 0.60.0\n"]}], "source": ["# !pip install jax jaxlib matplotlib numpy iminuit numba-stats\n", "%config InlineBackend.figure_formats = ['svg']\n", "import jax\n", "from jax import numpy as jnp  # replacement for normal numpy\n", "from jax.scipy.special import erf  # replacement for scipy.special.erf\n", "from iminuit import Minuit\n", "import numba as nb\n", "import numpy as np  # original numpy still needed, since jax does not cover full API\n", "\n", "jax.config.update(\n", "    \"jax_enable_x64\", True\n", ")  # enable float64 precision, default is float32\n", "\n", "print(f\"JAX version {jax.__version__}\")\n", "print(f\"numba version {nb.__version__}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We generate some toy data and write the negative log-likelihood (nll) for a fit to binned data, assuming Poisson-distributed counts.\n", "\n", "**Note:** We write all statistical functions in pure Python code, to demonstrate <PERSON>'s ability to automatically differentiate and JIT compile this code. In practice, one should import JIT-able statistical distributions from jax.scipy.stats. The library versions can be expected to have fewer bugs and to be faster and more accurate than hand-written code."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# generate some toy data\n", "rng = np.random.default_rng(seed=1)\n", "n, xe = np.histogram(rng.normal(size=10000), bins=1000)\n", "\n", "\n", "def cdf(x, mu, sigma):\n", "    # cdf of a normal distribution, needed to compute the expected counts per bin\n", "    # better alternative for real code: from jax.scipy.stats.norm import cdf\n", "    z = (x - mu) / sigma\n", "    return 0.5 * (1 + erf(z / np.sqrt(2)))\n", "\n", "\n", "def nll(par):  # negative log-likelihood with constants stripped\n", "    amp = par[0]\n", "    mu, sigma = par[1:]\n", "    p = cdf(xe, mu, sigma)\n", "    mu = amp * jnp.diff(p)\n", "    result = jnp.sum(mu - n + n * jnp.log(n / (mu + 1e-100) + 1e-100))\n", "    return result"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Let's check results from all combinations of using JIT and gradient and then compare the execution times."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["start_values = (1.5 * np.sum(n), 1.0, 2.0)\n", "limits = ((0, None), (None, None), (0, None))\n", "\n", "\n", "def make_and_run_minuit(fcn, grad=None):\n", "    m = Minuit(fcn, start_values, grad=grad, name=(\"amp\", \"mu\", \"sigma\"))\n", "    m.errordef = Minuit.LIKELIHOOD\n", "    m.limits = limits\n", "    m.strategy = 0  # do not explicitly compute hessian after minimisation\n", "    m.migrad()\n", "    return m"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 496.2 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 66 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 1.84e-08 (Goal: 0.0001) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\"> time = 0.4 sec </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Covariance APPROXIMATE </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 496.2                      │              Nfcn = 66               │\n", "│ EDM = 1.84e-08 (Goal: 0.0001)    │            time = 0.4 sec            │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │        Covariance APPROXIMATE        │\n", "└──────────────────────────────────┴──────────────────────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["m1 = make_and_run_minuit(nll)\n", "m1.fmin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 496.2 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 26, Ngrad = 6 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 1.84e-08 (Goal: 0.0001) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\"> time = 1.2 sec </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Covariance APPROXIMATE </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 496.2                      │         Nfcn = 26, <PERSON>rad = 6         │\n", "│ EDM = 1.84e-08 (Goal: 0.0001)    │            time = 1.2 sec            │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │        Covariance APPROXIMATE        │\n", "└──────────────────────────────────┴──────────────────────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["m2 = make_and_run_minuit(nll, grad=jax.grad(nll))\n", "m2.fmin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 496.2 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 66 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 1.84e-08 (Goal: 0.0001) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\"> time = 0.1 sec </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Covariance APPROXIMATE </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 496.2                      │              Nfcn = 66               │\n", "│ EDM = 1.84e-08 (Goal: 0.0001)    │            time = 0.1 sec            │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │        Covariance APPROXIMATE        │\n", "└──────────────────────────────────┴──────────────────────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["m3 = make_and_run_minuit(jax.jit(nll))\n", "m3.fmin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 496.2 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 26, Ngrad = 6 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 1.84e-08 (Goal: 0.0001) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\"> time = 0.3 sec </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Covariance APPROXIMATE </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 496.2                      │         Nfcn = 26, <PERSON>rad = 6         │\n", "│ EDM = 1.84e-08 (Goal: 0.0001)    │            time = 0.3 sec            │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │        Covariance APPROXIMATE        │\n", "└──────────────────────────────────┴──────────────────────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["m4 = make_and_run_minuit(jax.jit(nll), grad=jax.jit(jax.grad(nll)))\n", "m4.fmin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 496.2 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 82 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 5.31e-05 (Goal: 0.0001) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\"> time = 2.0 sec </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> Covariance APPROXIMATE </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 496.2                      │              Nfcn = 82               │\n", "│ EDM = 5.31e-05 (Goal: 0.0001)    │            time = 2.0 sec            │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │        Covariance APPROXIMATE        │\n", "└──────────────────────────────────┴──────────────────────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["from numba_stats import norm  # numba jit-able version of norm\n", "\n", "\n", "@nb.njit\n", "def nb_nll(par):\n", "    amp = par[0]\n", "    mu, sigma = par[1:]\n", "    p = norm.cdf(xe, mu, sigma)\n", "    mu = amp * np.diff(p)\n", "    result = np.sum(mu - n + n * np.log(n / (mu + 1e-323) + 1e-323))\n", "    return result\n", "\n", "\n", "m5 = make_and_run_minuit(nb_nll)\n", "m5.fmin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from timeit import timeit\n", "\n", "times = {\n", "    \"no JIT, no grad\": \"m1\",\n", "    \"no JIT, grad\": \"m2\",\n", "    \"jax J<PERSON>, no grad\": \"m3\",\n", "    \"jax JIT, grad\": \"m4\",\n", "    \"numba JIT, no grad\": \"m5\",\n", "}\n", "for k, v in times.items():\n", "    t = timeit(\n", "        f\"{v}.values = start_values; {v}.migrad()\",\n", "        f\"from __main__ import {v}, start_values\",\n", "        number=1,\n", "    )\n", "    times[k] = t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"484.703348pt\" height=\"310.86825pt\" viewBox=\"0 0 484.**********.86825\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T10:48:38.246537</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 310.86825 \n", "L 484.**********.86825 \n", "L 484.703348 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 109.**********.312 \n", "L 466.**********.312 \n", "L 466.324687 7.2 \n", "L 109.204688 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 109.204688 59.616 \n", "L 230.214758 59.616 \n", "L 230.214758 19.296 \n", "L 109.204688 19.296 \n", "z\n", "\" clip-path=\"url(#p4a91d24278)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 109.204688 110.016 \n", "L 449.318973 110.016 \n", "L 449.318973 69.696 \n", "L 109.204688 69.696 \n", "z\n", "\" clip-path=\"url(#p4a91d24278)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 109.204688 160.416 \n", "L 136.40536 160.416 \n", "L 136.40536 120.096 \n", "L 109.204688 120.096 \n", "z\n", "\" clip-path=\"url(#p4a91d24278)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 109.204688 210.816 \n", "L 120.639993 210.816 \n", "L 120.639993 170.496 \n", "L 109.204688 170.496 \n", "z\n", "\" clip-path=\"url(#p4a91d24278)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 109.204688 261.216 \n", "L 116.450181 261.216 \n", "L 116.450181 220.896 \n", "L 109.204688 220.896 \n", "z\n", "\" clip-path=\"url(#p4a91d24278)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m19a15208b2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"109.204688\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(98.071875 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"148.890732\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.02 -->\n", "      <g transform=\"translate(137.75792 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"188.576777\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.04 -->\n", "      <g transform=\"translate(177.443964 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"228.262821\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.06 -->\n", "      <g transform=\"translate(217.130009 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"267.948866\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.08 -->\n", "      <g transform=\"translate(256.816053 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"307.634911\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(296.502098 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"347.320955\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.12 -->\n", "      <g transform=\"translate(336.188143 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"387.007\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.14 -->\n", "      <g transform=\"translate(375.874187 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m19a15208b2\" x=\"426.693044\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.16 -->\n", "      <g transform=\"translate(415.560232 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_10\">\n", "     <!-- execution time / s -->\n", "     <g transform=\"translate(243.048281 301.588562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6d\" d=\"M 3328 2828 \n", "Q 3544 3216 3844 3400 \n", "Q 4144 3584 4550 3584 \n", "Q 5097 3584 5394 3201 \n", "Q 5691 2819 5691 2113 \n", "L 5691 0 \n", "L 5113 0 \n", "L 5113 2094 \n", "Q 5113 2597 4934 2840 \n", "Q 4756 3084 4391 3084 \n", "Q 3944 3084 3684 2787 \n", "Q 3425 2491 3425 1978 \n", "L 3425 0 \n", "L 2847 0 \n", "L 2847 2094 \n", "Q 2847 2600 2669 2842 \n", "Q 2491 3084 2119 3084 \n", "Q 1678 3084 1418 2786 \n", "Q 1159 2488 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1356 3278 1631 3431 \n", "Q 1906 3584 2284 3584 \n", "Q 2666 3584 2933 3390 \n", "Q 3200 3197 3328 2828 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2f\" d=\"M 1625 4666 \n", "L 2156 4666 \n", "L 531 -594 \n", "L 0 -594 \n", "L 1625 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-65\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"59.773438\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"115.828125\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"177.351562\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"232.332031\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"295.710938\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"334.919922\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"362.703125\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"423.884766\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"487.263672\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"519.050781\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"558.259766\"/>\n", "      <use xlink:href=\"#DejaVuSans-6d\" x=\"586.042969\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"683.455078\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"744.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-2f\" x=\"776.765625\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"810.457031\"/>\n", "      <use xlink:href=\"#DejaVuSans-73\" x=\"842.244141\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m8657b63436\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8657b63436\" x=\"109.204688\" y=\"39.456\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- no JIT, no grad -->\n", "      <g transform=\"translate(29.635937 43.255219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-4a\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 325 \n", "Q 1259 -519 939 -900 \n", "Q 619 -1281 -91 -1281 \n", "L -331 -1281 \n", "L -331 -750 \n", "L -134 -750 \n", "Q 284 -750 456 -515 \n", "Q 628 -281 628 325 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2c\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 256 \n", "L 897 -744 \n", "L 494 -744 \n", "L 750 256 \n", "L 750 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-6e\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"63.378906\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"124.560547\"/>\n", "       <use xlink:href=\"#DejaVuSans-4a\" x=\"156.347656\"/>\n", "       <use xlink:href=\"#DejaVuSans-49\" x=\"185.839844\"/>\n", "       <use xlink:href=\"#DejaVuSans-54\" x=\"215.332031\"/>\n", "       <use xlink:href=\"#DejaVuSans-2c\" x=\"276.416016\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"308.203125\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"339.990234\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"403.369141\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"464.550781\"/>\n", "       <use xlink:href=\"#DejaVuSans-67\" x=\"496.337891\"/>\n", "       <use xlink:href=\"#DejaVuSans-72\" x=\"559.814453\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"600.927734\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"662.207031\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m8657b63436\" x=\"109.204688\" y=\"89.856\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- no JIT, grad -->\n", "      <g transform=\"translate(45.270313 93.655219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-6e\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"63.378906\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"124.560547\"/>\n", "       <use xlink:href=\"#DejaVuSans-4a\" x=\"156.347656\"/>\n", "       <use xlink:href=\"#DejaVuSans-49\" x=\"185.839844\"/>\n", "       <use xlink:href=\"#DejaVuSans-54\" x=\"215.332031\"/>\n", "       <use xlink:href=\"#DejaVuSans-2c\" x=\"276.416016\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"308.203125\"/>\n", "       <use xlink:href=\"#DejaVuSans-67\" x=\"339.990234\"/>\n", "       <use xlink:href=\"#DejaVuSans-72\" x=\"403.466797\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"444.580078\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"505.859375\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m8657b63436\" x=\"109.204688\" y=\"140.256\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- jax <PERSON>, no grad -->\n", "      <g transform=\"translate(27.267188 144.055219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-6a\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 -63 \n", "Q 1178 -731 923 -1031 \n", "Q 669 -1331 103 -1331 \n", "L -116 -1331 \n", "L -116 -844 \n", "L 38 -844 \n", "Q 366 -844 484 -692 \n", "Q 603 -541 603 -63 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-6a\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"27.783203\"/>\n", "       <use xlink:href=\"#DejaVuSans-78\" x=\"89.0625\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"148.242188\"/>\n", "       <use xlink:href=\"#DejaVuSans-4a\" x=\"180.029297\"/>\n", "       <use xlink:href=\"#DejaVuSans-49\" x=\"209.521484\"/>\n", "       <use xlink:href=\"#DejaVuSans-54\" x=\"239.013672\"/>\n", "       <use xlink:href=\"#DejaVuSans-2c\" x=\"300.097656\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"331.884766\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"363.671875\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"427.050781\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"488.232422\"/>\n", "       <use xlink:href=\"#DejaVuSans-67\" x=\"520.019531\"/>\n", "       <use xlink:href=\"#DejaVuSans-72\" x=\"583.496094\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"624.609375\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"685.888672\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m8657b63436\" x=\"109.204688\" y=\"190.656\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- jax <PERSON>, grad -->\n", "      <g transform=\"translate(42.901563 194.455219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-6a\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"27.783203\"/>\n", "       <use xlink:href=\"#DejaVuSans-78\" x=\"89.0625\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"148.242188\"/>\n", "       <use xlink:href=\"#DejaVuSans-4a\" x=\"180.029297\"/>\n", "       <use xlink:href=\"#DejaVuSans-49\" x=\"209.521484\"/>\n", "       <use xlink:href=\"#DejaVuSans-54\" x=\"239.013672\"/>\n", "       <use xlink:href=\"#DejaVuSans-2c\" x=\"300.097656\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"331.884766\"/>\n", "       <use xlink:href=\"#DejaVuSans-67\" x=\"363.671875\"/>\n", "       <use xlink:href=\"#DejaVuSans-72\" x=\"427.148438\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"468.261719\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"529.541016\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m8657b63436\" x=\"109.204688\" y=\"241.056\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- numba <PERSON>, no grad -->\n", "      <g transform=\"translate(7.2 244.855219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-6e\"/>\n", "       <use xlink:href=\"#DejaVuSans-75\" x=\"63.378906\"/>\n", "       <use xlink:href=\"#DejaVuSans-6d\" x=\"126.757812\"/>\n", "       <use xlink:href=\"#DejaVuSans-62\" x=\"224.169922\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"287.646484\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"348.925781\"/>\n", "       <use xlink:href=\"#DejaVuSans-4a\" x=\"380.712891\"/>\n", "       <use xlink:href=\"#DejaVuSans-49\" x=\"410.205078\"/>\n", "       <use xlink:href=\"#DejaVuSans-54\" x=\"439.697266\"/>\n", "       <use xlink:href=\"#DejaVuSans-2c\" x=\"500.78125\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"532.568359\"/>\n", "       <use xlink:href=\"#DejaVuSans-6e\" x=\"564.355469\"/>\n", "       <use xlink:href=\"#DejaVuSans-6f\" x=\"627.734375\"/>\n", "       <use xlink:href=\"#DejaVuSans-20\" x=\"688.916016\"/>\n", "       <use xlink:href=\"#DejaVuSans-67\" x=\"720.703125\"/>\n", "       <use xlink:href=\"#DejaVuSans-72\" x=\"784.179688\"/>\n", "       <use xlink:href=\"#DejaVuSans-61\" x=\"825.292969\"/>\n", "       <use xlink:href=\"#DejaVuSans-64\" x=\"886.572266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 109.**********.312 \n", "L 109.204688 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 109.**********.312 \n", "L 466.**********.312 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <!-- 16.7x -->\n", "    <g transform=\"translate(230.214758 39.456) scale(0.1 -0.1)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "     <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" x=\"159.033203\"/>\n", "     <use xlink:href=\"#DejaVuSans-78\" x=\"222.65625\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_17\">\n", "    <!-- 46.9x -->\n", "    <g transform=\"translate(449.318973 89.856) scale(0.1 -0.1)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-34\"/>\n", "     <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "     <use xlink:href=\"#DejaVuSans-39\" x=\"159.033203\"/>\n", "     <use xlink:href=\"#DejaVuSans-78\" x=\"222.65625\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_18\">\n", "    <!-- 3.8x -->\n", "    <g transform=\"translate(136.40536 140.256) scale(0.1 -0.1)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-33\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-78\" x=\"159.033203\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_19\">\n", "    <!-- 1.6x -->\n", "    <g transform=\"translate(120.639993 190.656) scale(0.1 -0.1)\">\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-78\" x=\"159.033203\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"text_20\">\n", "    <!-- 1.0x -->\n", "    <g transform=\"translate(116.450181 241.056) scale(0.1 -0.1)\">\n", "     <use xlink:href=\"#DejaVuSans-31\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "     <use xlink:href=\"#DejaVuSans-78\" x=\"159.033203\"/>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p4a91d24278\">\n", "   <rect x=\"109.204688\" y=\"7.2\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from matplotlib import pyplot as plt\n", "\n", "x = np.fromiter(times.values(), dtype=float)\n", "xmin = np.min(x)\n", "\n", "y = -np.arange(len(times))\n", "plt.barh(y, x)\n", "for yi, k, v in zip(y, times, x):\n", "    plt.text(v, yi, f\"{v / xmin:.1f}x\")\n", "plt.yticks(y, times.keys())\n", "for loc in (\"top\", \"right\"):\n", "    plt.gca().spines[loc].set_visible(False)\n", "plt.xlabel(\"execution time / s\");"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Conclusions:\n", "\n", "1. As expected, the best results with JAX are obtained by JIT compiling function and gradient and using the gradient in the minimization. However, the performance of the Numba JIT compiled function is comparable even without computing the gradient.\n", "\n", "1. JIT compiling the cost function with JAX but not using the gradient also gives good performance, but worse than using Numba for the same.\n", "\n", "3. Combining the JAX JIT with the JAX gradient calculation is very important. Using only the Python-computed gradient even reduces performance in this example.\n", "\n", "In general, the gain from using a gradient is larger for functions with hundreds of parameters, as is common in machine learning. Human-made models often have less than 10 parameters, and then the gain is not so dramatic."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Computing covariance matrices with JAX\n", "\n", "Automatic differentiation gives us another way to compute uncertainties of fitted parameters. MINUIT compute the uncertainties with the HESSE algorithm by default, which computes the matrix of second derivates approximately using finite differences and inverts this.\n", "\n", "Let's compare the output of HESSE with the exact (within floating point precision) computation using automatic differentiation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["sigma[amp]  : HESSE =  100.0, JAX =  100.0\n", "sigma[mu]   : HESSE = 0.0100, JAX = 0.0100\n", "sigma[sigma]: HESSE = 0.0071, JAX = 0.0071\n"]}], "source": ["m4.hesse()\n", "cov_hesse = m4.covariance\n", "\n", "\n", "def jax_covariance(par):\n", "    return jnp.linalg.inv(jax.he<PERSON>an(nll)(par))\n", "\n", "\n", "par = np.array(m4.values)\n", "cov_jax = jax_covariance(par)\n", "\n", "print(\n", "    f\"sigma[amp]  : HESSE = {cov_hesse[0, 0] ** 0.5:6.1f}, JAX = {cov_jax[0, 0] ** 0.5:6.1f}\"\n", ")\n", "print(\n", "    f\"sigma[mu]   : HESSE = {cov_hesse[1, 1] ** 0.5:6.4f}, JAX = {cov_jax[1, 1] ** 0.5:6.4f}\"\n", ")\n", "print(\n", "    f\"sigma[sigma]: HESSE = {cov_hesse[2, 2] ** 0.5:6.4f}, JAX = {cov_jax[2, 2] ** 0.5:6.4f}\"\n", ")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Success, HESSE and JAX give the same answer within the  relevant precision.\n", "\n", "**Note:** If you compute the covariance matrix in this way from a least-squares cost function instead of a negative log-likelihood, you must multiply it by 2.\n", "\n", "Let us compare the performance of HESSE with Jax."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["22.9 ms ± 2.51 ms per loop (mean ± std. dev. of 3 runs, 1 loop each)\n"]}], "source": ["%%timeit -n 1 -r 3\n", "m = Minuit(nll, par)\n", "m.errordef = Minuit.LIKELIHOOD\n", "m.hesse()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["35 ms ± 4.11 ms per loop (mean ± std. dev. of 3 runs, 1 loop each)\n"]}], "source": ["%%timeit -n 1 -r 3\n", "jax_covariance(par)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The computation with <PERSON> is slower, but it is also more accurate (although the added precision is not relevant).\n", "\n", "<PERSON>uit's HESSE algorithm still makes sense today. It has the advantage that it can process any function, while <PERSON> cannot. <PERSON> cannot differentiate a function that calls into C/C++ code or Cython code, for example.\n", "\n", "Final note: If we JIT compile `jax_covariance`, it greatly outperforms <PERSON><PERSON>'s HESSE algorithm, but that only makes sense if you need to compute the hessian at different parameter values, so that the extra time spend to compile is balanced by the time saved over many invocations. This is not what happens here, the Hessian in only needed at the best fit point."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["107 μs ± 10.3 μs per loop (mean ± std. dev. of 3 runs, 1 loop each)\n"]}], "source": ["%%timeit -n 1 -r 3 jit_jax_covariance = jax.jit(jax_covariance); jit_jax_covariance(par)\n", "jit_jax_covariance(par)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["It is much faster... but only because the compilation cost is excluded here."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["429 ms ± 0 ns per loop (mean ± std. dev. of 1 run, 1 loop each)\n"]}], "source": ["%%timeit -n 1 -r 1\n", "\n", "\n", "# if we include the JIT compilation cost, the performance drops dramatically\n", "@jax.jit\n", "def jax_covariance(par):\n", "    return jnp.linalg.inv(jax.he<PERSON>an(nll)(par))\n", "\n", "\n", "jax_covariance(par)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["With compilation cost included, it is much slower.\n", "\n", "Conclusion: Using the JIT compiler makes a lot of sense if the covariance matrix has to be computed repeatedly for the same cost function but different parameters, but this is not the case when we use it to compute parameter errors."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Fit data points with uncertainties in x and y\n", "\n", "Let's say we have some data points $(x_i \\pm \\sigma_{x,i}, y_i \\pm \\sigma_{y,i})$ and we have a model $y=f(x)$ that we want to adapt to this data. If $\\sigma_{x,i}$ was zero, we could use the usual least-squares method, minimizing the sum of squared residuals $r^2_i = (y_i - f(x_i))^2 / \\sigma^2_{y,i}$. Here, we don't know where to evaluate $f(x)$, since the exact $x$-location is only known up to $\\sigma_{x,i}$.\n", "\n", "We can approximately extend the standard least-squares method to handle this case. We use that the uncertainty along the $x$-axis can be converted into an additional uncertainty along the $y$-axis with error propagation,\n", "\n", "$$\n", "f(x_i \\pm \\sigma_{x,i}) \\simeq f(x_i) \\pm f'(x_i)\\,\\sigma_{x,i}.\n", "$$\n", "\n", "Using this, we obtain modified squared residuals\n", "\n", "$$\n", "r^2_i = \\frac{(y_i - f(x_i))^2}{\\sigma^2_{y,i} + (f'(x_i) \\,\\sigma_{x,i})^2}.\n", "$$\n", "\n", "We demonstrate this with a fit of a polynomial."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# polynomial model\n", "def f(x, par):\n", "    return jnp.polyval(par, x)\n", "\n", "\n", "# true polynomial f(x) = x^2 + 2 x + 3\n", "par_true = np.array((1, 2, 3))\n", "\n", "\n", "# grad computes derivative with respect to the first argument\n", "f_prime = jax.jit(jax.grad(f))\n", "\n", "\n", "# checking first derivative f'(x) = 2 x + 2\n", "assert f_prime(0.0, par_true) == 2\n", "assert f_prime(1.0, par_true) == 4\n", "assert f_prime(2.0, par_true) == 6\n", "# ok!\n", "\n", "# generate toy data\n", "n = 30\n", "data_x = np.linspace(-4, 7, n)\n", "data_y = f(data_x, par_true)\n", "\n", "rng = np.random.default_rng(seed=1)\n", "sigma_x = 0.5\n", "sigma_y = 5\n", "data_x += rng.normal(0, sigma_x, n)\n", "data_y += rng.normal(0, sigma_y, n)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"399.624687pt\" height=\"297.190125pt\" viewBox=\"0 0 399.**********.190125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T10:48:42.025913</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 297.190125 \n", "L 399.**********.190125 \n", "L 399.624687 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 35.**********.312 \n", "L 392.**********.312 \n", "L 392.424688 7.2 \n", "L 35.304688 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m93aaf9dc7f\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m93aaf9dc7f\" x=\"60.43744\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(53.066346 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m93aaf9dc7f\" x=\"114.837258\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(107.466164 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m93aaf9dc7f\" x=\"169.237075\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(166.055825 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m93aaf9dc7f\" x=\"223.636893\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(220.455643 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m93aaf9dc7f\" x=\"278.036711\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(274.855461 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m93aaf9dc7f\" x=\"332.436529\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(329.255279 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m93aaf9dc7f\" x=\"386.836346\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(383.655096 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"me37a87380c\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"257.847914\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −10 -->\n", "      <g transform=\"translate(7.2 261.647133) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"227.81513\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(21.942187 231.614349) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"197.782346\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 10 -->\n", "      <g transform=\"translate(15.579687 201.581564) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"167.749562\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(15.579687 171.54878) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"137.716777\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 30 -->\n", "      <g transform=\"translate(15.579687 141.515996) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"107.683993\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(15.579687 111.483212) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"77.651209\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(15.579687 81.450428) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"47.618425\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(15.579687 51.417644) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#me37a87380c\" x=\"35.304688\" y=\"17.585641\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 70 -->\n", "      <g transform=\"translate(15.579687 21.38486) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 51.537415 162.97677 \n", "L 78.737324 162.97677 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 68.328662 217.880554 \n", "L 95.52857 217.880554 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 71.965828 212.391024 \n", "L 99.165737 212.391024 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 60.066227 180.720244 \n", "L 87.266136 180.720244 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 100.419111 205.4945 \n", "L 127.61902 205.4945 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 104.494193 208.194967 \n", "L 131.694102 208.194967 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 101.438187 227.952248 \n", "L 128.638096 227.952248 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 126.961113 246.199608 \n", "L 154.161022 246.199608 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 134.333308 219.290286 \n", "L 161.533217 219.290286 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 143.692535 219.657339 \n", "L 170.892444 219.657339 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 150.396095 238.349871 \n", "L 177.596004 238.349871 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 167.762032 227.940004 \n", "L 194.961941 227.940004 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 160.628225 215.658972 \n", "L 187.828134 215.658972 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 178.745606 224.796426 \n", "L 205.945515 224.796426 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 184.72158 207.253655 \n", "L 211.921489 207.253655 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 209.739869 198.648311 \n", "L 236.939778 198.648311 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 212.453013 192.98767 \n", "L 239.652922 192.98767 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 218.252603 193.700445 \n", "L 245.452512 193.700445 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 221.913289 168.893341 \n", "L 249.113197 168.893341 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 239.366612 155.274504 \n", "L 266.566521 155.274504 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 253.292355 153.821752 \n", "L 280.492264 153.821752 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 259.750642 160.045521 \n", "L 286.95055 160.045521 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 291.415244 125.026595 \n", "L 318.615153 125.026595 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 297.824647 130.933708 \n", "L 325.024556 130.933708 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 257.578763 96.728382 \n", "L 284.778672 96.728382 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 279.077162 111.686696 \n", "L 306.277071 111.686696 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 312.70797 66.65823 \n", "L 339.907879 66.65823 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 319.660299 64.625219 \n", "L 346.860208 64.625219 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 338.624811 66.14515 \n", "L 365.82472 66.14515 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 348.992051 34.312392 \n", "L 376.19196 34.312392 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_2\">\n", "    <path d=\"M 65.137369 177.993162 \n", "L 65.137369 147.960378 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 81.928616 232.896946 \n", "L 81.928616 202.864162 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 85.565783 227.407416 \n", "L 85.565783 197.374632 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 73.666181 195.736636 \n", "L 73.666181 165.703852 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 114.019066 220.510892 \n", "L 114.019066 190.478108 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 118.094148 223.211359 \n", "L 118.094148 193.178575 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 115.038141 242.96864 \n", "L 115.038141 212.935856 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 140.561067 261.216 \n", "L 140.561067 231.183216 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 147.933262 234.306678 \n", "L 147.933262 204.273894 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 157.29249 234.673731 \n", "L 157.29249 204.640947 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 163.996049 253.366263 \n", "L 163.996049 223.333479 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 181.361987 242.956396 \n", "L 181.361987 212.923612 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 174.22818 230.675364 \n", "L 174.22818 200.64258 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 192.345561 239.812818 \n", "L 192.345561 209.780034 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 198.321535 222.270047 \n", "L 198.321535 192.237262 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 223.339823 213.664703 \n", "L 223.339823 183.631919 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 226.052968 208.004062 \n", "L 226.052968 177.971278 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 231.852557 208.716838 \n", "L 231.852557 178.684053 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 235.513243 183.909733 \n", "L 235.513243 153.876948 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 252.966567 170.290896 \n", "L 252.966567 140.258111 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 266.892309 168.838144 \n", "L 266.892309 138.80536 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 273.350596 175.061913 \n", "L 273.350596 145.029129 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 305.015199 140.042987 \n", "L 305.015199 110.010203 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 311.424601 145.9501 \n", "L 311.424601 115.917316 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 271.178717 111.744774 \n", "L 271.178717 81.71199 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 292.677116 126.703088 \n", "L 292.677116 96.670304 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 326.307925 81.674622 \n", "L 326.307925 51.641838 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 333.260253 79.641611 \n", "L 333.260253 49.608827 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 352.224766 81.161542 \n", "L 352.224766 51.128758 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 362.592006 49.328784 \n", "L 362.592006 19.296 \n", "\" clip-path=\"url(#pe325a7cc7c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <defs>\n", "     <path id=\"m2464aec3b4\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #1f77b4\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pe325a7cc7c)\">\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"65.137369\" y=\"162.97677\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"81.928616\" y=\"217.880554\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"85.565783\" y=\"212.391024\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"73.666181\" y=\"180.720244\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"114.019066\" y=\"205.4945\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"118.094148\" y=\"208.194967\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"115.038141\" y=\"227.952248\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"140.561067\" y=\"246.199608\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"147.933262\" y=\"219.290286\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"157.29249\" y=\"219.657339\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"163.996049\" y=\"238.349871\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"181.361987\" y=\"227.940004\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"174.22818\" y=\"215.658972\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"192.345561\" y=\"224.796426\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"198.321535\" y=\"207.253655\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"223.339823\" y=\"198.648311\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"226.052968\" y=\"192.98767\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"231.852557\" y=\"193.700445\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"235.513243\" y=\"168.893341\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"252.966567\" y=\"155.274504\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"266.892309\" y=\"153.821752\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"273.350596\" y=\"160.045521\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"305.015199\" y=\"125.026595\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"311.424601\" y=\"130.933708\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"271.178717\" y=\"96.728382\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"292.677116\" y=\"111.686696\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"326.307925\" y=\"66.65823\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"333.260253\" y=\"64.625219\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"352.224766\" y=\"66.14515\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m2464aec3b4\" x=\"362.592006\" y=\"34.312392\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 35.**********.312 \n", "L 35.304688 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 392.**********.312 \n", "L 392.424688 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 35.**********.312 \n", "L 392.**********.312 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 35.304688 7.2 \n", "L 392.424688 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pe325a7cc7c\">\n", "   <rect x=\"35.304688\" y=\"7.2\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.errorbar(data_x, data_y, sigma_y, sigma_x, fmt=\"o\");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["Array(876.49545695, dtype=float64)"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["# define the cost function\n", "@jax.jit\n", "def cost(par):\n", "    result = 0.0\n", "    for xi, yi in zip(data_x, data_y):\n", "        y_var = sigma_y**2 + (f_prime(xi, par) * sigma_x) ** 2\n", "        result += (yi - f(xi, par)) ** 2 / y_var\n", "    return result\n", "\n", "\n", "cost.errordef = Minuit.LEAST_SQUARES\n", "\n", "# test the jit-ed function\n", "cost(np.zeros(3))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 23.14 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 91 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 3.12e-05 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 1.25 </td>\n", "        <td> 0.15 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 1.5 </td>\n", "        <td> 0.5 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 2 </th>\n", "        <td> x2 </td>\n", "        <td> 1.6 </td>\n", "        <td> 1.5 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "        <th> x2 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 0.0223 </td>\n", "        <td style=\"background-color:rgb(181,181,250);color:black\"> -0.039 <strong>(-0.530)</strong> </td>\n", "        <td style=\"background-color:rgb(165,165,250);color:black\"> -0.150 <strong>(-0.657)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(181,181,250);color:black\"> -0.039 <strong>(-0.530)</strong> </td>\n", "        <td> 0.24 </td>\n", "        <td style=\"background-color:rgb(250,216,216);color:black\"> 0.17 <strong>(0.230)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x2 </th>\n", "        <td style=\"background-color:rgb(165,165,250);color:black\"> -0.150 <strong>(-0.657)</strong> </td>\n", "        <td style=\"background-color:rgb(250,216,216);color:black\"> 0.17 <strong>(0.230)</strong> </td>\n", "        <td> 2.32 </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 23.14                      │              Nfcn = 91               │\n", "│ EDM = 3.12e-05 (Goal: 0.0002)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │   1.25    │   0.15    │            │            │         │         │       │\n", "│ 1 │ x1   │    1.5    │    0.5    │            │            │         │         │       │\n", "│ 2 │ x2   │    1.6    │    1.5    │            │            │         │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌────┬──────────────────────┐\n", "│    │     x0     x1     x2 │\n", "├────┼──────────────────────┤\n", "│ x0 │ 0.0223 -0.039 -0.150 │\n", "│ x1 │ -0.039   0.24   0.17 │\n", "│ x2 │ -0.150   0.17   2.32 │\n", "└────┴──────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["m = Minuit(cost, np.zeros(3))\n", "m.migrad()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"391.245pt\" height=\"314.110125pt\" viewBox=\"0 0 391.245 314.110125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T10:48:42.979909</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 314.110125 \n", "L 391.245 314.110125 \n", "L 391.245 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 26.925 290.232 \n", "L 384.045 290.232 \n", "L 384.045 24.12 \n", "L 26.925 24.12 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m91b5921ee7\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m91b5921ee7\" x=\"52.057752\" y=\"290.232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- −4 -->\n", "      <g transform=\"translate(44.686659 304.830437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m91b5921ee7\" x=\"106.45757\" y=\"290.232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- −2 -->\n", "      <g transform=\"translate(99.086476 304.830437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m91b5921ee7\" x=\"160.857388\" y=\"290.232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(157.676138 304.830437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m91b5921ee7\" x=\"215.257206\" y=\"290.232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 2 -->\n", "      <g transform=\"translate(212.075956 304.830437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m91b5921ee7\" x=\"269.657023\" y=\"290.232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 4 -->\n", "      <g transform=\"translate(266.475773 304.830437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m91b5921ee7\" x=\"324.056841\" y=\"290.232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 6 -->\n", "      <g transform=\"translate(320.875591 304.830437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m91b5921ee7\" x=\"378.456659\" y=\"290.232\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 8 -->\n", "      <g transform=\"translate(375.275409 304.830437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_8\">\n", "      <defs>\n", "       <path id=\"m3bcc7cb636\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m3bcc7cb636\" x=\"26.925\" y=\"247.061821\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(13.5625 250.86104) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m3bcc7cb636\" x=\"26.925\" y=\"191.180396\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(7.2 194.979615) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m3bcc7cb636\" x=\"26.925\" y=\"135.298971\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(7.2 139.09819) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m3bcc7cb636\" x=\"26.925\" y=\"79.417546\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(7.2 83.216765) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 43.157727 186.740075 \n", "L 70.357636 186.740075 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 59.948974 237.819283 \n", "L 87.148883 237.819283 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 63.586141 232.712152 \n", "L 90.78605 232.712152 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 51.686539 203.247546 \n", "L 78.886448 203.247546 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 92.039424 226.296037 \n", "L 119.239333 226.296037 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 96.114506 228.808391 \n", "L 123.314415 228.808391 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 93.058499 247.189387 \n", "L 120.258408 247.189387 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 118.581425 264.165644 \n", "L 145.781334 264.165644 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 125.95362 239.130814 \n", "L 153.153529 239.130814 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 135.312848 239.472299 \n", "L 162.512757 239.472299 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 142.016407 256.862717 \n", "L 169.216316 256.862717 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 159.382345 247.177996 \n", "L 186.582253 247.177996 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 152.248538 235.752456 \n", "L 179.448447 235.752456 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 170.365919 244.253399 \n", "L 197.565828 244.253399 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 176.341893 227.93265 \n", "L 203.541802 227.93265 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 201.360181 219.926751 \n", "L 228.56009 219.926751 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 204.073326 214.660428 \n", "L 231.273235 214.660428 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 209.872915 215.323552 \n", "L 237.072824 215.323552 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 213.533601 192.2445 \n", "L 240.73351 192.2445 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 230.986925 179.574345 \n", "L 258.186834 179.574345 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 244.912667 178.222792 \n", "L 272.112576 178.222792 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 251.370954 184.013016 \n", "L 278.570863 184.013016 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 283.035557 151.433494 \n", "L 310.235466 151.433494 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 289.444959 156.92912 \n", "L 316.644868 156.92912 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 249.199075 125.106523 \n", "L 276.398984 125.106523 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 270.697474 139.022847 \n", "L 297.897383 139.022847 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 304.328283 97.131045 \n", "L 331.528191 97.131045 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 311.280612 95.239653 \n", "L 338.48052 95.239653 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 330.245124 96.653707 \n", "L 357.445033 96.653707 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 340.612364 67.038406 \n", "L 367.812273 67.038406 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"LineCollection_2\">\n", "    <path d=\"M 56.757682 200.710432 \n", "L 56.757682 172.769719 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 73.548929 251.78964 \n", "L 73.548929 223.848927 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 77.186095 246.682509 \n", "L 77.186095 218.741796 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 65.286494 217.217902 \n", "L 65.286494 189.27719 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 105.639378 240.266394 \n", "L 105.639378 212.325681 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 109.71446 242.778747 \n", "L 109.71446 214.838035 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 106.658454 261.159744 \n", "L 106.658454 233.219031 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 132.18138 278.136 \n", "L 132.18138 250.195287 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 139.553575 253.10117 \n", "L 139.553575 225.160458 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 148.912802 253.442655 \n", "L 148.912802 225.501942 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 155.616362 270.833073 \n", "L 155.616362 242.89236 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 172.982299 261.148353 \n", "L 172.982299 233.20764 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 165.848492 249.722812 \n", "L 165.848492 221.7821 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 183.965873 258.223755 \n", "L 183.965873 230.283043 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 189.941847 241.903006 \n", "L 189.941847 213.962294 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 214.960136 233.897107 \n", "L 214.960136 205.956394 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 217.67328 228.630784 \n", "L 217.67328 200.690072 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 223.47287 229.293908 \n", "L 223.47287 201.353196 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 227.133556 206.214856 \n", "L 227.133556 178.274144 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 244.586879 193.544702 \n", "L 244.586879 165.603989 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 258.512622 192.193148 \n", "L 258.512622 164.252436 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 264.970909 197.983372 \n", "L 264.970909 170.042659 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 296.635511 165.40385 \n", "L 296.635511 137.463137 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 303.044914 170.899476 \n", "L 303.044914 142.958764 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 262.79903 139.07688 \n", "L 262.79903 111.136167 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 284.297429 152.993203 \n", "L 284.297429 125.052491 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 317.928237 111.101402 \n", "L 317.928237 83.160689 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 324.880566 109.210009 \n", "L 324.880566 81.269297 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 343.845078 110.624063 \n", "L 343.845078 82.68335 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    <path d=\"M 354.212318 81.008762 \n", "L 354.212318 53.06805 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_12\">\n", "    <path d=\"M 56.757682 207.551725 \n", "L 62.736669 212.332066 \n", "L 68.715657 216.775 \n", "L 74.694645 220.880528 \n", "L 80.673632 224.64865 \n", "L 86.65262 228.079366 \n", "L 91.136861 230.43098 \n", "L 95.621102 232.592803 \n", "L 100.105342 234.564836 \n", "L 104.589583 236.347077 \n", "L 109.073824 237.939527 \n", "L 113.558065 239.342187 \n", "L 118.042305 240.555055 \n", "L 122.526546 241.578132 \n", "L 127.010787 242.411419 \n", "L 131.495028 243.054915 \n", "L 135.979268 243.508619 \n", "L 140.463509 243.772533 \n", "L 144.94775 243.846655 \n", "L 149.431991 243.730987 \n", "L 153.916231 243.425528 \n", "L 158.400472 242.930278 \n", "L 162.884713 242.245237 \n", "L 167.368954 241.370405 \n", "L 171.853194 240.305782 \n", "L 176.337435 239.051368 \n", "L 180.821676 237.607163 \n", "L 185.305917 235.973167 \n", "L 189.790157 234.14938 \n", "L 194.274398 232.135802 \n", "L 198.758639 229.932434 \n", "L 203.24288 227.539274 \n", "L 209.221867 224.053164 \n", "L 215.200855 220.229648 \n", "L 221.179843 216.068726 \n", "L 227.15883 211.570397 \n", "L 233.137818 206.734663 \n", "L 239.116806 201.561522 \n", "L 245.095793 196.050975 \n", "L 251.074781 190.203023 \n", "L 257.053769 184.017663 \n", "L 263.032756 177.494898 \n", "L 269.011744 170.634727 \n", "L 274.990732 163.43715 \n", "L 280.969719 155.902166 \n", "L 286.948707 148.029776 \n", "L 292.927695 139.81998 \n", "L 298.906682 131.272778 \n", "L 304.88567 122.38817 \n", "L 310.864658 113.166156 \n", "L 316.843645 103.606735 \n", "L 324.31738 91.182983 \n", "L 331.791115 78.232033 \n", "L 339.264849 64.753885 \n", "L 346.738584 50.748541 \n", "L 354.212318 36.216 \n", "L 354.212318 36.216 \n", "\" clip-path=\"url(#p41da7a9840)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_13\">\n", "    <defs>\n", "     <path id=\"m7ef997d004\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #1f77b4\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p41da7a9840)\">\n", "     <use xlink:href=\"#m7ef997d004\" x=\"56.757682\" y=\"186.740075\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"73.548929\" y=\"237.819283\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"77.186095\" y=\"232.712152\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"65.286494\" y=\"203.247546\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"105.639378\" y=\"226.296037\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"109.71446\" y=\"228.808391\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"106.658454\" y=\"247.189387\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"132.18138\" y=\"264.165644\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"139.553575\" y=\"239.130814\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"148.912802\" y=\"239.472299\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"155.616362\" y=\"256.862717\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"172.982299\" y=\"247.177996\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"165.848492\" y=\"235.752456\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"183.965873\" y=\"244.253399\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"189.941847\" y=\"227.93265\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"214.960136\" y=\"219.926751\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"217.67328\" y=\"214.660428\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"223.47287\" y=\"215.323552\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"227.133556\" y=\"192.2445\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"244.586879\" y=\"179.574345\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"258.512622\" y=\"178.222792\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"264.970909\" y=\"184.013016\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"296.635511\" y=\"151.433494\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"303.044914\" y=\"156.92912\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"262.79903\" y=\"125.106523\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"284.297429\" y=\"139.022847\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"317.928237\" y=\"97.131045\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"324.880566\" y=\"95.239653\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"343.845078\" y=\"96.653707\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m7ef997d004\" x=\"354.212318\" y=\"67.038406\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 26.925 290.232 \n", "L 26.925 24.12 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 384.045 290.232 \n", "L 384.045 24.12 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 26.925 290.232 \n", "L 384.045 290.232 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 26.925 24.12 \n", "L 384.045 24.12 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_12\">\n", "    <!-- $\\chi^2 / n_\\mathrm{dof} = 23.14 / 27 = 0.86$ -->\n", "    <g transform=\"translate(131.565 18.12) scale(0.12 -0.12)\">\n", "     <defs>\n", "      <path id=\"DejaVuSans-Oblique-3c7\" d=\"M 1922 -781 \n", "L 1691 416 \n", "L 394 -1334 \n", "L -284 -1334 \n", "L 1553 1141 \n", "L 1269 2613 \n", "Q 1194 3006 713 3006 \n", "L 559 3006 \n", "L 653 3500 \n", "L 872 3494 \n", "Q 1675 3472 1775 2950 \n", "L 2006 1753 \n", "L 3303 3503 \n", "L 3981 3503 \n", "L 2144 1028 \n", "L 2428 -444 \n", "Q 2503 -838 2984 -838 \n", "L 3138 -838 \n", "L 3044 -1331 \n", "L 2825 -1325 \n", "Q 2022 -1303 1922 -781 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2f\" d=\"M 1625 4666 \n", "L 2156 4666 \n", "L 531 -594 \n", "L 0 -594 \n", "L 1625 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-Oblique-6e\" d=\"M 3566 2113 \n", "L 3156 0 \n", "L 2578 0 \n", "L 2988 2091 \n", "Q 3016 2238 3031 2350 \n", "Q 3047 2463 3047 2528 \n", "Q 3047 2791 2881 2937 \n", "Q 2716 3084 2419 3084 \n", "Q 1956 3084 1622 2776 \n", "Q 1288 2469 1184 1941 \n", "L 800 0 \n", "L 225 0 \n", "L 903 3500 \n", "L 1478 3500 \n", "L 1363 2950 \n", "Q 1603 3253 1940 3418 \n", "Q 2278 3584 2650 3584 \n", "Q 3113 3584 3367 3334 \n", "Q 3622 3084 3622 2631 \n", "Q 3622 2519 3608 2391 \n", "Q 3594 2263 3566 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-66\" d=\"M 2375 4863 \n", "L 2375 4384 \n", "L 1825 4384 \n", "Q 1516 4384 1395 4259 \n", "Q 1275 4134 1275 3809 \n", "L 1275 3500 \n", "L 2222 3500 \n", "L 2222 3053 \n", "L 1275 3053 \n", "L 1275 0 \n", "L 697 0 \n", "L 697 3053 \n", "L 147 3053 \n", "L 147 3500 \n", "L 697 3500 \n", "L 697 3744 \n", "Q 697 4328 969 4595 \n", "Q 1241 4863 1831 4863 \n", "L 2375 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-3d\" d=\"M 678 2906 \n", "L 4684 2906 \n", "L 4684 2381 \n", "L 678 2381 \n", "L 678 2906 \n", "z\n", "M 678 1631 \n", "L 4684 1631 \n", "L 4684 1100 \n", "L 678 1100 \n", "L 678 1631 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "     </defs>\n", "     <use xlink:href=\"#DejaVuSans-Oblique-3c7\" transform=\"translate(0 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(63.652396 39.046875) scale(0.7)\"/>\n", "     <use xlink:href=\"#DejaVuSans-2f\" transform=\"translate(110.922904 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-Oblique-6e\" transform=\"translate(144.61431 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-64\" transform=\"translate(207.993216 -15.640625) scale(0.7)\"/>\n", "     <use xlink:href=\"#DejaVuSans-6f\" transform=\"translate(252.42681 -15.640625) scale(0.7)\"/>\n", "     <use xlink:href=\"#DejaVuSans-66\" transform=\"translate(295.253958 -15.640625) scale(0.7)\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" transform=\"translate(342.11431 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(445.385794 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-33\" transform=\"translate(509.008841 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" transform=\"translate(572.631888 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(604.418997 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-34\" transform=\"translate(668.042044 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-2f\" transform=\"translate(731.665091 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(761.731497 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-37\" transform=\"translate(825.354544 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-3d\" transform=\"translate(908.460013 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(1011.731497 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-2e\" transform=\"translate(1075.354544 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-38\" transform=\"translate(1104.516654 0.765625)\"/>\n", "     <use xlink:href=\"#DejaVuSans-36\" transform=\"translate(1168.139701 0.765625)\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 33.925 61.47625 \n", "L 88.45 61.47625 \n", "Q 90.45 61.47625 90.45 59.47625 \n", "L 90.45 31.12 \n", "Q 90.45 29.12 88.45 29.12 \n", "L 33.925 29.12 \n", "Q 31.925 29.12 31.925 31.12 \n", "L 31.925 59.47625 \n", "Q 31.925 61.47625 33.925 61.47625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_14\">\n", "     <path d=\"M 35.925 37.218437 \n", "L 45.925 37.218437 \n", "L 55.925 37.218437 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- fit -->\n", "     <g transform=\"translate(63.925 40.718437) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-66\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"35.205078\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"62.988281\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"LineCollection_3\">\n", "     <path d=\"M 40.925 51.896562 \n", "L 50.925 51.896562 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"LineCollection_4\">\n", "     <path d=\"M 45.925 56.896562 \n", "L 45.925 46.896562 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5\"/>\n", "    </g>\n", "    <g id=\"line2d_15\"/>\n", "    <g id=\"line2d_16\">\n", "     <g>\n", "      <use xlink:href=\"#m7ef997d004\" x=\"45.925\" y=\"51.896562\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- data -->\n", "     <g transform=\"translate(63.925 55.396562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-64\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"163.964844\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p41da7a9840\">\n", "   <rect x=\"26.925\" y=\"24.12\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plt.errorbar(data_x, data_y, sigma_y, sigma_x, fmt=\"o\", label=\"data\")\n", "x = np.linspace(data_x[0], data_x[-1], 200)\n", "par = np.array(m.values)\n", "plt.plot(x, f(x, par), label=\"fit\")\n", "plt.legend()\n", "\n", "# check fit quality\n", "chi2 = m.fval\n", "ndof = len(data_y) - 3\n", "plt.title(f\"$\\\\chi^2 / n_\\\\mathrm{{dof}} = {chi2:.2f} / {ndof} = {chi2 / ndof:.2f}$\");"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We obtained a good fit."]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "Python 3.8.14 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 2}