{"cells": [{"cell_type": "markdown", "id": "0", "metadata": {}, "source": ["# How to draw error bands\n", "\n", "We show two ways to compute 1-sigma bands around a fitted curve.\n", "\n", "Whether the curve describes a probability density (from a maximum-likelihood fit) or an expectation (from a least-squares fit) does not matter, the procedure is the same. We demonstrate this on an unbinned extended maximum-likelihood fit of a Gaussian."]}, {"cell_type": "code", "execution_count": null, "id": "1", "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "import numpy as np\n", "from numba_stats import norm\n", "from iminuit import Minuit\n", "from iminuit.cost import ExtendedUnbinnedNLL\n", "import matplotlib.pyplot as plt\n", "\n", "# generate toy sample\n", "rng = np.random.default_rng(1)\n", "x = rng.normal(size=100)\n", "\n", "# bin it\n", "w, xe = np.histogram(x, bins=100, range=(-5, 5))\n", "\n", "# compute bin-wise density estimates\n", "werr = w**0.5\n", "cx = 0.5 * (xe[1:] + xe[:-1])\n", "dx = np.diff(xe)\n", "d = w / dx\n", "derr = werr / dx\n", "\n", "\n", "# define model and cost function\n", "def model(x, par):\n", "    return par[0], par[0] * norm.pdf(x, par[1], par[2])\n", "\n", "\n", "cost = ExtendedUnbinnedNLL(x, model)\n", "\n", "# fit the model\n", "m = Minuit(cost, (1, 0, 1))\n", "m.migrad()\n", "m.hesse()\n", "\n", "# plot everything\n", "plt.errorbar(cx, d, derr, fmt=\"o\", label=\"data\", zorder=0)\n", "\n", "plt.plot(cx, model(cx, m.values)[1], lw=3, label=\"fit\")\n", "plt.legend(\n", "    frameon=False,\n", "    title=f\"$n = {m.values[0]:.2f} +/- {m.errors[0]:.2f}$\\n\"\n", "    f\"$\\\\mu = {m.values[1]:.2f} +/- {m.errors[1]:.2f}$\\n\"\n", "    f\"$\\\\sigma = {m.values[2]:.2f} +/- {m.errors[2]:.2f}$\",\n", ");"]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["We want to understand how uncertain the Gaussian curve is. Thus we want to draw a 1-sigma error band around the curve, which approximates the 68 % confidence interval."]}, {"cell_type": "markdown", "id": "3", "metadata": {}, "source": ["## With error propagation\n", "\n", "The uncertainty is quantified in form of the covariance matrix of the fitted parameters. We can use [error propagation](https://en.wikipedia.org/wiki/Propagation_of_uncertainty) to obtain the uncertainty of the curve,\n", "\n", "$$\n", "C' = J \\, C \\, J^T,\n", "$$\n", "\n", "where $C$ is the covariance matrix of the input vector, $C'$ is the covariance matrix of the output vector and $J$ is the matrix of first derivatives of the mapping function between input and output. The mapping in this case is the curve, $\\vec y = f(\\vec{x}; \\vec{p})$, regarded as a function of $\\vec{p}$ and not of $\\vec{x}$, which is fixed. The function maps from $\\vec{p}$ to $\\vec{y}$ and the Jacobi matrix is made from elements\n", "\n", "$$\n", "J_{ik} = \\frac{\\partial y_i}{\\partial p_k}.\n", "$$\n", "\n", "To compute the derivatives one can sometimes use [Sympy](https://www.sympy.org/en/index.html) or an auto-differentiation tool like [JAX](https://jax.readthedocs.io/en/latest/) if the function permits it, but in general they need to be computed numerically. The library [<PERSON><PERSON>](https://github.com/h<PERSON><PERSON><PERSON><PERSON>/jaco<PERSON>) provides a fast and robust calculator for numerical derivatives and a function for error propagation."]}, {"cell_type": "code", "execution_count": null, "id": "4", "metadata": {}, "outputs": [], "source": ["from jacobi import propagate\n", "\n", "# run error propagation\n", "y, ycov = propagate(lambda p: model(cx, p)[1], m.values, m.covariance)\n", "\n", "# plot everything\n", "plt.errorbar(cx, d, derr, fmt=\"o\", label=\"data\", zorder=0)\n", "\n", "plt.plot(cx, y, lw=3, label=\"fit\")\n", "\n", "# draw 1 sigma error band\n", "yerr_prop = np.diag(ycov) ** 0.5\n", "plt.fill_between(cx, y - yerr_prop, y + yerr_prop, facecolor=\"C1\", alpha=0.5)\n", "\n", "plt.legend(\n", "    frameon=False,\n", "    title=f\"$n = {m.values[0]:.2f} +/- {m.errors[0]:.2f}$\\n\"\n", "    f\"$\\\\mu = {m.values[1]:.2f} +/- {m.errors[1]:.2f}$\\n\"\n", "    f\"$\\\\sigma = {m.values[2]:.2f} +/- {m.errors[2]:.2f}$\",\n", ");"]}, {"cell_type": "markdown", "id": "5", "metadata": {}, "source": ["Error propagation is relatively fast."]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["%%timeit -r 1 -n 1000\n", "propagate(lambda p: model(cx, p)[1], m.values, m.covariance)"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## With the bootstrap\n", "\n", "Another generic way to compute uncertainties is bootstrapping. We know that the parameters asymptotically follow a multivariate normal distribution, so we can simulate new experiments with varied parameter values."]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["rng = np.random.default_rng(1)\n", "\n", "par_b = rng.multivariate_normal(m.values, m.covariance, size=1000)\n", "\n", "# standard deviation of bootstrapped curves\n", "y_b = [model(cx, p)[1] for p in par_b]\n", "yerr_boot = np.std(y_b, axis=0)\n", "\n", "# plot everything\n", "plt.errorbar(cx, d, derr, fmt=\"o\", label=\"data\", zorder=0)\n", "\n", "plt.plot(cx, y, lw=3, label=\"fit\")\n", "\n", "# draw 1 sigma error band\n", "plt.fill_between(cx, y - yerr_boot, y + yerr_boot, facecolor=\"C1\", alpha=0.5)\n", "\n", "plt.legend(\n", "    frameon=False,\n", "    title=f\"$n = {m.values[0]:.2f} +/- {m.errors[0]:.2f}$\\n\"\n", "    f\"$\\\\mu = {m.values[1]:.2f} +/- {m.errors[1]:.2f}$\\n\"\n", "    f\"$\\\\sigma = {m.values[2]:.2f} +/- {m.errors[2]:.2f}$\",\n", ");"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["The result is visually indistinguishable from before, as it should be. If you worry about deviations between the two methods, read on."]}, {"cell_type": "markdown", "id": "10", "metadata": {}, "source": ["In this example, computing the band from 1000 samples is slower than error propagation."]}, {"cell_type": "code", "execution_count": null, "id": "11", "metadata": {}, "outputs": [], "source": ["%%timeit -r 1 -n 100\n", "par_b = rng.multivariate_normal(m.values, m.covariance, size=1000)\n", "y_b = [model(cx, p)[1] for p in par_b]\n", "np.std(y_b, axis=0)"]}, {"cell_type": "markdown", "id": "12", "metadata": {}, "source": ["However, the calculation time scales linearly with the number of samples. One can simply draw fewer samples if the additional uncertainty is acceptable. If we draw only 50 samples, bootstrapping wins over numerical error propagation."]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["%%timeit -r 1 -n 1000\n", "rng = np.random.default_rng(1)\n", "par_b = rng.multivariate_normal(m.values, m.covariance, size=50)\n", "y_b = [model(cx, p)[1] for p in par_b]\n", "np.std(y_b, axis=0)"]}, {"cell_type": "markdown", "id": "14", "metadata": {}, "source": ["Let's see how the result looks, whether it deviates noticably."]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# compute bootstrapped curves with 50 samples\n", "par_b = rng.multivariate_normal(m.values, m.covariance, size=50)\n", "y_b = [model(cx, p)[1] for p in par_b]\n", "yerr_boot_50 = np.std(y_b, axis=0)\n", "\n", "# plot everything\n", "plt.errorbar(cx, d, derr, fmt=\"o\", label=\"data\", zorder=0)\n", "\n", "plt.plot(cx, y, lw=3, label=\"fit\")\n", "\n", "# draw 1 sigma error band\n", "plt.fill_between(cx, y - yerr_boot_50, y + yerr_boot_50, facecolor=\"C1\", alpha=0.5)\n", "\n", "plt.legend(\n", "    frameon=False,\n", "    title=f\"$n = {m.values[0]:.2f} +/- {m.errors[0]:.2f}$\\n\"\n", "    f\"$\\\\mu = {m.values[1]:.2f} +/- {m.errors[1]:.2f}$\\n\"\n", "    f\"$\\\\sigma = {m.values[2]:.2f} +/- {m.errors[2]:.2f}$\",\n", ");"]}, {"cell_type": "markdown", "id": "16", "metadata": {}, "source": ["No, the result is still visually indistinguishable. This suggests that 50 samples can be enough for plotting.\n", "\n", "Numerically, the three error bands differ at the 10 % level in the central region (expected relative error is $50^{-1/2} \\approx 0.14$). The eye cannot pickup these differences, but they are there. The curves differ more in the tails, which is not visible in linear scale, but noticable in log-scale."]}, {"cell_type": "code", "execution_count": null, "id": "17", "metadata": {}, "outputs": [], "source": ["fig, ax = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "plt.sca(ax[0])\n", "plt.plot(cx, y - yerr_prop, \"-C0\", label=\"prop\")\n", "plt.plot(cx, y + yerr_prop, \"-C0\", label=\"prop\")\n", "plt.plot(cx, y - yerr_boot, \"--C1\", label=\"boot[1000]\")\n", "plt.plot(cx, y + yerr_boot, \"--C1\", label=\"boot[1000]\")\n", "plt.plot(cx, y - yerr_boot_50, \":C2\", label=\"boot[50]\")\n", "plt.plot(cx, y + yerr_boot_50, \":C2\", label=\"boot[50]\")\n", "plt.legend()\n", "plt.semilogy()\n", "\n", "plt.sca(ax[1])\n", "plt.plot(cx, yerr_boot / yerr_prop, label=\"boot[1000] / prop\")\n", "plt.plot(cx, yerr_boot_50 / yerr_prop, label=\"boot[50] / prop\")\n", "plt.legend()\n", "plt.axhline(1, ls=\"--\", color=\"0.5\", zorder=0)\n", "for delta in (-0.1, 0.1):\n", "    plt.axhline(1 + delta, ls=\":\", color=\"0.5\", zorder=0)\n", "plt.ylim(0.5, 1.5);"]}, {"cell_type": "markdown", "id": "18", "metadata": {}, "source": ["We see that the bootstrapped bands are a bit wider in the tails. This is caused by non-linearities that are neglected in error propagation."]}, {"cell_type": "markdown", "id": "19", "metadata": {}, "source": ["## Which is better? Error propagation or bootstrap?\n", "\n", "There is no clear-cut answer. At the visual level, both methods are usually fine (even with a small number of bootstrap samples). Which calculation is more accurate depends on details of the problem. Fortunately, the sources of error are orthogonal for both methods, so each method can be used to check the other.\n", "\n", "* The bootstrap error is caused by sampling. It can be reduced by drawing more samples, the relative error is proportional to $N^{-1/2}$.\n", "* The propagation error is caused by using a truncated Taylor series in the computation."]}], "metadata": {"kernelspec": {"display_name": "Python 3.8.13 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 5}