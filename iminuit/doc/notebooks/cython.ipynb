{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Using Cython\n", "\n", "We show how to use Cython to accelerate the computation of a cost function and how to avoid some pitfalls.\n", "\n", "**Disclaimer:** If you do not care specifically about [<PERSON><PERSON><PERSON>](https://cython.org) and just want to make your code faster, prefer [<PERSON><PERSON>](https://numba.pydata.org) (see the corresponding Numba tutorial for more details), or try to run iminuit in the PyPy interpreter. Numba is more powerful and easier to use, and you don't have to learn the awkward Cython dialect. Cython is a good choice when you have to call into C code from Python, but it is not a good choice to call into C++ code, for this [pybind11](https://pybind11.readthedocs.io/en/stable/) is the ideal choice. Cython does not fully support the C++ language, it was designed for C.\n", "\n", "With that out of the way, let's see how to use iminuit with a Cython-compiled function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# setup of the notebook\n", "%load_ext Cython\n", "from iminuit import Minuit"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The following cell is Cython code and will be compiled to machine code behind the scenes."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%%cython\n", "\n", "def cython_func(double x, double y, double z):\n", "    return (x - 1.) ** 2 + (y - 2.) ** 2 + (z - 3.) ** 2 + 1."]}, {"cell_type": "markdown", "metadata": {}, "source": ["Minuit can work with this compiled function like it was a native Python function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 1 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 36 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 2.38e-18 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x </td>\n", "        <td> 1 </td>\n", "        <td> 1 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> y </td>\n", "        <td> 2 </td>\n", "        <td> 1 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 2 </th>\n", "        <td> z </td>\n", "        <td> 3 </td>\n", "        <td> 1 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x </th>\n", "        <th> y </th>\n", "        <th> z </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x </th>\n", "        <td> 1 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> y </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 1 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> z </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 1 </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 1                          │              Nfcn = 36               │\n", "│ EDM = 2.38e-18 (Goal: 0.0002)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x    │     1     │     1     │            │            │         │         │       │\n", "│ 1 │ y    │     2     │     1     │            │            │         │         │       │\n", "│ 2 │ z    │     3     │     1     │            │            │         │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌───┬───────┐\n", "│   │ x y z │\n", "├───┼───────┤\n", "│ x │ 1 0 0 │\n", "│ y │ 0 1 0 │\n", "│ z │ 0 0 1 │\n", "└───┴───────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["m = Minuit(cython_func, 1, 1, 1)\n", "m.migrad()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the past, <PERSON><PERSON><PERSON> would not create a proper signature for the function by default, which iminuit uses need to get the parameter names. This was improved at some point.\n", "\n", "If you encouter a function without a signature, you can tell <PERSON><PERSON> explicitly about the names of the parameters with the keyword `name`. This can also be used to override the automatic detection and use other names."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 1 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 36 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 9.78e-20 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> a </td>\n", "        <td> 1 </td>\n", "        <td> 1 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> b </td>\n", "        <td> 2 </td>\n", "        <td> 1 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 2 </th>\n", "        <td> c </td>\n", "        <td> 3 </td>\n", "        <td> 1 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> a </th>\n", "        <th> b </th>\n", "        <th> c </th>\n", "    </tr>\n", "    <tr>\n", "        <th> a </th>\n", "        <td> 1 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> b </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 1 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> c </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 1 </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 1                          │              Nfcn = 36               │\n", "│ EDM = 9.78e-20 (Goal: 0.0002)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ a    │     1     │     1     │            │            │         │         │       │\n", "│ 1 │ b    │     2     │     1     │            │            │         │         │       │\n", "│ 2 │ c    │     3     │     1     │            │            │         │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌───┬───────┐\n", "│   │ a b c │\n", "├───┼───────┤\n", "│ a │ 1 0 0 │\n", "│ b │ 0 1 0 │\n", "│ c │ 0 0 1 │\n", "└───┴───────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["m = Minuit(cython_func, 1, 1, 1, name=(\"a\", \"b\", \"c\"))\n", "m.migrad()"]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "Python 3.8.13 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 4}