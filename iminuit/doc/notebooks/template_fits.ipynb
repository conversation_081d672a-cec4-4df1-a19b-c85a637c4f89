{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Template fits\n", "\n", "In applications we are interested in separating a signal component from background components, we often fit parameteric models to data. Sometimes constructing a parametric model for some component is difficult. In that case, one fits a template instead which may be obtained from simulation or from a calibration sample in which a pure component can be isolated.\n", "\n", "The challenge then is to propagate the uncertainty of the template into the result. The template is now also estimated from a sample (be it simulated or a calibration sample), and the uncertainty associated to that can be substantial. We investigate different approaches for template fits, including the Barlow-Beeston and Barlow-Beeston-lite methods.\n", "\n", "**Note:** This work has been published: [<PERSON><PERSON>, <PERSON><PERSON>, Eur.Phys.J.C 82 (2022) 11, 1043](https://doi.org/10.1140/epjc/s10052-022-11019-z)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "from iminuit import Minuit\n", "from iminuit.cost import poisson_chi2, Template\n", "import numpy as np\n", "from scipy.stats import norm, truncexpon\n", "from scipy.optimize import minimize\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As a toy example, we generate a mixture of two components: a normally distributed signal and exponentially distributed background."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate(rng, nmc, truth, bins):\n", "    xe = np.linspace(0, 2, bins + 1)\n", "    b = np.diff(truncexpon(1, 0, 2).cdf(xe))\n", "    s = np.diff(norm(1, 0.1).cdf(xe))\n", "    n = rng.poisson(b * truth[0]) + rng.poisson(s * truth[1])\n", "    t = np.array([rng.poisson(b * nmc), rng.poisson(s * nmc)])\n", "    return xe, n, t\n", "\n", "\n", "rng = np.random.default_rng(1)\n", "truth = 750, 250\n", "xe, n, t = generate(rng, 500, truth, 15)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Data is visualized on the left-hand side. The templates are shown on the right-hand side. To show the effect of uncertainties in the template, this example intentially uses templates with poor statistical resolution."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"598.4875pt\" height=\"252.838125pt\" viewBox=\"0 0 598.4875 252.838125\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:42:55.149783</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 252.838125 \n", "L 598.4875 252.838125 \n", "L 598.4875 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 33.2875 228.96 \n", "L 286.**********.96 \n", "L 286.923864 7.2 \n", "L 33.2875 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 44.816426 228.96 \n", "L 44.816426 116.026667 \n", "L 60.188326 116.026667 \n", "L 60.188326 130.693333 \n", "L 75.560227 130.693333 \n", "L 75.560227 117.493333 \n", "L 90.932128 117.493333 \n", "L 90.932128 135.093333 \n", "L 106.304029 135.093333 \n", "L 106.304029 133.626667 \n", "L 121.67593 133.626667 \n", "L 121.67593 143.893333 \n", "L 137.047831 143.893333 \n", "L 137.047831 64.693333 \n", "L 152.419731 64.693333 \n", "L 152.419731 17.76 \n", "L 167.791632 17.76 \n", "L 167.791632 89.626667 \n", "L 183.163533 89.626667 \n", "L 183.163533 157.093333 \n", "L 198.535434 157.093333 \n", "L 198.535434 170.293333 \n", "L 213.907335 170.293333 \n", "L 213.907335 174.693333 \n", "L 229.279236 174.693333 \n", "L 229.279236 176.16 \n", "L 244.651136 176.16 \n", "L 244.651136 179.093333 \n", "L 260.023037 179.093333 \n", "L 260.023037 186.426667 \n", "L 275.394938 186.426667 \n", "L 275.394938 228.96 \n", "\" clip-path=\"url(#pf7cfb2b4e5)\" style=\"opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mc2d0f23a4d\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"44.816426\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(36.864863 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"102.461054\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(94.509491 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"160.105682\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(152.154119 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"217.75031\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(209.798747 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"275.394938\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(267.443376 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m4479b254b4\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"33.2875\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(19.925 232.759219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"33.2875\" y=\"199.626667\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(13.5625 203.425885) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"33.2875\" y=\"170.293333\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(13.5625 174.092552) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"33.2875\" y=\"140.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(13.5625 144.759219) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"33.2875\" y=\"111.626667\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(13.5625 115.425885) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"33.2875\" y=\"82.293333\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(7.2 86.092552) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"33.2875\" y=\"52.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 120 -->\n", "      <g transform=\"translate(7.2 56.759219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"33.2875\" y=\"23.626667\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 140 -->\n", "      <g transform=\"translate(7.2 27.425885) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 33.2875 228.96 \n", "L 33.2875 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 286.**********.96 \n", "L 286.923864 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 33.2875 228.96 \n", "L 286.**********.96 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 33.2875 7.2 \n", "L 286.923864 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 337.651136 228.96 \n", "L 591.2875 228.96 \n", "L 591.2875 7.2 \n", "L 337.651136 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 349.180062 228.96 \n", "L 349.180062 194.444825 \n", "L 364.551963 194.444825 \n", "L 364.551963 181.296187 \n", "L 379.923864 181.296187 \n", "L 379.923864 191.157665 \n", "L 395.295764 191.157665 \n", "L 395.295764 191.979455 \n", "L 410.667665 191.979455 \n", "L 410.667665 192.801245 \n", "L 426.039566 192.801245 \n", "L 426.039566 193.623035 \n", "L 441.411467 193.623035 \n", "L 441.411467 196.088405 \n", "L 456.783368 196.088405 \n", "L 456.783368 204.306304 \n", "L 472.155269 204.306304 \n", "L 472.155269 201.840934 \n", "L 487.527169 201.840934 \n", "L 487.527169 207.593463 \n", "L 502.89907 207.593463 \n", "L 502.89907 210.880623 \n", "L 518.270971 210.880623 \n", "L 518.270971 214.989572 \n", "L 533.642872 214.989572 \n", "L 533.642872 211.702412 \n", "L 549.014773 211.702412 \n", "L 549.014773 209.237043 \n", "L 564.386674 209.237043 \n", "L 564.386674 207.593463 \n", "L 579.758574 207.593463 \n", "L 579.758574 228.96 \n", "\" clip-path=\"url(#p5151778db8)\" style=\"fill: #1f77b4; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 349.180062 228.96 \n", "L 349.180062 228.96 \n", "L 364.551963 228.96 \n", "L 364.551963 228.96 \n", "L 379.**********.96 \n", "L 379.**********.96 \n", "L 395.295764 228.96 \n", "L 395.295764 228.96 \n", "L 410.667665 228.96 \n", "L 410.667665 228.13821 \n", "L 426.039566 228.13821 \n", "L 426.039566 222.385681 \n", "L 441.411467 222.385681 \n", "L 441.411467 141.028482 \n", "L 456.783368 141.028482 \n", "L 456.783368 17.76 \n", "L 472.155269 17.76 \n", "L 472.155269 137.741323 \n", "L 487.527169 137.741323 \n", "L 487.527169 218.276732 \n", "L 502.89907 218.276732 \n", "L 502.89907 228.96 \n", "L 518.270971 228.96 \n", "L 518.270971 228.96 \n", "L 533.642872 228.96 \n", "L 533.642872 228.96 \n", "L 549.014773 228.96 \n", "L 549.014773 228.96 \n", "L 564.386674 228.96 \n", "L 564.386674 228.96 \n", "L 579.758574 228.96 \n", "L 579.758574 228.96 \n", "\" clip-path=\"url(#p5151778db8)\" style=\"fill: #ff7f0e; opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"349.180062\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(341.228499 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"406.82469\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(398.873128 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"464.469318\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(456.517756 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"522.113946\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 1.5 -->\n", "      <g transform=\"translate(514.162384 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mc2d0f23a4d\" x=\"579.758574\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 2.0 -->\n", "      <g transform=\"translate(571.807012 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"337.651136\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(324.288636 232.759219) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"337.651136\" y=\"187.870506\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 50 -->\n", "      <g transform=\"translate(317.926136 191.669725) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"337.651136\" y=\"146.781012\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(311.563636 150.58023) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"337.651136\" y=\"105.691518\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 150 -->\n", "      <g transform=\"translate(311.563636 109.490736) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"337.651136\" y=\"64.602023\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(311.563636 68.401242) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m4479b254b4\" x=\"337.651136\" y=\"23.512529\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 250 -->\n", "      <g transform=\"translate(311.563636 27.311748) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 337.651136 228.96 \n", "L 337.651136 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 591.2875 228.96 \n", "L 591.2875 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 337.651136 228.96 \n", "L 591.2875 228.96 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 337.651136 7.2 \n", "L 591.2875 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pf7cfb2b4e5\">\n", "   <rect x=\"33.2875\" y=\"7.2\" width=\"253.636364\" height=\"221.76\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p5151778db8\">\n", "   <rect x=\"337.651136\" y=\"7.2\" width=\"253.636364\" height=\"221.76\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 1000x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 2, figsize=(10, 4), sharex=True)\n", "ax[0].stairs(n, xe, fill=True, color=\"k\", alpha=0.5, label=\"data\")\n", "for i, ti in enumerate(t):\n", "    ax[1].stairs(ti, xe, fill=True, alpha=0.5, label=f\"template {i}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Bootstrapping template uncertainties\n", "\n", "Bootstrapping is a general purpose technique to include uncertainties backed up by bootstrap theory, so it can be applied to this problem.\n", "We perform a standard fit and pretend that the templates have no uncertainties. Then, we repeat this fit many times with templates that are fluctuated around the actual values assuming a Poisson distribution.\n", "\n", "Here is the cost function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = -991.9 (χ²/ndof = -76.3) </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 119 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 0.000125 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 782 </td>\n", "        <td> 31 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 201 </td>\n", "        <td> 20 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 985 </td>\n", "        <td style=\"background-color:rgb(208,208,250);color:black\"> -0.2e3 <strong>(-0.322)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(208,208,250);color:black\"> -0.2e3 <strong>(-0.322)</strong> </td>\n", "        <td> 404 </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = -991.9 (χ²/ndof = -76.3)   │              Nfcn = 119              │\n", "│ EDM = 0.000125 (Goal: 0.0002)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │    782    │    31     │            │            │    0    │         │       │\n", "│ 1 │ x1   │    201    │    20     │            │            │    0    │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌────┬───────────────┐\n", "│    │     x0     x1 │\n", "├────┼───────────────┤\n", "│ x0 │    985 -0.2e3 │\n", "│ x1 │ -0.2e3    404 │\n", "└────┴───────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["def cost(yields):\n", "    mu = 0\n", "    for y, c in zip(yields, t):\n", "        mu += y * c / np.sum(c)\n", "    r = poisson_chi2(n, mu)\n", "    return r\n", "\n", "\n", "cost.errordef = Minuit.LEAST_SQUARES\n", "cost.ndata = np.prod(n.shape)\n", "\n", "starts = np.ones(2)\n", "m = Minuit(cost, starts)\n", "m.limits = (0, None)\n", "m.migrad()\n", "m.hesse()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The uncertainties reported by the fit correspond to the uncertainty in the data, but not the uncertainty in the templates. The chi2/ndof is also very large, since the uncertainties in the template are not considered in the fit.\n", "\n", "We bootstrap the templates 1000 times and compute the covariance of the fitted results."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["b = 100\n", "rng = np.random.default_rng(1)\n", "pars = []\n", "for ib in range(b):\n", "    ti = rng.poisson(t)\n", "\n", "    def cost(yields):\n", "        mu = 0\n", "        for y, c in zip(yields, ti):\n", "            mu += y * c / np.sum(c)\n", "        r = poisson_chi2(n, mu)\n", "        return r\n", "\n", "    mi = Minuit(cost, m.values[:])\n", "    mi.errordef = Minuit.LEAST_SQUARES\n", "    mi.limits = (0, None)\n", "    mi.strategy = 0\n", "    mi.migrad()\n", "    assert mi.valid\n", "    pars.append(mi.values[:])\n", "\n", "cov2 = np.cov(np.transpose(pars), ddof=1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We print the uncertainties from the different stages and the correlation between the two yields.\n", "\n", "To obtain the full error, we add the independent covariance matrices from the original fit and the bootstrap."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["fit\n", "  b 782 +- 31\n", "  s 201 +- 20\n", "  correlation -0.32\n", "bootstrap\n", "  b 782 +- 18\n", "  s 201 +- 18\n", "  correlation -1.00\n", "fit+bootstrap\n", "  b 782 +- 36\n", "  s 201 +- 27\n", "  correlation -0.53\n"]}], "source": ["cov1 = m.covariance\n", "\n", "for title, cov in zip((\"fit\", \"bootstrap\", \"fit+bootstrap\"), (cov1, cov2, cov1 + cov2)):\n", "    print(title)\n", "    for label, p, e in zip((\"b\", \"s\"), m.values, np.diag(cov) ** 0.5):\n", "        print(f\"  {label} {p:.0f} +- {e:.0f}\")\n", "    print(f\"  correlation {cov[0, 1] / np.prod(np.diag(cov)) ** 0.5:.2f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The bootstrapped template errors are much larger than the fit errors in this case, since the sample used to generate the templates is much smaller than the data sample.\n", "\n", "The bootstrapped errors for both yields are nearly equal (they become exactly equal if the template sample is large) and the correlation is close to -1 (and becomes exactly -1 in large samples). This is expected, since the data sample is fixed in each iteration. Under these conditions, a change in the templates can only increase the yield of one component at an equal loss for the other component."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Template fit with nuisance parameters\n", "\n", "As described in [<PERSON> and <PERSON>, Comput.Phys.Commun. 77 (1993) 219-228](https://doi.org/10.1016/0010-4655(93)90005-W), the correct treatment from first principles is to write down the likelihood function for this case, in which the observed values and unknown parameters are clearly stated. The insight is that the true contents of the bins for the templates are unknown and we need to introduce a nuisance parameter for each bin entry in the template. The combined likelihood for the problem is then combines the estimation of the template yields with the estimation of unknown templates.\n", "\n", "This problem can be handled straight-forwardly with Minuit, but it leads to the introduction of a large number of nuisance parameters, one for each entry in each template. We again write a cost function for this case (here a class for convenience).\n", "\n", "As a technical detail, it is necessary to increase the call limit in Migrad for the fit to fully converge, since the limit set by Minuit's default heuristic is too tight for this application."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = -2138 (χ²/ndof = -164.5) </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 1859 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 1.87e-05 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#FFF79A;color:black\"> SOME parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 780 </td>\n", "        <td> 40 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 199 </td>\n", "        <td> 26 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 2 </th>\n", "        <td> x2 </td>\n", "        <td> 47 </td>\n", "        <td> 5 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 3 </th>\n", "        <td> x3 </td>\n", "        <td> 50 </td>\n", "        <td> 5 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 4 </th>\n", "        <td> x4 </td>\n", "        <td> 48 </td>\n", "        <td> 5 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 5 </th>\n", "        <td> x5 </td>\n", "        <td> 43 </td>\n", "        <td> 4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 6 </th>\n", "        <td> x6 </td>\n", "        <td> 43 </td>\n", "        <td> 4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 7 </th>\n", "        <td> x7 </td>\n", "        <td> 39 </td>\n", "        <td> 4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 8 </th>\n", "        <td> x8 </td>\n", "        <td> 42 </td>\n", "        <td> 5 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 9 </th>\n", "        <td> x9 </td>\n", "        <td> 29 </td>\n", "        <td> 5 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 10 </th>\n", "        <td> x10 </td>\n", "        <td> 33 </td>\n", "        <td> 5 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 11 </th>\n", "        <td> x11 </td>\n", "        <td> 28 </td>\n", "        <td> 4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 12 </th>\n", "        <td> x12 </td>\n", "        <td> 24.6 </td>\n", "        <td> 3.3 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 13 </th>\n", "        <td> x13 </td>\n", "        <td> 21.5 </td>\n", "        <td> 3.0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 14 </th>\n", "        <td> x14 </td>\n", "        <td> 22.7 </td>\n", "        <td> 3.1 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 15 </th>\n", "        <td> x15 </td>\n", "        <td> 23.1 </td>\n", "        <td> 3.2 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 16 </th>\n", "        <td> x16 </td>\n", "        <td> 21.9 </td>\n", "        <td> 3.1 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 17 </th>\n", "        <td> x17 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 18 </th>\n", "        <td> x18 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 19 </th>\n", "        <td> x19 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 20 </th>\n", "        <td> x20 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 21 </th>\n", "        <td> x21 </td>\n", "        <td> 1.0 </td>\n", "        <td> 0.9 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 22 </th>\n", "        <td> x22 </td>\n", "        <td> 7.8 </td>\n", "        <td> 2.7 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 23 </th>\n", "        <td> x23 </td>\n", "        <td> 109 </td>\n", "        <td> 10 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 24 </th>\n", "        <td> x24 </td>\n", "        <td> 255 </td>\n", "        <td> 16 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 25 </th>\n", "        <td> x25 </td>\n", "        <td> 111 </td>\n", "        <td> 10 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 26 </th>\n", "        <td> x26 </td>\n", "        <td> 13 </td>\n", "        <td> 4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 27 </th>\n", "        <td> x27 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 28 </th>\n", "        <td> x28 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 29 </th>\n", "        <td> x29 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 30 </th>\n", "        <td> x30 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 31 </th>\n", "        <td> x31 </td>\n", "        <td> 0.0 </td>\n", "        <td> 0.4 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "        <th> x2 </th>\n", "        <th> x3 </th>\n", "        <th> x4 </th>\n", "        <th> x5 </th>\n", "        <th> x6 </th>\n", "        <th> x7 </th>\n", "        <th> x8 </th>\n", "        <th> x9 </th>\n", "        <th> x10 </th>\n", "        <th> x11 </th>\n", "        <th> x12 </th>\n", "        <th> x13 </th>\n", "        <th> x14 </th>\n", "        <th> x15 </th>\n", "        <th> x16 </th>\n", "        <th> x17 </th>\n", "        <th> x18 </th>\n", "        <th> x19 </th>\n", "        <th> x20 </th>\n", "        <th> x21 </th>\n", "        <th> x22 </th>\n", "        <th> x23 </th>\n", "        <th> x24 </th>\n", "        <th> x25 </th>\n", "        <th> x26 </th>\n", "        <th> x27 </th>\n", "        <th> x28 </th>\n", "        <th> x29 </th>\n", "        <th> x30 </th>\n", "        <th> x31 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 1.26e+03 </td>\n", "        <td style=\"background-color:rgb(183,183,250);color:black\"> -0.5e3 <strong>(-0.516)</strong> </td>\n", "        <td style=\"background-color:rgb(237,237,250);color:black\"> -17 <strong>(-0.104)</strong> </td>\n", "        <td style=\"background-color:rgb(236,236,250);color:black\"> -18 <strong>(-0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(236,236,250);color:black\"> -18 <strong>(-0.105)</strong> </td>\n", "        <td style=\"background-color:rgb(237,237,250);color:black\"> -16 <strong>(-0.100)</strong> </td>\n", "        <td style=\"background-color:rgb(238,238,250);color:black\"> -15 <strong>(-0.096)</strong> </td>\n", "        <td style=\"background-color:rgb(242,242,250);color:black\"> -10 <strong>(-0.065)</strong> </td>\n", "        <td style=\"background-color:rgb(250,218,218);color:black\"> 40 <strong>(0.213)</strong> </td>\n", "        <td style=\"background-color:rgb(250,200,200);color:black\"> 60 <strong>(0.331)</strong> </td>\n", "        <td style=\"background-color:rgb(250,217,217);color:black\"> 37 <strong>(0.221)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -2 <strong>(-0.012)</strong> </td>\n", "        <td style=\"background-color:rgb(240,240,250);color:black\"> -9 <strong>(-0.078)</strong> </td>\n", "        <td style=\"background-color:rgb(241,241,250);color:black\"> -8 <strong>(-0.073)</strong> </td>\n", "        <td style=\"background-color:rgb(240,240,250);color:black\"> -8 <strong>(-0.075)</strong> </td>\n", "        <td style=\"background-color:rgb(240,240,250);color:black\"> -8 <strong>(-0.075)</strong> </td>\n", "        <td style=\"background-color:rgb(240,240,250);color:black\"> -8 <strong>(-0.074)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0.5 <strong>(-0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(246,246,250);color:black\"> -3 <strong>(-0.028)</strong> </td>\n", "        <td style=\"background-color:rgb(241,241,250);color:black\"> -0.03e3 <strong>(-0.070)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 0.05e3 <strong>(0.085)</strong> </td>\n", "        <td style=\"background-color:rgb(246,246,250);color:black\"> -0.01e3 <strong>(-0.034)</strong> </td>\n", "        <td style=\"background-color:rgb(244,244,250);color:black\"> -6 <strong>(-0.050)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(183,183,250);color:black\"> -0.5e3 <strong>(-0.516)</strong> </td>\n", "        <td> 675 </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 17 <strong>(0.142)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 18 <strong>(0.145)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 18 <strong>(0.143)</strong> </td>\n", "        <td style=\"background-color:rgb(250,230,230);color:black\"> 16 <strong>(0.137)</strong> </td>\n", "        <td style=\"background-color:rgb(250,230,230);color:black\"> 15 <strong>(0.131)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 10 <strong>(0.088)</strong> </td>\n", "        <td style=\"background-color:rgb(212,212,250);color:black\"> -40 <strong>(-0.292)</strong> </td>\n", "        <td style=\"background-color:rgb(191,191,250);color:black\"> -60 <strong>(-0.453)</strong> </td>\n", "        <td style=\"background-color:rgb(211,211,250);color:black\"> -37 <strong>(-0.302)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 2 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 9 <strong>(0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 8 <strong>(0.100)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 8 <strong>(0.102)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 8 <strong>(0.103)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 8 <strong>(0.100)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0.5 <strong>(0.018)</strong> </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 3 <strong>(0.039)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 0.03e3 <strong>(0.095)</strong> </td>\n", "        <td style=\"background-color:rgb(235,235,250);color:black\"> -0.05e3 <strong>(-0.116)</strong> </td>\n", "        <td style=\"background-color:rgb(250,243,243);color:black\"> 0.01e3 <strong>(0.047)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 6 <strong>(0.068)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x2 </th>\n", "        <td style=\"background-color:rgb(237,237,250);color:black\"> -17 <strong>(-0.104)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 17 <strong>(0.142)</strong> </td>\n", "        <td> 22 </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 3 <strong>(0.150)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 3 <strong>(0.148)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.141)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.140)</strong> </td>\n", "        <td style=\"background-color:rgb(250,231,231);color:black\"> 3 <strong>(0.126)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 1 <strong>(0.036)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.024)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 2 <strong>(0.093)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.110)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.103)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.107)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 1 <strong>(0.104)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -2 <strong>(-0.023)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.009)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x3 </th>\n", "        <td style=\"background-color:rgb(236,236,250);color:black\"> -18 <strong>(-0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 18 <strong>(0.145)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 3 <strong>(0.150)</strong> </td>\n", "        <td> 23.3 </td>\n", "        <td style=\"background-color:rgb(250,227,227);color:black\"> 3 <strong>(0.152)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 3 <strong>(0.144)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.143)</strong> </td>\n", "        <td style=\"background-color:rgb(250,231,231);color:black\"> 3 <strong>(0.128)</strong> </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 1 <strong>(0.037)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.025)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 2 <strong>(0.095)</strong> </td>\n", "        <td style=\"background-color:rgb(250,233,233);color:black\"> 2 <strong>(0.112)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.105)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.108)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.109)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0.0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.020)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -2 <strong>(-0.024)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x4 </th>\n", "        <td style=\"background-color:rgb(236,236,250);color:black\"> -18 <strong>(-0.105)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 18 <strong>(0.143)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 3 <strong>(0.148)</strong> </td>\n", "        <td style=\"background-color:rgb(250,227,227);color:black\"> 3 <strong>(0.152)</strong> </td>\n", "        <td> 22.7 </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.143)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.141)</strong> </td>\n", "        <td style=\"background-color:rgb(250,231,231);color:black\"> 3 <strong>(0.127)</strong> </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 1 <strong>(0.037)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.024)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 2 <strong>(0.094)</strong> </td>\n", "        <td style=\"background-color:rgb(250,233,233);color:black\"> 2 <strong>(0.111)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.104)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.107)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.108)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.105)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0.0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -2 <strong>(-0.024)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x5 </th>\n", "        <td style=\"background-color:rgb(237,237,250);color:black\"> -16 <strong>(-0.100)</strong> </td>\n", "        <td style=\"background-color:rgb(250,230,230);color:black\"> 16 <strong>(0.137)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.141)</strong> </td>\n", "        <td style=\"background-color:rgb(250,228,228);color:black\"> 3 <strong>(0.144)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.143)</strong> </td>\n", "        <td> 19.9 </td>\n", "        <td style=\"background-color:rgb(250,230,230);color:black\"> 3 <strong>(0.135)</strong> </td>\n", "        <td style=\"background-color:rgb(250,232,232);color:black\"> 2 <strong>(0.121)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 1 <strong>(0.035)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.023)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.021)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 1 <strong>(0.090)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.099)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.102)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.103)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.100)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0.0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.018)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -2 <strong>(-0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.009)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x6 </th>\n", "        <td style=\"background-color:rgb(238,238,250);color:black\"> -15 <strong>(-0.096)</strong> </td>\n", "        <td style=\"background-color:rgb(250,230,230);color:black\"> 15 <strong>(0.131)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.140)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.143)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.141)</strong> </td>\n", "        <td style=\"background-color:rgb(250,230,230);color:black\"> 3 <strong>(0.135)</strong> </td>\n", "        <td> 19.9 </td>\n", "        <td style=\"background-color:rgb(250,232,232);color:black\"> 2 <strong>(0.120)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 1 <strong>(0.036)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -0 <strong>(-0.021)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 1 <strong>(0.089)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.105)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.098)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.101)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.102)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.099)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(246,246,250);color:black\"> -0.1 <strong>(-0.031)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.021)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x7 </th>\n", "        <td style=\"background-color:rgb(242,242,250);color:black\"> -10 <strong>(-0.065)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 10 <strong>(0.088)</strong> </td>\n", "        <td style=\"background-color:rgb(250,231,231);color:black\"> 3 <strong>(0.126)</strong> </td>\n", "        <td style=\"background-color:rgb(250,231,231);color:black\"> 3 <strong>(0.128)</strong> </td>\n", "        <td style=\"background-color:rgb(250,231,231);color:black\"> 3 <strong>(0.127)</strong> </td>\n", "        <td style=\"background-color:rgb(250,232,232);color:black\"> 2 <strong>(0.121)</strong> </td>\n", "        <td style=\"background-color:rgb(250,232,232);color:black\"> 2 <strong>(0.120)</strong> </td>\n", "        <td> 18.1 </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 1 <strong>(0.042)</strong> </td>\n", "        <td style=\"background-color:rgb(249,249,250);color:black\"> -0 <strong>(-0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 1 <strong>(0.029)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.082)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 1 <strong>(0.094)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 1 <strong>(0.088)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 1 <strong>(0.090)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 1 <strong>(0.091)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 1 <strong>(0.089)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(239,239,250);color:black\"> -1 <strong>(-0.086)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(249,249,250);color:black\"> -1 <strong>(-0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 1 <strong>(0.012)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.012)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x8 </th>\n", "        <td style=\"background-color:rgb(250,218,218);color:black\"> 40 <strong>(0.213)</strong> </td>\n", "        <td style=\"background-color:rgb(212,212,250);color:black\"> -40 <strong>(-0.292)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 1 <strong>(0.036)</strong> </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 1 <strong>(0.037)</strong> </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 1 <strong>(0.037)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 1 <strong>(0.035)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 1 <strong>(0.036)</strong> </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 1 <strong>(0.042)</strong> </td>\n", "        <td> 27.6 </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 4 <strong>(0.137)</strong> </td>\n", "        <td style=\"background-color:rgb(250,233,233);color:black\"> 3 <strong>(0.113)</strong> </td>\n", "        <td style=\"background-color:rgb(250,243,243);color:black\"> 1 <strong>(0.048)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.027)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.025)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.026)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.026)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.026)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(222,222,250);color:black\"> -12 <strong>(-0.217)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 9 <strong>(0.109)</strong> </td>\n", "        <td style=\"background-color:rgb(250,243,243);color:black\"> 2 <strong>(0.046)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x9 </th>\n", "        <td style=\"background-color:rgb(250,200,200);color:black\"> 60 <strong>(0.331)</strong> </td>\n", "        <td style=\"background-color:rgb(191,191,250);color:black\"> -60 <strong>(-0.453)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.024)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.025)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.024)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.023)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -0 <strong>(-0.021)</strong> </td>\n", "        <td style=\"background-color:rgb(249,249,250);color:black\"> -0 <strong>(-0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 4 <strong>(0.137)</strong> </td>\n", "        <td> 26.2 </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.138)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.018)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0.0 <strong>(-0.000)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 2 <strong>(0.036)</strong> </td>\n", "        <td style=\"background-color:rgb(242,242,250);color:black\"> -5 <strong>(-0.060)</strong> </td>\n", "        <td style=\"background-color:rgb(250,242,242);color:black\"> 3 <strong>(0.056)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 <strong>(-0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x10 </th>\n", "        <td style=\"background-color:rgb(250,217,217);color:black\"> 37 <strong>(0.221)</strong> </td>\n", "        <td style=\"background-color:rgb(211,211,250);color:black\"> -37 <strong>(-0.302)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.021)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 1 <strong>(0.029)</strong> </td>\n", "        <td style=\"background-color:rgb(250,233,233);color:black\"> 3 <strong>(0.113)</strong> </td>\n", "        <td style=\"background-color:rgb(250,229,229);color:black\"> 3 <strong>(0.138)</strong> </td>\n", "        <td> 22.2 </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 1 <strong>(0.038)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.015)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.015)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.005)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 2 <strong>(0.032)</strong> </td>\n", "        <td style=\"background-color:rgb(250,233,233);color:black\"> 8 <strong>(0.110)</strong> </td>\n", "        <td style=\"background-color:rgb(223,223,250);color:black\"> -10 <strong>(-0.204)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x11 </th>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -2 <strong>(-0.012)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 2 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 2 <strong>(0.093)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 2 <strong>(0.095)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 2 <strong>(0.094)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 1 <strong>(0.090)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 1 <strong>(0.089)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.082)</strong> </td>\n", "        <td style=\"background-color:rgb(250,243,243);color:black\"> 1 <strong>(0.048)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0 <strong>(0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 1 <strong>(0.038)</strong> </td>\n", "        <td> 13 </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.070)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.066)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.067)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.068)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.066)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 1 <strong>(0.011)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 1 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(230,230,250);color:black\"> -2 <strong>(-0.150)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x12 </th>\n", "        <td style=\"background-color:rgb(240,240,250);color:black\"> -9 <strong>(-0.078)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 9 <strong>(0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.110)</strong> </td>\n", "        <td style=\"background-color:rgb(250,233,233);color:black\"> 2 <strong>(0.112)</strong> </td>\n", "        <td style=\"background-color:rgb(250,233,233);color:black\"> 2 <strong>(0.111)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.105)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 1 <strong>(0.094)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.027)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.018)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.070)</strong> </td>\n", "        <td> 10.7 </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.077)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.079)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.080)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.078)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x13 </th>\n", "        <td style=\"background-color:rgb(241,241,250);color:black\"> -8 <strong>(-0.073)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 8 <strong>(0.100)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.103)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.105)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.104)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.099)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.098)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 1 <strong>(0.088)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.025)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.015)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.066)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.077)</strong> </td>\n", "        <td> 9.19 </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.074)</strong> </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.075)</strong> </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.073)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.005)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x14 </th>\n", "        <td style=\"background-color:rgb(240,240,250);color:black\"> -8 <strong>(-0.075)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 8 <strong>(0.102)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.108)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.107)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.102)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.101)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 1 <strong>(0.090)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.026)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.067)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.079)</strong> </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.074)</strong> </td>\n", "        <td> 9.75 </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.077)</strong> </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.075)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x15 </th>\n", "        <td style=\"background-color:rgb(240,240,250);color:black\"> -8 <strong>(-0.075)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 8 <strong>(0.103)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.107)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.109)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.108)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.103)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.102)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 1 <strong>(0.091)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.026)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.068)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.080)</strong> </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.075)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.077)</strong> </td>\n", "        <td> 9.93 </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.076)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x16 </th>\n", "        <td style=\"background-color:rgb(240,240,250);color:black\"> -8 <strong>(-0.074)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 8 <strong>(0.100)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 1 <strong>(0.104)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.106)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 2 <strong>(0.105)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.100)</strong> </td>\n", "        <td style=\"background-color:rgb(250,235,235);color:black\"> 1 <strong>(0.099)</strong> </td>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 1 <strong>(0.089)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.026)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.015)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 1 <strong>(0.066)</strong> </td>\n", "        <td style=\"background-color:rgb(250,238,238);color:black\"> 1 <strong>(0.078)</strong> </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.073)</strong> </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.075)</strong> </td>\n", "        <td style=\"background-color:rgb(250,239,239);color:black\"> 1 <strong>(0.076)</strong> </td>\n", "        <td> 9.37 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x17 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x18 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x19 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x20 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x21 </th>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -0.5 <strong>(-0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 0.5 <strong>(0.018)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0.0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0.0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0.0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(246,246,250);color:black\"> -0.1 <strong>(-0.031)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0.0 <strong>(-0.000)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0.99 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0.0 <strong>(-0.000)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.000)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x22 </th>\n", "        <td style=\"background-color:rgb(246,246,250);color:black\"> -3 <strong>(-0.028)</strong> </td>\n", "        <td style=\"background-color:rgb(250,244,244);color:black\"> 3 <strong>(0.039)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(239,239,250);color:black\"> -1 <strong>(-0.086)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.005)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.005)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.006)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 </td>\n", "        <td> 7.53 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x23 </th>\n", "        <td style=\"background-color:rgb(241,241,250);color:black\"> -0.03e3 <strong>(-0.070)</strong> </td>\n", "        <td style=\"background-color:rgb(250,236,236);color:black\"> 0.03e3 <strong>(0.095)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.020)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.018)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(222,222,250);color:black\"> -12 <strong>(-0.217)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 2 <strong>(0.036)</strong> </td>\n", "        <td style=\"background-color:rgb(250,245,245);color:black\"> 2 <strong>(0.032)</strong> </td>\n", "        <td style=\"background-color:rgb(250,247,247);color:black\"> 1 <strong>(0.019)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.002)</strong> </td>\n", "        <td> 103 </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.028)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x24 </th>\n", "        <td style=\"background-color:rgb(250,237,237);color:black\"> 0.05e3 <strong>(0.085)</strong> </td>\n", "        <td style=\"background-color:rgb(235,235,250);color:black\"> -0.05e3 <strong>(-0.116)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -2 <strong>(-0.023)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -2 <strong>(-0.024)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -2 <strong>(-0.024)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -2 <strong>(-0.022)</strong> </td>\n", "        <td style=\"background-color:rgb(247,247,250);color:black\"> -1 <strong>(-0.021)</strong> </td>\n", "        <td style=\"background-color:rgb(249,249,250);color:black\"> -1 <strong>(-0.008)</strong> </td>\n", "        <td style=\"background-color:rgb(250,234,234);color:black\"> 9 <strong>(0.109)</strong> </td>\n", "        <td style=\"background-color:rgb(242,242,250);color:black\"> -5 <strong>(-0.060)</strong> </td>\n", "        <td style=\"background-color:rgb(250,233,233);color:black\"> 8 <strong>(0.110)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 1 <strong>(0.011)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(248,248,250);color:black\"> -1 <strong>(-0.017)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0.0 <strong>(-0.000)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(250,246,246);color:black\"> 0 <strong>(0.028)</strong> </td>\n", "        <td> 244 </td>\n", "        <td style=\"background-color:rgb(250,243,243);color:black\"> 0.01e3 <strong>(0.045)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 <strong>(-0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x25 </th>\n", "        <td style=\"background-color:rgb(246,246,250);color:black\"> -0.01e3 <strong>(-0.034)</strong> </td>\n", "        <td style=\"background-color:rgb(250,243,243);color:black\"> 0.01e3 <strong>(0.047)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.009)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.009)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 1 <strong>(0.012)</strong> </td>\n", "        <td style=\"background-color:rgb(250,243,243);color:black\"> 2 <strong>(0.046)</strong> </td>\n", "        <td style=\"background-color:rgb(250,242,242);color:black\"> 3 <strong>(0.056)</strong> </td>\n", "        <td style=\"background-color:rgb(223,223,250);color:black\"> -10 <strong>(-0.204)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 1 <strong>(0.016)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.007)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 <strong>(0.000)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(250,243,243);color:black\"> 0.01e3 <strong>(0.045)</strong> </td>\n", "        <td> 103 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x26 </th>\n", "        <td style=\"background-color:rgb(244,244,250);color:black\"> -6 <strong>(-0.050)</strong> </td>\n", "        <td style=\"background-color:rgb(250,240,240);color:black\"> 6 <strong>(0.068)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.014)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.013)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.012)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.004)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 <strong>(-0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.003)</strong> </td>\n", "        <td style=\"background-color:rgb(230,230,250);color:black\"> -2 <strong>(-0.150)</strong> </td>\n", "        <td style=\"background-color:rgb(250,248,248);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,249,249);color:black\"> 0 <strong>(0.010)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0.0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.001)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 <strong>(-0.002)</strong> </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 <strong>(0.001)</strong> </td>\n", "        <td> 13.2 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x27 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x28 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x29 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x30 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x31 </th>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> -0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td style=\"background-color:rgb(250,250,250);color:black\"> 0 </td>\n", "        <td> 0 </td>\n", "    </tr>\n", "</table><?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"360pt\" height=\"288pt\" viewBox=\"0 0 360 288\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:42:55.875327</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 288 \n", "L 360 288 \n", "L 360 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "L 29.08774 3.00024 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.992832 206.066313 \n", "L 43.992832 170.42864 \n", "L 63.866288 170.42864 \n", "L 63.866288 123.315763 \n", "L 83.739743 123.315763 \n", "L 83.739743 158.601394 \n", "L 103.613199 158.601394 \n", "L 103.613199 161.55471 \n", "L 123.486655 161.55471 \n", "L 123.486655 163.769891 \n", "L 143.360111 163.769891 \n", "L 143.360111 161.543943 \n", "L 163.233566 161.543943 \n", "L 163.233566 97.126091 \n", "L 183.107022 97.126091 \n", "L 183.107022 15.634592 \n", "L 202.980478 15.634592 \n", "L 202.980478 114.861171 \n", "L 222.853934 114.861171 \n", "L 222.853934 208.539922 \n", "L 242.727389 208.539922 \n", "L 242.727389 230.341273 \n", "L 262.600845 230.341273 \n", "L 262.600845 245.648626 \n", "L 282.474301 245.648626 \n", "L 282.474301 233.387286 \n", "L 302.347757 233.387286 \n", "L 302.347757 224.268813 \n", "L 322.221212 224.268813 \n", "L 322.221212 218.21979 \n", "L 342.094668 218.21979 \n", "L 342.094668 246.259361 \n", "L 342.094668 246.259361 \n", "L 342.094668 246.259361 \n", "L 322.221212 246.259361 \n", "L 322.221212 251.208362 \n", "L 302.347757 251.208362 \n", "L 302.347757 258.586926 \n", "L 282.474301 258.586926 \n", "L 282.474301 268.321635 \n", "L 262.600845 268.321635 \n", "L 262.600845 256.133927 \n", "L 242.727389 256.133927 \n", "L 242.727389 237.064004 \n", "L 222.853934 237.064004 \n", "L 222.853934 149.959518 \n", "L 202.980478 149.959518 \n", "L 202.980478 53.699848 \n", "L 183.107022 53.699848 \n", "L 183.107022 135.009273 \n", "L 163.233566 135.009273 \n", "L 163.233566 197.836475 \n", "L 143.360111 197.836475 \n", "L 143.360111 200.275098 \n", "L 123.486655 200.275098 \n", "L 123.486655 198.443206 \n", "L 103.613199 198.443206 \n", "L 103.613199 195.89751 \n", "L 83.739743 195.89751 \n", "L 83.739743 165.194993 \n", "L 63.866288 165.194993 \n", "L 63.866288 206.066313 \n", "L 43.992832 206.066313 \n", "L 43.992832 206.066313 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mc452463483\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"43.992832\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(32.860019 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"81.255561\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(70.122749 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"118.518291\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(107.385478 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"155.78102\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(144.648208 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"193.04375\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(181.910937 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"230.30648\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.25 -->\n", "      <g transform=\"translate(219.173667 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"267.569209\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.50 -->\n", "      <g transform=\"translate(256.436397 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"304.831939\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.75 -->\n", "      <g transform=\"translate(293.699126 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mc452463483\" x=\"342.094668\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.00 -->\n", "      <g transform=\"translate(330.961856 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"ma98d5ec4c0\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#ma98d5ec4c0\" x=\"29.08774\" y=\"267.463579\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(9.36274 271.262797) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#ma98d5ec4c0\" x=\"29.08774\" y=\"231.200422\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(9.36274 234.99964) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#ma98d5ec4c0\" x=\"29.08774\" y=\"194.937265\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(9.36274 198.736483) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#ma98d5ec4c0\" x=\"29.08774\" y=\"158.674108\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(9.36274 162.473326) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#ma98d5ec4c0\" x=\"29.08774\" y=\"122.410951\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(3.00024 126.21017) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#ma98d5ec4c0\" x=\"29.08774\" y=\"86.147794\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 120 -->\n", "      <g transform=\"translate(3.00024 89.947013) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#ma98d5ec4c0\" x=\"29.08774\" y=\"49.884637\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 140 -->\n", "      <g transform=\"translate(3.00024 53.683856) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#ma98d5ec4c0\" x=\"29.08774\" y=\"13.62148\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 160 -->\n", "      <g transform=\"translate(3.00024 17.420699) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 53.92956 180.023977 \n", "L 53.92956 148.203186 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 73.803015 197.086496 \n", "L 73.803015 167.403823 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 93.676471 181.733483 \n", "L 93.676471 150.119995 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 113.549927 202.189896 \n", "L 113.549927 173.179371 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 133.423383 200.489621 \n", "L 133.423383 171.25333 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 153.296838 212.372179 \n", "L 153.296838 184.754982 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 173.170294 119.841716 \n", "L 173.170294 81.464398 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 193.04375 64.3899 \n", "L 193.04375 20.874111 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 212.917206 149.149217 \n", "L 212.917206 113.804263 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 232.790662 227.574106 \n", "L 232.790662 202.189896 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 252.664117 242.667839 \n", "L 252.664117 219.733005 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 272.537573 247.668904 \n", "L 272.537573 225.610887 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 292.411029 249.332 \n", "L 292.411029 227.574106 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 312.284485 252.651805 \n", "L 312.284485 231.506933 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 332.15794 260.909312 \n", "L 332.15794 241.381004 \n", "\" clip-path=\"url(#pb844d1bb67)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <defs>\n", "     <path id=\"m6d5c7a81d8\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pb844d1bb67)\">\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"53.92956\" y=\"164.113581\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"73.803015\" y=\"182.24516\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"93.676471\" y=\"165.926739\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"113.549927\" y=\"187.684633\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"133.423383\" y=\"185.871475\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"153.296838\" y=\"198.56358\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"173.170294\" y=\"100.653057\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"193.04375\" y=\"42.632006\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"212.917206\" y=\"131.47674\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"232.790662\" y=\"214.882001\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"252.664117\" y=\"231.200422\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"272.537573\" y=\"236.639895\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"292.411029\" y=\"238.453053\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"312.284485\" y=\"242.079369\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m6d5c7a81d8\" x=\"332.15794\" y=\"251.145158\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 29.08774 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 29.08774 3.00024 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pb844d1bb67\">\n", "   <rect x=\"29.08774\" y=\"3.00024\" width=\"327.91202\" height=\"265.321395\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = -2138 (χ²/ndof = -164.5)   │             Nfcn = 1859              │\n", "│ EDM = 1.87e-05 (Goal: 0.0002)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│     SOME parameters at limit     │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │    780    │    40     │            │            │    0    │         │       │\n", "│ 1 │ x1   │    199    │    26     │            │            │    0    │         │       │\n", "│ 2 │ x2   │    47     │     5     │            │            │    0    │         │       │\n", "│ 3 │ x3   │    50     │     5     │            │            │    0    │         │       │\n", "│ 4 │ x4   │    48     │     5     │            │            │    0    │         │       │\n", "│ 5 │ x5   │    43     │     4     │            │            │    0    │         │       │\n", "│ 6 │ x6   │    43     │     4     │            │            │    0    │         │       │\n", "│ 7 │ x7   │    39     │     4     │            │            │    0    │         │       │\n", "│ 8 │ x8   │    42     │     5     │            │            │    0    │         │       │\n", "│ 9 │ x9   │    29     │     5     │            │            │    0    │         │       │\n", "│ 10│ x10  │    33     │     5     │            │            │    0    │         │       │\n", "│ 11│ x11  │    28     │     4     │            │            │    0    │         │       │\n", "│ 12│ x12  │   24.6    │    3.3    │            │            │    0    │         │       │\n", "│ 13│ x13  │   21.5    │    3.0    │            │            │    0    │         │       │\n", "│ 14│ x14  │   22.7    │    3.1    │            │            │    0    │         │       │\n", "│ 15│ x15  │   23.1    │    3.2    │            │            │    0    │         │       │\n", "│ 16│ x16  │   21.9    │    3.1    │            │            │    0    │         │       │\n", "│ 17│ x17  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "│ 18│ x18  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "│ 19│ x19  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "│ 20│ x20  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "│ 21│ x21  │    1.0    │    0.9    │            │            │    0    │         │       │\n", "│ 22│ x22  │    7.8    │    2.7    │            │            │    0    │         │       │\n", "│ 23│ x23  │    109    │    10     │            │            │    0    │         │       │\n", "│ 24│ x24  │    255    │    16     │            │            │    0    │         │       │\n", "│ 25│ x25  │    111    │    10     │            │            │    0    │         │       │\n", "│ 26│ x26  │    13     │     4     │            │            │    0    │         │       │\n", "│ 27│ x27  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "│ 28│ x28  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "│ 29│ x29  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "│ 30│ x30  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "│ 31│ x31  │    0.0    │    0.4    │            │            │    0    │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌─────┬─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐\n", "│     │       x0       x1       x2       x3       x4       x5       x6       x7       x8       x9      x10      x11      x12      x13      x14      x15      x16      x17      x18      x19      x20      x21      x22      x23      x24      x25      x26      x27      x28      x29      x30      x31 │\n", "├─────┼─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤\n", "│  x0 │ 1.26e+03   -0.5e3      -17      -18      -18      -16      -15      -10       40       60       37       -2       -9       -8       -8       -8       -8       -0       -0       -0       -0     -0.5       -3  -0.03e3   0.05e3  -0.01e3       -6       -0       -0       -0       -0       -0 │\n", "│  x1 │   -0.5e3      675       17       18       18       16       15       10      -40      -60      -37        2        9        8        8        8        8        0        0        0        0      0.5        3   0.03e3  -0.05e3   0.01e3        6        0        0        0        0        0 │\n", "│  x2 │      -17       17       22        3        3        3        3        3        1       -1        0        2        2        1        2        2        1       -0        0        0        0        0        0        1       -2        0        0        0        0        0        0        0 │\n", "│  x3 │      -18       18        3     23.3        3        3        3        3        1       -1        1        2        2        2        2        2        2        0       -0        0        0      0.0        0        1       -2        0        0        0        0        0        0        0 │\n", "│  x4 │      -18       18        3        3     22.7        3        3        3        1       -1        0        2        2        2        2        2        2        0        0       -0        0      0.0        0        1       -2        0        0        0        0        0        0        0 │\n", "│  x5 │      -16       16        3        3        3     19.9        3        2        1       -1        0        1        2        1        1        1        1        0        0        0       -0      0.0        0        1       -2        0        0        0        0        0        0        0 │\n", "│  x6 │      -15       15        3        3        3        3     19.9        2        1       -0        0        1        2        1        1        1        1        0        0        0        0     -0.1        0        1       -1        0        0        0        0        0        0        0 │\n", "│  x7 │      -10       10        3        3        3        2        2     18.1        1       -0        1        1        1        1        1        1        1        0        0        0        0      0.0       -1        1       -1        1        0        0        0        0        0        0 │\n", "│  x8 │       40      -40        1        1        1        1        1        1     27.6        4        3        1        0        0        0        0        0       -0        0        0        0      0.0        0      -12        9        2        0       -0       -0        0        0        0 │\n", "│  x9 │       60      -60       -1       -1       -1       -1       -0       -0        4     26.2        3        0       -0       -0       -0       -0       -0       -0        0       -0       -0     -0.0        0        2       -5        3       -0       -0       -0       -0        0        0 │\n", "│ x10 │       37      -37        0        1        0        0        0        1        3        3     22.2        1        0        0        0        0        0       -0        0        0        0      0.0        0        2        8      -10        0       -0       -0       -0        0        0 │\n", "│ x11 │       -2        2        2        2        2        1        1        1        1        0        1       13        1        1        1        1        1        0        0        0        0        0        0        1        1        1       -2        0        0        0        0        0 │\n", "│ x12 │       -9        9        2        2        2        2        2        1        0       -0        0        1     10.7        1        1        1        1        0        0        0        0      0.0        0        0       -1        0        0       -0        0        0        0        0 │\n", "│ x13 │       -8        8        1        2        2        1        1        1        0       -0        0        1        1     9.19        1        1        1        0        0        0        0      0.0        0        0       -1        0        0        0       -0        0        0        0 │\n", "│ x14 │       -8        8        2        2        2        1        1        1        0       -0        0        1        1        1     9.75        1        1        0        0        0        0      0.0        0        0       -1        0        0        0        0       -0        0        0 │\n", "│ x15 │       -8        8        2        2        2        1        1        1        0       -0        0        1        1        1        1     9.93        1        0        0        0        0      0.0        0        0       -1        0        0        0        0        0       -0        0 │\n", "│ x16 │       -8        8        1        2        2        1        1        1        0       -0        0        1        1        1        1        1     9.37        0        0        0        0      0.0        0        0       -1        0        0        0        0        0        0       -0 │\n", "│ x17 │       -0        0       -0        0        0        0        0        0       -0       -0       -0        0        0        0        0        0        0        0        0        0        0        0        0        0       -0       -0        0        0        0        0        0        0 │\n", "│ x18 │       -0        0        0       -0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0 │\n", "│ x19 │       -0        0        0        0       -0        0        0        0        0       -0        0        0        0        0        0        0        0        0        0        0        0        0        0        0       -0        0        0        0        0        0        0        0 │\n", "│ x20 │       -0        0        0        0        0       -0        0        0        0       -0        0        0        0        0        0        0        0        0        0        0        0        0        0        0       -0        0        0        0        0        0        0        0 │\n", "│ x21 │     -0.5      0.5        0      0.0      0.0      0.0     -0.1      0.0      0.0     -0.0      0.0        0      0.0      0.0      0.0      0.0      0.0        0        0        0        0     0.99      0.0      0.0     -0.0      0.0      0.0        0        0        0        0        0 │\n", "│ x22 │       -3        3        0        0        0        0        0       -1        0        0        0        0        0        0        0        0        0        0        0        0        0      0.0     7.53        0        0        0        0        0        0        0        0        0 │\n", "│ x23 │  -0.03e3   0.03e3        1        1        1        1        1        1      -12        2        2        1        0        0        0        0        0        0        0        0        0      0.0        0      103        0        0        0        0        0        0        0        0 │\n", "│ x24 │   0.05e3  -0.05e3       -2       -2       -2       -2       -1       -1        9       -5        8        1       -1       -1       -1       -1       -1       -0        0       -0       -0     -0.0        0        0      244   0.01e3       -0       -0       -0       -0       -0        0 │\n", "│ x25 │  -0.01e3   0.01e3        0        0        0        0        0        1        2        3      -10        1        0        0        0        0        0       -0        0        0        0      0.0        0        0   0.01e3      103        0       -0       -0       -0        0        0 │\n", "│ x26 │       -6        6        0        0        0        0        0        0        0       -0        0       -2        0        0        0        0        0        0        0        0        0      0.0        0        0       -0        0     13.2        0        0        0        0        0 │\n", "│ x27 │       -0        0        0        0        0        0        0        0       -0       -0       -0        0       -0        0        0        0        0        0        0        0        0        0        0        0       -0       -0        0        0        0        0        0        0 │\n", "│ x28 │       -0        0        0        0        0        0        0        0       -0       -0       -0        0        0       -0        0        0        0        0        0        0        0        0        0        0       -0       -0        0        0        0        0        0        0 │\n", "│ x29 │       -0        0        0        0        0        0        0        0        0       -0       -0        0        0        0       -0        0        0        0        0        0        0        0        0        0       -0       -0        0        0        0        0        0        0 │\n", "│ x30 │       -0        0        0        0        0        0        0        0        0        0        0        0        0        0        0       -0        0        0        0        0        0        0        0        0       -0        0        0        0        0        0        0        0 │\n", "│ x31 │       -0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0       -0        0        0        0        0        0        0        0        0        0        0        0        0        0        0        0 │\n", "└─────┴─────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["class BB:\n", "    def __init__(self, xe, n, t):\n", "        self.xe = xe\n", "        self.data = n, t\n", "\n", "    def _pred(self, par):\n", "        bins = len(self.xe) - 1\n", "        yields = par[:2]\n", "        nuisances = np.array(par[2:])\n", "        b = nuisances[:bins]\n", "        s = nuisances[bins:]\n", "        mu = 0\n", "        for y, c in zip(yields, (b, s)):\n", "            mu += y * np.array(c) / np.sum(c)\n", "        return mu, b, s\n", "\n", "    def __call__(self, par):\n", "        n, t = self.data\n", "        mu, b, s = self._pred(par)\n", "        r = poisson_chi2(n, mu) + poisson_chi2(t[0], b) + poisson_chi2(t[1], s)\n", "        return r\n", "\n", "    @property\n", "    def ndata(self):\n", "        n, t = self.data\n", "        return np.prod(n.shape) + np.prod(t.shape)\n", "\n", "    def visualize(self, args):\n", "        n, t = self.data\n", "        ne = n**0.5\n", "        xe = self.xe\n", "        cx = 0.5 * (xe[1:] + xe[:-1])\n", "        plt.errorbar(cx, n, ne, fmt=\"ok\")\n", "        mu = 0\n", "        mu_var = 0\n", "        for y, c in zip(args[:2], t):\n", "            f = 1 / np.sum(c)\n", "            mu += c * y * f\n", "            mu_var += c * (f * y) ** 2\n", "        mu_err = mu_var**0.5\n", "        plt.stairs(mu + mu_err, xe, baseline=mu - mu_err, fill=True, color=\"C0\")\n", "        # plt.stairs(mu, xe, color=\"C0\")\n", "\n", "\n", "m1 = Minuit(BB(xe, n, t), np.concatenate([truth, t[0], t[1]]))\n", "m1.limits = (0, None)\n", "m1.migrad(ncall=100000)\n", "m1.hesse()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The result of this fit is comparable to the bootstrap method for this example, but the chi2/ndof is now reasonable and the uncertainties are correct without further work. This method should perform better than the bootstrap method, if the count per bin in the templates is small.\n", "\n", "Another advantage is of this technique is that one can profile over the likelihood to obtain a 2D confidence regions, which is not possible with the bootstrap technique."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"411.285625pt\" height=\"310.86825pt\" viewBox=\"0 0 411.**********.86825\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:42:57.151545</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 310.86825 \n", "L 411.**********.86825 \n", "L 411.285625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 46.**********.312 \n", "L 404.**********.312 \n", "L 404.085625 7.2 \n", "L 46.965625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m9436b8c946\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9436b8c946\" x=\"74.094476\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 740 -->\n", "      <g transform=\"translate(64.550726 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m9436b8c946\" x=\"140.6446\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 760 -->\n", "      <g transform=\"translate(131.10085 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m9436b8c946\" x=\"207.194724\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 780 -->\n", "      <g transform=\"translate(197.650974 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m9436b8c946\" x=\"273.744848\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(264.201098 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m9436b8c946\" x=\"340.294972\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 820 -->\n", "      <g transform=\"translate(330.751222 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_6\">\n", "     <!-- x0 -->\n", "     <g transform=\"translate(219.385 301.588562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-30\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_6\">\n", "      <defs>\n", "       <path id=\"m6e16e9c156\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m6e16e9c156\" x=\"46.965625\" y=\"271.501349\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 160 -->\n", "      <g transform=\"translate(20.878125 275.300568) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m6e16e9c156\" x=\"46.965625\" y=\"237.704197\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 170 -->\n", "      <g transform=\"translate(20.878125 241.503416) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m6e16e9c156\" x=\"46.965625\" y=\"203.907045\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 180 -->\n", "      <g transform=\"translate(20.878125 207.706264) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m6e16e9c156\" x=\"46.965625\" y=\"170.109893\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 190 -->\n", "      <g transform=\"translate(20.878125 173.909112) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m6e16e9c156\" x=\"46.965625\" y=\"136.312741\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(20.878125 140.11196) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m6e16e9c156\" x=\"46.965625\" y=\"102.515589\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 210 -->\n", "      <g transform=\"translate(20.878125 106.314808) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m6e16e9c156\" x=\"46.965625\" y=\"68.718437\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 220 -->\n", "      <g transform=\"translate(20.878125 72.517656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m6e16e9c156\" x=\"46.965625\" y=\"34.921285\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 230 -->\n", "      <g transform=\"translate(20.878125 38.720503) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- x1 -->\n", "     <g transform=\"translate(14.798438 146.396625) rotate(-90) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-78\"/>\n", "      <use xlink:href=\"#DejaVuSans-31\" x=\"59.179688\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"ContourSet_1\">\n", "    <path d=\"M 119.774006 192.535551 \n", "L 125.90749 197.918145 \n", "L 135.26859 204.455969 \n", "L 144.361039 211.368083 \n", "L 154.079778 217.336623 \n", "L 163.42367 223.533007 \n", "L 173.275281 228.856636 \n", "L 182.962487 234.480566 \n", "L 193.083729 239.251216 \n", "L 203.073546 244.298712 \n", "L 213.438402 248.494183 \n", "L 223.699778 252.944205 \n", "L 234.286065 256.523986 \n", "L 244.812585 260.341576 \n", "L 255.618417 263.244541 \n", "L 266.370674 266.350333 \n", "L 277.352675 268.470409 \n", "L 288.310909 270.753399 \n", "L 299.436428 271.93701 \n", "L 310.556404 273.167935 \n", "L 321.741325 273.312 \n", "L 335.806076 273.166468 \n", "L 349.654319 270.665329 \n", "L 363.242197 266.992054 \n", "L 369.87662 264.571973 \n", "L 376.092872 261.195506 \n", "L 382.432094 257.43655 \n", "L 387.898429 252.460549 \n", "L 393.29005 247.339765 \n", "L 397.000501 240.858368 \n", "L 400.616555 234.339598 \n", "L 402.422458 227.085681 \n", "L 404.054546 219.810699 \n", "L 404.085625 212.34931 \n", "L 403.664403 206.489671 \n", "L 403.986059 200.623524 \n", "L 401.884939 195.138413 \n", "L 400.756449 189.365459 \n", "L 398.764281 183.854841 \n", "L 397.840186 178.059999 \n", "L 392.552743 167.679933 \n", "L 387.852123 157.090646 \n", "L 381.266897 147.593753 \n", "L 375.3415 137.668931 \n", "L 367.819527 128.930892 \n", "L 360.952579 119.836378 \n", "L 352.933765 111.772789 \n", "L 345.318517 103.3254 \n", "L 336.796686 95.824329 \n", "L 328.584239 87.985061 \n", "L 319.626016 81.039296 \n", "L 310.911982 73.783749 \n", "L 301.564532 67.393075 \n", "L 292.522183 60.795005 \n", "L 282.942391 55.032223 \n", "L 273.521667 49.006122 \n", "L 263.627156 43.822188 \n", "L 253.861843 38.390263 \n", "L 243.676338 33.823262 \n", "L 233.594225 29.024634 \n", "L 223.140937 25.130596 \n", "L 212.742547 21.014326 \n", "L 202.021183 17.866815 \n", "L 191.35358 14.530968 \n", "L 180.420131 12.249806 \n", "L 169.508933 9.815429 \n", "L 158.406348 8.55853 \n", "L 147.310234 7.205595 \n", "L 136.134454 7.204527 \n", "L 128.473692 7.2 \n", "L 120.846176 7.924138 \n", "L 113.219673 8.688174 \n", "L 105.718673 10.282649 \n", "L 98.217308 11.970239 \n", "L 90.992584 14.625543 \n", "L 83.808125 17.416714 \n", "L 77.157582 21.342394 \n", "L 70.339271 25.534904 \n", "L 64.587531 31.139189 \n", "L 58.731648 36.707964 \n", "L 54.868518 43.848788 \n", "L 50.842917 50.88205 \n", "L 48.963826 58.790406 \n", "L 47.034696 66.663936 \n", "L 47.086769 74.777439 \n", "L 47.65447 80.720136 \n", "L 46.965625 86.649605 \n", "L 49.428705 92.09151 \n", "L 50.395151 97.999929 \n", "L 52.610002 103.537843 \n", "L 53.197354 109.485389 \n", "L 58.823552 119.846918 \n", "L 62.171589 124.761947 \n", "L 63.572743 130.566776 \n", "L 70.605848 139.902435 \n", "L 76.764341 149.832298 \n", "L 84.740434 158.323391 \n", "L 91.927517 167.285808 \n", "L 97.980428 172.827723 \n", "\" clip-path=\"url(#p69a9a68413)\" style=\"fill: none; stroke: #440154; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 46.**********.312 \n", "L 46.965625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 404.**********.312 \n", "L 404.085625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 46.**********.312 \n", "L 404.**********.312 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 46.965625 7.2 \n", "L 404.085625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_16\">\n", "    <g clip-path=\"url(#p69a9a68413)\">\n", "     <!-- 0.68 -->\n", "     <g style=\"fill: #440154\" transform=\"translate(98.262317 177.815713) rotate(-317.808702) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-30\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-38\" x=\"159.033203\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p69a9a68413\">\n", "   <rect x=\"46.965625\" y=\"7.2\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["m1.draw_mncontour(\"x0\", \"x1\");"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Before moving on, we briefly explore a possible refinement of the previous method, which is to hide the nuisance parameters from <PERSON><PERSON> with a nested fit. It turns out that this technique is not an improvement, but it is useful to show that explicitly.\n", "\n", "The idea is to construct an outer cost function, which only has the yields as parameters. Inside the outer cost function, the best nuisance parameters are found for the current yields with an inner cost function. Technically, this is achieved by calling a minimizer on the inner cost function at every call to the outer cost function.\n", "\n", "Technical detail: It is important here to adjust Minuit's expectation of how accurate the cost function is computed. Usually, Minuit performs its internal calculations under the assumption that the cost function is accurate to machine precision. This is usually not the case when a minimizer is used internally to optimize the inner function. We perform the internal minimization with SciPy, which allows us to set the tolerance. We set it here to 1e-8, which is sufficient for this problem and saves a bit of time on the internal minimisation. We then instruct <PERSON><PERSON> to expect only this precision."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = -2140 (χ²/ndof = -164.6) </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 55 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 3.28e-05 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 784 </td>\n", "        <td> 35 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 199 </td>\n", "        <td> 26 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 1.22e+03 </td>\n", "        <td style=\"background-color:rgb(184,184,250);color:black\"> -0.5e3 <strong>(-0.506)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(184,184,250);color:black\"> -0.5e3 <strong>(-0.506)</strong> </td>\n", "        <td> 665 </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = -2140 (χ²/ndof = -164.6)   │              Nfcn = 55               │\n", "│ EDM = 3.28e-05 (Goal: 0.0002)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │    784    │    35     │            │            │    0    │         │       │\n", "│ 1 │ x1   │    199    │    26     │            │            │    0    │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌────┬───────────────────┐\n", "│    │       x0       x1 │\n", "├────┼───────────────────┤\n", "│ x0 │ 1.22e+03   -0.5e3 │\n", "│ x1 │   -0.5e3      665 │\n", "└────┴───────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["precision = 1e-8\n", "\n", "\n", "def cost(yields):\n", "    bins = len(n)\n", "\n", "    def inner(nuisance):\n", "        b = nuisance[:bins]\n", "        s = nuisance[bins:]\n", "        mu = 0\n", "        for y, c in zip(yields, (b, s)):\n", "            mu += y * c / np.sum(c)\n", "        r = poisson_chi2(n, mu) + poisson_chi2(t[0], b) + poisson_chi2(t[1], s)\n", "        return r\n", "\n", "    bounds = np.zeros((2 * bins, 2))\n", "    bounds[:, 1] = np.inf\n", "    r = minimize(inner, np.ravel(t), bounds=bounds, tol=precision)\n", "    assert r.success\n", "    return r.fun\n", "\n", "\n", "cost.errordef = Minuit.LEAST_SQUARES\n", "cost.ndata = np.prod(n.shape)\n", "\n", "m2 = Minuit(cost, truth)\n", "m2.precision = precision\n", "m2.limits = (0, None)\n", "m2.migrad()\n", "m2.hesse()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We obtain the exact same result as expected, but the runtime is much longer (more than a factor 10), which disfavors this technique compared to the straight-forward fit. The minimization is not as efficient, because Minuit cannot exploit correlations between the internal and the external parameters that allow it to converge it faster when it sees all parameters at once."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Template fits\n", "\n", "The implementation described by [<PERSON> and <PERSON>, Comput.Phys.Commun. 77 (1993) 219-228](https://doi.org/10.1016/0010-4655(93)90005-W) solves the problem similarly to the nested fit described above, but the solution to the inner problem is found with an efficient algorithm.\n", "\n", "The <PERSON>-<PERSON><PERSON> approach still requires numerically solving a non-linear equation per bin. Several authors tried to replace the stop with approximations.\n", "- [Conway, PHYSTAT 2011, https://arxiv.org/abs/1103.0354](https://doi.org/10.48550/arXiv.1103.0354) uses an approximation in which the optimal nuisance parameters can be found by bin-by-bin by solving a quadratic equation which has only one allowed solution.\n", "- [<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, JHEP 06 (2019) 030](https://doi.org/10.48550/arXiv.1103.0354) use a Bayesian treatment in which the uncertainty from the finite size of the simulation sample is modeled with a prior (which is itself a posterior conditioned on the simulation result). A closed formula for the likelihood can be derived.\n", "- [<PERSON><PERSON>, <PERSON><PERSON>, Eur.Phys.J.C 82 (2022) 11, 1043](https://doi.org/10.1140/epjc/s10052-022-11019-z) derived an approximation similar to <PERSON>, but which treats data and simulation symmetrically.\n", "\n", "All three methods are implemented in the built-in `Template` cost function (see documentation for details)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 8.447 (χ²/ndof = 0.6) </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 46 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 7.7e-06 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 790 </td>\n", "        <td> 50 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 197 </td>\n", "        <td> 28 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 2.56e+03 </td>\n", "        <td style=\"background-color:rgb(203,203,250);color:black\"> -0.5e3 <strong>(-0.358)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(203,203,250);color:black\"> -0.5e3 <strong>(-0.358)</strong> </td>\n", "        <td> 764 </td>\n", "    </tr>\n", "</table><?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"360pt\" height=\"288pt\" viewBox=\"0 0 360 288\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:42:59.131447</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 288 \n", "L 360 288 \n", "L 360 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "L 29.08774 3.00024 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.992832 205.123597 \n", "L 43.992832 168.946272 \n", "L 63.866288 168.946272 \n", "L 63.866288 121.119977 \n", "L 83.739743 121.119977 \n", "L 83.739743 156.939929 \n", "L 103.613199 156.939929 \n", "L 103.613199 159.937967 \n", "L 123.486655 159.937967 \n", "L 123.486655 162.201601 \n", "L 143.360111 162.201601 \n", "L 143.360111 160.046342 \n", "L 163.233566 160.046342 \n", "L 163.233566 96.12696 \n", "L 183.107022 96.12696 \n", "L 183.107022 15.634592 \n", "L 202.980478 15.634592 \n", "L 202.980478 114.194981 \n", "L 222.853934 114.194981 \n", "L 222.853934 207.830549 \n", "L 242.727389 207.830549 \n", "L 242.727389 229.766146 \n", "L 262.600845 229.766146 \n", "L 262.600845 245.305295 \n", "L 282.474301 245.305295 \n", "L 282.474301 232.858284 \n", "L 302.347757 232.858284 \n", "L 302.347757 223.601733 \n", "L 322.221212 223.601733 \n", "L 322.221212 217.461111 \n", "L 342.094668 217.461111 \n", "L 342.094668 245.925278 \n", "L 342.094668 245.925278 \n", "L 342.094668 245.925278 \n", "L 322.221212 245.925278 \n", "L 322.221212 250.94922 \n", "L 302.347757 250.94922 \n", "L 302.347757 258.439516 \n", "L 282.474301 258.439516 \n", "L 282.474301 268.321635 \n", "L 262.600845 268.321635 \n", "L 262.600845 255.949372 \n", "L 242.727389 255.949372 \n", "L 242.727389 236.767758 \n", "L 222.853934 236.767758 \n", "L 222.853934 149.694118 \n", "L 202.980478 149.694118 \n", "L 202.980478 53.996746 \n", "L 183.107022 53.996746 \n", "L 183.107022 134.46711 \n", "L 163.233566 134.46711 \n", "L 163.233566 196.879349 \n", "L 143.360111 196.879349 \n", "L 143.360111 199.258467 \n", "L 123.486655 199.258467 \n", "L 123.486655 197.385055 \n", "L 103.613199 197.385055 \n", "L 103.613199 194.800811 \n", "L 83.739743 194.800811 \n", "L 83.739743 163.633373 \n", "L 63.866288 163.633373 \n", "L 63.866288 205.123597 \n", "L 43.992832 205.123597 \n", "L 43.992832 205.123597 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 43.992832 205.123597 \n", "L 43.992832 168.946272 \n", "L 63.866288 168.946272 \n", "L 63.866288 121.119977 \n", "L 83.739743 121.119977 \n", "L 83.739743 156.939929 \n", "L 103.613199 156.939929 \n", "L 103.613199 159.937967 \n", "L 123.486655 159.937967 \n", "L 123.486655 162.201601 \n", "L 143.360111 162.201601 \n", "L 143.360111 160.046342 \n", "L 163.233566 160.046342 \n", "L 163.233566 96.12696 \n", "L 183.107022 96.12696 \n", "L 183.107022 15.634592 \n", "L 202.980478 15.634592 \n", "L 202.980478 114.194981 \n", "L 222.853934 114.194981 \n", "L 222.853934 207.830549 \n", "L 242.727389 207.830549 \n", "L 242.727389 229.766146 \n", "L 262.600845 229.766146 \n", "L 262.600845 245.305295 \n", "L 282.474301 245.305295 \n", "L 282.474301 232.858284 \n", "L 302.347757 232.858284 \n", "L 302.347757 223.601733 \n", "L 322.221212 223.601733 \n", "L 322.221212 217.461111 \n", "L 342.094668 217.461111 \n", "L 342.094668 245.925278 \n", "L 342.094668 245.925278 \n", "L 342.094668 245.925278 \n", "L 322.221212 245.925278 \n", "L 322.221212 250.94922 \n", "L 302.347757 250.94922 \n", "L 302.347757 258.439516 \n", "L 282.474301 258.439516 \n", "L 282.474301 268.321635 \n", "L 262.600845 268.321635 \n", "L 262.600845 255.949372 \n", "L 242.727389 255.949372 \n", "L 242.727389 236.767758 \n", "L 222.853934 236.767758 \n", "L 222.853934 149.694118 \n", "L 202.980478 149.694118 \n", "L 202.980478 53.996746 \n", "L 183.107022 53.996746 \n", "L 183.107022 134.46711 \n", "L 163.233566 134.46711 \n", "L 163.233566 196.879349 \n", "L 143.360111 196.879349 \n", "L 143.360111 199.258467 \n", "L 123.486655 199.258467 \n", "L 123.486655 197.385055 \n", "L 103.613199 197.385055 \n", "L 103.613199 194.800811 \n", "L 83.739743 194.800811 \n", "L 83.739743 163.633373 \n", "L 63.866288 163.633373 \n", "L 63.866288 205.123597 \n", "L 43.992832 205.123597 \n", "L 43.992832 205.123597 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"mc546034feb\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"43.992832\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(32.860019 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"81.255561\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(70.122749 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"118.518291\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(107.385478 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"155.78102\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(144.648208 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"193.04375\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(181.910937 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"230.30648\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.25 -->\n", "      <g transform=\"translate(219.173667 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"267.569209\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.50 -->\n", "      <g transform=\"translate(256.436397 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"304.831939\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.75 -->\n", "      <g transform=\"translate(293.699126 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mc546034feb\" x=\"342.094668\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.00 -->\n", "      <g transform=\"translate(330.961856 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m9532aa9350\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m9532aa9350\" x=\"29.08774\" y=\"267.879835\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(9.36274 271.679054) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m9532aa9350\" x=\"29.08774\" y=\"231.496805\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(9.36274 235.296023) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m9532aa9350\" x=\"29.08774\" y=\"195.113774\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(9.36274 198.912993) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m9532aa9350\" x=\"29.08774\" y=\"158.730744\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(9.36274 162.529963) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m9532aa9350\" x=\"29.08774\" y=\"122.347713\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(3.00024 126.146932) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m9532aa9350\" x=\"29.08774\" y=\"85.964683\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 120 -->\n", "      <g transform=\"translate(3.00024 89.763902) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m9532aa9350\" x=\"29.08774\" y=\"49.581653\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 140 -->\n", "      <g transform=\"translate(3.00024 53.380871) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m9532aa9350\" x=\"29.08774\" y=\"13.198622\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 160 -->\n", "      <g transform=\"translate(3.00024 16.997841) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 53.92956 180.151188 \n", "L 53.92956 148.225209 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 73.803015 197.270111 \n", "L 73.803015 167.489317 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 93.676471 181.866345 \n", "L 93.676471 150.148355 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 113.549927 202.39038 \n", "L 113.549927 173.283956 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 133.423383 200.684485 \n", "L 133.423383 171.351548 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 153.296838 212.606323 \n", "L 153.296838 184.897832 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 173.170294 119.769985 \n", "L 173.170294 81.265805 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 193.04375 64.134865 \n", "L 193.04375 20.475228 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 212.917206 149.174367 \n", "L 212.917206 113.712575 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 232.790662 227.858502 \n", "L 232.790662 202.39038 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 252.664117 243.002129 \n", "L 252.664117 219.99148 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 272.537573 248.019726 \n", "L 272.537573 225.888793 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 292.411029 249.68832 \n", "L 292.411029 227.858502 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 312.284485 253.019099 \n", "L 312.284485 231.804329 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 332.15794 261.303902 \n", "L 332.15794 241.711041 \n", "\" clip-path=\"url(#p5661765f78)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <defs>\n", "     <path id=\"m623b1c5204\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p5661765f78)\">\n", "     <use xlink:href=\"#m623b1c5204\" x=\"53.92956\" y=\"164.188198\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"73.803015\" y=\"182.379714\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"93.676471\" y=\"166.00735\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"113.549927\" y=\"187.837168\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"133.423383\" y=\"186.018017\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"153.296838\" y=\"198.752077\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"173.170294\" y=\"100.517895\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"193.04375\" y=\"42.305046\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"212.917206\" y=\"131.443471\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"232.790662\" y=\"215.124441\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"252.664117\" y=\"231.496805\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"272.537573\" y=\"236.954259\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"292.411029\" y=\"238.773411\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"312.284485\" y=\"242.411714\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m623b1c5204\" x=\"332.15794\" y=\"251.507471\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 29.08774 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 29.08774 3.00024 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p5661765f78\">\n", "   <rect x=\"29.08774\" y=\"3.00024\" width=\"327.91202\" height=\"265.321395\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 8.447 (χ²/ndof = 0.6)      │              Nfcn = 46               │\n", "│ EDM = 7.7e-06 (Goal: 0.0002)     │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │    790    │    50     │            │            │    0    │         │       │\n", "│ 1 │ x1   │    197    │    28     │            │            │    0    │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌────┬───────────────────┐\n", "│    │       x0       x1 │\n", "├────┼───────────────────┤\n", "│ x0 │ 2.56e+03   -0.5e3 │\n", "│ x1 │   -0.5e3      764 │\n", "└────┴───────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["c = Template(n, xe, t, method=\"jsc\")  # Conway\n", "m3 = Minuit(c, *truth)\n", "m3.limits = (0, None)\n", "m3.migrad()\n", "m3.hesse()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 54.95 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 46 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 5.98e-07 (Goal: 0.0001) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 760 </td>\n", "        <td> 50 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 203 </td>\n", "        <td> 27 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 2.25e+03 </td>\n", "        <td style=\"background-color:rgb(206,206,250);color:black\"> -0.4e3 <strong>(-0.341)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(206,206,250);color:black\"> -0.4e3 <strong>(-0.341)</strong> </td>\n", "        <td> 739 </td>\n", "    </tr>\n", "</table><?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"360pt\" height=\"288pt\" viewBox=\"0 0 360 288\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:42:59.412000</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 288 \n", "L 360 288 \n", "L 360 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "L 29.08774 3.00024 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.992832 208.360877 \n", "L 43.992832 174.036714 \n", "L 63.866288 174.036714 \n", "L 63.866288 128.660291 \n", "L 83.739743 128.660291 \n", "L 83.739743 162.645388 \n", "L 103.613199 162.645388 \n", "L 103.613199 165.489853 \n", "L 123.486655 165.489853 \n", "L 123.486655 167.587078 \n", "L 143.360111 167.587078 \n", "L 143.360111 165.188941 \n", "L 163.233566 165.188941 \n", "L 163.233566 99.556864 \n", "L 183.107022 99.556864 \n", "L 183.107022 15.634592 \n", "L 202.980478 15.634592 \n", "L 202.980478 116.481379 \n", "L 222.853934 116.481379 \n", "L 222.853934 210.266135 \n", "L 242.727389 210.266135 \n", "L 242.727389 231.741127 \n", "L 262.600845 231.741127 \n", "L 262.600845 246.484293 \n", "L 282.474301 246.484293 \n", "L 282.474301 234.674872 \n", "L 302.347757 234.674872 \n", "L 302.347757 225.892482 \n", "L 322.221212 225.892482 \n", "L 322.221212 220.066409 \n", "L 342.094668 220.066409 \n", "L 342.094668 247.072517 \n", "L 342.094668 247.072517 \n", "L 342.094668 247.072517 \n", "L 322.221212 247.072517 \n", "L 322.221212 251.839111 \n", "L 302.347757 251.839111 \n", "L 302.347757 258.945721 \n", "L 282.474301 258.945721 \n", "L 282.474301 268.321635 \n", "L 262.600845 268.321635 \n", "L 262.600845 256.583133 \n", "L 242.727389 256.583133 \n", "L 242.727389 237.786179 \n", "L 222.853934 237.786179 \n", "L 222.853934 150.612905 \n", "L 202.980478 150.612905 \n", "L 202.980478 52.991349 \n", "L 183.107022 52.991349 \n", "L 183.107022 136.335889 \n", "L 163.233566 136.335889 \n", "L 163.233566 200.166709 \n", "L 143.360111 200.166709 \n", "L 143.360111 202.749645 \n", "L 123.486655 202.749645 \n", "L 123.486655 201.018738 \n", "L 103.613199 201.018738 \n", "L 103.613199 198.566869 \n", "L 83.739743 198.566869 \n", "L 83.739743 168.995965 \n", "L 63.866288 168.995965 \n", "L 63.866288 208.360877 \n", "L 43.992832 208.360877 \n", "L 43.992832 208.360877 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 43.992832 208.360877 \n", "L 43.992832 174.036714 \n", "L 63.866288 174.036714 \n", "L 63.866288 128.660291 \n", "L 83.739743 128.660291 \n", "L 83.739743 162.645388 \n", "L 103.613199 162.645388 \n", "L 103.613199 165.489853 \n", "L 123.486655 165.489853 \n", "L 123.486655 167.587078 \n", "L 143.360111 167.587078 \n", "L 143.360111 165.188941 \n", "L 163.233566 165.188941 \n", "L 163.233566 99.556864 \n", "L 183.107022 99.556864 \n", "L 183.107022 15.634592 \n", "L 202.980478 15.634592 \n", "L 202.980478 116.481379 \n", "L 222.853934 116.481379 \n", "L 222.853934 210.266135 \n", "L 242.727389 210.266135 \n", "L 242.727389 231.741127 \n", "L 262.600845 231.741127 \n", "L 262.600845 246.484293 \n", "L 282.474301 246.484293 \n", "L 282.474301 234.674872 \n", "L 302.347757 234.674872 \n", "L 302.347757 225.892482 \n", "L 322.221212 225.892482 \n", "L 322.221212 220.066409 \n", "L 342.094668 220.066409 \n", "L 342.094668 247.072517 \n", "L 342.094668 247.072517 \n", "L 342.094668 247.072517 \n", "L 322.221212 247.072517 \n", "L 322.221212 251.839111 \n", "L 302.347757 251.839111 \n", "L 302.347757 258.945721 \n", "L 282.474301 258.945721 \n", "L 282.474301 268.321635 \n", "L 262.600845 268.321635 \n", "L 262.600845 256.583133 \n", "L 242.727389 256.583133 \n", "L 242.727389 237.786179 \n", "L 222.853934 237.786179 \n", "L 222.853934 150.612905 \n", "L 202.980478 150.612905 \n", "L 202.980478 52.991349 \n", "L 183.107022 52.991349 \n", "L 183.107022 136.335889 \n", "L 163.233566 136.335889 \n", "L 163.233566 200.166709 \n", "L 143.360111 200.166709 \n", "L 143.360111 202.749645 \n", "L 123.486655 202.749645 \n", "L 123.486655 201.018738 \n", "L 103.613199 201.018738 \n", "L 103.613199 198.566869 \n", "L 83.739743 198.566869 \n", "L 83.739743 168.995965 \n", "L 63.866288 168.995965 \n", "L 63.866288 208.360877 \n", "L 43.992832 208.360877 \n", "L 43.992832 208.360877 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m8e6dc81b02\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"43.992832\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(32.860019 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"81.255561\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(70.122749 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"118.518291\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(107.385478 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"155.78102\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(144.648208 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"193.04375\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(181.910937 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"230.30648\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.25 -->\n", "      <g transform=\"translate(219.173667 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"267.569209\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.50 -->\n", "      <g transform=\"translate(256.436397 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"304.831939\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.75 -->\n", "      <g transform=\"translate(293.699126 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m8e6dc81b02\" x=\"342.094668\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.00 -->\n", "      <g transform=\"translate(330.961856 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m2fdfb784c9\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m2fdfb784c9\" x=\"29.08774\" y=\"266.388618\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(9.36274 270.187836) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m2fdfb784c9\" x=\"29.08774\" y=\"230.355437\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(9.36274 234.154656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m2fdfb784c9\" x=\"29.08774\" y=\"194.322257\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(9.36274 198.121475) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m2fdfb784c9\" x=\"29.08774\" y=\"158.289076\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(9.36274 162.088295) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m2fdfb784c9\" x=\"29.08774\" y=\"122.255895\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(3.00024 126.055114) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m2fdfb784c9\" x=\"29.08774\" y=\"86.222715\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 120 -->\n", "      <g transform=\"translate(3.00024 90.021933) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m2fdfb784c9\" x=\"29.08774\" y=\"50.189534\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 140 -->\n", "      <g transform=\"translate(3.00024 53.988753) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m2fdfb784c9\" x=\"29.08774\" y=\"14.156354\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 160 -->\n", "      <g transform=\"translate(3.00024 17.955572) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 53.92956 179.503547 \n", "L 53.92956 147.884559 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 73.803015 196.457858 \n", "L 73.803015 166.963429 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 93.676471 181.202211 \n", "L 93.676471 149.789213 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 113.549927 201.528893 \n", "L 113.549927 172.702348 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 133.423383 199.839401 \n", "L 133.423383 170.788522 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 153.296838 211.646601 \n", "L 153.296838 184.204548 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 173.170294 119.702954 \n", "L 173.170294 81.56902 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 193.04375 64.602806 \n", "L 193.04375 21.36299 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 212.917206 148.824591 \n", "L 212.917206 113.70379 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 232.790662 226.752119 \n", "L 232.790662 201.528893 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 252.664117 241.750129 \n", "L 252.664117 218.960745 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 272.537573 246.719478 \n", "L 272.537573 224.80135 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 292.411029 248.372027 \n", "L 292.411029 226.752119 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 312.284485 251.670778 \n", "L 312.284485 230.660004 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 332.15794 259.875917 \n", "L 332.15794 240.471456 \n", "\" clip-path=\"url(#p0ae01d5295)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <defs>\n", "     <path id=\"mc935719d64\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p0ae01d5295)\">\n", "     <use xlink:href=\"#mc935719d64\" x=\"53.92956\" y=\"163.694053\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"73.803015\" y=\"181.710643\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"93.676471\" y=\"165.495712\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"113.549927\" y=\"187.11562\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"133.423383\" y=\"185.313961\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"153.296838\" y=\"197.925575\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"173.170294\" y=\"100.635987\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"193.04375\" y=\"42.982898\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"212.917206\" y=\"131.26419\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"232.790662\" y=\"214.140506\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"252.664117\" y=\"230.355437\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"272.537573\" y=\"235.760414\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"292.411029\" y=\"237.562073\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"312.284485\" y=\"241.165391\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#mc935719d64\" x=\"332.15794\" y=\"250.173686\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 29.08774 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 29.08774 3.00024 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p0ae01d5295\">\n", "   <rect x=\"29.08774\" y=\"3.00024\" width=\"327.91202\" height=\"265.321395\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 54.95                      │              Nfcn = 46               │\n", "│ EDM = 5.98e-07 (Goal: 0.0001)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │    760    │    50     │            │            │    0    │         │       │\n", "│ 1 │ x1   │    203    │    27     │            │            │    0    │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌────┬───────────────────┐\n", "│    │       x0       x1 │\n", "├────┼───────────────────┤\n", "│ x0 │ 2.25e+03   -0.4e3 │\n", "│ x1 │   -0.4e3      739 │\n", "└────┴───────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["c = Template(n, xe, t, method=\"asy\")  # <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>\n", "m4 = Minuit(c, *truth)\n", "m4.limits = (0, None)\n", "m4.migrad()\n", "m4.hesse()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 8.503 (χ²/ndof = 0.7) </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 45 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 9.17e-06 (Goal: 0.0002) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> x0 </td>\n", "        <td> 780 </td>\n", "        <td> 50 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> x1 </td>\n", "        <td> 199 </td>\n", "        <td> 28 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td> 0 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table><table>\n", "    <tr>\n", "        <td></td>\n", "        <th> x0 </th>\n", "        <th> x1 </th>\n", "    </tr>\n", "    <tr>\n", "        <th> x0 </th>\n", "        <td> 2.45e+03 </td>\n", "        <td style=\"background-color:rgb(204,204,250);color:black\"> -0.5e3 <strong>(-0.351)</strong> </td>\n", "    </tr>\n", "    <tr>\n", "        <th> x1 </th>\n", "        <td style=\"background-color:rgb(204,204,250);color:black\"> -0.5e3 <strong>(-0.351)</strong> </td>\n", "        <td> 756 </td>\n", "    </tr>\n", "</table><?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"360pt\" height=\"288pt\" viewBox=\"0 0 360 288\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:42:59.643360</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 288 \n", "L 360 288 \n", "L 360 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "L 29.08774 3.00024 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 43.992832 206.060731 \n", "L 43.992832 170.419862 \n", "L 63.866288 170.419862 \n", "L 63.866288 123.30276 \n", "L 83.739743 123.30276 \n", "L 83.739743 158.591555 \n", "L 103.613199 158.591555 \n", "L 103.613199 161.545136 \n", "L 123.486655 161.545136 \n", "L 123.486655 163.760604 \n", "L 143.360111 163.760604 \n", "L 143.360111 161.535075 \n", "L 163.233566 161.535075 \n", "L 163.233566 97.120175 \n", "L 183.107022 97.120175 \n", "L 183.107022 15.634592 \n", "L 202.980478 15.634592 \n", "L 202.980478 114.857226 \n", "L 222.853934 114.857226 \n", "L 222.853934 208.535722 \n", "L 242.727389 208.535722 \n", "L 242.727389 230.337867 \n", "L 262.600845 230.337867 \n", "L 262.600845 245.646593 \n", "L 282.474301 245.646593 \n", "L 282.474301 233.384153 \n", "L 302.347757 233.384153 \n", "L 302.347757 224.264863 \n", "L 322.221212 224.264863 \n", "L 322.221212 218.215297 \n", "L 342.094668 218.215297 \n", "L 342.094668 246.257382 \n", "L 342.094668 246.257382 \n", "L 342.094668 246.257382 \n", "L 322.221212 246.257382 \n", "L 322.221212 251.206827 \n", "L 302.347757 251.206827 \n", "L 302.347757 258.586053 \n", "L 282.474301 258.586053 \n", "L 282.474301 268.321635 \n", "L 262.600845 268.321635 \n", "L 262.600845 256.132834 \n", "L 242.727389 256.132834 \n", "L 242.727389 237.062249 \n", "L 222.853934 237.062249 \n", "L 222.853934 149.957942 \n", "L 202.980478 149.957942 \n", "L 202.980478 53.701596 \n", "L 183.107022 53.701596 \n", "L 183.107022 135.006057 \n", "L 163.233566 135.006057 \n", "L 163.233566 197.830807 \n", "L 143.360111 197.830807 \n", "L 143.360111 200.269077 \n", "L 123.486655 200.269077 \n", "L 123.486655 198.43694 \n", "L 103.613199 198.43694 \n", "L 103.613199 195.891016 \n", "L 83.739743 195.891016 \n", "L 83.739743 165.185746 \n", "L 63.866288 165.185746 \n", "L 63.866288 206.060731 \n", "L 43.992832 206.060731 \n", "L 43.992832 206.060731 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #1f77b4; stroke-linejoin: miter\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 43.992832 206.060731 \n", "L 43.992832 170.419862 \n", "L 63.866288 170.419862 \n", "L 63.866288 123.30276 \n", "L 83.739743 123.30276 \n", "L 83.739743 158.591555 \n", "L 103.613199 158.591555 \n", "L 103.613199 161.545136 \n", "L 123.486655 161.545136 \n", "L 123.486655 163.760604 \n", "L 143.360111 163.760604 \n", "L 143.360111 161.535075 \n", "L 163.233566 161.535075 \n", "L 163.233566 97.120175 \n", "L 183.107022 97.120175 \n", "L 183.107022 15.634592 \n", "L 202.980478 15.634592 \n", "L 202.980478 114.857226 \n", "L 222.853934 114.857226 \n", "L 222.853934 208.535722 \n", "L 242.727389 208.535722 \n", "L 242.727389 230.337867 \n", "L 262.600845 230.337867 \n", "L 262.600845 245.646593 \n", "L 282.474301 245.646593 \n", "L 282.474301 233.384153 \n", "L 302.347757 233.384153 \n", "L 302.347757 224.264863 \n", "L 322.221212 224.264863 \n", "L 322.221212 218.215297 \n", "L 342.094668 218.215297 \n", "L 342.094668 246.257382 \n", "L 342.094668 246.257382 \n", "L 342.094668 246.257382 \n", "L 322.221212 246.257382 \n", "L 322.221212 251.206827 \n", "L 302.347757 251.206827 \n", "L 302.347757 258.586053 \n", "L 282.474301 258.586053 \n", "L 282.474301 268.321635 \n", "L 262.600845 268.321635 \n", "L 262.600845 256.132834 \n", "L 242.727389 256.132834 \n", "L 242.727389 237.062249 \n", "L 222.853934 237.062249 \n", "L 222.853934 149.957942 \n", "L 202.980478 149.957942 \n", "L 202.980478 53.701596 \n", "L 183.107022 53.701596 \n", "L 183.107022 135.006057 \n", "L 163.233566 135.006057 \n", "L 163.233566 197.830807 \n", "L 143.360111 197.830807 \n", "L 143.360111 200.269077 \n", "L 123.486655 200.269077 \n", "L 123.486655 198.43694 \n", "L 103.613199 198.43694 \n", "L 103.613199 195.891016 \n", "L 83.739743 195.891016 \n", "L 83.739743 165.185746 \n", "L 63.866288 165.185746 \n", "L 63.866288 206.060731 \n", "L 43.992832 206.060731 \n", "L 43.992832 206.060731 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: #1f77b4\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m20acb9528c\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"43.992832\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(32.860019 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"81.255561\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 0.25 -->\n", "      <g transform=\"translate(70.122749 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"118.518291\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 0.50 -->\n", "      <g transform=\"translate(107.385478 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"155.78102\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.75 -->\n", "      <g transform=\"translate(144.648208 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"193.04375\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 1.00 -->\n", "      <g transform=\"translate(181.910937 282.920073) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"230.30648\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 1.25 -->\n", "      <g transform=\"translate(219.173667 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"267.569209\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 1.50 -->\n", "      <g transform=\"translate(256.436397 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"304.831939\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 1.75 -->\n", "      <g transform=\"translate(293.699126 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#m20acb9528c\" x=\"342.094668\" y=\"268.321635\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 2.00 -->\n", "      <g transform=\"translate(330.961856 282.920073) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_10\">\n", "      <defs>\n", "       <path id=\"m150a6bb05d\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m150a6bb05d\" x=\"29.08774\" y=\"267.465037\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(9.36274 271.264255) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m150a6bb05d\" x=\"29.08774\" y=\"231.200163\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(9.36274 234.999382) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m150a6bb05d\" x=\"29.08774\" y=\"194.93529\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(9.36274 198.734508) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m150a6bb05d\" x=\"29.08774\" y=\"158.670416\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(9.36274 162.469635) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#m150a6bb05d\" x=\"29.08774\" y=\"122.405543\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(3.00024 126.204761) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#m150a6bb05d\" x=\"29.08774\" y=\"86.140669\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 120 -->\n", "      <g transform=\"translate(3.00024 89.939888) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#m150a6bb05d\" x=\"29.08774\" y=\"49.875796\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 140 -->\n", "      <g transform=\"translate(3.00024 53.675014) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m150a6bb05d\" x=\"29.08774\" y=\"13.610922\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 160 -->\n", "      <g transform=\"translate(3.00024 17.410141) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"LineCollection_1\">\n", "    <path d=\"M 53.92956 180.021296 \n", "L 53.92956 148.198998 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 73.803015 197.084623 \n", "L 73.803015 167.400545 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 93.676471 181.730883 \n", "L 93.676471 150.115899 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 113.549927 202.188264 \n", "L 113.549927 173.176366 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 133.423383 200.487909 \n", "L 133.423383 171.250233 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 153.296838 212.371029 \n", "L 153.296838 184.752525 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 173.170294 119.836186 \n", "L 173.170294 81.457051 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 193.04375 64.381745 \n", "L 193.04375 20.863897 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 212.917206 149.145074 \n", "L 212.917206 113.798448 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 232.790662 227.573676 \n", "L 232.790662 202.188264 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 252.664117 242.668123 \n", "L 252.664117 219.732203 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 272.537573 247.669425 \n", "L 272.537573 225.610364 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 292.411029 249.3326 \n", "L 292.411029 227.573676 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 312.284485 252.652562 \n", "L 312.284485 231.506689 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "    <path d=\"M 332.15794 260.91046 \n", "L 332.15794 241.381228 \n", "\" clip-path=\"url(#p106c4dc794)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <defs>\n", "     <path id=\"m64f3782e5c\" d=\"M 0 3 \n", "C 0.795609 3 1.55874 2.683901 2.12132 2.12132 \n", "C 2.683901 1.55874 3 0.795609 3 0 \n", "C 3 -0.795609 2.683901 -1.55874 2.12132 -2.12132 \n", "C 1.55874 -2.683901 0.795609 -3 0 -3 \n", "C -0.795609 -3 -1.55874 -2.683901 -2.12132 -2.12132 \n", "C -2.683901 -1.55874 -3 -0.795609 -3 0 \n", "C -3 0.795609 -2.683901 1.55874 -2.12132 2.12132 \n", "C -1.55874 2.683901 -0.795609 3 0 3 \n", "z\n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p106c4dc794)\">\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"53.92956\" y=\"164.110147\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"73.803015\" y=\"182.242584\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"93.676471\" y=\"165.923391\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"113.549927\" y=\"187.682315\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"133.423383\" y=\"185.869071\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"153.296838\" y=\"198.561777\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"173.170294\" y=\"100.646618\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"193.04375\" y=\"42.622821\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"212.917206\" y=\"131.471761\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"232.790662\" y=\"214.88097\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"252.664117\" y=\"231.200163\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"272.537573\" y=\"236.639894\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"292.411029\" y=\"238.453138\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"312.284485\" y=\"242.079625\" style=\"stroke: #000000\"/>\n", "     <use xlink:href=\"#m64f3782e5c\" x=\"332.15794\" y=\"251.145844\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 29.08774 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 356.99976 268.321635 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 29.08774 268.321635 \n", "L 356.99976 268.321635 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 29.08774 3.00024 \n", "L 356.99976 3.00024 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p106c4dc794\">\n", "   <rect x=\"29.08774\" y=\"3.00024\" width=\"327.91202\" height=\"265.321395\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 8.503 (χ²/ndof = 0.7)      │              Nfcn = 45               │\n", "│ EDM = 9.17e-06 (Goal: 0.0002)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘\n", "┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ x0   │    780    │    50     │            │            │    0    │         │       │\n", "│ 1 │ x1   │    199    │    28     │            │            │    0    │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘\n", "┌────┬───────────────────┐\n", "│    │       x0       x1 │\n", "├────┼───────────────────┤\n", "│ x0 │ 2.45e+03   -0.5e3 │\n", "│ x1 │   -0.5e3      756 │\n", "└────┴───────────────────┘"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["c = Template(n, xe, t, method=\"da\")  # <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "m5 = Minuit(c, *truth)\n", "m5.limits = (0, None)\n", "m5.migrad()\n", "m5.hesse()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["full fit\n", "  x0 784 +- 35\n", "  x1 199 +- 26\n", "  correlation -0.52\n", "T(JSC)\n", "  x0 793 +- 51\n", "  x1 197 +- 28\n", "  correlation -0.36\n", "T(ASY)\n", "  x0 760 +- 47\n", "  x1 203 +- 27\n", "  correlation -0.34\n", "T(DA)\n", "  x0 784 +- 49\n", "  x1 199 +- 27\n", "  correlation -0.35\n"]}], "source": ["for title, m in zip((\"full fit\", \"T(JSC)\", \"T(ASY)\", \"T(DA)\"), (m1, m3, m4, m5)):\n", "    print(title)\n", "    cov = m.covariance\n", "    for label, p, e in zip((\"x0\", \"x1\"), m.values, np.diag(cov) ** 0.5):\n", "        print(f\"  {label} {p:.0f} +- {e:.0f}\")\n", "    print(f\"  correlation {cov[0, 1] / (cov[0, 0] * cov[1, 1]) ** 0.5:.2f}\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The yields found by the approximate Template methods (T) differ from those found with the exact Barlow-Beeston likelihood (BB) method, because the likelihoods are different. In this particular case, the uncertainty for the signal estimated by the Template methods is larger, but this not the case in general.\n", "\n", "The difference shows up in particular in the 68 % confidence regions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"411.285625pt\" height=\"310.86825pt\" viewBox=\"0 0 411.**********.86825\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:43:01.217634</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 310.86825 \n", "L 411.**********.86825 \n", "L 411.285625 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 46.**********.312 \n", "L 404.**********.312 \n", "L 404.085625 7.2 \n", "L 46.965625 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"me93251f8b2\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#me93251f8b2\" x=\"78.088775\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 700 -->\n", "      <g transform=\"translate(68.545025 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#me93251f8b2\" x=\"122.384878\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 725 -->\n", "      <g transform=\"translate(112.841128 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#me93251f8b2\" x=\"166.680981\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 750 -->\n", "      <g transform=\"translate(157.137231 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#me93251f8b2\" x=\"210.977084\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 775 -->\n", "      <g transform=\"translate(201.433334 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#me93251f8b2\" x=\"255.273186\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 800 -->\n", "      <g transform=\"translate(245.729436 287.910437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#me93251f8b2\" x=\"299.569289\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 825 -->\n", "      <g transform=\"translate(290.025539 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#me93251f8b2\" x=\"343.865392\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 850 -->\n", "      <g transform=\"translate(334.321642 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#me93251f8b2\" x=\"388.161495\" y=\"273.312\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 875 -->\n", "      <g transform=\"translate(378.617745 287.910437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_9\">\n", "     <!-- background yield -->\n", "     <g transform=\"translate(182.377188 301.588562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6b\" d=\"M 581 4863 \n", "L 1159 4863 \n", "L 1159 1991 \n", "L 2875 3500 \n", "L 3609 3500 \n", "L 1753 1863 \n", "L 3688 0 \n", "L 2938 0 \n", "L 1159 1709 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-75\" d=\"M 544 1381 \n", "L 544 3500 \n", "L 1119 3500 \n", "L 1119 1403 \n", "Q 1119 906 1312 657 \n", "Q 1506 409 1894 409 \n", "Q 2359 409 2629 706 \n", "Q 2900 1003 2900 1516 \n", "L 2900 3500 \n", "L 3475 3500 \n", "L 3475 0 \n", "L 2900 0 \n", "L 2900 538 \n", "Q 2691 219 2414 64 \n", "Q 2138 -91 1772 -91 \n", "Q 1169 -91 856 284 \n", "Q 544 659 544 1381 \n", "z\n", "M 1991 3584 \n", "L 1991 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-64\" d=\"M 2906 2969 \n", "L 2906 4863 \n", "L 3481 4863 \n", "L 3481 0 \n", "L 2906 0 \n", "L 2906 525 \n", "Q 2725 213 2448 61 \n", "Q 2172 -91 1784 -91 \n", "Q 1150 -91 751 415 \n", "Q 353 922 353 1747 \n", "Q 353 2572 751 3078 \n", "Q 1150 3584 1784 3584 \n", "Q 2172 3584 2448 3432 \n", "Q 2725 3281 2906 2969 \n", "z\n", "M 947 1747 \n", "Q 947 1113 1208 752 \n", "Q 1469 391 1925 391 \n", "Q 2381 391 2643 752 \n", "Q 2906 1113 2906 1747 \n", "Q 2906 2381 2643 2742 \n", "Q 2381 3103 1925 3103 \n", "Q 1469 3103 1208 2742 \n", "Q 947 2381 947 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-62\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"63.476562\"/>\n", "      <use xlink:href=\"#DejaVuSans-63\" x=\"124.755859\"/>\n", "      <use xlink:href=\"#DejaVuSans-6b\" x=\"179.736328\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"237.646484\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"301.123047\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"339.986328\"/>\n", "      <use xlink:href=\"#DejaVuSans-75\" x=\"401.167969\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"464.546875\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"527.925781\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"591.402344\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"623.189453\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"682.369141\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"710.152344\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"771.675781\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"799.458984\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <defs>\n", "       <path id=\"m616dd518cc\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m616dd518cc\" x=\"46.965625\" y=\"251.892694\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 160 -->\n", "      <g transform=\"translate(20.878125 255.691912) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#m616dd518cc\" x=\"46.965625\" y=\"196.910784\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 180 -->\n", "      <g transform=\"translate(20.878125 200.710002) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#m616dd518cc\" x=\"46.965625\" y=\"141.928874\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 200 -->\n", "      <g transform=\"translate(20.878125 145.728092) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#m616dd518cc\" x=\"46.965625\" y=\"86.946964\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 220 -->\n", "      <g transform=\"translate(20.878125 90.746183) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#m616dd518cc\" x=\"46.965625\" y=\"31.965054\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 240 -->\n", "      <g transform=\"translate(20.878125 35.764273) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_15\">\n", "     <!-- signal yield -->\n", "     <g transform=\"translate(14.798438 168.623187) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-73\" d=\"M 2834 3397 \n", "L 2834 2853 \n", "Q 2591 2978 2328 3040 \n", "Q 2066 3103 1784 3103 \n", "Q 1356 3103 1142 2972 \n", "Q 928 2841 928 2578 \n", "Q 928 2378 1081 2264 \n", "Q 1234 2150 1697 2047 \n", "L 1894 2003 \n", "Q 2506 1872 2764 1633 \n", "Q 3022 1394 3022 966 \n", "Q 3022 478 2636 193 \n", "Q 2250 -91 1575 -91 \n", "Q 1294 -91 989 -36 \n", "Q 684 19 347 128 \n", "L 347 722 \n", "Q 666 556 975 473 \n", "Q 1284 391 1588 391 \n", "Q 1994 391 2212 530 \n", "Q 2431 669 2431 922 \n", "Q 2431 1156 2273 1281 \n", "Q 2116 1406 1581 1522 \n", "L 1381 1569 \n", "Q 847 1681 609 1914 \n", "Q 372 2147 372 2553 \n", "Q 372 3047 722 3315 \n", "Q 1072 3584 1716 3584 \n", "Q 2034 3584 2315 3537 \n", "Q 2597 3491 2834 3397 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-73\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"52.099609\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"79.882812\"/>\n", "      <use xlink:href=\"#DejaVuSans-6e\" x=\"143.359375\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"206.738281\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"268.017578\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"295.800781\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"327.587891\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"386.767578\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"414.550781\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"476.074219\"/>\n", "      <use xlink:href=\"#DejaVuSans-64\" x=\"503.857422\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <path d=\"M 134.581366 91.875419 \n", "L 134.883657 96.709271 \n", "L 134.516859 101.532363 \n", "L 135.828409 105.958865 \n", "L 136.343026 110.764834 \n", "L 137.522399 115.269431 \n", "L 137.835155 120.107227 \n", "L 140.831016 128.535402 \n", "L 142.613792 132.533338 \n", "L 143.359885 137.255046 \n", "L 147.104901 144.848768 \n", "L 150.384201 152.925822 \n", "L 154.631343 159.832565 \n", "L 158.45835 167.122688 \n", "L 162.959466 173.418023 \n", "L 167.210468 180.106633 \n", "L 171.974738 185.903043 \n", "L 176.552146 192.03935 \n", "L 181.536782 197.357284 \n", "L 186.378367 202.979669 \n", "L 191.553441 207.834542 \n", "L 196.528914 212.874745 \n", "L 201.77474 217.20504 \n", "L 206.933023 221.779604 \n", "L 212.322424 225.6601 \n", "L 217.641843 229.765785 \n", "L 223.160964 233.178426 \n", "L 228.624983 236.798119 \n", "L 234.262012 239.709951 \n", "L 239.867217 242.815218 \n", "L 245.62115 245.17652 \n", "L 251.346556 247.702803 \n", "L 257.194297 249.427295 \n", "L 263.029382 251.284303 \n", "L 268.953544 252.247065 \n", "L 274.874754 253.248312 \n", "L 280.830547 253.365496 \n", "L 288.319803 253.247119 \n", "L 295.693772 251.212666 \n", "L 302.929101 248.224786 \n", "L 306.461825 246.256267 \n", "L 309.77188 243.509814 \n", "L 313.147416 240.45224 \n", "L 316.058152 236.404709 \n", "L 318.929104 232.23941 \n", "L 320.90486 226.967374 \n", "L 322.830351 221.664939 \n", "L 323.791966 215.764528 \n", "L 324.661027 209.846981 \n", "L 324.677576 203.77781 \n", "L 324.453282 199.011518 \n", "L 324.624559 194.239934 \n", "L 323.505746 189.778288 \n", "L 322.904843 185.082507 \n", "L 321.844045 180.600113 \n", "L 321.351979 175.886527 \n", "L 318.5365 167.443274 \n", "L 316.033494 158.829837 \n", "L 312.526966 151.104966 \n", "L 309.371787 143.032013 \n", "L 305.366456 135.924401 \n", "L 301.709916 128.526829 \n", "L 297.440024 121.967821 \n", "L 293.385026 115.096628 \n", "L 288.847287 108.995179 \n", "L 284.474289 102.618637 \n", "L 279.704178 96.968879 \n", "L 275.064094 91.067141 \n", "L 270.086726 85.868901 \n", "L 265.271819 80.501962 \n", "L 260.170732 75.814456 \n", "L 255.154346 70.912762 \n", "L 249.885676 66.696096 \n", "L 244.685803 62.277711 \n", "L 239.262183 58.562866 \n", "L 233.893619 54.659612 \n", "L 228.327409 51.492161 \n", "L 222.790432 48.143944 \n", "L 217.081476 45.583726 \n", "L 211.401147 42.870313 \n", "L 205.579259 41.014792 \n", "L 199.76922 39.034644 \n", "L 193.85727 38.01227 \n", "L 187.948766 36.911778 \n", "L 181.997841 36.910909 \n", "L 177.918607 36.907227 \n", "L 173.857077 37.496249 \n", "L 169.796085 38.117723 \n", "L 165.801922 39.414686 \n", "L 161.807564 40.78739 \n", "L 157.960513 42.947242 \n", "L 154.134904 45.217609 \n", "L 150.593595 48.410798 \n", "L 146.962953 51.821029 \n", "L 143.900243 56.379613 \n", "L 140.782078 60.909312 \n", "L 138.725023 66.717733 \n", "L 136.581455 72.438661 \n", "L 135.580869 78.8714 \n", "L 134.553638 85.275812 \n", "L 134.581366 91.875419 \n", "\" clip-path=\"url(#p8280cf1dfa)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_15\">\n", "    <path d=\"M 115.867491 109.881916 \n", "L 115.854373 115.542041 \n", "L 116.582742 121.088238 \n", "L 118.476144 131.959443 \n", "L 120.340062 137.050805 \n", "L 120.556424 142.896544 \n", "L 124.966041 152.019524 \n", "L 128.769964 161.551634 \n", "L 133.853975 169.518599 \n", "L 138.681733 177.811787 \n", "L 144.29536 184.813486 \n", "L 149.65624 191.904076 \n", "L 155.528479 197.941345 \n", "L 161.311126 204.172041 \n", "L 167.463471 209.482387 \n", "L 173.547092 214.962803 \n", "L 179.908663 219.621967 \n", "L 186.219664 224.438921 \n", "L 192.745698 228.508073 \n", "L 199.03731 232.594178 \n", "L 205.488863 236.02781 \n", "L 211.911254 239.588201 \n", "L 218.461858 242.533078 \n", "L 224.989427 245.594705 \n", "L 231.619972 248.066904 \n", "L 238.233359 250.646 \n", "L 244.92877 252.651944 \n", "L 251.677361 254.307659 \n", "L 258.294969 256.945066 \n", "L 265.117971 257.479606 \n", "L 271.882521 258.962053 \n", "L 278.663643 260.083779 \n", "L 285.474307 260.623827 \n", "L 292.28339 261.216 \n", "L 299.103154 261.200894 \n", "L 305.698525 261.213161 \n", "L 312.282796 260.61958 \n", "L 318.86748 260.021264 \n", "L 325.413207 258.760557 \n", "L 331.959786 257.448448 \n", "L 338.425839 255.388328 \n", "L 344.877238 253.206236 \n", "L 351.17903 250.147715 \n", "L 357.761383 246.675063 \n", "L 364.027962 242.003169 \n", "L 370.160402 236.874756 \n", "L 375.624704 230.170065 \n", "L 380.958687 222.734019 \n", "L 384.536159 213.091598 \n", "L 387.024173 202.720333 \n", "L 387.852898 191.728904 \n", "L 387.538424 185.933184 \n", "L 387.680242 180.121127 \n", "L 385.952464 174.943585 \n", "L 384.868165 169.361204 \n", "L 382.21971 158.57971 \n", "L 377.723995 149.388885 \n", "L 373.503237 140.119098 \n", "L 368.386244 132.008439 \n", "L 363.321149 123.845563 \n", "L 357.703178 116.605804 \n", "L 352.21778 109.475218 \n", "L 346.361409 103.094261 \n", "L 340.521281 96.682722 \n", "L 334.401049 90.934411 \n", "L 328.29678 85.154683 \n", "L 321.970076 79.982748 \n", "L 315.656359 74.77449 \n", "L 309.158253 70.144255 \n", "L 302.716245 65.890846 \n", "L 296.473044 60.975315 \n", "L 289.777568 57.800957 \n", "L 283.235488 53.932522 \n", "L 276.557834 50.672404 \n", "L 270.031216 46.744389 \n", "L 263.174313 44.558567 \n", "L 256.418685 41.713118 \n", "L 249.619074 39.159934 \n", "L 242.741154 37.174892 \n", "L 235.864806 35.174673 \n", "L 228.926947 33.776341 \n", "L 221.987849 32.375017 \n", "L 215.007104 31.621282 \n", "L 208.025091 30.882936 \n", "L 201.026908 30.852646 \n", "L 194.346905 30.86284 \n", "L 187.683319 31.589112 \n", "L 181.021929 32.386095 \n", "L 174.421375 33.991516 \n", "L 167.824503 35.715036 \n", "L 161.357899 38.373441 \n", "L 154.919066 41.2097 \n", "L 148.727119 45.153195 \n", "L 142.310748 49.543289 \n", "L 136.408174 55.417785 \n", "L 130.604468 61.59123 \n", "L 125.843714 69.630849 \n", "L 121.605266 78.463426 \n", "L 118.415852 88.300713 \n", "L 116.472289 98.901037 \n", "L 115.867491 109.881916 \n", "\" clip-path=\"url(#p8280cf1dfa)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 63.2818 97.866624 \n", "L 63.714012 103.217045 \n", "L 63.198352 108.549643 \n", "L 65.517581 118.609229 \n", "L 67.359019 128.872055 \n", "L 71.199029 137.703323 \n", "L 74.523388 146.873829 \n", "L 79.124849 154.602435 \n", "L 83.443729 162.667167 \n", "L 88.55534 169.525983 \n", "L 93.422733 176.489509 \n", "L 98.812029 182.456458 \n", "L 104.107363 188.611804 \n", "L 109.782503 193.889866 \n", "L 115.387444 199.32956 \n", "L 121.279655 203.981387 \n", "L 127.120807 208.781085 \n", "L 133.185501 212.859102 \n", "L 139.034363 216.947212 \n", "L 145.050693 220.401091 \n", "L 151.037957 223.97224 \n", "L 157.160182 226.941902 \n", "L 163.259546 230.019158 \n", "L 169.46786 232.517465 \n", "L 175.659196 235.113989 \n", "L 181.937734 237.144821 \n", "L 188.272143 238.836845 \n", "L 194.472456 241.465393 \n", "L 200.888518 242.042764 \n", "L 207.243993 243.524691 \n", "L 213.617617 244.656438 \n", "L 220.023003 245.207561 \n", "L 226.427127 245.865214 \n", "L 232.844236 245.687091 \n", "L 239.276666 245.863278 \n", "L 245.694553 245.169818 \n", "L 252.112492 244.522854 \n", "L 258.483932 243.162636 \n", "L 264.855614 241.745766 \n", "L 271.130964 239.523078 \n", "L 277.389595 237.173992 \n", "L 283.469243 233.88243 \n", "L 289.804942 230.175114 \n", "L 295.765821 225.191199 \n", "L 301.597908 219.801887 \n", "L 306.650718 212.76881 \n", "L 311.644551 205.224831 \n", "L 314.696342 195.502413 \n", "L 316.875182 185.333839 \n", "L 317.565467 180.058929 \n", "L 317.596173 174.6766 \n", "L 317.323892 169.273385 \n", "L 317.48483 163.859436 \n", "L 315.119422 153.682418 \n", "L 312.897941 143.476401 \n", "L 308.980308 134.582809 \n", "L 305.334179 125.582012 \n", "L 300.718637 117.724886 \n", "L 296.210362 109.747985 \n", "L 291.100186 102.69857 \n", "L 286.133018 95.705959 \n", "L 280.761284 89.474792 \n", "L 275.420204 83.186359 \n", "L 269.775032 77.573695 \n", "L 264.154779 71.910983 \n", "L 258.294704 66.867329 \n", "L 252.453379 61.773665 \n", "L 246.41526 57.266158 \n", "L 240.421407 53.144386 \n", "L 234.649298 48.320518 \n", "L 228.391722 45.285611 \n", "L 222.302612 41.514963 \n", "L 216.068944 38.37041 \n", "L 209.997169 34.538449 \n", "L 203.580479 32.463821 \n", "L 197.271406 29.70685 \n", "L 190.917649 27.245425 \n", "L 184.484666 25.342802 \n", "L 178.053871 23.420729 \n", "L 171.56159 22.085973 \n", "L 165.068619 20.743736 \n", "L 158.534524 20.028204 \n", "L 151.999468 19.322515 \n", "L 145.448626 19.296 \n", "L 138.934601 19.303281 \n", "L 132.438417 20.050821 \n", "L 125.944303 20.86447 \n", "L 119.515962 22.510095 \n", "L 113.090919 24.268523 \n", "L 106.805966 26.984831 \n", "L 100.548439 29.870232 \n", "L 94.554915 33.884144 \n", "L 88.352869 38.320737 \n", "L 82.6941 44.253292 \n", "L 77.116506 50.435889 \n", "L 72.626377 58.472072 \n", "L 67.89651 66.528643 \n", "L 65.658214 76.858355 \n", "L 63.849904 87.19363 \n", "L 63.228989 92.486963 \n", "L 63.2818 97.866624 \n", "\" clip-path=\"url(#p8280cf1dfa)\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 100.888369 97.866624 \n", "L 101.375499 103.217045 \n", "L 100.793249 108.549643 \n", "L 103.36848 118.609229 \n", "L 105.386889 128.872055 \n", "L 109.580787 137.703323 \n", "L 113.22502 146.873829 \n", "L 118.123782 154.602435 \n", "L 122.77027 162.667167 \n", "L 128.189943 169.525983 \n", "L 133.368382 176.489509 \n", "L 139.05293 182.456458 \n", "L 144.651168 188.611804 \n", "L 150.617179 193.889866 \n", "L 156.51737 199.32956 \n", "L 162.695661 203.981387 \n", "L 168.825598 208.781085 \n", "L 175.172051 212.859102 \n", "L 181.296575 216.947212 \n", "L 187.583501 220.401091 \n", "L 193.842455 223.97224 \n", "L 200.232477 226.941902 \n", "L 206.600398 230.019158 \n", "L 213.074316 232.517465 \n", "L 219.531762 235.113989 \n", "L 226.074223 237.144821 \n", "L 232.671226 238.836845 \n", "L 239.137392 241.465393 \n", "L 245.814459 242.042764 \n", "L 252.432285 243.524691 \n", "L 259.067841 244.656438 \n", "L 265.734611 245.207561 \n", "L 272.400083 245.865214 \n", "L 279.078114 245.687091 \n", "L 285.627639 245.863278 \n", "L 292.163824 245.169818 \n", "L 298.70006 244.522854 \n", "L 305.193059 243.162636 \n", "L 311.686119 241.745766 \n", "L 318.089526 239.523078 \n", "L 324.476296 237.173992 \n", "L 330.696298 233.88243 \n", "L 337.1833 230.175114 \n", "L 343.319686 225.191199 \n", "L 349.321533 219.801887 \n", "L 354.588788 212.76881 \n", "L 359.760702 205.224831 \n", "L 363.059824 195.502413 \n", "L 365.385012 185.333839 \n", "L 366.123645 180.058929 \n", "L 366.157144 174.6766 \n", "L 365.853821 169.273385 \n", "L 366.032525 163.859436 \n", "L 363.422124 153.682418 \n", "L 360.997652 143.476401 \n", "L 356.743577 134.582809 \n", "L 352.783945 125.582012 \n", "L 347.875591 117.724886 \n", "L 343.049666 109.747985 \n", "L 337.637322 102.69857 \n", "L 332.366641 95.705959 \n", "L 326.703136 89.474792 \n", "L 321.064424 83.186359 \n", "L 315.130544 77.573695 \n", "L 309.218334 71.910983 \n", "L 303.073269 66.867329 \n", "L 296.94507 61.773665 \n", "L 290.625601 57.266158 \n", "L 284.360369 53.144386 \n", "L 278.309012 48.320518 \n", "L 271.786631 45.285611 \n", "L 265.427501 41.514963 \n", "L 258.927581 38.37041 \n", "L 252.586 34.538449 \n", "L 245.907612 32.463821 \n", "L 239.33471 29.70685 \n", "L 232.718144 27.245425 \n", "L 226.02416 25.342802 \n", "L 219.332517 23.420729 \n", "L 212.580664 22.085973 \n", "L 205.828288 20.743736 \n", "L 199.035526 20.028204 \n", "L 192.241906 19.322515 \n", "L 185.432699 19.296 \n", "L 178.804193 19.303281 \n", "L 172.192126 20.050821 \n", "L 165.581889 20.86447 \n", "L 159.032487 22.510095 \n", "L 152.485991 24.268523 \n", "L 146.069308 26.984831 \n", "L 139.678795 29.870232 \n", "L 133.533651 33.884144 \n", "L 127.16349 38.320737 \n", "L 121.303218 44.253292 \n", "L 115.539534 50.435889 \n", "L 110.810109 58.472072 \n", "L 106.599022 66.528643 \n", "L 103.429464 76.858355 \n", "L 101.49593 87.19363 \n", "L 100.830404 92.486963 \n", "L 100.888369 97.866624 \n", "\" clip-path=\"url(#p8280cf1dfa)\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 46.**********.312 \n", "L 46.965625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 404.**********.312 \n", "L 404.085625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 46.**********.312 \n", "L 404.**********.312 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 46.965625 7.2 \n", "L 404.085625 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 331.877813 73.9125 \n", "L 397.085625 73.9125 \n", "Q 399.085625 73.9125 399.085625 71.9125 \n", "L 399.085625 14.2 \n", "Q 399.085625 12.2 397.085625 12.2 \n", "L 331.877813 12.2 \n", "Q 329.877813 12.2 329.877813 14.2 \n", "L 329.877813 71.9125 \n", "Q 329.877813 73.9125 331.877813 73.9125 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_18\">\n", "     <path d=\"M 333.877813 20.298437 \n", "L 343.877813 20.298437 \n", "L 353.877813 20.298437 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_16\">\n", "     <!-- BB -->\n", "     <g transform=\"translate(361.877813 23.798437) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-42\" d=\"M 1259 2228 \n", "L 1259 519 \n", "L 2272 519 \n", "Q 2781 519 3026 730 \n", "Q 3272 941 3272 1375 \n", "Q 3272 1813 3026 2020 \n", "Q 2781 2228 2272 2228 \n", "L 1259 2228 \n", "z\n", "M 1259 4147 \n", "L 1259 2741 \n", "L 2194 2741 \n", "Q 2656 2741 2882 2914 \n", "Q 3109 3088 3109 3444 \n", "Q 3109 3797 2882 3972 \n", "Q 2656 4147 2194 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 2241 4666 \n", "Q 2963 4666 3353 4366 \n", "Q 3744 4066 3744 3513 \n", "Q 3744 3084 3544 2831 \n", "Q 3344 2578 2956 2516 \n", "Q 3422 2416 3680 2098 \n", "Q 3938 1781 3938 1306 \n", "Q 3938 681 3513 340 \n", "Q 3088 0 2303 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-42\"/>\n", "      <use xlink:href=\"#DejaVuSans-42\" x=\"68.603516\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_19\">\n", "     <path d=\"M 333.877813 34.976562 \n", "L 343.877813 34.976562 \n", "L 353.877813 34.976562 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- T(JSC) -->\n", "     <g transform=\"translate(361.877813 38.476562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-54\" d=\"M -19 4666 \n", "L 3928 4666 \n", "L 3928 4134 \n", "L 2272 4134 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 4134 \n", "L -19 4134 \n", "L -19 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-28\" d=\"M 1984 4856 \n", "Q 1566 4138 1362 3434 \n", "Q 1159 2731 1159 2009 \n", "Q 1159 1288 1364 580 \n", "Q 1569 -128 1984 -844 \n", "L 1484 -844 \n", "Q 1016 -109 783 600 \n", "Q 550 1309 550 2009 \n", "Q 550 2706 781 3412 \n", "Q 1013 4119 1484 4856 \n", "L 1984 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4a\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 325 \n", "Q 1259 -519 939 -900 \n", "Q 619 -1281 -91 -1281 \n", "L -331 -1281 \n", "L -331 -750 \n", "L -134 -750 \n", "Q 284 -750 456 -515 \n", "Q 628 -281 628 325 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-43\" d=\"M 4122 4306 \n", "L 4122 3641 \n", "Q 3803 3938 3442 4084 \n", "Q 3081 4231 2675 4231 \n", "Q 1875 4231 1450 3742 \n", "Q 1025 3253 1025 2328 \n", "Q 1025 1406 1450 917 \n", "Q 1875 428 2675 428 \n", "Q 3081 428 3442 575 \n", "Q 3803 722 4122 1019 \n", "L 4122 359 \n", "Q 3791 134 3420 21 \n", "Q 3050 -91 2638 -91 \n", "Q 1578 -91 968 557 \n", "Q 359 1206 359 2328 \n", "Q 359 3453 968 4101 \n", "Q 1578 4750 2638 4750 \n", "Q 3056 4750 3426 4639 \n", "Q 3797 4528 4122 4306 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-29\" d=\"M 513 4856 \n", "L 1013 4856 \n", "Q 1481 4119 1714 3412 \n", "Q 1947 2706 1947 2009 \n", "Q 1947 1309 1714 600 \n", "Q 1481 -109 1013 -844 \n", "L 513 -844 \n", "Q 928 -128 1133 580 \n", "Q 1338 1288 1338 2009 \n", "Q 1338 2731 1133 3434 \n", "Q 928 4138 513 4856 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"61.083984\"/>\n", "      <use xlink:href=\"#DejaVuSans-4a\" x=\"100.097656\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"129.589844\"/>\n", "      <use xlink:href=\"#DejaVuSans-43\" x=\"193.066406\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"262.890625\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_20\">\n", "     <path d=\"M 333.877813 49.654687 \n", "L 343.877813 49.654687 \n", "L 353.877813 49.654687 \n", "\" style=\"fill: none; stroke: #2ca02c; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_18\">\n", "     <!-- T(ASY) -->\n", "     <g transform=\"translate(361.877813 53.154687) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-41\" d=\"M 2188 4044 \n", "L 1331 1722 \n", "L 3047 1722 \n", "L 2188 4044 \n", "z\n", "M 1831 4666 \n", "L 2547 4666 \n", "L 4325 0 \n", "L 3669 0 \n", "L 3244 1197 \n", "L 1141 1197 \n", "L 716 0 \n", "L 50 0 \n", "L 1831 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-59\" d=\"M -13 4666 \n", "L 666 4666 \n", "L 1959 2747 \n", "L 3244 4666 \n", "L 3922 4666 \n", "L 2272 2222 \n", "L 2272 0 \n", "L 1638 0 \n", "L 1638 2222 \n", "L -13 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"61.083984\"/>\n", "      <use xlink:href=\"#DejaVuSans-41\" x=\"100.097656\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"168.505859\"/>\n", "      <use xlink:href=\"#DejaVuSans-59\" x=\"231.982422\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"293.066406\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_21\">\n", "     <path d=\"M 333.877813 64.332812 \n", "L 343.877813 64.332812 \n", "L 353.877813 64.332812 \n", "\" style=\"fill: none; stroke: #d62728; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_19\">\n", "     <!-- T(DA) -->\n", "     <g transform=\"translate(361.877813 67.832812) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-44\" d=\"M 1259 4147 \n", "L 1259 519 \n", "L 2022 519 \n", "Q 2988 519 3436 956 \n", "Q 3884 1394 3884 2338 \n", "Q 3884 3275 3436 3711 \n", "Q 2988 4147 2022 4147 \n", "L 1259 4147 \n", "z\n", "M 628 4666 \n", "L 1925 4666 \n", "Q 3281 4666 3915 4102 \n", "Q 4550 3538 4550 2338 \n", "Q 4550 1131 3912 565 \n", "Q 3275 0 1925 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-54\"/>\n", "      <use xlink:href=\"#DejaVuSans-28\" x=\"61.083984\"/>\n", "      <use xlink:href=\"#DejaVuSans-44\" x=\"100.097656\"/>\n", "      <use xlink:href=\"#DejaVuSans-41\" x=\"175.349609\"/>\n", "      <use xlink:href=\"#DejaVuSans-29\" x=\"243.757812\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p8280cf1dfa\">\n", "   <rect x=\"46.965625\" y=\"7.2\" width=\"357.12\" height=\"266.112\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["c1 = m1.mncontour(\"x0\", \"x1\")\n", "c3 = m3.mncontour(\"x0\", \"x1\")\n", "c4 = m4.mncontour(\"x0\", \"x1\")\n", "c5 = m5.mncontour(\"x0\", \"x1\")\n", "plt.plot(c1[:, 0], c1[:, 1], label=\"BB\")\n", "plt.plot(c3[:, 0], c3[:, 1], label=\"T(JSC)\")\n", "plt.plot(c4[:, 0], c4[:, 1], label=\"T(ASY)\")\n", "plt.plot(c5[:, 0], c4[:, 1], label=\"T(DA)\")\n", "plt.xlabel(\"background yield\")\n", "plt.ylabel(\"signal yield\")\n", "plt.legend();"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["In this particular example, the BB method produces the smallest uncertainty for the background yield. The uncertainty for the signal yield is similar for all methods.\n", "\n", "In general, the approximate methods perform well, the `T(DA)` method in particular is very close to the performance of the exact BB likelihood. The `T(DA)` method also has the advantage that it can be used with weighted data and/or weighted simulated samples.\n", "\n", "For an in-depth comparison of the four methods, see [<PERSON><PERSON>, <PERSON><PERSON>, Eur.Phys.J.C 82 (2022) 11, 1043](https://doi.org/10.1140/epjc/s10052-022-11019-z)."]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "Python 3.8.12 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 2}