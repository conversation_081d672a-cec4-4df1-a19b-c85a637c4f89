{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Using external minimizers\n", "\n", "We show how to use an external minimizer to find the minimum of a function and then use iminuit to compute the parameter uncertainties.\n", "\n", "We will demonstrate this with a maximum-likelihood fit of a normal distribution, which is carried out with `scipy.optimize.minimize`. iminuit is then used to compute the parameter uncertainties.\n", "\n", "Note: imin<PERSON> can call the scipy minimizers directly with `Minuit.scipy`, so `scipy.optimize.minimize` is only used here to demonstrate the general approach."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from iminuit import Minuit\n", "import numpy as np\n", "from scipy.stats import norm\n", "from scipy.optimize import minimize"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# normally distributed data\n", "rng = np.random.default_rng(1)\n", "x = rng.normal(size=1000)\n", "\n", "\n", "# negative log-likelihood for a normal distribution\n", "def nll(par):\n", "    return -np.sum(norm.logpdf(x, par[0], par[1]))\n", "\n", "\n", "nll.errordef = Minuit.LIKELIHOOD\n", "\n", "# minimize nll with scipy.optimize.minimize\n", "result = minimize(nll, np.ones(2))\n", "result"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# initialize Minuit with the fit result from scipy.optimize.minimize\n", "m = Minuit(nll, result.x)\n", "m.hesse()  # this also works without calling MIGRAD before"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can also compute the \"Hesse errors\" at any other point than the minimum. These cannot be interpreted as parameter uncertainties, they are just some numbers related to the second derivative of the cost function at that point."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.values = (1.0, 0.5)\n", "m.hesse()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON><PERSON> now reports that the minimum is invalid, which is correct, but it does not matter for the Hesse errors, which are computed anyway.\n", "\n", "Likewise, it one can also run MINOS to get MINOS estimates. Note that MINOS can fail if the starting point is not actually a minimum. So here we reset the values to the solution found by scipy.optimize."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.values = result.x\n", "m.minos()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can see that MINOS ran successfully. The Hesse Errors were also updated, because MINOS needs HESSE to run first. HESSE is called automatically in this case."]}], "metadata": {"kernelspec": {"display_name": "Python 3.8.14 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 4}