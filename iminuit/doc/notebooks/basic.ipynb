{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Basics\n", "\n", "You will learn basic usage of iminuit and how to approach standard fitting problems with `iminuit`.\n", "\n", "`iminuit` is a Python frontend to the `Minuit2` library in C++, an integrated software that combines a local minimizer (called MIGRAD) and two error calculators (called HESSE and MINOS). You provide it an analytical function, which accepts one or several parameters, and an initial guess of the parameter values. It will then find a local minimum of this function starting from the initial guess. In that regard, iminuit minimizer is like other local minimizers, like those in `scipy.optimize`.\n", "\n", "In addition, iminuit has the ability to compute **uncertainty estimates** for model parameters. `iminuit` was designed to solve statistics problems, where uncertainty estimates are an essential part of the result. The two ways of computing uncertainty estimates, HESSE and MINOS, have different advantages and disadvantages.\n", "\n", "`iminuit` is the successor of `pyminuit`. If you used `pyminuit` before, you will find iminuit very familiar. An important feature of `iminuit` (and `pyminuit`) is that it uses introspection to detect the parameter names of your function. This is very convenient, especially when you work interactively in a Jupyter notebook. It also provides special output routines for Jupyter notebooks to pretty print the fit results, as you will see below."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# basic setup of the notebook\n", "%config InlineBackend.figure_formats = ['svg']\n", "from matplotlib import pyplot as plt\n", "import numpy as np\n", "\n", "# everything in iminuit is done through the Minuit object, so we import it\n", "from iminuit import Minuit\n", "\n", "# we also need a cost function to fit and import the LeastSquares function\n", "from iminuit.cost import LeastSquares\n", "\n", "# display iminuit version\n", "import iminuit\n", "\n", "print(\"iminuit version:\", iminuit.__version__)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Quick start\n", "\n", "In this first section, we look at a simple case where line should be fitted to scattered $(x, y)$ data. A line has two parameters $(\\alpha, \\beta)$. We go through the full fit, showing all basic steps to get you started quickly. In the following sections we will revisit the steps in more detail."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# our line model, unicode parameter names are supported :)\n", "def line(x, α, β):\n", "    return α + x * β\n", "\n", "\n", "# generate random toy data with random offsets in y\n", "rng = np.random.default_rng(1)\n", "data_x = np.linspace(0, 1, 10)\n", "data_yerr = 0.1  # could also be an array\n", "data_y = rng.normal(line(data_x, 1, 2), data_yerr)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To recover the parameters α and β of the line model from this data, we need to minimize a suitable cost function. The cost function must be twice differentiable and have a minimum at the optimal parameters. We use the method of least-squares here, whose cost function computes the sum of squared residuals between the model and the data. The task of iminuit is to find the minimum of that function.\n", "\n", "The iminuit module provides the `LeastSquares` class to conveniently generate a least-squares cost function.\n", "We will revisit how to write one by hand in a later section. Using a built-in cost function comes with some perks, for example, the fit (if data are 1D) is automatically visualized in a Jupyter notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["least_squares = LeastSquares(data_x, data_y, data_yerr, line)\n", "\n", "m = Minuit(least_squares, α=0, β=0)  # starting values for α and β\n", "\n", "m.migrad()  # finds minimum of least_squares function\n", "m.he<PERSON>()  # accurately computes uncertainties"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["And that is all for a basic fit. The fit result is immediately visible here in the notebook, since calls to `m.migrad()` and `m.hesse()` return the `Minuit` object, which then automatically renders its state in a <PERSON>pyter notebook.\n", "\n", "The automatically generated plot of the fitted function is intentionally very basic. You can make a nicer plot by hand with `matplotlib`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# draw data and fitted line\n", "plt.errorbar(data_x, data_y, data_yerr, fmt=\"ok\", label=\"data\")\n", "plt.plot(data_x, line(data_x, *m.values), label=\"fit\")\n", "\n", "# display legend with some fit info\n", "fit_info = [\n", "    f\"$\\\\chi^2$/$n_\\\\mathrm{{dof}}$ = {m.fval:.1f} / {m.ndof:.0f} = {m.fmin.reduced_chi2:.1f}\",\n", "]\n", "for p, v, e in zip(m.parameters, m.values, m.errors):\n", "    fit_info.append(f\"{p} = ${v:.3f} \\\\pm {e:.3f}$\")\n", "\n", "plt.legend(title=\"\\n\".join(fit_info), frameon=False)\n", "plt.xlabel(\"x\")\n", "plt.ylabel(\"y\");"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["In the following, we dive into details step by step; how the Minuit object is initialized, how to run the algorithms, and how to get the results.\n", "\n", "`iminuit` was designed to make it easy to fit cost functions like `least_squares(...)`, where the parameters are individual arguments of the function. There is an alternative function signature that <PERSON><PERSON> supports, which is more convenient when you explore models that have a not-yet-defined number of parameters, for example, a polynomial. Here, the parameters are passed as a NumPy array. We will discuss both in the following, but focus on the first.\n", "\n", "## Initialize the Minuit object\n", "\n", "To minimize a function, one has to create an instance of the Minuit class and pass the function and a starting value for each parameter. This does not start the minimization yet, this will come later.\n", "\n", "The `Minuit` object uses introspection to get the number and names of the function parameters automatically, so that they can be initialized with keywords."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = Minuit(least_squares, α=0, β=0)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["If we forget a parameter or mistype them, <PERSON><PERSON> will raise an error. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    Minuit(least_squares)\n", "except RuntimeError:\n", "    import traceback\n", "\n", "    traceback.print_exc()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["try:\n", "    Minuit(least_squares, a=0, b=0)\n", "except RuntimeError:\n", "    import traceback\n", "\n", "    traceback.print_exc()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Initial parameter values\n", "\n", "The main algorithm MIGRAD is a local minimizer. It searches for a local minimum by a doing a mix of Newton steps and gradient-descents from a starting point. If your function has several minima, the minimum found will depend on the starting point. Even if it has only one minimum, iminuit will converge to it faster if you start in the proximity of the minimum.\n", "\n", "You can set the starting point using the parameter names as keywords, `<name> = <value>`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Minuit(least_squares, α=5, β=5)  # pass starting values for α and β"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Alternatively, the starting values can also be passed as positional arguments."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Minuit(least_squares, 5, 5)  # another way of passing starting values for α and β"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["You can also use iminuit with functions that accept NumPy arrays. This has pros and cons.\n", "\n", "**Pros**\n", "\n", "- Easy to change number of fitted parameters\n", "- Sometimes simpler function body that's easier to read\n", "- Technically this is more efficient, but this is hardly going to be noticeable\n", "\n", "**Cons**\n", "\n", "- imin<PERSON> cannot figure out names for each parameter\n", "\n", "To demonstrate, use a version of the line model which accepts the parameters as a NumPy array."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def line_np(x, par):\n", "    return np.polyval(par, x)  # for len(par) == 2, this is a line"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Calling `line_np` with more or less arguments is easy and will use a polynomial of the corresponding order to predict the behavior of the data.\n", "\n", "The built-in cost functions support such a model. For it to be detected properly, you need to pass the starting values in form a single sequence of numbers. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["least_squares_np = LeastSquares(data_x, data_y, data_yerr, line_np)\n", "\n", "Minuit(least_squares_np, (5, 5))  # pass starting values as a sequence"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Any sequence will work for initialization, you can also pass a list or a NumPy array here. `iminuit` uses the length of the sequence to detect how many parameters the model has. By default, the parameters are named automatically `x0` to `xN`. One can override this with the keyword `name`, passing a sequence of parameter names."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Minuit(least_squares_np, (5, 5), name=(\"a\", \"b\"))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Since `least_squares_np` works for parameter arrays of any length, one can easily change the number of fitted parameters."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# fit a forth order polynomial\n", "Minuit(least_squares_np, (5, 5, 5, 5))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["It is often useful to try different orders of a polynomial model. If the order is too small, the polynomial will not follow the data. If it is too large, it will overfit the data and pick up random fluctuations and not the underlying trend. One can figure out the right order by experimenting or using an algorithm like cross-validation.\n", "\n", "### Inspecting current parameters\n", "\n", "You can check the current parameter values and settings with the method `Minuit.params` at any time. It returns a special list of `Param` objects which pretty-prints in Jupyter and in the terminal."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.params"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This produces a nice table with numbers rounded according to the rules of the *Particle Data Group*. The table will be updated once you run the actual minimization. To look at the initial conditions later, use `Minuit.init_params`. We will come back to the meaning of *Hesse Error* and *Minos Error* later.\n", "\n", "`Minuit.params` returns a tuple-like container of `Param` objects, which are data objects with attributes that one can query. Use `repr()` to get a detailed representation of the data object."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for p in m.params:\n", "    print(repr(p), \"\\n\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Parameters with limits\n", "\n", "`iminuit` allows you to set parameter limits. Often a parameter is limited mathematically or physically to a certain range. For example, if your function contains `sqrt(x)`, then $x$ must be non-negative, $x \\ge 0$. You can set upper-, lower-, or two-sided limits for each parameter individually with the `limits` property.\n", "\n", "- Lower limit: use `Minuit.limits[<name>] = (<value>, None)` or `(<value>, float(\"infinity\"))`\n", "- Upper limit: use `Minuit.limits[<name>] = (None, <value>)` or `(-float(\"infinity\"), <value>)`\n", "- Two-sided limit: use `Minuit.limits[<name>] = (<min_value>, <max_value>)`\n", "- Remove limits: use `Minuit.limits[<name>] = None` or `(-float(\"infinity\"), float(\"infinity\")`\n", "\n", "You can also set limits for several parameters at once with a sequence. To impose the limits $α \\ge 0$ and $0 \\le β \\le 10$ in our example, we use:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.limits = [(0, None), (0, 10)]\n", "m.params"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["It is also possible for the cost function to declare limits on its parameters. For this you need the `Annotated` type, which is available in Python-3.9 or later, and from the package `typing-extensions` in Python-3.8. The restrictions should be imported from the external package `annotated-types`. The built-in cost functions propagate such annotations of model parameters. "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Annotated and Gt are imported from iminuit.typing here for universal compatibility,\n", "# but users should in general import them from external packages `typing-extensions` and\n", "# `annotated-types` to decouple models from the `iminuit` package\n", "from iminuit.typing import Annotated, Gt\n", "\n", "\n", "def line_with_positive_slope(x, slope: Annotated[float, Gt(0)], offset):\n", "    return slope * x + offset\n", "\n", "\n", "lsq = LeastSquares(data_x, data_y, data_yerr, line_with_positive_slope)\n", "\n", "Minuit(lsq, 1, 0)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["You can reset the limit automatically set by such an annotation by calling `minuit_instance.limit[\"slope\"] = None` before fitting, if you wish.\n", "\n", "### Fixing and releasing parameters\n", "\n", "Sometimes you have a parameter which you want to set to a fixed value temporarily. Perhaps you have a guess for its value, and you want to see how the other parameters adapt when this parameter is fixed to that value.\n", "\n", "Or you have a complex function with many parameters that do not all affect the function at the same scale. Then you can manually help the minimizer to find the minimum faster by first fixing the less important parameters to initial guesses and fit only the important parameters. Once the minimum is found under these conditions, you can release the fixed parameters and optimize all parameters together. <PERSON><PERSON> remembers the last state of the minimization and starts from there. The minimization time roughly scales with the square of the number of parameters. Iterated minimization over subspaces of the parameters can reduce that time.\n", "\n", "To fix an individual parameter, use `minuit_instance.fixed[<name>] = True`. In our example, we fix α:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.fixed[\"α\"] = True\n", "m.params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# migrad will not vary α, only β\n", "m.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Now we release α and fix β and minimize again, you can also use the parameter index instead of its name."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.fixed[0] = False\n", "m.fixed[1] = True\n", "m.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We could iterate this and would slowly approach the minimum, but that's silly; instead we release both parameters and run again. The array-like views support broadcasting to enable this shortcut notation:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.fixed = False\n", "m.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["It is also possible to fix a parameter and set a value with one convenient call, using `Minuit.fixto`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.fixto(\"α\", 3)\n", "m.params"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Varying starting points for minimization\n", "\n", "It is sometimes useful to change the values of some fixed parameters by hand and fit the others or to restart the fit from another starting point. For example, if the cost function has several minima, changing the starting value can be used to find the other minimum."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def cost_function_with_two_minima(x):\n", "    return x**4 - x**2 + 1\n", "\n", "\n", "x = np.linspace(-1.5, 1.5)\n", "plt.plot(x, cost_function_with_two_minima(x));"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# starting at -0.1 gives the left minimum\n", "m = Minuit(cost_function_with_two_minima, x=-0.1)\n", "m.migrad()\n", "print(\"starting value -0.1, minimum at\", m.values[\"x\"])\n", "\n", "# changing the starting value to 0.1 gives the right minimum\n", "m.values[\"x\"] = 0.1  # m.values[0] = 0.1 also works\n", "m.migrad()\n", "print(\"starting value +0.1, minimum at\", m.values[\"x\"])"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Advanced: Simplex and Scan minimizers \n", "\n", "`iminuit` also offers two other minimizers which are less powerful than MIGRAD, but may be useful in special cases.\n", "\n", "#### SIMPLEX\n", "\n", "The Nelder-Mead method (aka SIMPLEX) is well described on [Wikipedia](https://en.wikipedia.org/wiki/Nelder%E2%80%93Mead_method). It is a gradient-free minimization method that usually converges more slowly, but may be more robust. For some problems it can help to start the minimization with SIMPLEX and then finish with MIGRAD. Since the default stopping criterion for SIMPLEX is much more lax than MIGRAD, either running MIGRAD after SIMPLEX or reducing the tolerance with `Minuit.tol` is strongly recommended."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Minuit(cost_function_with_two_minima, x=10).simplex()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Let's run MIGRAD after SIMPLEX to finish the minimization."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Minuit(cost_function_with_two_minima, x=10).simplex().migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This combination uses slightly fewer function evaluations and produced a more accurate result than just running MIGRAD alone in this case (for another problem this may not be true)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Minuit(cost_function_with_two_minima, x=10).migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### <PERSON>an\n", "\n", "Scan is a last resort. It does an N-dimensional grid scan over the parameter space. The number of function evaluations scale like $n^k$, where $k$ is the number of parameters and $n$ the number of steps along one dimension. Using scan for high-dimensional problems is unfeasible, but it can be useful in low-dimensional problems and when all but a few parameters are fixed. The scan needs bounds, which are best set with `Minuit.limits`. The number of scan points is set with the `ncall` keyword."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = Minuit(cost_function_with_two_minima, x=10)\n", "m.limits = (-10, 10)\n", "m.scan(ncall=50)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The scan brought us in proximity of the minimum.\n", "\n", "In this case, the minimum is considered valid, because the EDM value is smaller than the EDM goal, but the scan may also end up in an invalid minimum, which is also ok. The scan minimizes the cost function using a finite number of steps, regardless of the EDM value (which is only computed after the scan for the minimum).\n", "\n", "One should always run MIGRAD or SIMPLEX after a SCAN."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Advanced: <PERSON><PERSON><PERSON><PERSON>\n", "\n", "If you do not use one of the cost functions from the `iminuit.cost` module, you may need to pass an additional parameter to Minuit.\n", "\n", "Minuit by default assumes that the function scales like a chi-square function when one of the parameters is moved away from the minimum. If your cost function is constructed as a log-likelihood, it scales differently, and you must indicate that to Min<PERSON> wit the `errordef` parameter. Setting this is not needed for the cost functions in `iminuit.cost`.\n", "\n", "The `errordef` parameter is required to compute correct uncertainties. If you don't care about uncertainty estimates (but why are you using Minuit then?), you can ignore it. Minuit supports two kinds of cost functions, the *negative log-likelihood* and the *least-squares* function. Each has a corresponding value for `errordef`:\n", "\n", " - `0.5` or the constant `Minuit.LIKELIHOOD` for negative log-likelihood functions \n", " - `1` or the constant `Minuit.LEAST_SQUARES` for least-squares functions (the default)\n", "\n", "If you like to understand the origin of these numbers, have a look into the study **Hesse and Minos**, which explains in depth how uncertainties are computed.\n", "\n", "For our custom cost function, we could set `m.errordef=1` or `m.errordef=Minuit.LEAST_SQUARES`, which is more readable."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# a simple least-squares cost function looks like this...\n", "def custom_least_squares(a, b):\n", "    ym = line(data_x, a, b)\n", "    z = (data_y - ym) / data_yerr\n", "    return np.sum(z**2)\n", "\n", "\n", "m = Minuit(custom_least_squares, 1, 2)\n", "m.migrad()  # standard errordef, correct in this case"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.errordef = Minuit.LIKELIHOOD  # errordef for negative log-likelihoods, wrong here\n", "m.migrad()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The reported errors are now by a factor `sqrt(2)` smaller than they really are.\n", "\n", "An even better way is to add an attribute called `errordef` to the cost function. If such an attribute is present, <PERSON><PERSON> uses it. Since this cost function has the default scaling, we do not need to set anything, but keep it in mind for negative log-likelihoods."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# artificial cost function that scales like a negative log-likelihood\n", "def custom_least_squares_2(a, b):\n", "    return 0.5 * custom_least_squares(a, b)\n", "\n", "\n", "# Instead of calling Minuit.errordef, we assign an errordef attribute to the cost\n", "# function. Minuit will automatically use this value.\n", "custom_least_squares_2.errordef = Minuit.LIKELIHOOD\n", "\n", "m = Minuit(custom_least_squares_2, 1, 2)\n", "m.migrad()  # uses the correct errordef automatically"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We get the correct errors. The built-in cost functions from the module `iminuit.cost` all define the `errordef` attribute, so you don't need to worry about that.\n", "\n", "If the cost function defines the `errordef`, it should not be necessary to set it to another value, so `Minuit` warns you if you try to set it."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# raises a warning\n", "m.errordef = 1"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Advanced: Initial step sizes\n", "\n", "<PERSON><PERSON> uses a gradient-descent method to find the minimum, and the gradient is computed numerically using finite differences. The initial step size is used to compute the first gradient. A good step size is small compared to the curvature of the function, but large compared to numerical resolution. Using a good step size can slightly accelerate the convergence, but <PERSON><PERSON> is not very sensitive to the choice. If you don't provide a value, <PERSON><PERSON><PERSON> will guess a step size based on a heuristic.\n", "\n", "You can set initial step sizes with the `errors` property, `Minuit.errors[<name>] = <step size>`. Using an appropriate step size is important when you have you a parameter which has physical bounds. Varying the initial parameter value by the step size may not create a situation where the parameter goes outside its bounds. For example, a parameter $x$ with $x > 0$ and initial value $0.1$ may not have a step size of $0.2$.\n", "\n", "In our example, we could use an initial step size of $\\Delta α = 0.1$ and $\\Delta β = 0.2$. Setting both can be done conveniently by assigning a sequence:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = Minuit(least_squares, α=5, β=5)\n", "m.errors = (0.1, 0.2)  # assigning sequences works\n", "m.params"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Broadcasting is also supported."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.errors = 0.3  # broadcasting\n", "m.params"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Only positive step sizes are allowed. Non-positive values are replaced with the heuristic and a warning is emitted."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.errors[\"β\"] = -0.3\n", "m.params"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Advanced: Override parameter name detection\n", "\n", "`iminuit` tries hard to detect the parameter names correctly. It works for a large variety of cases. For example, if you pass a functor instead of a function, it will use the arguments of the `__call__` method, automatically skipping `self`. It even tries to parse the docstring if all else fails.\n", "\n", "You can check which parameter names imin<PERSON> finds for your function with the `describe` function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from iminuit import describe\n", "\n", "\n", "def foo(x, y, z):\n", "    pass\n", "\n", "\n", "assert describe(foo) == [\"x\", \"y\", \"z\"]\n", "\n", "\n", "class Foo:\n", "    def __call__(self, a, b):\n", "        pass\n", "\n", "\n", "assert describe(Foo()) == [\"a\", \"b\"]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Sometimes parameter names cannot be determined, for example, when a function accepts a variable number of arguments."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def func_varargs(*args):  # function with variable number of arguments\n", "    return np.sum((np.array(args) - 1) ** 2)\n", "\n", "\n", "assert describe(func_varargs) == []"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["`describe` cannot detect the number and names of the parameters in this case and returns an empty list. If you work with functions that accept a variable number of arguments a lot, it is better to use a cost function which accepts a parameter array (this is explained in the next section).\n", "\n", "When iminuit cannot detect the arguments, but you know how many arguments there are, or if you simply want to override the names found by `iminuit`, you can do that with the keyword `name`, like so:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["Minuit(func_varargs, name=(\"a\", \"b\"), a=1, b=2).migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Alternative interface: `iminuit.minimize`\n", "\n", "Those familiar with `scipy` may find the `minimize` function useful. It exactly mimics the function interface of `scipy.optimize.minimize`, but uses `Minuit` for the actual minimization. The `scipy` package must be installed to use it."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from iminuit import minimize  # has same interface as scipy.optimize.minimize\n", "\n", "minimize(least_squares_np, (5, 5))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This interface is handy if you want to be able to switch between iminuit and `scipy.optimize.minimize`, but we recommend the standard interface instead. It is an advantage of Minuit that you can interact and manually steer the minimization process. This is not as convenient with a functional interface like `minimize`."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Investigating the fit status\n", "\n", "Calling `Minuit.migrad()` runs the actual minimization with the MIGRAD algorithm. MIGRAD essentially tries a Newton-step and if that does not produce a smaller function value, it tries a line search along the direction of the gradient. So far so ordinary. The clever bits in MIGRAD are how various pathological cases are handled.\n", "\n", "Let's look again at the output of `Minuit.migrad()`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m = Minuit(least_squares, α=5, β=5)\n", "m.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The `Minuit.migrad` method returns the Minuit instance so that one can chain method calls. The instance also pretty prints the latest state of the minimization.\n", "\n", "The first block in this output is showing information about the function minimum. This is good for a quick check:\n", "\n", "- All blocks should be green.\n", "- Purple means something bad. \n", "- Yellow may be bad or not. Be careful.\n", "\n", "Let's see how it looks when the function is bad."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m_bad = Minuit(lambda x: 0, x=1)  # a constant function has no minimum\n", "m_bad.migrad()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Coming back to our previous good example, the info about the function minimum can be directly accessed with `Minuit.fmin`:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.fmin"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# print(repr(...)) to see a detailed representation of the data object\n", "print(repr(m.fmin))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The most important one here is `is_valid`. If this is false, the fit did not converge and the result is useless. Since this is so often queried, a shortcut is provided with `Minuit.valid`.\n", "\n", "If the fit fails, there is usually a numerical or logical issue.\n", "\n", "- The fit function is not analytical everywhere in the parameter space or does not have a local minimum (the minimum may be at infinity, the extremum may be a saddle point or maximum). Indicators for this are `is_above_max_edm=True`, `hesse_failed=True`, `has_posdef_covar=False`, or `has_made_posdef_covar=True`. A non-analytical function is one with a discrete step, for example.\n", "- MIGRAD reached the call limit before the convergence so that `has_reached_call_limit=True`. The number of function calls is given by `nfcn`, and the call limit can be changed with the keyword argument `ncall` in the method `Minuit.migrad`. Note that `nfcn` can be slightly larger than `ncall`, because MIGRAD internally only checks this condition after a full iteration, in which several function calls can happen.\n", "\n", "MIGRAD detects convergence by a small `edm` value, the *estimated distance to minimum*. This is the difference between the current minimum value of the minimized function and the prediction based on the current local quadratic approximation of the function (something that MIGRAD computes as part of its algorithm). If the fit did not converge, `is_above_max_edm` is true.\n", "\n", "If you are interested in parameter uncertainties, you should make sure that:\n", "\n", "- `has_covariance`, `has_accurate_covar`, and `has_posdef_covar` are true.\n", "- `has_made_posdef_covar` and `hesse_failed` are false.\n", "\n", "The second object of interest after the fit is the parameter list, which can be directly accessed with `Minuit.params`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for p in m.params:\n", "    print(repr(p))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["`m.params` is a tuple-like container of `Param` data objects which contain information about the fitted parameters. Important fields are:\n", "\n", "- `number`: parameter index.\n", "- `name`: parameter name.\n", "- `value`: value of the parameter at the minimum.\n", "- `error`: uncertainty estimate for the parameter value.\n", "\n", "Whether the uncertainty estimate is accurate depends on the correct mathematical modeling of your fitting problem and using the right `errordef` value for <PERSON><PERSON>. What do we mean by correct mathematical modelling? If you look into the function `simple_least_squares(a, b)`, you see that each squared residual is divided by the expected variance of the residual. This is necessary to get accurate uncertainty estimates for the parameters.\n", "\n", "Sometimes the expected variance of the residual is not well known. If the cost function to minimize satisfies certain conditions, there is a simple test to check whether the residual variances are ok. One should look at the function value at the minimum, given by `Minuit.fmin.fval`, and divide it by the so-called degrees of freedom, which is difference of the number of residuals and the number of fitted parameters, and can be queried with the attribute `Minuit.ndof`. This is called reduced chi2, it can be directly queried with `Minuit.fmin.reduced_chi2`.\n", "\n", "The reduced chi2 is available for all built-in binned cost functions and the `LeastSquares` cost function in `iminuit.cost`. It cannot be automatically provided for unbinned cost functions, since that requires binning the data, which has to be defined by the user. For unbinned cost functions, you can still compute a reduced chi2 yourself, but it is not possible to do automatically. Querying `Minuit.fmin.reduced_chi2` is safe, it either returns a valid value or `nan` if the chi2 cannot be computed automatically for the current cost function."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["f\"𝜒²/ndof = {m.fval:.2f} / {m.ndof} = {m.fmin.reduced_chi2:.2f}\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["This value should be around 1. The more data points one has, the closer. If the value is much larger than 1, then the data variance is underestimated or the model does not describe the data. If the value is much smaller than 1, then the data variance is overestimated (perhaps because of positive correlations between the fluctuations of the data values).\n", "\n", "The last block shows the covariance matrix, this is useful to check for large correlations which are usually a sign of trouble."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.covariance"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We will discuss this matrix in more detail in the next section.\n", "\n", "##  Parameter uncertainties, covariance, and confidence intervals/regions\n", "\n", "You saw how to get the uncertainty of each individual parameter and how to access the full covariance matrix of all parameters together, which includes the correlations. Correlations are essential additional information if you want to work with parameter uncertainties seriously.\n", "\n", "Minuit offers two ways to compute the parameter uncertainties, Hesse and Minos. Both have pros and cons.\n", "\n", "### Hesse for covariance and correlation matrices\n", "\n", "The Hesse algorithm numerically computes the matrix of second derivatives at the function minimum (called the Hesse matrix) and inverts it. The Hesse matrix is symmetric by construction. In the limit of infinite data samples to fit, the result of this computation converges to the true covariance matrix of the parameters. It is often a good and sometimes even an unbiased estimate for finite samples. These errors obtained from this method are sometimes called *parabolic errors*, because the Hesse matrix method is exact if the function is a hyperparabola (third and higher-order derivatives are all zero). The errors are also by construction symmetric in positive and negative direction.\n", "\n", "**Pros**\n", "\n", "- (Comparably) fast computation.\n", "- Provides covariance matrix for error propagation.\n", "- Provides symmetric errors which are easy to work with.\n", "\n", "**Cons**\n", "\n", "- May not have good coverage probability when sample size is small.\n", "\n", "The MIGRAD algorithm computes an approximation of the Hesse matrix automatically during minimization. When the default strategy is used, <PERSON>uit does a check whether this approximation is sufficiently accurate and if not, it computes the Hesse matrix automatically.\n", "\n", "All this happens inside the C++ Minuit2 code and is a bit intransparent, so to be on the safe side, we recommend to call `Minuit.hesse` explicitly after the minimization, if exact errors are important."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# let's mess up the current errors a bit so that he<PERSON> has something to do\n", "m.errors = (0.16, 0.2)\n", "m.params"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.he<PERSON>().params  # note the change in \"Hesse Error\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["#### Covariance and correlation Matrix\n", "\n", "To see the covariance matrix of the parameters, you do:"]}, {"cell_type": "code", "execution_count": null, "metadata": {"inputHidden": false, "outputHidden": false}, "outputs": [], "source": ["m.covariance"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The parameters α and β are strongly anti-correlated, the numerical value of the correlation is shown in parentheses. The correlation is also highlighted by the blue color of the off-diagonal elements."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(repr(m.covariance))  # use print(repr(...) to skip pretty printing"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To get the correlation matrix, use:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.covariance.correlation()  # returns a newly created correlation matrix"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["Nonzero correlation is not necessarily a bad thing, but if you have freedom in redefining the parameters of the fit function, it is good to chose parameters which are not strongly correlated.\n", "\n", "**Warning:** <PERSON><PERSON> cannot accurately minimize the function if two parameters are (almost) perfectly (anti-)correlated. It also means that one of two parameters is superfluous, it doesn't add new information. You should rethink the cost function in this case and try to remove one of the parameters from the fit, either by fixing its value or by expressing it as a function of the other parameters.\n", "\n", "Both matrices are subclasses of `numpy.ndarray`, so you can use them everywhere you would use a NumPy array. In addition, these matrices support value access via parameter names:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.covariance[\"α\", \"β\"]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### MINOS for non-parabolic minima\n", "\n", "Minuit has another algorithm to compute uncertainties: MINOS. It implements the so-called profile likelihood method, where the neighborhood around the function minimum is scanned until the contour is found where the function increase by the value of `errordef`. The contour defines a confidence region that covers the true parameter point with a certain probability. The probability is exactly known in the limit of infinitely large data samples, but approximate for the finite case. Please consult a textbook about statistics about the mathematical details or look at the tutorial \"Error computation with HESSE and MINOS\".\n", "\n", "**Pros**\n", "\n", "- Produces asymmetric errors, which may better visualize the uncertainty in the parameter.\n", "- Produces pretty two-dimensional confidence regions for scientific plots.\n", "\n", "**Cons**\n", "\n", "- Computationally expensive.\n", "- Asymmetric errors are difficult to error-propagate, see [<PERSON> 2004](https://arxiv.org/pdf/physics/0406120.pdf).\n", "\n", "MINOS is not automatically called during minimization, it needs to be called explicitly afterwards, like so:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.minos()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["By now you are probably used to seeing green colors, which indicate that Minos ran successful. Be careful when these are red instead, Minos can fail. The fields in the new Minos table mean the following:\n", "\n", "- Valid: Whether <PERSON><PERSON> considers the scan result valid.\n", "- At Limit: True if <PERSON><PERSON> hit a parameter limit before the finishing the contour, which would be bad.\n", "- Max FCN: True if <PERSON><PERSON> reached the maximum number of allowed calls before finishing the contour, also bad.\n", "- New Min: True if <PERSON><PERSON> discovered a deeper local minimum in the neighborhood of the current one. Not necessarily bad, but should not happen.\n", "\n", "The errors computed by <PERSON><PERSON> are now also shown in the parameter list."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.params"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["**Note:** If the absolute values of the Minos errors are very close to the Hesse Error, the function is well approximated by a hyperparabola around the minimum. You can use this as a check instead of explicitly plotting the function around the minimum (for which we provide tools, see below).\n", "\n", "### Coverage probability of intervals/regions constructed with Hesse and Minos algorithms\n", "\n", "It is important to construct confidence intervals (and confidence regions in multiple dimensions) which have a well-defined coverage probability. How confidence intervals are correctly interpreted is [explained in the Wikipedia](https://en.wikipedia.org/wiki/Confidence_interval), [this Stackoverflow article](https://stats.stackexchange.com/questions/26450/why-does-a-95-confidence-interval-ci-not-imply-a-95-chance-of-containing-the/26457#26457), and in good introductory text books on statistics. The slightly unintuitive interpretation is the price to pay in the frequentist framework of statistics to avoid subjective priors.\n", "\n", "Standard one-dimensional confidence intervals should have 68 % coverage probability. As previously mentioned, the coverage probability of the intervals constructed from the uncertainties reported by Hesse and Minos are not necessarily the standard 68 %.\n", "\n", "Whether Hesse or Minos produce an interval with a coverage probability closer to the desired level in finite samples depends on the case. There are theoretical results which suggest that Hesse may be slightly better, but we also found special cases where Minos intervals performed better.\n", "\n", "Some sources claim that Minos gives better coverage when the cost function is not parabolic around the minimum; that is not generally true, in fact Hesse intervals may have better coverage.\n", "\n", "As a rule-of-thumb, use Hesse as the default and try both algorithms if accurate coverage probability matters.\n", "\n", "## Quick access to fit results\n", "\n", "You get the main fit results with properties and methods from the `Minuit` object. We used several of them already. Here is a summary:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(m.values)  # array-like view of the parameter values"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# access values by name or index\n", "print(\"by name \", m.values[\"α\"])\n", "print(\"by index\", m.values[0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# iterate over values\n", "for key, value in zip(m.parameters, m.values):\n", "    print(f\"{key} = {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# slicing works\n", "print(m.values[:1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(m.errors)  # array-like view of symmetric uncertainties"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["`Minuit.errors` supports the same access as `Minuit.values`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(m.params)  # parameter info (using str(m.params))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(repr(m.params))  # parameter info (using repr(m.params))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# asymmetric uncertainties (using str(m.merrors))\n", "print(m.merrors)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# asymmetric uncertainties (using repr(m.merrors))\n", "print(repr(m.merrors))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(m.covariance)  # covariance matrix computed by Hesse (using str(m.covariance))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\n", "    repr(m.covariance)\n", ")  # covariance matrix computed by Hesse (using repr(m.covariance))"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["As already mentioned, you can play around with iminuit by assigning new values to `m.values` and `m.errors` and then run `m.migrad()` again. The values will be used as a starting point.\n", "\n", "## Plotting\n", "\n", "`iminuit` comes with built-in methods to draw the likelihood around the minimum. These can be used to draw confidence regions with a defined confidence level or for debugging the likelihood.\n", "\n", "### Drawing confidence regions\n", "\n", "To get a generic overview, use the method `Minuit.draw_mnmatrix`. It shows scans over the likelihood where all other parameters than the ones scanned are minimized, in other words, it is using the <PERSON><PERSON> algorithm. The regions and intervals found in this way correspond to uncertainty intervals. It is also a great way to see whether the likelihood is sane around the minimum.  "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# find the minimum again after messing around with the parameters\n", "m.migrad()\n", "\n", "# draw matrix of likelihood contours for all pairs of parameters at 1, 2, 3 sigma\n", "m.draw_mnmatrix();"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The diagonal cells show the 1D profile around each parameter. The points were the horizontal lines cross the profile correspond to confidence intervals with confidence level `cl` (a probability). The off-diagonal cells show confidence regions with confidence level `cl`. Asymptotically (in large samples), the `cl` is equal to the probability that the region contains the true value. In finite samples, this is usually only approximately so.\n", "\n", "For convenience, the drawing functions interpret `cl >= 1` as the number of standard deviations with a confidence level that corresponds to a standard normal distribution:\n", "\n", "- cl = 1: 68.3 %\n", "- cl = 2: 95.4 %\n", "- cl = 3: 99.7 %\n", "\n", "Drawing all profiles and regions can be time-consuming. The following commands show how to draw only individual contours or profiles."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# draw three confidence regions with 68%, 90%, 99% confidence level\n", "m.draw_mncontour(\"α\", \"β\", cl=(0.68, 0.9, 0.99));"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# get individual contours to plot them yourself\n", "pts = m.mncontour(\"α\", \"β\", cl=0.68, size=20)\n", "x, y = np.transpose(pts)\n", "plt.plot(x, y, \"o-\");"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["To make the contour look nicer, you can increase the `size` parameter or use the `interpolated` parameter to do cubic spline interpolation or use the `experimental` algorithm."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# draw original points\n", "plt.plot(x, y, \".\", label=\"size=20\")\n", "\n", "# draw interpolated points\n", "pts2 = m.mncontour(\"α\", \"β\", cl=0.68, size=20, interpolated=100)\n", "x2, y2 = np.transpose(pts2)\n", "plt.plot(x2, y2, label=\"size=20, interpolated\")\n", "\n", "# actual curve at higher resolution\n", "pts = m.mncontour(\"α\", \"β\", cl=0.68, size=100)\n", "x3, y3 = np.transpose(pts)\n", "plt.plot(x3, y3, \"-\", label=\"size=100\")\n", "\n", "plt.legend();"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# with experimental algorithm\n", "pts = m.mncontour(\"α\", \"β\", cl=0.68, size=50, experimental=True)\n", "x4, y4 = np.transpose(pts)\n", "plt.plot(x4, y4, \"-\", label=\"size=50 experimental\");"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["The experimental algorithm takes more time but produces a smoother contour.\n", "\n", "To draw the 1D profile, call `Minuit.draw_mnprofile`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.draw_mnprofile(\"α\");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# or use this to plot the result of the scan yourself\n", "a, fa, ok = m.mnprofile(\"α\")\n", "plt.plot(a, fa);"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Likelihood debugging\n", "\n", "`mnmatrix`, `mnprofile`, and `mncontour` do <PERSON><PERSON> scans. If you have trouble with <PERSON><PERSON> or with the minimization, you should check how the likelihood looks like where you are. The following functions perform no minimization, they just draw the likelihood function as it is at certain coordinates."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# draw 1D scan over likelihood, the minimum value is subtracted by default\n", "m.draw_profile(\"α\");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# or draw it yourself, the minimum value is not subtracted here\n", "x, y = m.profile(\"α\")\n", "plt.plot(x, y);"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# draw 2D scan over likelihood\n", "m.draw_contour(\"α\", \"β\");"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# or use this to plot the result of the scan yourself\n", "x, y, z = m.contour(\"α\", \"β\", subtract_min=True)\n", "cs = plt.contour(x, y, z, (1, 2, 3, 4))  # these are not sigmas, just the contour values\n", "plt.clabel(cs);"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Interactive fit\n", "\n", "In Jupyter notebooks, it is possible to fit a model to data interactively, by calling `Minuit.interactive`. This functionality requires optional extra packages. If they are not there, you will get a friendly error message telling you what you need to install."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["m.interactive()"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["You can change the parameter values with the sliders. Clicking the \"Fit\" button runs `Minuit.migrad` with these as starting values.\n", "\n", "**Note:** If you see this notebook on ReadTheDocs or otherwise statically rendered, changing the sliders won't change the plot. This requires a running Jupyter kernel.\n", "\n", "Interactive fits are useful to find starting values and to debug the fit. The following issues are easy to detect:\n", "\n", "- Starting values are way off.\n", "- You forgot to set limits on some parameters.\n", "- Some parameters are strongly correlated.\n", "- Your model is not analytical.\n", "\n", "Strong correlations are caused when a change to one parameter can be almost perfectly undone by a changing one or more other parameters. If the model suddenly jumps when you move the sliders, this may indicate that the model is not analytical, but also note that the sliders have finite resolution and the model curve is also only drawn with finite resolution. Set tighter limits on the affected parameter or investigate the root cause with numerical experiments.\n", "\n", "`Minuit.interactive` uses the `visualize` method on the cost function, if it is available. All built-in cost functions provide this method, but it only works for 1D distributions, since there is no obvious general way to visualize data-model agreement in higher dimensions. You can provide your visualization though, see the documentation of `Minuit.interactive`. This can also be useful to draw the model in more detail, for example, if you want to give different components in an additive model different colors (e.g. signal and background)."]}], "metadata": {"kernel_info": {"name": "python3"}, "kernelspec": {"display_name": "Python 3.8.13 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "nteract": {"version": "0.12.3"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 4}