{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# Hesse and Minos\n", "\n", "We discuss how uncertainty computation with Hesse and Minos works and how the two approaches differ.\n", "\n", "iminuit (and C++ MINUIT) offers two major ways of computing parameter uncertainties from the cost function (which must be of least-squares form or negative log-likelihood), HESSE and MINOS. These have different pros and cons, on which we already touched in the basic tutorial. Here we want to go a bit deeper into the pros and cons and also try to reveal a bit of the math behind the two approaches.\n", "\n", "There are several sources which explain what HESSE and MINOS do, in particular the MINUIT User's Guide (linked from the [iminuit documentation](https://scikit-hep.org/iminuit/about.html)). The mathematical details are covered in <PERSON><PERSON>, \"Statistical Methods in Experimental Physics\", 2nd edition, World Scientific (2006)."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We need to use a couple of technical terms from mathematical statistics when talk about proven properties. We also use recurring variable names with the same meaning throughout.\n", "\n", "* $n$ number of [observations](https://en.wikipedia.org/wiki/Sample_%28statistics%29)\n", "* $x$ observation from data distribution $f(x; \\theta)$ with parameter $\\theta$ (not an angle!)\n", "* $\\hat \\theta$ estimate of $\\theta$ obtained from a sample (the parameter value obtained from the fit)\n", "* $\\mathcal L$ likelihood function\n", "* $\\mathcal I$ [Fisher information](https://en.wikipedia.org/wiki/Fisher_information)\n", "* $E[\\dots]$ expectation value of $\\dots$ over distribution of $x$\n", "* $V[\\dots]$ variance of $\\dots$\n", "\n", "The terms **asymptotic** and **asymptotic limit** refer to inference from an infinite data sample. Mathematical properties of statistical methods are most commonly computed in this limit."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## HESSE\n", "\n", "HESSE in a nutshell:\n", "\n", "* Computes approximate covariance matrix $C_{ij}$ for all fitted parameters\n", "* Square-root of diagonal elements of $C_{ij}$ corresponds to one standard deviation; to obtain $k$ standard deviations, multiply covariance matrix $C$ by $k^2$\n", "* Can be used to form confidence intervals $\\hat \\theta_i \\pm C_{ii}^{1/2}$\n", "* Only way to obtain parameter correlations\n", "* Comparably fast, requires:\n", "    * Numerical computation of all second derivatives of the cost function (Hessian matrix)\n", "    * Inverting this matrix\n", "* Cannot compute uncertainty for only one parameter if there are several\n", "* Approximately computed as by-product of MIGRAD algorithm (usually accurate enough when `strategy` >= 1 is used)\n", "\n", "The algorithm derives its name from the Hessian matrix of second derivatives which is computed to obtain the uncertainties.\n", "\n", "### How does it work?\n", "\n", "The matrix of second derivatives of the cost function is computed for all free parameters and multiplied by 2. The result is inverted and multiplied by `errordef`.\n", "\n", "MINUIT implements parameter limits by applying a variable transformation and therefore distinguishes internal and external parameter space. The Hessian matrix is computed in the internal parameter space and transformed using the chain rule to external space.\n", "\n", "### Why does it work?\n", "\n", "One can prove under general conditions in the asymptotic limit that a parameter estimate $\\hat\\theta$ obtained from the least-squares and the maximum-likelihood methods is normally distributed with minimum variance, given by the [<PERSON><PERSON><PERSON><PERSON> lower bound](https://en.wikipedia.org/wiki/Cram%C3%A9r%E2%80%93Rao_bound), which is the minimum for any unbiased estimator (these methods are asymptotically unbiased).\n", "\n", "$$\n", "V(\\hat \\theta) \\underset{n\\rightarrow \\infty}{\\longrightarrow} \\left\\{ n E\\left[\\left( \\frac{\\partial \\ln\\! f(x;\\theta)}{\\partial \\theta} \\right)^2 \\right]_{\\theta = \\hat\\theta} \\right\\}^{-1} = \\left\\{ -n E\\left[\\frac{\\partial^2 \\ln\\! f(x;\\theta)}{\\partial \\theta^2} \\right]_{\\theta = \\hat\\theta} \\right\\}^{-1}\n", "$$\n", "\n", "The expectation here is taken over the data distribution. Since the expectation value is constant, we see that the variance of $\\hat\\theta$ scales goes down with $n^{-1}$ in the asymptotic limit.\n", "\n", "If the data range is independent of $\\theta$, which we usually assume (but see <PERSON><PERSON> book for a counter example), we can swap integration over $x$ and differentiation with respect to $\\theta$. Doing this and replacing the expectation with its plug-in estimate, the arithmetic average, we obtain:\n", "\n", "$$\n", "-n E\\left[\\frac{\\partial^2 \\ln\\! f(x;\\theta)}{\\partial \\theta^2} \\right]_{\\theta = \\hat \\theta} = -n \\frac{1}{n} \\sum_i \\frac{\\partial^2 \\ln\\! f(x_i; \\theta)}{\\partial \\theta^2}\\Big\\vert_{\\theta = \\hat \\theta} = \\frac{\\partial^2 \\big(-\\sum_i \\ln\\! f(x_i; \\theta)\\big)}{\\partial \\theta^2}\\Big\\vert_{\\theta = \\hat \\theta}\n", "$$\n", "\n", "We now see that the numerator contains the negative log-likelihood  function that we often plug into iminuit. If there is a vector of parameters $\\hat{\\vec \\theta}$, then this turns into the Hessian matrix of second derivatives.\n", "\n", "#### A bit of history\n", "\n", "So what about these factors of $2$ and `errordef` in MINUIT that were mentioned above? These don't appear here. There are historical reasons for those. In the asymptotic limit, the least-squares cost function that corresponds to the log-likelihood is $Q = -2 \\ln\\! \\mathcal{L} - \\text{constants}$. MINUIT was originally developed with least-squares fits in mind, therefore its internal math assumes the $Q$ form. If the second derivatives are computed from $Q$, the constants are removed but the Hessian must be multiplied by a factor of two to get the right variance. Correspondingly, if the user puts in a negative log-likelihood function, the same procedure now introduces an extra factor of two $2$, which must be compensated by the `errordef` value of 0.5 for the negative log-likelihood.\n", "\n", "### Why is HESSE approximate\n", "\n", "We started out from <PERSON><PERSON><PERSON><PERSON> bound, the asymptotic limit for the parameter variance. How fast the finite sample approaches the limit depends on the problem at hand. For normal distributed data, the bound is exact.\n", "\n", "We further approximated the computation of the bound by replacing the expectation over the likelihood with the sample mean of the likelihood. \n", "\n", "## MINOS\n", "\n", "MINOS in a nutshell:\n", "\n", "* Approximate confidence intervals (intervals are wrongly claimed to be \"exact\" in some sources, including the MINUIT paper from 1975)\n", "* Cannot compute parameter correlations\n", "* Some (unverified) sources claim better coverage probability than intervals based on HESSE\n", "* In general slower than HESSE (requiring more function evaluations):\n", "    * Iteratively computes the value pair $\\theta^d, \\theta^u$ which increases the cost function by `errordef` over the minimum\n", "    * If the cost function has several parameters, it is minimised with respect to all other parameters during this scan\n", "* Can be used to compute uncertainty for only one parameter - but this is not more efficient than HESSE, since the computation requires at least one evaluation of HESSE\n", "\n", "The MINOS algorithm computes [profile-likelihood based](https://en.wikipedia.org/wiki/Likelihood_function#Profile_likelihood) confidence intervals.\n", "\n", "### How does it work?\n", "\n", "MINOS scans the likelihood along one parameter $\\theta_i$,  while minimizing the likelihood with respect to all other parameters $\\theta_k$ with $k \\ne i$. This is effectively the same as expressing the other parameter estimates as a function of $\\theta_i$, $\\hat \\theta_k(\\theta_i)$, and scanning the now one-dimensional negative log-likelihood $-\\ln \\mathcal{L}(\\theta_i; \\theta_k = \\hat \\theta_k(\\theta_i) , k\\ne i)$. This is called the [profile likelihood](https://en.wikipedia.org/wiki/Likelihood_function#Profile_likelihood) for parameter $\\theta_i$.\n", "\n", "One follows this curve until $-\\ln \\mathcal{L}$ increases by `errordef` with respect to its minimum and stores the two corresponding values $\\theta^d_i$ and $\\theta^u_i$. The interval $(\\theta^d_i, \\theta^u_i)$ has $68\\,\\%$ coverage probability in the asymptotic limit. In multi-dimensional parameter space, the computation is comparably expensive due to the iterative steps of scanning and minimization.\n", "\n", "An efficient algorithm to compute the interval is described by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON> in *A Method for Computing Profile-Likelihood-Based Confidence Intervals*, Journal of the Royal Statistical Society C37 (1988) 87–94 [DOI](https://doi.org/10.2307%2F2347496).\n", "\n", "### Why does it work?\n", "\n", "We define the likelihood ratio $\\ell(\\vec\\theta) = \\mathcal{L}(\\vec\\theta) / \\mathcal{L}(\\hat{\\vec\\theta})$. In the asymptotic limit, $-2 \\ln \\ell(\\vec\\theta)$ is $\\chi^2(k)$ distributed, where $k$ is the number of fitted parameters. For $k = 1$, the $\\chi^2$-interval $[0, 1)$ contains $68\\,\\%$ probability. For a single parameter fit therefore a corresponding interval is found by the two values $\\theta^d$ and $\\theta^u$ which solve\n", "\n", "$$\n", "-2\\ln\\ell(\\theta) = 1 \\Leftrightarrow -\\ln\\ell(\\theta) = 1/2\n", "$$\n", "\n", "We recognize the difference in the negative log-likelihood on the left-hand side and our `errordef = 0.5` on the right-hand side. Confidence intervals with other coverage probability can be constructed by finding the corresponding upper value of the $\\chi^2$-interval with that integrated probability. In a least-squares fit, we have\n", "\n", "$$\n", "\\Delta Q(\\theta) = -2 \\ln \\ell(\\theta) = 1\n", "$$\n", "\n", "therefore `errordef = 1` is the crossing value for a least-squares cost function.\n", "\n", "In the multi-parameter case, when we search for the interval for $\\theta_i$, the other parameters are effectively fixed to their best-fit values for the current value $\\theta_i$ in the scan, $\\theta_k = \\hat\\theta_k(\\theta_i)$. Therefore, during the scan they are not free. The profile likelihood is effectively a likelihood for a single parameter. Thus, $68\\,\\%$-intervals are also here obtained when the negative log-likelihood crosses the value $1/2$ above the minimum. \n", "\n", "### Why is MINOS approximate\n", "\n", "In some sources, the MINOS intervals are wrongly described as *exact*. They are not exact, because for finite samples the intervals do not necessarily have $68\\,\\%$ coverage probability, only in the asymptotic limit.\n", "\n", "Some sources claim that two approximations are involved in the HESSE calculation of an interval, but only one in the MINOS calculation and conclude that MINOS intervals therefore approach the asymptotic limit more rapidly. This claim is disputed by others."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Coverage probability\n", "\n", "We compute the coverage probability of HESSE and MINOS intervals in toy experiments.\n", "\n", "### Poisson distributed data\n", "\n", "We construct HESSE and MINOS intervals for a counting experiment. We consider the extreme case of a single observation $k$ drawn from a Poisson distribution $P(k;\\lambda)$. We use the maximum-likelihood method to find the best estimate for $\\lambda$ for each $k$ under the constraint $\\lambda > 0$, which is trivially just $\\hat \\lambda = k$, and construct intervals with the HESSE and MINOS algorithms to check their coverage.\n", "\n", "This case can be fully handled analytically, but here we use iminuit's HESSE and MINOS algorithms to compute the intervals."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%config InlineBackend.figure_formats = ['svg']\n", "import numpy as np\n", "from matplotlib import pyplot as plt\n", "from scipy.stats import multivariate_normal, poisson\n", "from argparse import Namespace\n", "from iminuit import Minuit\n", "from functools import lru_cache\n", "import joblib"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"570.745312pt\" height=\"266.51625pt\" viewBox=\"0 0 570.**********.51625\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:25:35.063905</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 266.51625 \n", "L 570.**********.51625 \n", "L 570.745312 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 61.**********.96 \n", "L 289.61804 228.96 \n", "L 289.61804 7.2 \n", "L 61.345313 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m74dd88d871\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"71.721346\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0 -->\n", "      <g transform=\"translate(68.540096 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"113.225478\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 20 -->\n", "      <g transform=\"translate(106.862978 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"154.72961\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 40 -->\n", "      <g transform=\"translate(148.36711 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"196.233742\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 60 -->\n", "      <g transform=\"translate(189.871242 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-36\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"237.737874\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 80 -->\n", "      <g transform=\"translate(231.375374 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-38\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"279.242007\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 100 -->\n", "      <g transform=\"translate(269.698257 243.558437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_7\">\n", "     <!-- $k$ -->\n", "     <g transform=\"translate(172.581676 257.236562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-Oblique-6b\" d=\"M 1172 4863 \n", "L 1747 4863 \n", "L 1197 2028 \n", "L 3169 3500 \n", "L 3916 3500 \n", "L 1716 1825 \n", "L 3322 0 \n", "L 2625 0 \n", "L 1131 1709 \n", "L 800 0 \n", "L 225 0 \n", "L 1172 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-6b\" transform=\"translate(0 0.015625)\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_7\">\n", "      <defs>\n", "       <path id=\"mb7cc2c6803\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"218.79086\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- −10.0 -->\n", "      <g transform=\"translate(23.7 222.590079) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"211.035156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"194.018482\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- −7.5 -->\n", "      <g transform=\"translate(30.0625 197.817701) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"169.246105\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- −5.0 -->\n", "      <g transform=\"translate(30.0625 173.045323) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"144.473727\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- −2.5 -->\n", "      <g transform=\"translate(30.0625 148.272945) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"179.199219\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"119.701349\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(38.442188 123.500568) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"94.928971\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 2.5 -->\n", "      <g transform=\"translate(38.442188 98.72819) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"70.156593\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- 5.0 -->\n", "      <g transform=\"translate(38.442188 73.955812) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-35\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"45.384215\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- 7.5 -->\n", "      <g transform=\"translate(38.442188 49.183434) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-37\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"61.345313\" y=\"20.611837\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 10.0 -->\n", "      <g transform=\"translate(32.079688 24.411056) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- $\\lambda^d - \\hat\\lambda$, $\\lambda^u - \\hat\\lambda$ -->\n", "     <g transform=\"translate(17.620313 150.83) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-Oblique-3bb\" d=\"M 2350 4316 \n", "L 3125 0 \n", "L 2516 0 \n", "L 2038 2588 \n", "L 328 0 \n", "L -281 0 \n", "L 1903 3356 \n", "L 1794 3975 \n", "Q 1725 4369 1391 4369 \n", "L 1091 4369 \n", "L 1184 4863 \n", "L 1550 4856 \n", "Q 2253 4847 2350 4316 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-Oblique-64\" d=\"M 2675 525 \n", "Q 2444 222 2128 65 \n", "Q 1813 -91 1428 -91 \n", "Q 903 -91 598 267 \n", "Q 294 625 294 1247 \n", "Q 294 1766 478 2236 \n", "Q 663 2706 1013 3078 \n", "Q 1244 3325 1534 3454 \n", "Q 1825 3584 2144 3584 \n", "Q 2481 3584 2739 3421 \n", "Q 2997 3259 3138 2956 \n", "L 3513 4863 \n", "L 4091 4863 \n", "L 3144 0 \n", "L 2566 0 \n", "L 2675 525 \n", "z\n", "M 891 1350 \n", "Q 891 897 1095 644 \n", "Q 1300 391 1663 391 \n", "Q 1931 391 2161 520 \n", "Q 2391 650 2566 903 \n", "Q 2750 1166 2856 1509 \n", "Q 2963 1853 2963 2188 \n", "Q 2963 2622 2758 2865 \n", "Q 2553 3109 2194 3109 \n", "Q 1922 3109 1687 2981 \n", "Q 1453 2853 1288 2613 \n", "Q 1106 2353 998 2009 \n", "Q 891 1666 891 1350 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-302\" d=\"M -1831 5119 \n", "L -1369 5119 \n", "L -603 3944 \n", "L -1038 3944 \n", "L -1600 4709 \n", "L -2163 3944 \n", "L -2597 3944 \n", "L -1831 5119 \n", "z\n", "M -1600 3584 \n", "L -1600 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-2c\" d=\"M 750 794 \n", "L 1409 794 \n", "L 1409 256 \n", "L 897 -744 \n", "L 494 -744 \n", "L 750 256 \n", "L 750 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-Oblique-75\" d=\"M 428 1388 \n", "L 838 3500 \n", "L 1416 3500 \n", "L 1006 1409 \n", "Q 975 1256 961 1147 \n", "Q 947 1038 947 966 \n", "Q 947 700 1109 554 \n", "Q 1272 409 1569 409 \n", "Q 2031 409 2368 721 \n", "Q 2706 1034 2809 1563 \n", "L 3194 3500 \n", "L 3769 3500 \n", "L 3091 0 \n", "L 2516 0 \n", "L 2631 550 \n", "Q 2388 244 2052 76 \n", "Q 1716 -91 1338 -91 \n", "Q 878 -91 622 161 \n", "Q 366 413 366 863 \n", "Q 366 956 381 1097 \n", "Q 397 1238 428 1388 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-3bb\" transform=\"translate(0 0.53125)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-64\" transform=\"translate(66.627396 38.8125) scale(0.7)\"/>\n", "      <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(133.277786 0.53125)\"/>\n", "      <use xlink:href=\"#DejaVuSans-302\" transform=\"translate(294.432083 33.015625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-3bb\" transform=\"translate(236.549271 0.53125)\"/>\n", "      <use xlink:href=\"#DejaVuSans-2c\" transform=\"translate(295.728958 0.53125)\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" transform=\"translate(327.516068 0.53125)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-3bb\" transform=\"translate(359.303177 0.53125)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-75\" transform=\"translate(425.930573 38.8125) scale(0.7)\"/>\n", "      <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(492.512604 0.53125)\"/>\n", "      <use xlink:href=\"#DejaVuSans-302\" transform=\"translate(653.666901 33.015625)\"/>\n", "      <use xlink:href=\"#DejaVuSans-Oblique-3bb\" transform=\"translate(595.784089 0.53125)\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_16\">\n", "    <path d=\"M 71.721346 123.805771 \n", "L 73.796552 129.116595 \n", "L 75.871759 133.460175 \n", "L 77.946965 136.734245 \n", "L 80.022172 139.441477 \n", "L 82.097379 141.807893 \n", "L 84.172585 143.938296 \n", "L 86.247792 145.892679 \n", "L 88.322998 147.7091 \n", "L 90.398205 149.413516 \n", "L 92.473412 151.024571 \n", "L 94.548618 152.723087 \n", "L 96.623825 154.17838 \n", "L 98.699032 155.574059 \n", "L 100.774238 156.772507 \n", "L 102.849445 158.074493 \n", "L 104.924651 159.333649 \n", "L 106.999858 160.553949 \n", "L 109.075065 161.738787 \n", "L 111.150271 162.8911 \n", "L 113.225478 163.994012 \n", "L 115.300684 165.090264 \n", "L 117.375891 166.160509 \n", "L 119.451098 167.206531 \n", "L 121.526304 168.229906 \n", "L 123.601511 169.561172 \n", "L 125.676717 170.531823 \n", "L 127.751924 171.484471 \n", "L 129.827131 172.420083 \n", "L 131.902337 173.339524 \n", "L 133.977544 174.243621 \n", "L 136.052751 175.133104 \n", "L 138.127957 176.008661 \n", "L 140.203164 176.870923 \n", "L 142.27837 177.720489 \n", "L 144.353577 178.557866 \n", "L 146.428784 179.383627 \n", "L 148.50399 180.198183 \n", "L 150.579197 181.001968 \n", "L 152.654403 181.795441 \n", "L 154.72961 182.578985 \n", "L 156.804817 183.352933 \n", "L 158.880023 184.117641 \n", "L 160.95523 184.873469 \n", "L 163.030436 185.620656 \n", "L 165.105643 186.359539 \n", "L 167.18085 187.09033 \n", "L 169.256056 187.813339 \n", "L 171.331263 188.528808 \n", "L 173.40647 189.23699 \n", "L 175.481676 189.937983 \n", "L 177.556883 190.632163 \n", "L 179.632089 191.319592 \n", "L 181.707296 192.000565 \n", "L 183.782503 192.675205 \n", "L 185.857709 193.34368 \n", "L 187.932916 194.006155 \n", "L 190.008122 194.662818 \n", "L 192.083329 195.313822 \n", "L 194.158536 195.959267 \n", "L 196.233742 196.599363 \n", "L 198.308949 197.234157 \n", "L 200.384155 197.86382 \n", "L 202.459362 198.4885 \n", "L 204.534569 199.108194 \n", "L 206.609775 199.723202 \n", "L 208.684982 200.333461 \n", "L 210.760189 200.939225 \n", "L 212.835395 201.540454 \n", "L 214.910602 202.137437 \n", "L 216.985808 202.730128 \n", "L 219.061015 203.318519 \n", "L 221.136222 203.902874 \n", "L 223.211428 204.483182 \n", "L 225.286635 205.059569 \n", "L 227.361841 205.63212 \n", "L 229.437048 206.200891 \n", "L 231.512255 206.765913 \n", "L 233.587461 207.327411 \n", "L 235.662668 207.885186 \n", "L 237.737874 208.439602 \n", "L 239.813081 208.990486 \n", "L 241.888288 209.537992 \n", "L 243.963494 210.082387 \n", "L 246.038701 210.623395 \n", "L 248.113908 211.161195 \n", "L 250.189114 211.695893 \n", "L 252.264321 212.227528 \n", "L 254.339527 212.756043 \n", "L 256.414734 213.281638 \n", "L 258.489941 213.804317 \n", "L 260.565147 214.324071 \n", "L 262.640354 214.840995 \n", "L 264.71556 215.355191 \n", "L 266.790767 215.866609 \n", "L 268.865974 216.375379 \n", "L 270.94118 216.881463 \n", "L 273.016387 217.384886 \n", "L 275.091593 217.885778 \n", "L 277.1668 218.384108 \n", "L 279.242007 218.88 \n", "\" clip-path=\"url(#p6840b4b4ad)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_17\">\n", "    <path d=\"M 71.721346 115.596927 \n", "L 73.796552 110.286259 \n", "L 75.871759 105.861855 \n", "L 77.946965 102.669181 \n", "L 80.022172 99.962351 \n", "L 82.097379 97.595865 \n", "L 84.172585 95.465278 \n", "L 86.247792 93.510719 \n", "L 88.322998 91.694152 \n", "L 90.398205 89.989625 \n", "L 92.473412 88.378484 \n", "L 94.548618 87.157062 \n", "L 96.623825 85.671529 \n", "L 98.699032 84.248906 \n", "L 100.774238 82.63223 \n", "L 102.849445 81.329886 \n", "L 104.924651 80.070451 \n", "L 106.999858 78.849931 \n", "L 109.075065 77.664916 \n", "L 111.150271 76.51246 \n", "L 113.225478 75.359446 \n", "L 115.300684 74.267746 \n", "L 117.375891 73.201451 \n", "L 119.451098 72.158877 \n", "L 121.526304 71.138531 \n", "L 123.601511 70.632574 \n", "L 125.676717 69.631611 \n", "L 127.751924 68.650884 \n", "L 129.827131 67.68919 \n", "L 131.902337 66.745458 \n", "L 133.977544 65.818683 \n", "L 136.052751 64.907978 \n", "L 138.127957 64.012522 \n", "L 140.203164 63.131562 \n", "L 142.27837 62.264395 \n", "L 144.353577 61.41042 \n", "L 146.428784 60.568979 \n", "L 148.50399 59.739589 \n", "L 150.579197 58.921748 \n", "L 152.654403 58.11494 \n", "L 154.72961 57.318724 \n", "L 156.804817 56.532721 \n", "L 158.880023 55.756532 \n", "L 160.95523 54.989755 \n", "L 163.030436 54.232116 \n", "L 165.105643 53.483244 \n", "L 167.18085 52.742898 \n", "L 169.256056 52.01074 \n", "L 171.331263 51.286502 \n", "L 173.40647 50.569909 \n", "L 175.481676 49.860841 \n", "L 177.556883 49.158901 \n", "L 179.632089 48.46401 \n", "L 181.707296 47.775858 \n", "L 183.782503 47.094303 \n", "L 185.857709 46.419163 \n", "L 187.932916 45.750264 \n", "L 190.008122 45.0874 \n", "L 192.083329 44.430409 \n", "L 194.158536 43.77918 \n", "L 196.233742 43.133492 \n", "L 198.308949 42.493289 \n", "L 200.384155 41.858393 \n", "L 202.459362 41.228645 \n", "L 204.534569 40.604041 \n", "L 206.609775 39.984272 \n", "L 208.684982 39.369399 \n", "L 210.760189 38.759156 \n", "L 212.835395 38.153581 \n", "L 214.910602 37.552378 \n", "L 216.985808 36.955589 \n", "L 219.061015 36.363214 \n", "L 221.136222 35.774985 \n", "L 223.211428 35.190908 \n", "L 225.286635 34.610858 \n", "L 227.361841 34.034738 \n", "L 229.437048 33.462492 \n", "L 231.512255 32.894087 \n", "L 233.587461 32.329291 \n", "L 235.662668 31.768302 \n", "L 237.737874 31.210751 \n", "L 239.813081 30.656811 \n", "L 241.888288 30.106323 \n", "L 243.963494 29.55902 \n", "L 246.038701 29.015169 \n", "L 248.113908 28.474598 \n", "L 250.189114 27.937195 \n", "L 252.264321 27.402911 \n", "L 254.339527 26.871812 \n", "L 256.414734 26.343686 \n", "L 258.489941 25.818536 \n", "L 260.565147 25.296362 \n", "L 262.640354 24.777071 \n", "L 264.71556 24.260561 \n", "L 266.790767 23.746879 \n", "L 268.865974 23.235888 \n", "L 270.94118 22.727632 \n", "L 273.016387 22.222078 \n", "L 275.091593 21.719107 \n", "L 277.1668 21.218734 \n", "L 279.242007 20.72084 \n", "\" clip-path=\"url(#p6840b4b4ad)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_18\">\n", "    <path d=\"M 71.721346 119.701349 \n", "L 73.796552 126.620675 \n", "L 75.871759 130.625294 \n", "L 77.946965 133.732677 \n", "L 80.022172 136.363308 \n", "L 82.097379 138.68592 \n", "L 84.172585 140.788793 \n", "L 86.247792 142.724335 \n", "L 88.322998 144.527082 \n", "L 90.398205 146.221108 \n", "L 92.473412 147.823988 \n", "L 94.548618 149.349716 \n", "L 96.623825 150.807134 \n", "L 98.699032 152.20531 \n", "L 100.774238 153.550488 \n", "L 102.849445 154.849118 \n", "L 104.924651 156.105307 \n", "L 106.999858 157.322963 \n", "L 109.075065 158.505427 \n", "L 111.150271 159.655585 \n", "L 113.225478 160.775953 \n", "L 115.300684 161.868722 \n", "L 117.375891 162.935841 \n", "L 119.451098 163.979037 \n", "L 121.526304 164.999848 \n", "L 123.601511 166.001031 \n", "L 125.676717 166.980959 \n", "L 127.751924 167.932488 \n", "L 129.827131 168.876622 \n", "L 131.902337 169.804062 \n", "L 133.977544 170.715664 \n", "L 136.052751 171.612212 \n", "L 138.127957 172.494427 \n", "L 140.203164 173.362977 \n", "L 142.27837 174.218478 \n", "L 144.353577 175.0615 \n", "L 146.428784 175.892574 \n", "L 148.50399 176.712195 \n", "L 150.579197 177.520823 \n", "L 152.654403 178.318889 \n", "L 154.72961 179.106796 \n", "L 156.804817 179.884923 \n", "L 158.880023 180.653626 \n", "L 160.95523 181.413238 \n", "L 163.030436 182.164075 \n", "L 165.105643 182.906434 \n", "L 167.18085 183.640596 \n", "L 169.256056 184.366827 \n", "L 171.331263 185.085379 \n", "L 173.40647 185.796489 \n", "L 175.481676 186.500385 \n", "L 177.556883 187.197281 \n", "L 179.632089 187.887383 \n", "L 181.707296 188.570886 \n", "L 183.782503 189.247974 \n", "L 185.857709 189.918827 \n", "L 187.932916 190.583612 \n", "L 190.008122 191.242491 \n", "L 192.083329 191.89562 \n", "L 194.158536 192.543146 \n", "L 196.233742 193.18521 \n", "L 198.308949 193.82195 \n", "L 200.384155 194.453495 \n", "L 202.459362 195.07997 \n", "L 204.534569 195.701496 \n", "L 206.609775 196.318187 \n", "L 208.684982 196.930156 \n", "L 210.760189 197.537508 \n", "L 212.835395 198.140348 \n", "L 214.910602 198.738773 \n", "L 216.985808 199.33288 \n", "L 219.061015 199.922761 \n", "L 221.136222 200.508504 \n", "L 223.211428 201.090196 \n", "L 225.286635 201.667919 \n", "L 227.361841 202.241754 \n", "L 229.437048 202.811778 \n", "L 231.512255 203.378067 \n", "L 233.587461 203.940691 \n", "L 235.662668 204.499723 \n", "L 237.737874 205.055229 \n", "L 239.813081 205.607276 \n", "L 241.888288 206.155927 \n", "L 243.963494 206.701244 \n", "L 246.038701 207.243288 \n", "L 248.113908 207.782117 \n", "L 250.189114 208.317786 \n", "L 252.264321 208.850352 \n", "L 254.339527 209.379867 \n", "L 256.414734 209.906384 \n", "L 258.489941 210.429952 \n", "L 260.565147 210.950621 \n", "L 262.640354 211.468438 \n", "L 264.71556 211.983449 \n", "L 266.790767 212.495701 \n", "L 268.865974 213.005236 \n", "L 270.94118 213.512097 \n", "L 273.016387 214.016327 \n", "L 275.091593 214.517965 \n", "L 277.1668 215.017052 \n", "L 279.242007 215.513625 \n", "\" clip-path=\"url(#p6840b4b4ad)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_19\">\n", "    <path d=\"M 71.721346 114.763216 \n", "L 73.796552 106.248049 \n", "L 75.871759 102.207677 \n", "L 77.946965 99.088373 \n", "L 80.022172 96.451691 \n", "L 82.097379 94.125294 \n", "L 84.172585 92.020066 \n", "L 86.247792 90.082818 \n", "L 88.322998 88.278781 \n", "L 90.398205 86.583748 \n", "L 92.473412 84.980061 \n", "L 94.548618 83.453321 \n", "L 96.623825 81.995419 \n", "L 98.699032 80.596827 \n", "L 100.774238 79.251476 \n", "L 102.849445 77.952498 \n", "L 104.924651 76.696004 \n", "L 106.999858 75.478079 \n", "L 109.075065 74.288047 \n", "L 111.150271 73.138005 \n", "L 113.225478 72.017813 \n", "L 115.300684 70.925135 \n", "L 117.375891 69.858101 \n", "L 119.451098 68.814987 \n", "L 121.526304 67.794251 \n", "L 123.601511 66.791877 \n", "L 125.676717 65.812125 \n", "L 127.751924 64.85097 \n", "L 129.827131 63.907395 \n", "L 131.902337 62.98047 \n", "L 133.977544 62.069344 \n", "L 136.052751 61.173237 \n", "L 138.127957 60.29143 \n", "L 140.203164 59.423261 \n", "L 142.27837 58.568116 \n", "L 144.353577 57.725427 \n", "L 146.428784 56.894664 \n", "L 148.50399 56.075336 \n", "L 150.579197 55.266984 \n", "L 152.654403 54.469177 \n", "L 154.72961 53.681515 \n", "L 156.804817 52.90362 \n", "L 158.880023 52.135136 \n", "L 160.95523 51.375732 \n", "L 163.030436 50.625093 \n", "L 165.105643 49.882922 \n", "L 167.18085 49.148938 \n", "L 169.256056 48.422878 \n", "L 171.331263 47.704489 \n", "L 173.40647 46.993534 \n", "L 175.481676 46.289787 \n", "L 177.556883 45.593033 \n", "L 179.632089 44.903067 \n", "L 181.707296 44.219695 \n", "L 183.782503 43.542731 \n", "L 185.857709 42.871999 \n", "L 187.932916 42.20733 \n", "L 190.008122 41.548561 \n", "L 192.083329 40.89554 \n", "L 194.158536 40.248117 \n", "L 196.233742 39.606152 \n", "L 198.308949 38.969508 \n", "L 200.384155 38.338055 \n", "L 202.459362 37.71167 \n", "L 204.534569 37.09023 \n", "L 206.609775 36.473622 \n", "L 208.684982 35.861734 \n", "L 210.760189 35.25446 \n", "L 212.835395 34.651696 \n", "L 214.910602 34.053344 \n", "L 216.985808 33.459308 \n", "L 219.061015 32.869496 \n", "L 221.136222 32.28382 \n", "L 223.211428 31.702193 \n", "L 225.286635 31.124532 \n", "L 227.361841 30.550759 \n", "L 229.437048 29.980794 \n", "L 231.512255 29.414564 \n", "L 233.587461 28.851995 \n", "L 235.662668 28.293019 \n", "L 237.737874 27.737566 \n", "L 239.813081 27.185571 \n", "L 241.888288 26.63697 \n", "L 243.963494 26.091702 \n", "L 246.038701 25.549706 \n", "L 248.113908 25.010925 \n", "L 250.189114 24.475301 \n", "L 252.264321 23.942779 \n", "L 254.339527 23.413307 \n", "L 256.414734 22.886833 \n", "L 258.489941 22.363307 \n", "L 260.565147 21.842678 \n", "L 262.640354 21.324901 \n", "L 264.71556 20.809928 \n", "L 266.790767 20.297714 \n", "L 268.865974 19.788216 \n", "L 270.94118 19.281391 \n", "L 273.016387 18.777196 \n", "L 275.091593 18.275593 \n", "L 277.1668 17.77654 \n", "L 279.242007 17.28 \n", "\" clip-path=\"url(#p6840b4b4ad)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 61.**********.96 \n", "L 61.345313 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 289.61804 228.96 \n", "L 289.61804 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 61.345312 228.96 \n", "L 289.61804 228.96 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 61.345312 7.2 \n", "L 289.61804 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 335.272585 228.96 \n", "L 563.545312 228.96 \n", "L 563.545312 7.2 \n", "L 335.272585 7.2 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"line2d_20\">\n", "    <path d=\"M 335.272585 105.184062 \n", "L 563.545312 105.184062 \n", "\" clip-path=\"url(#p3ea3275f2e)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"345.648618\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- $\\mathdefault{10^{-1}}$ -->\n", "      <g transform=\"translate(333.898618 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-2212\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(186.855469 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"414.822172\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- $\\mathdefault{10^{0}}$ -->\n", "      <g transform=\"translate(406.022172 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"483.995726\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(475.195726 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m74dd88d871\" x=\"553.169279\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(544.369279 243.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_25\">\n", "      <defs>\n", "       <path id=\"m5905ab2a53\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"338.945008\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"342.48341\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"366.471933\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"378.652791\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"387.295247\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"393.998857\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"399.476106\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"404.107053\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"408.118562\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"411.656964\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"435.645487\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"447.826345\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"456.468801\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"463.172411\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"468.649659\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"473.280607\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"477.292116\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"480.830517\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"504.81904\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"516.999898\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_31\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"525.642355\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_32\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"532.345965\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_33\">\n", "     <g id=\"line2d_47\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"537.823213\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_34\">\n", "     <g id=\"line2d_48\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"542.45416\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_35\">\n", "     <g id=\"line2d_49\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"546.465669\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_36\">\n", "     <g id=\"line2d_50\">\n", "      <g>\n", "       <use xlink:href=\"#m5905ab2a53\" x=\"550.004071\" y=\"228.96\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_22\">\n", "     <!-- $\\lambda$ -->\n", "     <g transform=\"translate(446.408949 257.236562) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-Oblique-3bb\" transform=\"translate(0 0.015625)\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_51\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"335.272585\" y=\"211.025455\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 0.3 -->\n", "      <g transform=\"translate(312.36946 214.824673) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_52\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"335.272585\" y=\"183.172456\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(312.36946 186.971675) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_53\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"335.272585\" y=\"155.319458\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_25\">\n", "      <!-- 0.5 -->\n", "      <g transform=\"translate(312.36946 159.118677) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_54\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"335.272585\" y=\"127.46646\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(312.36946 131.265679) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_55\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"335.272585\" y=\"99.613462\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.7 -->\n", "      <g transform=\"translate(312.36946 103.412681) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_15\">\n", "     <g id=\"line2d_56\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"335.272585\" y=\"71.760464\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(312.36946 75.559683) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_16\">\n", "     <g id=\"line2d_57\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"335.272585\" y=\"43.907466\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_29\">\n", "      <!-- 0.9 -->\n", "      <g transform=\"translate(312.36946 47.706685) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-39\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_17\">\n", "     <g id=\"line2d_58\">\n", "      <g>\n", "       <use xlink:href=\"#mb7cc2c6803\" x=\"335.272585\" y=\"16.054468\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_30\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(312.36946 19.853687) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_31\">\n", "     <!-- coverage probability -->\n", "     <g transform=\"translate(306.289773 169.559687) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"116.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"175.341797\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"236.865234\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"277.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"339.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"402.734375\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"464.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"496.044922\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"559.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"598.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"659.566406\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"723.042969\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"784.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"847.798828\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"875.582031\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"903.365234\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"931.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"970.357422\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_59\">\n", "    <path d=\"M 345.648618 17.28 \n", "L 347.744787 17.892766 \n", "L 349.840955 18.11559 \n", "L 351.937123 18.282708 \n", "L 354.033291 17.948472 \n", "L 356.12946 18.39412 \n", "L 358.225628 18.338414 \n", "L 360.321796 19.174004 \n", "L 362.417965 19.675358 \n", "L 364.514133 20.566654 \n", "L 366.610301 21.235126 \n", "L 368.70647 22.070716 \n", "L 370.802638 22.516364 \n", "L 372.898806 23.68619 \n", "L 374.994974 23.40766 \n", "L 377.091143 26.081547 \n", "L 379.187311 25.134545 \n", "L 381.283479 28.086963 \n", "L 383.379648 29.479613 \n", "L 385.475816 31.707853 \n", "L 387.571984 33.601857 \n", "L 389.668152 216.985996 \n", "L 391.764321 218.88 \n", "L 393.860489 209.465687 \n", "L 395.956657 206.346151 \n", "L 398.052826 204.730677 \n", "L 400.148994 173.089671 \n", "L 402.245162 173.201083 \n", "L 404.341331 159.664526 \n", "L 406.437499 159.553114 \n", "L 408.533667 156.879226 \n", "L 410.629835 153.091219 \n", "L 412.726004 143.119845 \n", "L 414.822172 144.958143 \n", "L 416.91834 138.32913 \n", "L 419.014509 134.318298 \n", "L 421.110677 132.925648 \n", "L 423.206845 103.067234 \n", "L 425.303013 99.446344 \n", "L 427.399182 96.605338 \n", "L 429.49535 93.764333 \n", "L 431.591518 91.480387 \n", "L 433.687687 93.820039 \n", "L 435.783855 143.621199 \n", "L 437.880023 138.106306 \n", "L 439.976192 131.64441 \n", "L 442.07236 126.686576 \n", "L 444.168528 123.734159 \n", "L 446.264696 100.838994 \n", "L 448.360865 96.883868 \n", "L 450.457033 95.156983 \n", "L 452.553201 147.799149 \n", "L 454.64937 117.272263 \n", "L 456.745538 113.261431 \n", "L 458.841706 110.030484 \n", "L 460.937874 86.411141 \n", "L 463.034043 128.524874 \n", "L 465.130211 105.239768 \n", "L 467.226379 98.1094 \n", "L 469.322548 114.821199 \n", "L 471.418716 110.587544 \n", "L 473.514884 93.374391 \n", "L 475.611053 121.561625 \n", "L 477.707221 101.841702 \n", "L 479.803389 109.362012 \n", "L 481.899557 109.751954 \n", "L 483.995726 113.150019 \n", "L 486.091894 95.825455 \n", "L 488.188062 100.226228 \n", "L 490.284231 104.125648 \n", "L 492.380399 112.927195 \n", "L 494.476567 99.50205 \n", "L 496.572735 103.457176 \n", "L 498.668904 107.468008 \n", "L 500.765072 98.889284 \n", "L 502.86124 101.396054 \n", "L 504.957409 107.85795 \n", "L 507.053577 109.974778 \n", "L 509.149745 100.05911 \n", "L 511.245914 102.621586 \n", "L 513.342082 95.045571 \n", "L 515.43825 100.504758 \n", "L 517.534418 106.74383 \n", "L 519.630587 99.891992 \n", "L 521.726755 105.741122 \n", "L 523.822923 112.648665 \n", "L 525.919092 102.343056 \n", "L 528.01526 108.860658 \n", "L 530.111428 112.760077 \n", "L 532.207596 106.74383 \n", "L 534.303765 103.902824 \n", "L 536.399933 107.85795 \n", "L 538.496101 109.473424 \n", "L 540.59227 105.963946 \n", "L 542.688438 104.292766 \n", "L 544.784606 108.080774 \n", "L 546.880775 103.735706 \n", "L 548.976943 107.523714 \n", "L 551.073111 104.125648 \n", "L 553.169279 102.955822 \n", "\" clip-path=\"url(#p3ea3275f2e)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_60\">\n", "    <path d=\"M 345.648618 42.73764 \n", "L 347.744787 46.247118 \n", "L 349.840955 44.631644 \n", "L 351.937123 48.475358 \n", "L 354.033291 49.589478 \n", "L 356.12946 53.154662 \n", "L 358.225628 54.603017 \n", "L 360.321796 59.282321 \n", "L 362.417965 62.067621 \n", "L 364.514133 63.237447 \n", "L 366.610301 65.744217 \n", "L 368.70647 70.646344 \n", "L 370.802638 74.824294 \n", "L 372.898806 79.503598 \n", "L 374.994974 80.506306 \n", "L 377.091143 86.076905 \n", "L 379.187311 25.134545 \n", "L 381.283479 28.086963 \n", "L 383.379648 29.479613 \n", "L 385.475816 31.707853 \n", "L 387.571984 33.601857 \n", "L 389.668152 34.326035 \n", "L 391.764321 37.612689 \n", "L 393.860489 41.010754 \n", "L 395.956657 206.346151 \n", "L 398.052826 204.730677 \n", "L 400.148994 200.719845 \n", "L 402.245162 203.505145 \n", "L 404.341331 196.20766 \n", "L 406.437499 194.425068 \n", "L 408.533667 196.096248 \n", "L 410.629835 193.923714 \n", "L 412.726004 143.119845 \n", "L 414.822172 144.958143 \n", "L 416.91834 138.32913 \n", "L 419.014509 134.318298 \n", "L 421.110677 132.925648 \n", "L 423.206845 131.198762 \n", "L 425.303013 129.081934 \n", "L 427.399182 131.254468 \n", "L 429.49535 93.764333 \n", "L 431.591518 91.480387 \n", "L 433.687687 93.820039 \n", "L 435.783855 94.098569 \n", "L 437.880023 96.71675 \n", "L 439.976192 104.738414 \n", "L 442.07236 126.686576 \n", "L 444.168528 123.734159 \n", "L 446.264696 127.132224 \n", "L 448.360865 126.018104 \n", "L 450.457033 95.156983 \n", "L 452.553201 93.987157 \n", "L 454.64937 96.71675 \n", "L 456.745538 113.261431 \n", "L 458.841706 110.030484 \n", "L 460.937874 111.088897 \n", "L 463.034043 89.196441 \n", "L 465.130211 127.577872 \n", "L 467.226379 98.1094 \n", "L 469.322548 101.284642 \n", "L 471.418716 110.587544 \n", "L 473.514884 113.707079 \n", "L 475.611053 93.374391 \n", "L 477.707221 101.841702 \n", "L 479.803389 98.38793 \n", "L 481.899557 109.751954 \n", "L 483.995726 113.150019 \n", "L 486.091894 95.825455 \n", "L 488.188062 100.226228 \n", "L 490.284231 82.623133 \n", "L 492.380399 90.143443 \n", "L 494.476567 99.50205 \n", "L 496.572735 103.457176 \n", "L 498.668904 107.468008 \n", "L 500.765072 111.423133 \n", "L 502.86124 101.396054 \n", "L 504.957409 104.627002 \n", "L 507.053577 109.974778 \n", "L 509.149745 100.05911 \n", "L 511.245914 100.727582 \n", "L 513.342082 105.239768 \n", "L 515.43825 112.370135 \n", "L 517.534418 106.74383 \n", "L 519.630587 110.420426 \n", "L 521.726755 105.741122 \n", "L 523.822923 109.027776 \n", "L 525.919092 111.088897 \n", "L 528.01526 97.496634 \n", "L 530.111428 102.175938 \n", "L 532.207596 104.404178 \n", "L 534.303765 113.150019 \n", "L 536.399933 105.796828 \n", "L 538.496101 100.449052 \n", "L 540.59227 97.050986 \n", "L 542.688438 104.292766 \n", "L 544.784606 108.080774 \n", "L 546.880775 103.735706 \n", "L 548.976943 100.671876 \n", "L 551.073111 97.496634 \n", "L 553.169279 102.955822 \n", "\" clip-path=\"url(#p3ea3275f2e)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 335.272585 228.96 \n", "L 335.272585 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_9\">\n", "    <path d=\"M 563.545312 228.96 \n", "L 563.545312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 335.272585 228.96 \n", "L 563.545312 228.96 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 335.272585 7.2 \n", "L 563.545312 7.2 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_12\">\n", "     <path d=\"M 491.265625 44.55625 \n", "L 556.545312 44.55625 \n", "Q 558.545312 44.55625 558.545312 42.55625 \n", "L 558.545312 14.2 \n", "Q 558.545312 12.2 556.545312 12.2 \n", "L 491.265625 12.2 \n", "Q 489.265625 12.2 489.265625 14.2 \n", "L 489.265625 42.55625 \n", "Q 489.265625 44.55625 491.265625 44.55625 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_61\">\n", "     <path d=\"M 493.265625 20.298437 \n", "L 503.265625 20.298437 \n", "L 513.265625 20.298437 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_32\">\n", "     <!-- HESSE -->\n", "     <g transform=\"translate(521.265625 23.798437) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-48\"/>\n", "      <use xlink:href=\"#DejaVuSans-45\" x=\"75.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"138.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"201.855469\"/>\n", "      <use xlink:href=\"#DejaVuSans-45\" x=\"265.332031\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_62\">\n", "     <path d=\"M 493.265625 34.976562 \n", "L 503.265625 34.976562 \n", "L 513.265625 34.976562 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_33\">\n", "     <!-- MINOS -->\n", "     <g transform=\"translate(521.265625 38.476562) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4d\" d=\"M 628 4666 \n", "L 1569 4666 \n", "L 2759 1491 \n", "L 3956 4666 \n", "L 4897 4666 \n", "L 4897 0 \n", "L 4281 0 \n", "L 4281 4097 \n", "L 3078 897 \n", "L 2444 897 \n", "L 1241 4097 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4e\" d=\"M 628 4666 \n", "L 1478 4666 \n", "L 3547 763 \n", "L 3547 4666 \n", "L 4159 4666 \n", "L 4159 0 \n", "L 3309 0 \n", "L 1241 3903 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4f\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1225 4090 567 \n", "Q 3503 -91 2522 -91 \n", "Q 1538 -91 948 565 \n", "Q 359 1222 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4d\"/>\n", "      <use xlink:href=\"#DejaVuSans-49\" x=\"86.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-4e\" x=\"115.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-4f\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"269.287109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"p6840b4b4ad\">\n", "   <rect x=\"61.345313\" y=\"7.2\" width=\"228.272727\" height=\"221.76\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p3ea3275f2e\">\n", "   <rect x=\"335.272585\" y=\"7.2\" width=\"228.272727\" height=\"221.76\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 900x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["@lru_cache(10000)\n", "def run(k):\n", "    m = Minuit(lambda lambd: -poisson.logpmf(k, lambd), lambd=k + 1)\n", "    m.limits[\"lambd\"] = (0, None)\n", "    m.errordef = Minuit.LIKELIHOOD\n", "    m.migrad()\n", "    m.hesse()\n", "    m.minos()\n", "    assert m.valid\n", "\n", "    p = m.values[\"lambd\"]\n", "    dp = m.errors[\"lambd\"]\n", "    pm = max(p + m.merrors[\"lambd\"].lower, 0.0), p + m.merrors[\"lambd\"].upper\n", "\n", "    r = p, dp, *pm\n", "    return r\n", "\n", "\n", "rng = np.random.default_rng(seed=1)\n", "nmc = 5000\n", "mu = 10 ** np.linspace(-1, 2, 100)\n", "\n", "pcov = {\n", "    \"HESSE\": np.empty_like(mu),\n", "    \"MINOS\": np.empty_like(mu),\n", "}\n", "\n", "for i, mui in enumerate(mu):\n", "    nh = 0\n", "    nm = 0\n", "    for imc in range(nmc):\n", "        k = rng.poisson(mui)\n", "\n", "        p, dp, pd, pu = run(k)\n", "\n", "        if p - dp < mui < p + dp:\n", "            nh += 1\n", "        if pd < mui < pu:\n", "            nm += 1\n", "\n", "    pcov[\"HESSE\"][i] = nh / nmc\n", "    pcov[\"MINOS\"][i] = nm / nmc\n", "\n", "fig, ax = plt.subplots(1, 2, figsize=(9, 4))\n", "\n", "plt.sca(ax[0])\n", "n = np.arange(101)\n", "interval = {\n", "    \"HESSE\": np.empty((len(n), 2)),\n", "    \"MINOS\": np.empty((len(n), 2)),\n", "}\n", "for i, k in enumerate(n):\n", "    p, dp, pd, pu = run(k)\n", "    interval[\"HESSE\"][i] = (p - dp, p + dp)\n", "    interval[\"MINOS\"][i] = (pd, pu)\n", "\n", "for algo, vals in interval.items():\n", "    plt.plot(n, vals[:, 0] - n, color=\"C0\" if algo == \"HESSE\" else \"C1\", label=algo)\n", "    plt.plot(n, vals[:, 1] - n, color=\"C0\" if algo == \"HESSE\" else \"C1\", label=algo)\n", "plt.xlabel(\"$k$\")\n", "plt.ylabel(r\"$\\lambda^d - \\hat\\lambda$, $\\lambda^u - \\hat\\lambda$\")\n", "\n", "plt.sca(ax[1])\n", "for algo, vals in pcov.items():\n", "    plt.plot(mu, vals, label=algo)\n", "\n", "plt.axhline(0.68, ls=\":\", color=\"0.5\", zorder=0)\n", "plt.xlabel(r\"$\\lambda$\")\n", "plt.ylabel(\"coverage probability\")\n", "plt.legend()\n", "plt.semilogx();"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["In this special case, the intervals found by both methods are very close. The MINOS interval is identical to the HESSE interval up to a small almost constant shift. Visually, the rate of converge to the asymptotic coverage probability of 68% seems to be equal for both methods."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We can speak about the rate of convergence although we have drawn only a single observation from the Poisson pdf. The log-likelihood for $n$ observations with the same expectation is identical to the log-likelihood for one observation with an $n$-times higher expectation up to additive constants.\n", "\n", "$$\n", "\\ln L = \\sum_i^n (\\lambda_i - k_i \\ln \\lambda_i)\n", "= \\lambda - \\sum_i k_i (\\ln \\lambda - \\ln n)\n", "= \\lambda - k \\ln \\lambda + k \\ln n\n", "$$\n", "\n", "with $\\sum_i^n \\lambda_i = \\lambda$, $\\sum_i^n k_i = k$, and $\\lambda_i = \\lambda / n$. Therefore, the test cases with large values of $\\lambda$ correspond to large observation samples with a small constant $\\lambda$."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["### Fit of transformed normally distributed data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/html": ["<table>\n", "    <tr>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Minimizer\"> Migrad </th>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Minimum value of function\"> FCN = 3.122 </td>\n", "        <td style=\"text-align:center\" title=\"Total number of function and (optional) gradient evaluations\"> Nfcn = 168 </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:left\" title=\"Estimated distance to minimum and goal\"> EDM = 2.02e-05 (Goal: 0.0001) </td>\n", "        <td style=\"text-align:center\" title=\"Total run time of algorithms\">  </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Valid Minimum </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below EDM threshold (goal x 10) </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> No parameters at limit </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Below call limit </td>\n", "    </tr>\n", "    <tr>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Hesse ok </td>\n", "        <td style=\"text-align:center;background-color:#92CCA6;color:black\"> Covariance accurate </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌─────────────────────────────────────────────────────────────────────────┐\n", "│                                Mi<PERSON>                                   │\n", "├──────────────────────────────────┬──────────────────────────────────────┤\n", "│ FCN = 3.122                      │              Nfcn = 168              │\n", "│ EDM = 2.02e-05 (Goal: 0.0001)    │                                      │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│          Valid Minimum           │   Below EDM threshold (goal x 10)    │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│      No parameters at limit      │           Below call limit           │\n", "├──────────────────────────────────┼──────────────────────────────────────┤\n", "│             Hesse ok             │         Covariance accurate          │\n", "└──────────────────────────────────┴──────────────────────────────────────┘"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table>\n", "    <tr>\n", "        <td></td>\n", "        <th title=\"Variable name\"> Name </th>\n", "        <th title=\"Value of parameter\"> Value </th>\n", "        <th title=\"Hesse error\"> <PERSON> Error </th>\n", "        <th title=\"<PERSON>os lower error\"> <PERSON><PERSON> Error- </th>\n", "        <th title=\"Minos upper error\"> Minos Error+ </th>\n", "        <th title=\"Lower limit of the parameter\"> Limit- </th>\n", "        <th title=\"Upper limit of the parameter\"> Limit+ </th>\n", "        <th title=\"Is the parameter fixed in the fit\"> Fixed </th>\n", "    </tr>\n", "    <tr>\n", "        <th> 0 </th>\n", "        <td> cx </td>\n", "        <td> 0.11 </td>\n", "        <td> 0.06 </td>\n", "        <td> -0.08 </td>\n", "        <td> 0.05 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "    <tr>\n", "        <th> 1 </th>\n", "        <td> cy </td>\n", "        <td> 0.05 </td>\n", "        <td> 0.10 </td>\n", "        <td> -0.11 </td>\n", "        <td> 0.08 </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "        <td>  </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌───┬──────┬───────────┬───────────┬────────────┬────────────┬─────────┬─────────┬───────┐\n", "│   │ Name │   Value   │ <PERSON> Err │ <PERSON>os Err- │ Minos Err+ │ Limit-  │ Limit+  │ Fixed │\n", "├───┼──────┼───────────┼───────────┼────────────┼────────────┼─────────┼─────────┼───────┤\n", "│ 0 │ cx   │   0.11    │   0.06    │   -0.08    │    0.05    │         │         │       │\n", "│ 1 │ cy   │   0.05    │   0.10    │   -0.11    │    0.08    │         │         │       │\n", "└───┴──────┴───────────┴───────────┴────────────┴────────────┴─────────┴─────────┴───────┘"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<table>\n", "    <tr>\n", "        <td></td>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Parameter name\"> cx </th>\n", "        <th colspan=\"2\" style=\"text-align:center\" title=\"Parameter name\"> cy </th>\n", "    </tr>\n", "    <tr>\n", "        <th title=\"Lower and upper minos error of the parameter\"> Error </th>\n", "        <td> -0.08 </td>\n", "        <td> 0.05 </td>\n", "        <td> -0.11 </td>\n", "        <td> 0.08 </td>\n", "    </tr>\n", "    <tr>\n", "        <th title=\"Validity of lower/upper minos error\"> Valid </th>\n", "        <td style=\"background-color:#92CCA6;color:black\"> True </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> True </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> True </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> True </td>\n", "    </tr>\n", "    <tr>\n", "        <th title=\"Did scan hit limit of any parameter?\"> At Limit </th>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "    </tr>\n", "    <tr>\n", "        <th title=\"Did scan hit function call limit?\"> Max FCN </th>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "    </tr>\n", "    <tr>\n", "        <th title=\"New minimum found when doing scan?\"> New Min </th>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "        <td style=\"background-color:#92CCA6;color:black\"> False </td>\n", "    </tr>\n", "</table>"], "text/plain": ["┌──────────┬───────────────────────┬───────────────────────┐\n", "│          │          cx           │          cy           │\n", "├──────────┼───────────┬───────────┼───────────┬───────────┤\n", "│  Error   │   -0.08   │   0.05    │   -0.11   │   0.08    │\n", "│  Valid   │   True    │   True    │   True    │   True    │\n", "│ At Limit │   False   │   False   │   False   │   False   │\n", "│ Max FCN  │   False   │   False   │   False   │   False   │\n", "│ New Min  │   False   │   False   │   False   │   False   │\n", "└──────────┴───────────┴───────────┴───────────┴───────────┘"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"547.263835pt\" height=\"285.355313pt\" viewBox=\"0 0 547.**********.355313\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:25:35.736815</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 285.355313 \n", "L 547.**********.355313 \n", "L 547.263835 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 255.00375 136.919062 \n", "C 255.00375 122.**********.**********.93939 246.563513 94.487124 \n", "C 240.991401 81.**********.823669 68.**********.52775 58.515063 \n", "C 212.231831 48.219144 200.007955 40.051411 186.555689 34.4793 \n", "C 173.103423 28.907189 158.684378 26.039062 144.12375 26.039062 \n", "C 129.563122 26.039062 115.144077 28.907189 101.691811 34.4793 \n", "C 88.239545 40.051411 76.015669 48.219144 65.71975 58.515063 \n", "C 55.423831 68.810982 47.256099 81.034857 41.683987 94.487124 \n", "C 36.111876 107.93939 33.24375 122.358434 33.24375 136.919062 \n", "C 33.24375 151.479691 36.111876 165.898735 41.683987 179.351001 \n", "C 47.256099 192.803268 55.423831 205.027143 65.71975 215.323062 \n", "C 76.015669 225.618981 88.239545 233.786714 101.691811 239.358825 \n", "C 115.144077 244.930936 129.563122 247.799063 144.12375 247.799063 \n", "C 158.684378 247.799063 173.103423 244.930936 186.555689 239.358825 \n", "C 200.007955 233.786714 212.231831 225.618981 222.52775 215.323062 \n", "C 232.823669 205.027143 240.991401 192.803268 246.563513 179.351001 \n", "C 252.135624 165.898735 255.00375 151.479691 255.00375 136.919063 \n", "M 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <path d=\"M 144.12375 136.919062 \n", "L 255.00375 136.919062 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- 0° -->\n", "      <g transform=\"translate(263.3225 139.678438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-b0\" d=\"M 1600 4347 \n", "Q 1350 4347 1178 4173 \n", "Q 1006 4000 1006 3750 \n", "Q 1006 3503 1178 3333 \n", "Q 1350 3163 1600 3163 \n", "Q 1850 3163 2022 3333 \n", "Q 2194 3503 2194 3750 \n", "Q 2194 3997 2020 4172 \n", "Q 1847 4347 1600 4347 \n", "z\n", "M 1600 4750 \n", "Q 1800 4750 1984 4673 \n", "Q 2169 4597 2303 4453 \n", "Q 2447 4313 2519 4134 \n", "Q 2591 3956 2591 3750 \n", "Q 2591 3338 2302 3052 \n", "Q 2013 2766 1594 2766 \n", "Q 1172 2766 890 3047 \n", "Q 609 3328 609 3750 \n", "Q 609 4169 896 4459 \n", "Q 1184 4750 1600 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-b0\" x=\"63.623047\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <path d=\"M 144.12375 136.919062 \n", "L 222.52775 58.515063 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- 45° -->\n", "      <g transform=\"translate(223.564745 51.374943) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-35\" d=\"M 691 4666 \n", "L 3169 4666 \n", "L 3169 4134 \n", "L 1269 4134 \n", "L 1269 2991 \n", "Q 1406 3038 1543 3061 \n", "Q 1681 3084 1819 3084 \n", "Q 2600 3084 3056 2656 \n", "Q 3513 2228 3513 1497 \n", "Q 3513 744 3044 326 \n", "Q 2575 -91 1722 -91 \n", "Q 1428 -91 1123 -41 \n", "Q 819 9 494 109 \n", "L 494 744 \n", "Q 775 591 1075 516 \n", "Q 1375 441 1709 441 \n", "Q 2250 441 2565 725 \n", "Q 2881 1009 2881 1497 \n", "Q 2881 1984 2565 2268 \n", "Q 2250 2553 1709 2553 \n", "Q 1456 2553 1204 2497 \n", "Q 953 2441 691 2322 \n", "L 691 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-34\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-b0\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <path d=\"M 144.12375 136.919062 \n", "L 144.12375 26.039062 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_3\">\n", "      <!-- 90° -->\n", "      <g transform=\"translate(135.26125 14.798437) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-39\" d=\"M 703 97 \n", "L 703 672 \n", "Q 941 559 1184 500 \n", "Q 1428 441 1663 441 \n", "Q 2288 441 2617 861 \n", "Q 2947 1281 2994 2138 \n", "Q 2813 1869 2534 1725 \n", "Q 2256 1581 1919 1581 \n", "Q 1219 1581 811 2004 \n", "Q 403 2428 403 3163 \n", "Q 403 3881 828 4315 \n", "Q 1253 4750 1959 4750 \n", "Q 2769 4750 3195 4129 \n", "Q 3622 3509 3622 2328 \n", "Q 3622 1225 3098 567 \n", "Q 2575 -91 1691 -91 \n", "Q 1453 -91 1209 -44 \n", "Q 966 3 703 97 \n", "z\n", "M 1959 2075 \n", "Q 2384 2075 2632 2365 \n", "Q 2881 2656 2881 3163 \n", "Q 2881 3666 2632 3958 \n", "Q 2384 4250 1959 4250 \n", "Q 1534 4250 1286 3958 \n", "Q 1038 3666 1038 3163 \n", "Q 1038 2656 1286 2365 \n", "Q 1534 2075 1959 2075 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-39\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-b0\" x=\"127.246094\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <path d=\"M 144.12375 136.919062 \n", "L 65.71975 58.515063 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 135° -->\n", "      <g transform=\"translate(43.776505 51.374943) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-33\" d=\"M 2597 2516 \n", "Q 3050 2419 3304 2112 \n", "Q 3559 1806 3559 1356 \n", "Q 3559 666 3084 287 \n", "Q 2609 -91 1734 -91 \n", "Q 1441 -91 1130 -33 \n", "Q 819 25 488 141 \n", "L 488 750 \n", "Q 750 597 1062 519 \n", "Q 1375 441 1716 441 \n", "Q 2309 441 2620 675 \n", "Q 2931 909 2931 1356 \n", "Q 2931 1769 2642 2001 \n", "Q 2353 2234 1838 2234 \n", "L 1294 2234 \n", "L 1294 2753 \n", "L 1863 2753 \n", "Q 2328 2753 2575 2939 \n", "Q 2822 3125 2822 3475 \n", "Q 2822 3834 2567 4026 \n", "Q 2313 4219 1838 4219 \n", "Q 1578 4219 1281 4162 \n", "Q 984 4106 628 3988 \n", "L 628 4550 \n", "Q 988 4650 1302 4700 \n", "Q 1616 4750 1894 4750 \n", "Q 2613 4750 3031 4423 \n", "Q 3450 4097 3450 3541 \n", "Q 3450 3153 3228 2886 \n", "Q 3006 2619 2597 2516 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-33\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-b0\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <path d=\"M 144.12375 136.919062 \n", "L 33.24375 136.919062 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 180° -->\n", "      <g transform=\"translate(7.2 139.678438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-b0\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <path d=\"M 144.12375 136.919062 \n", "L 65.71975 215.323062 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 225° -->\n", "      <g transform=\"translate(43.776505 227.981932) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-b0\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <path d=\"M 144.12375 136.919062 \n", "L 144.12375 247.799063 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 270° -->\n", "      <g transform=\"translate(132.08 264.558438) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-37\" d=\"M 525 4666 \n", "L 3525 4666 \n", "L 3525 4397 \n", "L 1831 0 \n", "L 1172 0 \n", "L 2766 4134 \n", "L 525 4134 \n", "L 525 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-32\"/>\n", "       <use xlink:href=\"#DejaVuSans-37\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-b0\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <path d=\"M 144.12375 136.919062 \n", "L 222.52775 215.323062 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 315° -->\n", "      <g transform=\"translate(220.383495 227.981932) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-33\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"127.246094\"/>\n", "       <use xlink:href=\"#DejaVuSans-b0\" x=\"190.869141\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_9\">\n", "      <path d=\"M 163.000321 136.919062 \n", "C 163.000321 134.440214 162.512042 131.985469 161.563428 129.695311 \n", "C 160.614814 127.405154 159.224312 125.324122 157.471502 123.571311 \n", "C 155.718691 121.8185 153.637659 120.427999 151.347501 119.479385 \n", "C 149.057344 118.53077 146.602599 118.042491 144.12375 118.042491 \n", "C 141.644901 118.042491 139.190156 118.53077 136.899999 119.479385 \n", "C 134.609841 120.427999 132.528809 121.8185 130.775998 123.571311 \n", "C 129.023188 125.324122 127.632686 127.405154 126.684072 129.695311 \n", "C 125.735458 131.985469 125.247179 134.440214 125.247179 136.919062 \n", "C 125.247179 139.397911 125.735458 141.852656 126.684072 144.142814 \n", "C 127.632686 146.432971 129.023188 148.514003 130.775998 150.266814 \n", "C 132.528809 152.019625 134.609841 153.410126 136.899999 154.35874 \n", "C 139.190156 155.307355 141.644901 155.795634 144.12375 155.795634 \n", "C 146.602599 155.795634 149.057344 155.307355 151.347501 154.35874 \n", "C 153.637659 153.410126 155.718691 152.019625 157.471502 150.266814 \n", "C 159.224312 148.514003 160.614814 146.432971 161.563428 144.142814 \n", "C 162.512042 141.852656 163.000321 139.397911 163.000321 136.919062 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(161.563428 127.615624) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_10\">\n", "      <path d=\"M 185.821105 136.919062 \n", "C 185.821105 131.443416 184.742522 126.021013 182.647083 120.962176 \n", "C 180.551644 115.903338 177.480099 111.306447 173.608233 107.43458 \n", "C 169.736366 103.562713 165.139475 100.491169 160.080637 98.39573 \n", "C 155.021799 96.30029 149.599397 95.221707 144.12375 95.221707 \n", "C 138.648103 95.221707 133.225701 96.30029 128.166863 98.39573 \n", "C 123.108025 100.491169 118.511134 103.562713 114.639267 107.43458 \n", "C 110.767401 111.306447 107.695856 115.903338 105.600417 120.962176 \n", "C 103.504978 126.021013 102.426395 131.443416 102.426395 136.919062 \n", "C 102.426395 142.394709 103.504978 147.817112 105.600417 152.875949 \n", "C 107.695856 157.934787 110.767401 162.531678 114.639267 166.403545 \n", "C 118.511134 170.275412 123.108025 173.346956 128.166863 175.442395 \n", "C 133.225701 177.537835 138.648103 178.616418 144.12375 178.616418 \n", "C 149.599397 178.616418 155.021799 177.537835 160.080637 175.442395 \n", "C 165.139475 173.346956 169.736366 170.275412 173.608233 166.403545 \n", "C 177.480099 162.531678 180.551644 157.934787 182.647083 152.875949 \n", "C 184.742522 147.817112 185.821105 142.394709 185.821105 136.919062 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(182.647083 118.882488) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_11\">\n", "      <path d=\"M 208.641889 136.919062 \n", "C 208.641889 128.446618 206.973002 120.056558 203.730738 112.22904 \n", "C 200.488474 104.401522 195.735886 97.288772 189.744964 91.297849 \n", "C 183.754041 85.306926 176.641291 80.554339 168.813773 77.312074 \n", "C 160.986255 74.06981 152.596194 72.400924 144.12375 72.400924 \n", "C 135.651306 72.400924 127.261245 74.06981 119.433727 77.312074 \n", "C 111.606209 80.554339 104.493459 85.306926 98.502536 91.297849 \n", "C 92.511614 97.288772 87.759026 104.401522 84.516762 112.22904 \n", "C 81.274498 120.056558 79.605611 128.446618 79.605611 136.919062 \n", "C 79.605611 145.391507 81.274498 153.781567 84.516762 161.609085 \n", "C 87.759026 169.436603 92.511614 176.549353 98.502536 182.540276 \n", "C 104.493459 188.531199 111.606209 193.283786 119.433727 196.526051 \n", "C 127.261245 199.768315 135.651306 201.437201 144.12375 201.437201 \n", "C 152.596194 201.437201 160.986255 199.768315 168.813773 196.526051 \n", "C 176.641291 193.283786 183.754041 188.531199 189.744964 182.540276 \n", "C 195.735886 176.549353 200.488474 169.436603 203.730738 161.609085 \n", "C 206.973002 153.781567 208.641889 145.391507 208.641889 136.919063 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(203.730738 110.149352) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_12\">\n", "      <path d=\"M 231.462673 136.919062 \n", "C 231.462673 125.44982 229.203482 114.092102 224.814393 103.495904 \n", "C 220.425304 92.899706 213.991674 83.271097 205.881695 75.161118 \n", "C 197.771716 67.051139 188.143107 60.617508 177.546909 56.228419 \n", "C 166.950711 51.83933 155.592992 49.58014 144.12375 49.58014 \n", "C 132.654508 49.58014 121.296789 51.83933 110.700591 56.228419 \n", "C 100.104393 60.617508 90.475784 67.051139 82.365805 75.161118 \n", "C 74.255826 83.271097 67.822196 92.899706 63.433107 103.495904 \n", "C 59.044018 114.092102 56.784827 125.44982 56.784827 136.919062 \n", "C 56.784827 148.388305 59.044018 159.746023 63.433107 170.342221 \n", "C 67.822196 180.938419 74.255826 190.567028 82.365805 198.677007 \n", "C 90.475784 206.786986 100.104393 213.220617 110.700591 217.609706 \n", "C 121.296789 221.998795 132.654508 224.257985 144.12375 224.257985 \n", "C 155.592992 224.257985 166.950711 221.998795 177.546909 217.609706 \n", "C 188.143107 213.220617 197.771716 206.786986 205.881695 198.677007 \n", "C 213.991674 190.567028 220.425304 180.938419 224.814393 170.342221 \n", "C 229.203482 159.746023 231.462673 148.388305 231.462673 136.919063 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_12\">\n", "      <!-- 0.15 -->\n", "      <g transform=\"translate(224.814393 101.416216) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_13\">\n", "      <path d=\"M 254.283457 136.919062 \n", "C 254.283457 122.453022 251.433962 108.127646 245.898048 94.762768 \n", "C 240.362134 81.397889 232.247461 69.253422 222.018426 59.024387 \n", "C 211.78939 48.795352 199.644923 40.680678 186.280045 35.144764 \n", "C 172.915166 29.60885 158.58979 26.759356 144.12375 26.759356 \n", "C 129.65771 26.759356 115.332334 29.60885 101.967455 35.144764 \n", "C 88.602577 40.680678 76.45811 48.795352 66.229074 59.024387 \n", "C 56.000039 69.253422 47.885366 81.397889 42.349452 94.762768 \n", "C 36.813538 108.127646 33.964043 122.453022 33.964043 136.919062 \n", "C 33.964043 151.385103 36.813538 165.710479 42.349452 179.075357 \n", "C 47.885366 192.440236 56.000039 204.584703 66.229074 214.813738 \n", "C 76.45811 225.042773 88.602577 233.157447 101.967455 238.693361 \n", "C 115.332334 244.229275 129.65771 247.078769 144.12375 247.078769 \n", "C 158.58979 247.078769 172.915166 244.229275 186.280045 238.693361 \n", "C 199.644923 233.157447 211.78939 225.042773 222.018426 214.813738 \n", "C 232.247461 204.584703 240.362134 192.440236 245.898048 179.075357 \n", "C 251.433962 165.710479 254.283457 151.385103 254.283457 136.919063 \n", "\" clip-path=\"url(#pa51f46bd9e)\" style=\"fill: none; stroke: #b0b0b0; stroke-width: 0.8; stroke-linecap: square\"/>\n", "     </g>\n", "     <g id=\"text_13\">\n", "      <!-- 0.20 -->\n", "      <g transform=\"translate(245.898048 92.68308) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_14\">\n", "    <defs>\n", "     <path id=\"m24dc0cb545\" d=\"M 0 1.5 \n", "C 0.397805 1.5 0.77937 1.341951 1.06066 1.06066 \n", "C 1.341951 0.77937 1.5 0.397805 1.5 0 \n", "C 1.5 -0.397805 1.341951 -0.77937 1.06066 -1.06066 \n", "C 0.77937 -1.341951 0.397805 -1.5 0 -1.5 \n", "C -0.397805 -1.5 -0.77937 -1.341951 -1.06066 -1.06066 \n", "C -1.341951 -0.77937 -1.5 -0.397805 -1.5 0 \n", "C -1.5 0.397805 -1.341951 0.77937 -1.06066 1.06066 \n", "C -0.77937 1.341951 -0.397805 1.5 0 1.5 \n", "z\n", "\" style=\"stroke: #1f77b4\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#pa51f46bd9e)\">\n", "     <use xlink:href=\"#m24dc0cb545\" x=\"194.488246\" y=\"74.388388\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m24dc0cb545\" x=\"192.755137\" y=\"226.600034\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m24dc0cb545\" x=\"175.747827\" y=\"63.870813\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m24dc0cb545\" x=\"147.882302\" y=\"133.561253\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "     <use xlink:href=\"#m24dc0cb545\" x=\"232.172545\" y=\"78.186489\" style=\"fill: #1f77b4; stroke: #1f77b4\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 255.00375 136.919062 \n", "C 255.00375 122.**********.**********.93939 246.563513 94.487124 \n", "C 240.991401 81.**********.823669 68.**********.52775 58.515063 \n", "C 212.231831 48.219144 200.007955 40.051411 186.555689 34.4793 \n", "C 173.103423 28.907189 158.684378 26.039062 144.12375 26.039062 \n", "C 129.563122 26.039062 115.144077 28.907189 101.691811 34.4793 \n", "C 88.239545 40.051411 76.015669 48.219144 65.71975 58.515063 \n", "C 55.423831 68.810982 47.256099 81.034857 41.683987 94.487124 \n", "C 36.111876 107.93939 33.24375 122.358434 33.24375 136.919062 \n", "C 33.24375 151.479691 36.111876 165.898735 41.683987 179.351001 \n", "C 47.256099 192.803268 55.423831 205.027143 65.71975 215.323062 \n", "C 76.015669 225.618981 88.239545 233.786714 101.691811 239.358825 \n", "C 115.144077 244.930936 129.563122 247.799062 144.12375 247.799062 \n", "C 158.684378 247.799062 173.103423 244.930936 186.555689 239.358825 \n", "C 200.007955 233.786714 212.231831 225.618981 222.52775 215.323062 \n", "C 232.823669 205.027143 240.991401 192.803268 246.563513 179.351001 \n", "C 252.135624 165.898735 255.00375 151.479691 255.00375 136.919063 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 307.171023 247.799063 \n", "L 528.931023 247.799063 \n", "L 528.931023 26.039062 \n", "L 307.171023 26.039062 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_15\">\n", "      <defs>\n", "       <path id=\"mae8719ffe1\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mae8719ffe1\" x=\"307.171023\" y=\"247.799063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_14\">\n", "      <!-- −0.10 -->\n", "      <g transform=\"translate(291.848366 262.3975) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2212\" d=\"M 678 2272 \n", "L 4684 2272 \n", "L 4684 1741 \n", "L 678 1741 \n", "L 678 2272 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"179.199219\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_16\">\n", "      <g>\n", "       <use xlink:href=\"#mae8719ffe1\" x=\"344.131023\" y=\"247.799063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- −0.05 -->\n", "      <g transform=\"translate(328.808366 262.3975) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#mae8719ffe1\" x=\"381.091023\" y=\"247.799063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(369.95821 262.3975) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#mae8719ffe1\" x=\"418.051023\" y=\"247.799063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_17\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(406.91821 262.3975) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#mae8719ffe1\" x=\"455.011023\" y=\"247.799063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(443.87821 262.3975) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#mae8719ffe1\" x=\"491.971023\" y=\"247.799063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0.15 -->\n", "      <g transform=\"translate(480.83821 262.3975) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#mae8719ffe1\" x=\"528.931023\" y=\"247.799063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 0.20 -->\n", "      <g transform=\"translate(517.79821 262.3975) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_21\">\n", "     <!-- cx -->\n", "     <g transform=\"translate(412.342429 276.075625) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"54.980469\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_22\">\n", "      <defs>\n", "       <path id=\"m1138149e3b\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m1138149e3b\" x=\"307.171023\" y=\"247.799063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- −0.10 -->\n", "      <g transform=\"translate(269.52571 251.598281) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"179.199219\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m1138149e3b\" x=\"307.171023\" y=\"210.839063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- −0.05 -->\n", "      <g transform=\"translate(269.52571 214.638281) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-2212\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"83.789062\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"147.412109\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"179.199219\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"242.822266\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_24\">\n", "      <g>\n", "       <use xlink:href=\"#m1138149e3b\" x=\"307.171023\" y=\"173.879063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_24\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(277.905398 177.678281) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_25\">\n", "      <g>\n", "       <use xlink:href=\"#m1138149e3b\" x=\"307.171023\" y=\"136.919063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_25\">\n", "      <!-- 0.05 -->\n", "      <g transform=\"translate(277.905398 140.718281) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m1138149e3b\" x=\"307.171023\" y=\"99.959063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_26\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(277.905398 103.758281) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m1138149e3b\" x=\"307.171023\" y=\"62.999063\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_27\">\n", "      <!-- 0.15 -->\n", "      <g transform=\"translate(277.905398 66.798281) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-35\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#m1138149e3b\" x=\"307.171023\" y=\"26.039062\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_28\">\n", "      <!-- 0.20 -->\n", "      <g transform=\"translate(277.905398 29.838281) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_29\">\n", "     <!-- cy -->\n", "     <g transform=\"translate(263.446023 142.627656) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"54.980469\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"ContourSet_1\">\n", "    <path d=\"M 421.869683 165.663385 \n", "L 423.586343 171.508705 \n", "L 425.766866 177.40379 \n", "L 426.895766 183.587016 \n", "L 428.35527 189.68741 \n", "L 428.953195 195.931403 \n", "L 429.740111 202.143363 \n", "L 430.066435 208.396458 \n", "L 430.442128 215.16154 \n", "L 430.998878 221.914134 \n", "L 431.543728 228.697184 \n", "L 433.345802 235.259132 \n", "L 436.330207 241.906217 \n", "L 441.233203 247.296118 \n", "L 447.77282 250.520227 \n", "L 451.32372 251.468555 \n", "L 454.999 251.491707 \n", "L 458.812982 251.476519 \n", "L 462.533016 250.635071 \n", "L 466.262947 249.86079 \n", "L 469.771557 248.377028 \n", "L 473.326872 247.040516 \n", "L 476.573968 245.07001 \n", "L 479.932603 243.296482 \n", "L 482.906877 240.93438 \n", "L 488.698672 236.169051 \n", "L 493.991549 230.85504 \n", "L 498.776352 225.082302 \n", "L 503.049694 218.921346 \n", "L 507.253122 212.832859 \n", "L 509.936779 205.938191 \n", "L 513.136213 199.275282 \n", "L 514.836438 192.082232 \n", "L 517.040013 185.034709 \n", "L 517.755777 177.685491 \n", "L 518.979625 170.405267 \n", "L 518.720356 163.027447 \n", "L 518.861009 157.626481 \n", "L 518.206602 152.263463 \n", "L 517.841442 146.872769 \n", "L 516.681599 141.595678 \n", "L 515.823039 136.260965 \n", "L 514.17 131.116671 \n", "L 512.832634 125.880555 \n", "L 510.700825 120.914586 \n", "L 508.901698 115.818015 \n", "L 506.308313 111.076047 \n", "L 504.066538 106.156221 \n", "L 501.030506 101.682659 \n", "L 498.367666 96.974614 \n", "L 494.910543 92.814722 \n", "L 491.850805 88.350452 \n", "L 487.996757 84.550715 \n", "L 484.553184 80.338913 \n", "L 480.308281 76.936227 \n", "L 476.521574 73.020244 \n", "L 471.933104 70.084257 \n", "L 467.775037 66.568107 \n", "L 462.936648 64.069541 \n", "L 458.437297 61.008774 \n", "L 453.392866 58.967652 \n", "L 448.591138 56.394992 \n", "L 443.367699 54.848751 \n", "L 438.318824 52.807742 \n", "L 432.96718 51.7994 \n", "L 427.722871 50.328665 \n", "L 422.293006 49.901568 \n", "L 416.91422 49.043716 \n", "L 411.471235 49.246586 \n", "L 407.344475 49.13207 \n", "L 403.247769 49.642228 \n", "L 399.128716 49.933105 \n", "L 395.106414 50.867075 \n", "L 391.036433 51.59139 \n", "L 387.145466 52.987721 \n", "L 383.183234 54.178703 \n", "L 379.511548 56.085648 \n", "L 375.600152 57.835326 \n", "L 372.18811 60.427307 \n", "L 368.61757 62.816657 \n", "L 365.817279 66.074896 \n", "L 362.945223 69.341298 \n", "L 361.045672 73.254071 \n", "L 359.171031 81.668018 \n", "L 360.240544 88.013704 \n", "L 363.074869 93.791089 \n", "L 366.592914 99.167041 \n", "L 371.399985 103.429619 \n", "L 375.904209 107.75509 \n", "L 380.715043 111.736749 \n", "L 385.542763 115.698987 \n", "L 390.040748 120.031925 \n", "L 394.771983 124.065801 \n", "L 398.751097 128.843184 \n", "L 403.162399 133.240023 \n", "L 406.575094 138.450137 \n", "L 410.472399 143.305929 \n", "L 410.494983 143.349512 \n", "\" clip-path=\"url(#p0296cabb22)\" style=\"fill: none; stroke: #440154; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"line2d_29\">\n", "    <defs>\n", "     <path id=\"ma3b01b748e\" d=\"M -3 0 \n", "L 3 0 \n", "M 0 3 \n", "L 0 -3 \n", "\" style=\"stroke: #000000\"/>\n", "    </defs>\n", "    <g clip-path=\"url(#p0296cabb22)\">\n", "     <use xlink:href=\"#ma3b01b748e\" x=\"462.012889\" y=\"134.313007\" style=\"stroke: #000000\"/>\n", "    </g>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 307.171023 247.799063 \n", "L 307.171023 26.039063 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 528.931023 247.799063 \n", "L 528.931023 26.039063 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_7\">\n", "    <path d=\"M 307.171023 247.799063 \n", "L 528.931023 247.799063 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_8\">\n", "    <path d=\"M 307.171023 26.039062 \n", "L 528.931023 26.039062 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"text_30\">\n", "    <g clip-path=\"url(#p0296cabb22)\">\n", "     <!-- 0.68 -->\n", "     <g style=\"fill: #440154\" transform=\"translate(408.181936 146.368044) rotate(-303.750955) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-30\"/>\n", "      <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "      <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      <use xlink:href=\"#DejaVuSans-38\" x=\"159.033203\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pa51f46bd9e\">\n", "   <path d=\"M 255.00375 136.919062 \n", "C 255.00375 122.**********.**********.93939 246.563513 94.487124 \n", "C 240.991401 81.**********.823669 68.**********.52775 58.515063 \n", "C 212.231831 48.219144 200.007955 40.051411 186.555689 34.4793 \n", "C 173.103423 28.907189 158.684378 26.039062 144.12375 26.039062 \n", "C 129.563122 26.039062 115.144077 28.907189 101.691811 34.4793 \n", "C 88.239545 40.051411 76.015669 48.219144 65.71975 58.515063 \n", "C 55.423831 68.810982 47.256099 81.034857 41.683987 94.487124 \n", "C 36.111876 107.93939 33.24375 122.358434 33.24375 136.919062 \n", "C 33.24375 151.479691 36.111876 165.898735 41.683987 179.351001 \n", "C 47.256099 192.803268 55.423831 205.027143 65.71975 215.323062 \n", "C 76.015669 225.618981 88.239545 233.786714 101.691811 239.358825 \n", "C 115.144077 244.930936 129.563122 247.799063 144.12375 247.799063 \n", "C 158.684378 247.799063 173.103423 244.930936 186.555689 239.358825 \n", "C 200.007955 233.786714 212.231831 225.618981 222.52775 215.323062 \n", "C 232.823669 205.027143 240.991401 192.803268 246.563513 179.351001 \n", "C 252.135624 165.898735 255.00375 151.479691 255.00375 136.919063 \n", "M 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "C 144.12375 136.919062 144.12375 136.919062 144.12375 136.919062 \n", "z\n", "\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p0296cabb22\">\n", "   <rect x=\"307.171023\" y=\"26.039062\" width=\"221.76\" height=\"221.76\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 900x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rng = np.random.default_rng(1)\n", "\n", "truth = Namespace(cr=0.1, cphi=0, sr=0.1, sphi=2)\n", "truth.cx = truth.cr * np.cos(truth.cphi)\n", "truth.cy = truth.cr * np.sin(truth.cphi)\n", "\n", "d_r = rng.normal(truth.cr, truth.sr, size=5)\n", "d_phi = rng.normal(truth.cphi, truth.sphi, size=d_r.shape)\n", "\n", "cov = np.eye(2)\n", "cov[0, 0] = truth.sr**2\n", "cov[1, 1] = truth.sphi**2\n", "\n", "\n", "def nll(cx, cy):\n", "    cr = np.linalg.norm((cx, cy))\n", "    cphi = np.arctan2(cy, cx)\n", "    return -np.sum(\n", "        multivariate_normal((cr, cphi), cov).logpdf(np.transpose((d_r, d_phi)))\n", "    )\n", "\n", "\n", "m = Minuit(nll, cx=0.1, cy=0)\n", "m.errordef = Minuit.LIKELIHOOD\n", "m.migrad()\n", "m.hesse()\n", "m.minos()\n", "display(m.fmin, m.params)\n", "display(m.merrors)\n", "\n", "plt.figure(figsize=(9, 4))\n", "\n", "plt.subplot(121, polar=True)\n", "plt.plot(d_phi, d_r, \".\")\n", "\n", "plt.subplot(122, aspect=\"equal\")\n", "m.draw_mncontour(\"cx\", \"cy\", size=100)\n", "plt.plot(m.values[\"cx\"], m.values[\"cy\"], \"+k\")\n", "plt.xlim(-0.1, 0.2)\n", "plt.ylim(-0.1, 0.2);"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["truth = Namespace(cr=0.1, cphi=0, sr=0.1, sphi=2)\n", "truth.cx = truth.cr * np.cos(truth.cphi)\n", "truth.cy = truth.cr * np.sin(truth.cphi)\n", "truth.cov = np.eye(2)\n", "truth.cov[0, 0] = truth.sr**2\n", "truth.cov[1, 1] = truth.sphi**2\n", "\n", "n_tries = 500  # increase this to 500 get smoother curves (running will take a while)\n", "n_data = np.unique(np.geomspace(5, 100, 20, dtype=int))\n", "\n", "\n", "@joblib.delayed\n", "def run(n):\n", "    rng = np.random.default_rng(seed=n)\n", "\n", "    n_h = 0\n", "    n_m = 0\n", "    h_lus = []\n", "    m_lus = []\n", "    xs = []\n", "    for i_try in range(n_tries):\n", "        while True:\n", "            d_r = rng.normal(truth.cr, truth.sr, size=n)\n", "            d_phi = rng.normal(truth.cphi, truth.sphi, size=n)\n", "\n", "            def nll(cx, cy):\n", "                cr = np.linalg.norm((cx, cy))\n", "                cphi = np.arctan2(cy, cx)\n", "                return -np.sum(\n", "                    multivariate_normal((cr, cphi), truth.cov).logpdf(\n", "                        np.transpose((d_r, d_phi))\n", "                    )\n", "                )\n", "\n", "            m = Minuit(nll, cx=0.1, cy=0)\n", "            m.errordef = Minuit.LIKELIHOOD\n", "            try:\n", "                m.migrad()\n", "                if not m.valid:\n", "                    continue\n", "\n", "                m.hesse()\n", "\n", "                if not m.accurate:\n", "                    continue\n", "\n", "                m.minos(\"cx\")\n", "                if m.merrors[\"cx\"].is_valid:\n", "                    break\n", "\n", "            except Exception as e:\n", "                print(f\"exception in n={n} i_try={i_try}\")\n", "                print(e)\n", "\n", "        x = m.values[\"cx\"]\n", "        dx = m.errors[\"cx\"]\n", "        me = m.merrors[\"cx\"]\n", "        h_lu = x - dx, x + dx\n", "        m_lu = x + me.lower, x + me.upper\n", "        if h_lu[0] < truth.cx < h_lu[1]:\n", "            n_h += 1\n", "        if m_lu[0] < truth.cx < m_lu[1]:\n", "            n_m += 1\n", "        xs.append(x)\n", "        h_lus.append(h_lu)\n", "        m_lus.append(m_lu)\n", "\n", "    x = np.mean(xs)\n", "    h_l, h_u = np.mean(h_lus, axis=0)\n", "    m_l, m_u = np.mean(m_lus, axis=0)\n", "    return n_h, n_m, x, h_l, h_u, m_l, m_u\n", "\n", "\n", "n_h, n_m, x, hl, hu, ml, mu = np.transpose(joblib.Parallel(-1)(run(x) for x in n_data))\n", "\n", "h_pcov = n_h.astype(float) / n_tries\n", "m_pcov = n_m.astype(float) / n_tries"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n", "<!DOCTYPE svg PUBLIC \"-//W3C//DTD SVG 1.1//EN\"\n", "  \"http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd\">\n", "<svg xmlns:xlink=\"http://www.w3.org/1999/xlink\" width=\"559.54375pt\" height=\"270.315469pt\" viewBox=\"0 0 559.54375 270.315469\" xmlns=\"http://www.w3.org/2000/svg\" version=\"1.1\">\n", " <metadata>\n", "  <rdf:RDF xmlns:dc=\"http://purl.org/dc/elements/1.1/\" xmlns:cc=\"http://creativecommons.org/ns#\" xmlns:rdf=\"http://www.w3.org/1999/02/22-rdf-syntax-ns#\">\n", "   <cc:Work>\n", "    <dc:type rdf:resource=\"http://purl.org/dc/dcmitype/StillImage\"/>\n", "    <dc:date>2024-08-22T11:26:45.991662</dc:date>\n", "    <dc:format>image/svg+xml</dc:format>\n", "    <dc:creator>\n", "     <cc:Agent>\n", "      <dc:title>Matplotlib v3.9.2, https://matplotlib.org/</dc:title>\n", "     </cc:Agent>\n", "    </dc:creator>\n", "   </cc:Work>\n", "  </rdf:RDF>\n", " </metadata>\n", " <defs>\n", "  <style type=\"text/css\">*{stroke-linejoin: round; stroke-linecap: butt}</style>\n", " </defs>\n", " <g id=\"figure_1\">\n", "  <g id=\"patch_1\">\n", "   <path d=\"M 0 270.315469 \n", "L 559.54375 270.315469 \n", "L 559.54375 0 \n", "L 0 0 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "  </g>\n", "  <g id=\"axes_1\">\n", "   <g id=\"patch_2\">\n", "    <path d=\"M 50.14375 232.759219 \n", "L 278.**********.759219 \n", "L 278.416477 10.999219 \n", "L 50.14375 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"PolyCollection_1\">\n", "    <path d=\"M 60.519783 21.079219 \n", "L 60.519783 208.686493 \n", "L 73.14958 193.520676 \n", "L 93.077921 173.626243 \n", "L 101.236999 163.788148 \n", "L 108.535543 159.47407 \n", "L 121.16534 148.598816 \n", "L 136.622962 136.772976 \n", "L 145.293275 132.749312 \n", "L 156.551303 130.014021 \n", "L 169.181099 121.78982 \n", "L 179.859441 117.76314 \n", "L 191.241058 112.46646 \n", "L 201.013868 107.985414 \n", "L 212.72614 106.135031 \n", "L 224.061094 102.896543 \n", "L 234.925901 102.238797 \n", "L 245.284278 98.847459 \n", "L 256.782417 94.981134 \n", "L 268.040444 95.585708 \n", "L 268.040444 66.997388 \n", "L 268.040444 66.997388 \n", "L 256.782417 63.87553 \n", "L 245.284278 64.886329 \n", "L 234.925901 65.426816 \n", "L 224.061094 62.302022 \n", "L 212.72614 61.103156 \n", "L 201.013868 58.987102 \n", "L 191.241058 58.859793 \n", "L 179.859441 58.900887 \n", "L 169.181099 56.140118 \n", "L 156.551303 56.077259 \n", "L 145.293275 52.563842 \n", "L 136.622962 49.823044 \n", "L 121.16534 47.829982 \n", "L 108.535543 47.153888 \n", "L 101.236999 42.044781 \n", "L 93.077921 40.965979 \n", "L 73.14958 29.041878 \n", "L 60.519783 21.079219 \n", "z\n", "\" clip-path=\"url(#pca5fb65b49)\" style=\"fill: #1f77b4; fill-opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"PolyCollection_2\">\n", "    <path d=\"M 60.519783 31.051445 \n", "L 60.519783 222.679219 \n", "L 73.14958 204.806686 \n", "L 93.077921 181.187566 \n", "L 101.236999 170.399544 \n", "L 108.535543 164.984881 \n", "L 121.16534 153.537382 \n", "L 136.622962 140.400978 \n", "L 145.293275 135.766549 \n", "L 156.551303 132.583762 \n", "L 169.181099 123.794278 \n", "L 179.859441 119.265916 \n", "L 191.241058 113.733269 \n", "L 201.013868 108.937969 \n", "L 212.72614 106.927001 \n", "L 224.061094 103.503273 \n", "L 234.925901 102.64295 \n", "L 245.284278 99.188733 \n", "L 256.782417 95.25113 \n", "L 268.040444 95.805253 \n", "L 268.040444 67.156535 \n", "L 268.040444 67.156535 \n", "L 256.782417 64.064824 \n", "L 245.284278 65.120496 \n", "L 234.925901 65.707211 \n", "L 224.061094 62.707807 \n", "L 212.72614 61.648386 \n", "L 201.013868 59.623245 \n", "L 191.241058 59.690456 \n", "L 179.859441 59.899254 \n", "L 169.181099 57.491775 \n", "L 156.551303 57.846373 \n", "L 145.293275 54.485308 \n", "L 136.622962 52.201643 \n", "L 121.16534 51.065786 \n", "L 108.535543 50.884637 \n", "L 101.236999 46.604251 \n", "L 93.077921 46.145185 \n", "L 73.14958 36.949897 \n", "L 60.519783 31.051445 \n", "z\n", "\" clip-path=\"url(#pca5fb65b49)\" style=\"fill: #ff7f0e; fill-opacity: 0.5\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_1\">\n", "    <g id=\"xtick_1\">\n", "     <g id=\"line2d_1\">\n", "      <defs>\n", "       <path id=\"m11b273fce0\" d=\"M 0 0 \n", "L 0 3.5 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m11b273fce0\" x=\"108.535543\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_1\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(99.735543 247.357656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-31\" d=\"M 794 531 \n", "L 1825 531 \n", "L 1825 4091 \n", "L 703 3866 \n", "L 703 4441 \n", "L 1819 4666 \n", "L 2450 4666 \n", "L 2450 531 \n", "L 3481 531 \n", "L 3481 0 \n", "L 794 0 \n", "L 794 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "        <path id=\"DejaVuSans-30\" d=\"M 2034 4250 \n", "Q 1547 4250 1301 3770 \n", "Q 1056 3291 1056 2328 \n", "Q 1056 1369 1301 889 \n", "Q 1547 409 2034 409 \n", "Q 2525 409 2770 889 \n", "Q 3016 1369 3016 2328 \n", "Q 3016 3291 2770 3770 \n", "Q 2525 4250 2034 4250 \n", "z\n", "M 2034 4750 \n", "Q 2819 4750 3233 4129 \n", "Q 3647 3509 3647 2328 \n", "Q 3647 1150 3233 529 \n", "Q 2819 -91 2034 -91 \n", "Q 1250 -91 836 529 \n", "Q 422 1150 422 2328 \n", "Q 422 3509 836 4129 \n", "Q 1250 4750 2034 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_2\">\n", "     <g id=\"line2d_2\">\n", "      <g>\n", "       <use xlink:href=\"#m11b273fce0\" x=\"268.040444\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_2\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(259.240444 247.357656) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-32\" d=\"M 1228 531 \n", "L 3431 531 \n", "L 3431 0 \n", "L 469 0 \n", "L 469 531 \n", "Q 828 903 1448 1529 \n", "Q 2069 2156 2228 2338 \n", "Q 2531 2678 2651 2914 \n", "Q 2772 3150 2772 3378 \n", "Q 2772 3750 2511 3984 \n", "Q 2250 4219 1831 4219 \n", "Q 1534 4219 1204 4116 \n", "Q 875 4013 500 3803 \n", "L 500 4441 \n", "Q 881 4594 1212 4672 \n", "Q 1544 4750 1819 4750 \n", "Q 2544 4750 2975 4387 \n", "Q 3406 4025 3406 3419 \n", "Q 3406 3131 3298 2873 \n", "Q 3191 2616 2906 2266 \n", "Q 2828 2175 2409 1742 \n", "Q 1991 1309 1228 531 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_3\">\n", "     <g id=\"line2d_3\">\n", "      <defs>\n", "       <path id=\"mfc07b07203\" d=\"M 0 0 \n", "L 0 2 \n", "\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"60.519783\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_4\">\n", "     <g id=\"line2d_4\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"73.14958\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_5\">\n", "     <g id=\"line2d_5\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"83.827921\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_6\">\n", "     <g id=\"line2d_6\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"93.077921\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_7\">\n", "     <g id=\"line2d_7\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"101.236999\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_8\">\n", "     <g id=\"line2d_8\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"156.551303\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_9\">\n", "     <g id=\"line2d_9\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"184.638722\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_10\">\n", "     <g id=\"line2d_10\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"204.567062\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_11\">\n", "     <g id=\"line2d_11\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"220.024684\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_12\">\n", "     <g id=\"line2d_12\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"232.654481\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_13\">\n", "     <g id=\"line2d_13\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"243.332822\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_14\">\n", "     <g id=\"line2d_14\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"252.582822\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_15\">\n", "     <g id=\"line2d_15\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"260.7419\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_3\">\n", "     <!-- n -->\n", "     <g transform=\"translate(161.111364 261.035781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6e\" d=\"M 3513 2113 \n", "L 3513 0 \n", "L 2938 0 \n", "L 2938 2094 \n", "Q 2938 2591 2744 2837 \n", "Q 2550 3084 2163 3084 \n", "Q 1697 3084 1428 2787 \n", "Q 1159 2491 1159 1978 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1366 3272 1645 3428 \n", "Q 1925 3584 2291 3584 \n", "Q 2894 3584 3203 3211 \n", "Q 3513 2838 3513 2113 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-6e\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_2\">\n", "    <g id=\"ytick_1\">\n", "     <g id=\"line2d_16\">\n", "      <defs>\n", "       <path id=\"m80fe54e2e5\" d=\"M 0 0 \n", "L -3.5 0 \n", "\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </defs>\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"50.14375\" y=\"213.191888\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_4\">\n", "      <!-- 0.00 -->\n", "      <g transform=\"translate(20.878125 216.991106) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-2e\" d=\"M 684 794 \n", "L 1344 794 \n", "L 1344 0 \n", "L 684 0 \n", "L 684 794 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_2\">\n", "     <g id=\"line2d_17\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"50.14375\" y=\"186.204639\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_5\">\n", "      <!-- 0.02 -->\n", "      <g transform=\"translate(20.878125 190.003858) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_3\">\n", "     <g id=\"line2d_18\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"50.14375\" y=\"159.217391\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_6\">\n", "      <!-- 0.04 -->\n", "      <g transform=\"translate(20.878125 163.01661) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-34\" d=\"M 2419 4116 \n", "L 825 1625 \n", "L 2419 1625 \n", "L 2419 4116 \n", "z\n", "M 2253 4666 \n", "L 3047 4666 \n", "L 3047 1625 \n", "L 3713 1625 \n", "L 3713 1100 \n", "L 3047 1100 \n", "L 3047 0 \n", "L 2419 0 \n", "L 2419 1100 \n", "L 313 1100 \n", "L 313 1709 \n", "L 2253 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_4\">\n", "     <g id=\"line2d_19\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"50.14375\" y=\"132.230143\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_7\">\n", "      <!-- 0.06 -->\n", "      <g transform=\"translate(20.878125 136.029362) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-36\" d=\"M 2113 2584 \n", "Q 1688 2584 1439 2293 \n", "Q 1191 2003 1191 1497 \n", "Q 1191 994 1439 701 \n", "Q 1688 409 2113 409 \n", "Q 2538 409 2786 701 \n", "Q 3034 994 3034 1497 \n", "Q 3034 2003 2786 2293 \n", "Q 2538 2584 2113 2584 \n", "z\n", "M 3366 4563 \n", "L 3366 3988 \n", "Q 3128 4100 2886 4159 \n", "Q 2644 4219 2406 4219 \n", "Q 1781 4219 1451 3797 \n", "Q 1122 3375 1075 2522 \n", "Q 1259 2794 1537 2939 \n", "Q 1816 3084 2150 3084 \n", "Q 2853 3084 3261 2657 \n", "Q 3669 2231 3669 1497 \n", "Q 3669 778 3244 343 \n", "Q 2819 -91 2113 -91 \n", "Q 1303 -91 875 529 \n", "Q 447 1150 447 2328 \n", "Q 447 3434 972 4092 \n", "Q 1497 4750 2381 4750 \n", "Q 2619 4750 2861 4703 \n", "Q 3103 4656 3366 4563 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_5\">\n", "     <g id=\"line2d_20\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"50.14375\" y=\"105.242895\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_8\">\n", "      <!-- 0.08 -->\n", "      <g transform=\"translate(20.878125 109.042113) scale(0.1 -0.1)\">\n", "       <defs>\n", "        <path id=\"DejaVuSans-38\" d=\"M 2034 2216 \n", "Q 1584 2216 1326 1975 \n", "Q 1069 1734 1069 1313 \n", "Q 1069 891 1326 650 \n", "Q 1584 409 2034 409 \n", "Q 2484 409 2743 651 \n", "Q 3003 894 3003 1313 \n", "Q 3003 1734 2745 1975 \n", "Q 2488 2216 2034 2216 \n", "z\n", "M 1403 2484 \n", "Q 997 2584 770 2862 \n", "Q 544 3141 544 3541 \n", "Q 544 4100 942 4425 \n", "Q 1341 4750 2034 4750 \n", "Q 2731 4750 3128 4425 \n", "Q 3525 4100 3525 3541 \n", "Q 3525 3141 3298 2862 \n", "Q 3072 2584 2669 2484 \n", "Q 3125 2378 3379 2068 \n", "Q 3634 1759 3634 1313 \n", "Q 3634 634 3220 271 \n", "Q 2806 -91 2034 -91 \n", "Q 1263 -91 848 271 \n", "Q 434 634 434 1313 \n", "Q 434 1759 690 2068 \n", "Q 947 2378 1403 2484 \n", "z\n", "M 1172 3481 \n", "Q 1172 3119 1398 2916 \n", "Q 1625 2713 2034 2713 \n", "Q 2441 2713 2670 2916 \n", "Q 2900 3119 2900 3481 \n", "Q 2900 3844 2670 4047 \n", "Q 2441 4250 2034 4250 \n", "Q 1625 4250 1398 4047 \n", "Q 1172 3844 1172 3481 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       </defs>\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_6\">\n", "     <g id=\"line2d_21\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"50.14375\" y=\"78.255646\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_9\">\n", "      <!-- 0.10 -->\n", "      <g transform=\"translate(20.878125 82.054865) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_7\">\n", "     <g id=\"line2d_22\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"50.14375\" y=\"51.268398\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_10\">\n", "      <!-- 0.12 -->\n", "      <g transform=\"translate(20.878125 55.067617) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_8\">\n", "     <g id=\"line2d_23\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"50.14375\" y=\"24.28115\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_11\">\n", "      <!-- 0.14 -->\n", "      <g transform=\"translate(20.878125 28.080368) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" x=\"95.410156\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"159.033203\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_12\">\n", "     <!-- cx -->\n", "     <g transform=\"translate(14.798437 127.587812) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-63\" d=\"M 3122 3366 \n", "L 3122 2828 \n", "Q 2878 2963 2633 3030 \n", "Q 2388 3097 2138 3097 \n", "Q 1578 3097 1268 2742 \n", "Q 959 2388 959 1747 \n", "Q 959 1106 1268 751 \n", "Q 1578 397 2138 397 \n", "Q 2388 397 2633 464 \n", "Q 2878 531 3122 666 \n", "L 3122 134 \n", "Q 2881 22 2623 -34 \n", "Q 2366 -91 2075 -91 \n", "Q 1284 -91 818 406 \n", "Q 353 903 353 1747 \n", "Q 353 2603 823 3093 \n", "Q 1294 3584 2113 3584 \n", "Q 2378 3584 2631 3529 \n", "Q 2884 3475 3122 3366 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-78\" d=\"M 3513 3500 \n", "L 2247 1797 \n", "L 3578 0 \n", "L 2900 0 \n", "L 1881 1375 \n", "L 863 0 \n", "L 184 0 \n", "L 1544 1831 \n", "L 300 3500 \n", "L 978 3500 \n", "L 1906 2253 \n", "L 2834 3500 \n", "L 3513 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"54.980469\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_24\">\n", "    <path d=\"M 60.519783 114.882856 \n", "L 73.14958 111.281277 \n", "L 93.077921 107.296111 \n", "L 101.236999 102.916465 \n", "L 108.535543 103.313979 \n", "L 121.16534 98.214399 \n", "L 136.622962 93.29801 \n", "L 145.293275 92.656577 \n", "L 156.551303 93.04564 \n", "L 169.181099 88.964969 \n", "L 179.859441 88.332013 \n", "L 191.241058 85.663126 \n", "L 201.013868 83.486258 \n", "L 212.72614 83.619093 \n", "L 224.061094 82.599283 \n", "L 234.925901 83.832807 \n", "L 245.284278 81.866894 \n", "L 256.782417 79.428332 \n", "L 268.040444 81.291548 \n", "\" clip-path=\"url(#pca5fb65b49)\" style=\"fill: none; stroke: #000000; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_3\">\n", "    <path d=\"M 50.14375 232.759219 \n", "L 50.14375 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_4\">\n", "    <path d=\"M 278.**********.759219 \n", "L 278.416477 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_5\">\n", "    <path d=\"M 50.14375 232.759219 \n", "L 278.**********.759219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_6\">\n", "    <path d=\"M 50.14375 10.999219 \n", "L 278.416477 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_1\">\n", "    <g id=\"patch_7\">\n", "     <path d=\"M 206.13679 48.355469 \n", "L 271.416477 48.355469 \n", "Q 273.416477 48.355469 273.416477 46.355469 \n", "L 273.416477 17.999219 \n", "Q 273.416477 15.999219 271.416477 15.999219 \n", "L 206.13679 15.999219 \n", "Q 204.13679 15.999219 204.13679 17.999219 \n", "L 204.13679 46.355469 \n", "Q 204.13679 48.355469 206.13679 48.355469 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"patch_8\">\n", "     <path d=\"M 208.13679 27.597656 \n", "L 228.13679 27.597656 \n", "L 228.13679 20.597656 \n", "L 208.13679 20.597656 \n", "z\n", "\" style=\"fill: #1f77b4; fill-opacity: 0.5\"/>\n", "    </g>\n", "    <g id=\"text_13\">\n", "     <!-- HESSE -->\n", "     <g transform=\"translate(236.13679 27.597656) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-48\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 2753 \n", "L 3553 2753 \n", "L 3553 4666 \n", "L 4184 4666 \n", "L 4184 0 \n", "L 3553 0 \n", "L 3553 2222 \n", "L 1259 2222 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-45\" d=\"M 628 4666 \n", "L 3578 4666 \n", "L 3578 4134 \n", "L 1259 4134 \n", "L 1259 2753 \n", "L 3481 2753 \n", "L 3481 2222 \n", "L 1259 2222 \n", "L 1259 531 \n", "L 3634 531 \n", "L 3634 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-53\" d=\"M 3425 4513 \n", "L 3425 3897 \n", "Q 3066 4069 2747 4153 \n", "Q 2428 4238 2131 4238 \n", "Q 1616 4238 1336 4038 \n", "Q 1056 3838 1056 3469 \n", "Q 1056 3159 1242 3001 \n", "Q 1428 2844 1947 2747 \n", "L 2328 2669 \n", "Q 3034 2534 3370 2195 \n", "Q 3706 1856 3706 1288 \n", "Q 3706 609 3251 259 \n", "Q 2797 -91 1919 -91 \n", "Q 1588 -91 1214 -16 \n", "Q 841 59 441 206 \n", "L 441 856 \n", "Q 825 641 1194 531 \n", "Q 1563 422 1919 422 \n", "Q 2459 422 2753 634 \n", "Q 3047 847 3047 1241 \n", "Q 3047 1584 2836 1778 \n", "Q 2625 1972 2144 2069 \n", "L 1759 2144 \n", "Q 1053 2284 737 2584 \n", "Q 422 2884 422 3419 \n", "Q 422 4038 858 4394 \n", "Q 1294 4750 2059 4750 \n", "Q 2388 4750 2728 4690 \n", "Q 3069 4631 3425 4513 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-48\"/>\n", "      <use xlink:href=\"#DejaVuSans-45\" x=\"75.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"138.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"201.855469\"/>\n", "      <use xlink:href=\"#DejaVuSans-45\" x=\"265.332031\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"patch_9\">\n", "     <path d=\"M 208.13679 42.275781 \n", "L 228.13679 42.275781 \n", "L 228.13679 35.275781 \n", "L 208.13679 35.275781 \n", "z\n", "\" style=\"fill: #ff7f0e; fill-opacity: 0.5\"/>\n", "    </g>\n", "    <g id=\"text_14\">\n", "     <!-- MINOS -->\n", "     <g transform=\"translate(236.13679 42.275781) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-4d\" d=\"M 628 4666 \n", "L 1569 4666 \n", "L 2759 1491 \n", "L 3956 4666 \n", "L 4897 4666 \n", "L 4897 0 \n", "L 4281 0 \n", "L 4281 4097 \n", "L 3078 897 \n", "L 2444 897 \n", "L 1241 4097 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-49\" d=\"M 628 4666 \n", "L 1259 4666 \n", "L 1259 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4e\" d=\"M 628 4666 \n", "L 1478 4666 \n", "L 3547 763 \n", "L 3547 4666 \n", "L 4159 4666 \n", "L 4159 0 \n", "L 3309 0 \n", "L 1241 3903 \n", "L 1241 0 \n", "L 628 0 \n", "L 628 4666 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-4f\" d=\"M 2522 4238 \n", "Q 1834 4238 1429 3725 \n", "Q 1025 3213 1025 2328 \n", "Q 1025 1447 1429 934 \n", "Q 1834 422 2522 422 \n", "Q 3209 422 3611 934 \n", "Q 4013 1447 4013 2328 \n", "Q 4013 3213 3611 3725 \n", "Q 3209 4238 2522 4238 \n", "z\n", "M 2522 4750 \n", "Q 3503 4750 4090 4092 \n", "Q 4678 3434 4678 2328 \n", "Q 4678 1225 4090 567 \n", "Q 3503 -91 2522 -91 \n", "Q 1538 -91 948 565 \n", "Q 359 1222 359 2328 \n", "Q 359 3434 948 4092 \n", "Q 1538 4750 2522 4750 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-4d\"/>\n", "      <use xlink:href=\"#DejaVuSans-49\" x=\"86.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-4e\" x=\"115.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-4f\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"269.287109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", "  <g id=\"axes_2\">\n", "   <g id=\"patch_10\">\n", "    <path d=\"M 324.071023 232.759219 \n", "L 552.34375 232.759219 \n", "L 552.34375 10.999219 \n", "L 324.071023 10.999219 \n", "z\n", "\" style=\"fill: #ffffff\"/>\n", "   </g>\n", "   <g id=\"line2d_25\">\n", "    <path d=\"M 324.071023 81.962419 \n", "L 552.34375 81.962419 \n", "\" clip-path=\"url(#p52a1b6022c)\" style=\"fill: none; stroke-dasharray: 1.5,2.475; stroke-dashoffset: 0; stroke: #808080; stroke-width: 1.5\"/>\n", "   </g>\n", "   <g id=\"matplotlib.axis_3\">\n", "    <g id=\"xtick_16\">\n", "     <g id=\"line2d_26\">\n", "      <g>\n", "       <use xlink:href=\"#m11b273fce0\" x=\"382.462816\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_15\">\n", "      <!-- $\\mathdefault{10^{1}}$ -->\n", "      <g transform=\"translate(373.662816 247.357656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.684375)\"/>\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(128.203125 38.965625) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_17\">\n", "     <g id=\"line2d_27\">\n", "      <g>\n", "       <use xlink:href=\"#m11b273fce0\" x=\"541.967717\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_16\">\n", "      <!-- $\\mathdefault{10^{2}}$ -->\n", "      <g transform=\"translate(533.167717 247.357656) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\" transform=\"translate(0 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" transform=\"translate(63.623047 0.765625)\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" transform=\"translate(128.203125 39.046875) scale(0.7)\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_18\">\n", "     <g id=\"line2d_28\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"334.447056\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_19\">\n", "     <g id=\"line2d_29\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"347.076853\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_20\">\n", "     <g id=\"line2d_30\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"357.755194\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_21\">\n", "     <g id=\"line2d_31\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"367.005193\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_22\">\n", "     <g id=\"line2d_32\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"375.164272\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_23\">\n", "     <g id=\"line2d_33\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"430.478575\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_24\">\n", "     <g id=\"line2d_34\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"458.565994\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_25\">\n", "     <g id=\"line2d_35\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"478.494335\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_26\">\n", "     <g id=\"line2d_36\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"493.951957\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_27\">\n", "     <g id=\"line2d_37\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"506.581754\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_28\">\n", "     <g id=\"line2d_38\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"517.260095\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_29\">\n", "     <g id=\"line2d_39\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"526.510095\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"xtick_30\">\n", "     <g id=\"line2d_40\">\n", "      <g>\n", "       <use xlink:href=\"#mfc07b07203\" x=\"534.669173\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.6\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_17\">\n", "     <!-- cx -->\n", "     <g transform=\"translate(432.498793 261.035781) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-78\" x=\"54.980469\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"matplotlib.axis_4\">\n", "    <g id=\"ytick_9\">\n", "     <g id=\"line2d_41\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"324.071023\" y=\"232.759219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_18\">\n", "      <!-- 0.0 -->\n", "      <g transform=\"translate(301.167898 236.558437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_10\">\n", "     <g id=\"line2d_42\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"324.071023\" y=\"188.407219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_19\">\n", "      <!-- 0.2 -->\n", "      <g transform=\"translate(301.167898 192.206437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-32\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_11\">\n", "     <g id=\"line2d_43\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"324.071023\" y=\"144.055219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_20\">\n", "      <!-- 0.4 -->\n", "      <g transform=\"translate(301.167898 147.854437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-34\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_12\">\n", "     <g id=\"line2d_44\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"324.071023\" y=\"99.703219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_21\">\n", "      <!-- 0.6 -->\n", "      <g transform=\"translate(301.167898 103.502437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-36\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_13\">\n", "     <g id=\"line2d_45\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"324.071023\" y=\"55.351219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_22\">\n", "      <!-- 0.8 -->\n", "      <g transform=\"translate(301.167898 59.150437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-30\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-38\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"ytick_14\">\n", "     <g id=\"line2d_46\">\n", "      <g>\n", "       <use xlink:href=\"#m80fe54e2e5\" x=\"324.071023\" y=\"10.999219\" style=\"stroke: #000000; stroke-width: 0.8\"/>\n", "      </g>\n", "     </g>\n", "     <g id=\"text_23\">\n", "      <!-- 1.0 -->\n", "      <g transform=\"translate(301.167898 14.798437) scale(0.1 -0.1)\">\n", "       <use xlink:href=\"#DejaVuSans-31\"/>\n", "       <use xlink:href=\"#DejaVuSans-2e\" x=\"63.623047\"/>\n", "       <use xlink:href=\"#DejaVuSans-30\" x=\"95.410156\"/>\n", "      </g>\n", "     </g>\n", "    </g>\n", "    <g id=\"text_24\">\n", "     <!-- coverage probability -->\n", "     <g transform=\"translate(295.08821 173.358906) rotate(-90) scale(0.1 -0.1)\">\n", "      <defs>\n", "       <path id=\"DejaVuSans-6f\" d=\"M 1959 3097 \n", "Q 1497 3097 1228 2736 \n", "Q 959 2375 959 1747 \n", "Q 959 1119 1226 758 \n", "Q 1494 397 1959 397 \n", "Q 2419 397 2687 759 \n", "Q 2956 1122 2956 1747 \n", "Q 2956 2369 2687 2733 \n", "Q 2419 3097 1959 3097 \n", "z\n", "M 1959 3584 \n", "Q 2709 3584 3137 3096 \n", "Q 3566 2609 3566 1747 \n", "Q 3566 888 3137 398 \n", "Q 2709 -91 1959 -91 \n", "Q 1206 -91 779 398 \n", "Q 353 888 353 1747 \n", "Q 353 2609 779 3096 \n", "Q 1206 3584 1959 3584 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-76\" d=\"M 191 3500 \n", "L 800 3500 \n", "L 1894 563 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2284 0 \n", "L 1503 0 \n", "L 191 3500 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-65\" d=\"M 3597 1894 \n", "L 3597 1613 \n", "L 953 1613 \n", "Q 991 1019 1311 708 \n", "Q 1631 397 2203 397 \n", "Q 2534 397 2845 478 \n", "Q 3156 559 3463 722 \n", "L 3463 178 \n", "Q 3153 47 2828 -22 \n", "Q 2503 -91 2169 -91 \n", "Q 1331 -91 842 396 \n", "Q 353 884 353 1716 \n", "Q 353 2575 817 3079 \n", "Q 1281 3584 2069 3584 \n", "Q 2775 3584 3186 3129 \n", "Q 3597 2675 3597 1894 \n", "z\n", "M 3022 2063 \n", "Q 3016 2534 2758 2815 \n", "Q 2500 3097 2075 3097 \n", "Q 1594 3097 1305 2825 \n", "Q 1016 2553 972 2059 \n", "L 3022 2063 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-72\" d=\"M 2631 2963 \n", "Q 2534 3019 2420 3045 \n", "Q 2306 3072 2169 3072 \n", "Q 1681 3072 1420 2755 \n", "Q 1159 2438 1159 1844 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2956 \n", "Q 1341 3275 1631 3429 \n", "Q 1922 3584 2338 3584 \n", "Q 2397 3584 2469 3576 \n", "Q 2541 3569 2628 3553 \n", "L 2631 2963 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-61\" d=\"M 2194 1759 \n", "Q 1497 1759 1228 1600 \n", "Q 959 1441 959 1056 \n", "Q 959 750 1161 570 \n", "Q 1363 391 1709 391 \n", "Q 2188 391 2477 730 \n", "Q 2766 1069 2766 1631 \n", "L 2766 1759 \n", "L 2194 1759 \n", "z\n", "M 3341 1997 \n", "L 3341 0 \n", "L 2766 0 \n", "L 2766 531 \n", "Q 2569 213 2275 61 \n", "Q 1981 -91 1556 -91 \n", "Q 1019 -91 701 211 \n", "Q 384 513 384 1019 \n", "Q 384 1609 779 1909 \n", "Q 1175 2209 1959 2209 \n", "L 2766 2209 \n", "L 2766 2266 \n", "Q 2766 2663 2505 2880 \n", "Q 2244 3097 1772 3097 \n", "Q 1472 3097 1187 3025 \n", "Q 903 2953 641 2809 \n", "L 641 3341 \n", "Q 956 3463 1253 3523 \n", "Q 1550 3584 1831 3584 \n", "Q 2591 3584 2966 3190 \n", "Q 3341 2797 3341 1997 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-67\" d=\"M 2906 1791 \n", "Q 2906 2416 2648 2759 \n", "Q 2391 3103 1925 3103 \n", "Q 1463 3103 1205 2759 \n", "Q 947 2416 947 1791 \n", "Q 947 1169 1205 825 \n", "Q 1463 481 1925 481 \n", "Q 2391 481 2648 825 \n", "Q 2906 1169 2906 1791 \n", "z\n", "M 3481 434 \n", "Q 3481 -459 3084 -895 \n", "Q 2688 -1331 1869 -1331 \n", "Q 1566 -1331 1297 -1286 \n", "Q 1028 -1241 775 -1147 \n", "L 775 -588 \n", "Q 1028 -725 1275 -790 \n", "Q 1522 -856 1778 -856 \n", "Q 2344 -856 2625 -561 \n", "Q 2906 -266 2906 331 \n", "L 2906 616 \n", "Q 2728 306 2450 153 \n", "Q 2172 0 1784 0 \n", "Q 1141 0 747 490 \n", "Q 353 981 353 1791 \n", "Q 353 2603 747 3093 \n", "Q 1141 3584 1784 3584 \n", "Q 2172 3584 2450 3431 \n", "Q 2728 3278 2906 2969 \n", "L 2906 3500 \n", "L 3481 3500 \n", "L 3481 434 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-20\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-70\" d=\"M 1159 525 \n", "L 1159 -1331 \n", "L 581 -1331 \n", "L 581 3500 \n", "L 1159 3500 \n", "L 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "z\n", "M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-62\" d=\"M 3116 1747 \n", "Q 3116 2381 2855 2742 \n", "Q 2594 3103 2138 3103 \n", "Q 1681 3103 1420 2742 \n", "Q 1159 2381 1159 1747 \n", "Q 1159 1113 1420 752 \n", "Q 1681 391 2138 391 \n", "Q 2594 391 2855 752 \n", "Q 3116 1113 3116 1747 \n", "z\n", "M 1159 2969 \n", "Q 1341 3281 1617 3432 \n", "Q 1894 3584 2278 3584 \n", "Q 2916 3584 3314 3078 \n", "Q 3713 2572 3713 1747 \n", "Q 3713 922 3314 415 \n", "Q 2916 -91 2278 -91 \n", "Q 1894 -91 1617 61 \n", "Q 1341 213 1159 525 \n", "L 1159 0 \n", "L 581 0 \n", "L 581 4863 \n", "L 1159 4863 \n", "L 1159 2969 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-69\" d=\"M 603 3500 \n", "L 1178 3500 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 3500 \n", "z\n", "M 603 4863 \n", "L 1178 4863 \n", "L 1178 4134 \n", "L 603 4134 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-6c\" d=\"M 603 4863 \n", "L 1178 4863 \n", "L 1178 0 \n", "L 603 0 \n", "L 603 4863 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-74\" d=\"M 1172 4494 \n", "L 1172 3500 \n", "L 2356 3500 \n", "L 2356 3053 \n", "L 1172 3053 \n", "L 1172 1153 \n", "Q 1172 725 1289 603 \n", "Q 1406 481 1766 481 \n", "L 2356 481 \n", "L 2356 0 \n", "L 1766 0 \n", "Q 1100 0 847 248 \n", "Q 594 497 594 1153 \n", "L 594 3053 \n", "L 172 3053 \n", "L 172 3500 \n", "L 594 3500 \n", "L 594 4494 \n", "L 1172 4494 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "       <path id=\"DejaVuSans-79\" d=\"M 2059 -325 \n", "Q 1816 -950 1584 -1140 \n", "Q 1353 -1331 966 -1331 \n", "L 506 -1331 \n", "L 506 -850 \n", "L 844 -850 \n", "Q 1081 -850 1212 -737 \n", "Q 1344 -625 1503 -206 \n", "L 1606 56 \n", "L 191 3500 \n", "L 800 3500 \n", "L 1894 763 \n", "L 2988 3500 \n", "L 3597 3500 \n", "L 2059 -325 \n", "z\n", "\" transform=\"scale(0.015625)\"/>\n", "      </defs>\n", "      <use xlink:href=\"#DejaVuSans-63\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"54.980469\"/>\n", "      <use xlink:href=\"#DejaVuSans-76\" x=\"116.162109\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"175.341797\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"236.865234\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"277.978516\"/>\n", "      <use xlink:href=\"#DejaVuSans-67\" x=\"339.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-65\" x=\"402.734375\"/>\n", "      <use xlink:href=\"#DejaVuSans-20\" x=\"464.257812\"/>\n", "      <use xlink:href=\"#DejaVuSans-70\" x=\"496.044922\"/>\n", "      <use xlink:href=\"#DejaVuSans-72\" x=\"559.521484\"/>\n", "      <use xlink:href=\"#DejaVuSans-6f\" x=\"598.384766\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"659.566406\"/>\n", "      <use xlink:href=\"#DejaVuSans-61\" x=\"723.042969\"/>\n", "      <use xlink:href=\"#DejaVuSans-62\" x=\"784.322266\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"847.798828\"/>\n", "      <use xlink:href=\"#DejaVuSans-6c\" x=\"875.582031\"/>\n", "      <use xlink:href=\"#DejaVuSans-69\" x=\"903.365234\"/>\n", "      <use xlink:href=\"#DejaVuSans-74\" x=\"931.148438\"/>\n", "      <use xlink:href=\"#DejaVuSans-79\" x=\"970.357422\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "   <g id=\"line2d_47\">\n", "    <path d=\"M 334.447056 72.648499 \n", "L 347.076853 60.673459 \n", "L 367.005193 72.648499 \n", "L 375.164272 72.648499 \n", "L 382.462816 72.648499 \n", "L 395.092612 79.744819 \n", "L 410.550234 70.874419 \n", "L 419.220548 83.292979 \n", "L 430.478575 78.857779 \n", "L 443.108372 72.648499 \n", "L 453.786713 76.640179 \n", "L 465.16833 78.414259 \n", "L 474.941141 74.866099 \n", "L 486.653413 76.640179 \n", "L 497.988367 76.196659 \n", "L 508.853174 77.970739 \n", "L 519.211551 71.761459 \n", "L 530.70969 73.092019 \n", "L 541.967717 83.736499 \n", "\" clip-path=\"url(#p52a1b6022c)\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"line2d_48\">\n", "    <path d=\"M 334.447056 63.778099 \n", "L 347.076853 58.455859 \n", "L 367.005193 69.987379 \n", "L 375.164272 69.987379 \n", "L 382.462816 69.100339 \n", "L 395.092612 76.196659 \n", "L 410.550234 68.656819 \n", "L 419.220548 79.301299 \n", "L 430.478575 78.414259 \n", "L 443.108372 73.979059 \n", "L 453.786713 76.640179 \n", "L 465.16833 76.640179 \n", "L 474.941141 75.753139 \n", "L 486.653413 76.640179 \n", "L 497.988367 75.309619 \n", "L 508.853174 75.753139 \n", "L 519.211551 71.317939 \n", "L 530.70969 73.092019 \n", "L 541.967717 83.292979 \n", "\" clip-path=\"url(#p52a1b6022c)\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_11\">\n", "    <path d=\"M 324.071023 232.759219 \n", "L 324.071023 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_12\">\n", "    <path d=\"M 552.34375 232.759219 \n", "L 552.34375 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_13\">\n", "    <path d=\"M 324.071023 232.759219 \n", "L 552.34375 232.759219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"patch_14\">\n", "    <path d=\"M 324.071023 10.999219 \n", "L 552.34375 10.999219 \n", "\" style=\"fill: none; stroke: #000000; stroke-width: 0.8; stroke-linejoin: miter; stroke-linecap: square\"/>\n", "   </g>\n", "   <g id=\"legend_2\">\n", "    <g id=\"patch_15\">\n", "     <path d=\"M 480.064062 48.355469 \n", "L 545.34375 48.355469 \n", "Q 547.34375 48.355469 547.34375 46.355469 \n", "L 547.34375 17.999219 \n", "Q 547.34375 15.999219 545.34375 15.999219 \n", "L 480.064062 15.999219 \n", "Q 478.064062 15.999219 478.064062 17.999219 \n", "L 478.064062 46.355469 \n", "Q 478.064062 48.355469 480.064062 48.355469 \n", "z\n", "\" style=\"fill: #ffffff; opacity: 0.8; stroke: #cccccc; stroke-linejoin: miter\"/>\n", "    </g>\n", "    <g id=\"line2d_49\">\n", "     <path d=\"M 482.064062 24.097656 \n", "L 492.064062 24.097656 \n", "L 502.064062 24.097656 \n", "\" style=\"fill: none; stroke: #1f77b4; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_25\">\n", "     <!-- HESSE -->\n", "     <g transform=\"translate(510.064062 27.597656) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-48\"/>\n", "      <use xlink:href=\"#DejaVuSans-45\" x=\"75.195312\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"138.378906\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"201.855469\"/>\n", "      <use xlink:href=\"#DejaVuSans-45\" x=\"265.332031\"/>\n", "     </g>\n", "    </g>\n", "    <g id=\"line2d_50\">\n", "     <path d=\"M 482.064062 38.775781 \n", "L 492.064062 38.775781 \n", "L 502.064062 38.775781 \n", "\" style=\"fill: none; stroke: #ff7f0e; stroke-width: 1.5; stroke-linecap: square\"/>\n", "    </g>\n", "    <g id=\"text_26\">\n", "     <!-- MINOS -->\n", "     <g transform=\"translate(510.064062 42.275781) scale(0.1 -0.1)\">\n", "      <use xlink:href=\"#DejaVuSans-4d\"/>\n", "      <use xlink:href=\"#DejaVuSans-49\" x=\"86.279297\"/>\n", "      <use xlink:href=\"#DejaVuSans-4e\" x=\"115.771484\"/>\n", "      <use xlink:href=\"#DejaVuSans-4f\" x=\"190.576172\"/>\n", "      <use xlink:href=\"#DejaVuSans-53\" x=\"269.287109\"/>\n", "     </g>\n", "    </g>\n", "   </g>\n", "  </g>\n", " </g>\n", " <defs>\n", "  <clipPath id=\"pca5fb65b49\">\n", "   <rect x=\"50.14375\" y=\"10.999219\" width=\"228.272727\" height=\"221.76\"/>\n", "  </clipPath>\n", "  <clipPath id=\"p52a1b6022c\">\n", "   <rect x=\"324.071023\" y=\"10.999219\" width=\"228.272727\" height=\"221.76\"/>\n", "  </clipPath>\n", " </defs>\n", "</svg>\n"], "text/plain": ["<Figure size 900x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 2, figsize=(9, 4))\n", "plt.sca(ax[0])\n", "plt.fill_between(n_data, hl, hu, alpha=0.5, label=\"HESSE\")\n", "plt.fill_between(n_data, ml, mu, alpha=0.5, label=\"MINOS\")\n", "plt.plot(n_data, x, \"-k\")\n", "plt.legend()\n", "plt.xlabel(\"n\")\n", "plt.ylabel(\"cx\")\n", "plt.semilogx()\n", "plt.sca(ax[1])\n", "plt.plot(n_data, h_pcov, label=\"HESSE\")\n", "plt.plot(n_data, m_pcov, label=\"MINOS\")\n", "plt.axhline(0.68, ls=\":\", color=\"0.5\", zorder=0)\n", "plt.xlabel(r\"cx\")\n", "plt.ylabel(\"coverage probability\")\n", "plt.legend()\n", "plt.ylim(0, 1)\n", "plt.semilogx();"]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "Python 3.8.14 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 4}