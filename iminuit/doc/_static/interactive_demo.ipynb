{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Short demo of interactive fit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "1ef533261cdf44c88ab722730615534d", "version_major": 2, "version_minor": 0}, "text/plain": ["HBox(children=(Output(), VBox(children=(HBox(children=(Button(description='Fit', style=ButtonStyle()), ToggleB…"]}, "execution_count": null, "metadata": {}, "output_type": "execute_result"}], "source": ["import numpy as np\n", "from scipy.stats import norm\n", "from iminuit import Minuit, cost\n", "\n", "truth = 100.0, 200.0, 0.3, 0.1, 0.7, 0.2\n", "\n", "\n", "def scaled_cdf(xe, n1, n2, mu1, sigma1, mu2, sigma2):\n", "    return n1 * norm.cdf(xe, mu1, sigma1) + n2 * norm.cdf(xe, mu2, sigma2)\n", "\n", "\n", "xe = np.linspace(0, 1)\n", "m = np.diff(scaled_cdf(xe, *truth))\n", "n = np.random.default_rng(1).poisson(m)  # generate random histogram\n", "\n", "c = cost.ExtendedBinnedNLL(n, xe, scaled_cdf)\n", "m = Minuit(c, *truth)\n", "\n", "m.interactive()"]}], "metadata": {"keep_output": true, "kernelspec": {"display_name": "Python 3.8.13 ('venv': venv)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}, "vscode": {"interpreter": {"hash": "bdbf20ff2e92a3ae3002db8b02bd1dd1b287e934c884beb29a73dced9dbd0fa3"}}}, "nbformat": 4, "nbformat_minor": 2}