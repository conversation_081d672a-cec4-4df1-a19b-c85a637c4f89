.. _scipy: https://scipy.org/
.. _scipy.optimize: https://docs.scipy.org/doc/scipy/tutorial/optimize.html
.. _numba: https://numba.pydata.org
.. _numba_stats: https://github.com/<PERSON><PERSON><PERSON>ski/numba-stats
.. _jax: https://jax.readthedocs.io
.. _nlopt: https://nlopt.readthedocs.io/en/latest
.. _pyhf: https://github.com/diana-hep/pyhf
.. _ctapipe: https://github.com/cta-observatory/ctapipe
.. _lauztat: https://github.com/marinang/lauztat
.. _threeML: https://github.com/threeML/threeML
.. _Veusz: https://github.com/veusz/veusz
.. _TensorProb: https://github.com/tensorprob/tensorprob
.. _Scikit-HEP: https://scikit-hep.org
.. _probfit: https://github.com/scikit-hep/probfit
.. _gammapy: https://github.com/gammapy/gammapy
.. _flavio: https://github.com/flav-io/flavio
.. _zfit: https://github.com/zfit/zfit
.. _Minuit2: https://root.cern.ch/root/htmldoc/guides/minuit2/Minuit2.html
.. _PyMinuit: http://code.google.com/p/pyminuit
.. _ipythonnb: http://ipython.org/ipython-doc/dev/notebook/index.html
.. _Quasi Newton Method: https://en.wikipedia.org/wiki/Quasi-Newton_method
.. _DFP formula: https://en.wikipedia.org/wiki/Davidon-Fletcher-Powell_formula
.. _Variable Metric Method for Minimization: https://www.osti.gov/biblio/4252678
.. _A New Approach to Variable Metric Algorithm: http://comjnl.oxfordjournals.org/content/13/3/317.full.pdf+html
.. _MINUIT paper: https://doi.org/10.1016/0010-4655(75)90039-9
.. _setuptools: https://pypi.python.org/pypi/setuptools
.. _pytest: http://pytest.org
.. _zenodo: https://zenodo.org/record/4310361
.. _RooFit: https://root.cern/manual/roofit/
