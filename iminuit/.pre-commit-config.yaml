# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
#
# See https://github.com/pre-commit/pre-commit

repos:
# Standard hooks
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v5.0.0
  hooks:
  - id: check-case-conflict
  - id: check-docstring-first
  - id: check-executables-have-shebangs
  - id: check-merge-conflict
  - id: check-symlinks
  - id: check-yaml
    args: ["--allow-multiple-documents"]
  - id: debug-statements
  - id: end-of-file-fixer
  - id: mixed-line-ending
  - id: sort-simple-yaml
  - id: file-contents-sorter
  - id: trailing-whitespace
    exclude: ^doc/_static/.*.svg

# Ruff linter and formatter
- repo: https://github.com/astral-sh/ruff-pre-commit
  rev: 'v0.12.0'
  hooks:
    - id: ruff
      args: [--fix, --show-fixes]
    - id: ruff-format

# C++ formatting
- repo: https://github.com/pre-commit/mirrors-clang-format
  rev: v20.1.6
  hooks:
  - id: clang-format
    files: "src"

# CMake formatting
- repo: https://github.com/cheshirekow/cmake-format-precommit
  rev: v0.6.13
  hooks:
  - id: cmake-format
    additional_dependencies: [pyyaml]
    types: [file]
    files: (\.cmake|CMakeLists.txt)(.in)?$

# Python type checking
- repo: https://github.com/pre-commit/mirrors-mypy
  rev: 'v1.16.1'
  hooks:
  - id: mypy
    additional_dependencies: [numpy]
    files: "src"

# Clear Jupyter notebook output and remove empty cells
# Override this by adding "keep_output": true to "metadata" block
- repo: https://github.com/kynan/nbstripout
  rev: 0.8.1
  hooks:
  - id: nbstripout
    args: [--drop-empty-cells]

- repo: https://github.com/python-jsonschema/check-jsonschema
  rev: 0.33.1
  hooks:
  - id: check-github-workflows

- repo: https://github.com/henryiii/validate-pyproject-schema-store
  rev: 2025.06.13
  hooks:
  - id: validate-pyproject
