#!/usr/bin/env python3
"""Simple test to reproduce the PyGAD batch function issue."""

import numpy as np
import pygad


def test_pygad_batch_issue():
    """Test PyGAD batch function to understand the issue"""
    print("Testing PyGAD batch function behavior...")
    
    def fitness_func_single(ga_instance, solution, solution_idx):
        """Single solution fitness function"""
        fitness = -np.sum(solution**2)  # Negative because PyGAD maximizes
        print(f"Single: solution_idx={solution_idx}, solution={solution}, fitness={fitness}")
        return fitness
    
    def fitness_func_batch(ga_instance, batch_solutions, batch_indices):
        """Batch fitness function - this is where the bug might occur"""
        print(f"\nBatch function called:")
        print(f"  batch_solutions type: {type(batch_solutions)}")
        print(f"  batch_solutions shape: {batch_solutions.shape}")
        print(f"  batch_indices type: {type(batch_indices)}")
        print(f"  batch_indices: {batch_indices}")
        
        # Calculate fitness for each solution in the batch
        fitness_values = []
        for i in range(batch_solutions.shape[0]):
            solution = batch_solutions[i]
            fitness = -np.sum(solution**2)  # Negative because PyGAD maximizes
            fitness_values.append(fitness)
            print(f"  solution[{i}]: {solution} -> fitness: {fitness}")
        
        print(f"  Returning: {fitness_values}")
        return fitness_values
    
    # Test single core first
    print("\n" + "="*50)
    print("Testing single core (no batch)")
    print("="*50)
    try:
        ga_single = pygad.GA(
            num_generations=2,
            num_parents_mating=2,
            fitness_func=fitness_func_single,
            sol_per_pop=4,
            num_genes=2,
            gene_space=[{'low': -2, 'high': 2}] * 2,
            random_seed=42
        )
        ga_single.run()
        solution, fitness, _ = ga_single.best_solution()
        print(f"\n✓ Single core: Best fitness = {fitness:.6f}, solution = {solution}")
    except Exception as e:
        print(f"✗ Single core failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test multi core with batch
    print("\n" + "="*50)
    print("Testing batch mode (fitness_batch_size=2)")
    print("="*50)
    try:
        ga_batch = pygad.GA(
            num_generations=2,
            num_parents_mating=2,
            fitness_func=fitness_func_batch,
            fitness_batch_size=2,  # This triggers batch mode
            sol_per_pop=4,
            num_genes=2,
            gene_space=[{'low': -2, 'high': 2}] * 2,
            random_seed=42
        )
        ga_batch.run()
        solution, fitness, _ = ga_batch.best_solution()
        print(f"\n✓ Batch mode: Best fitness = {fitness:.6f}, solution = {solution}")
        return True
    except Exception as e:
        print(f"\n✗ Batch mode failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("Testing PyGAD batch function behavior...")
    print("This will help us understand how PyGAD calls batch fitness functions")
    print("=" * 70)
    
    success = test_pygad_batch_issue()
    
    print("\n" + "=" * 70)
    print("SUMMARY:")
    if success:
        print("✅ PyGAD batch function works correctly")
    else:
        print("🐛 PyGAD batch function has issues")
